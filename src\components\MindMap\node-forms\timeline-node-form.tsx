'use client';

import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, Calendar } from 'lucide-react';

interface TimelineNodeData {
  title: string;
  startDate: string;
  endDate: string;
  status: 'scheduled' | 'active' | 'completed' | 'delayed';
}

interface TimelineNodeFormProps {
  data: TimelineNodeData;
  onSave: (data: Partial<TimelineNodeData>) => void;
  onCancel: () => void;
}

export function TimelineNodeForm({ data, onSave, onCancel }: TimelineNodeFormProps) {
  const [formData, setFormData] = useState<TimelineNodeData>({
    title: data.title || 'New Timeline',
    startDate: data.startDate || '',
    endDate: data.endDate || '',
    status: data.status || 'scheduled',
  });

  const handleChange = (field: keyof TimelineNodeData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const getDuration = () => {
    if (!formData.startDate || !formData.endDate) return null;
    const start = new Date(formData.startDate);
    const end = new Date(formData.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const duration = getDuration();

  return (
    <div className="min-w-[400px]">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Timeline Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => handleChange('title', e.target.value)}
            className="mt-1"
            placeholder="Enter timeline title"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="startDate">Start Date</Label>
            <Input
              id="startDate"
              type="date"
              value={formData.startDate}
              onChange={e => handleChange('startDate', e.target.value)}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="endDate">End Date</Label>
            <Input
              id="endDate"
              type="date"
              value={formData.endDate}
              onChange={e => handleChange('endDate', e.target.value)}
              className="mt-1"
            />
          </div>
        </div>

        {duration && (
          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
            <Calendar className="h-4 w-4" />
            <span>Duration: {duration} days</span>
          </div>
        )}

        <div>
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={value => handleChange('status', value as TimelineNodeData['status'])}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="delayed">Delayed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
