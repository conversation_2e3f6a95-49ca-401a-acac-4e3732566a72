import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/Utility/db';
import { GitHubRepository } from '@/models/GitHubRepository';
import { GitHubWebhook } from '@/models/GitHubWebhook';
import { Task } from '@/models/Task';
import { Project } from '@/models/Project';
import { GitHubService } from '@/services/GitHub.service';
import { NotificationService } from '@/services/Notification.service';

export async function POST(request: NextRequest) {
  try {
    // Set a timeout for the entire webhook processing
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Webhook processing timeout')), 25000)
    );

    const processingPromise = async () => {
      await connectDB();

      const signature = request.headers.get('x-hub-signature-256');
      const event = request.headers.get('x-github-event');
      const delivery = request.headers.get('x-github-delivery');

      if (!signature || !event || !delivery) {
        return NextResponse.json({ error: 'Missing required headers' }, { status: 400 });
      }

      const payload = await request.text();
      const data = JSON.parse(payload);

      // Find the repository based on the webhook payload
      const repository = await GitHubRepository.findOne({
        repositoryId: data.repository?.id?.toString(),
        isConnected: true,
      });

      if (!repository) {
        console.log('Repository not found or not connected:', data.repository?.id);
        return NextResponse.json({ error: 'Repository not found' }, { status: 404 });
      }

      // Find the webhook record to get the secret
      const webhook = await GitHubWebhook.findOne({
        repositoryId: repository._id,
        active: true,
      });

      if (!webhook) {
        console.log('Webhook not found for repository:', repository.repositoryId);
        return NextResponse.json({ error: 'Webhook not found' }, { status: 404 });
      }

      // Verify webhook signature
      const isValid = GitHubService.verifyWebhookSignature(payload, signature, webhook.secret);
      if (!isValid) {
        console.log('Invalid webhook signature');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
      }

      // Update webhook delivery stats
      webhook.deliveryCount += 1;
      webhook.lastDeliveryAt = new Date();
      await webhook.save();

      // Handle different GitHub events
      switch (event) {
        case 'issues':
          await handleIssueEvent(data, repository);
          break;
        case 'pull_request':
          await handlePullRequestEvent(data, repository);
          break;
        case 'ping':
          console.log('Webhook ping received for repository:', repository.fullName);
          break;
        default:
          console.log('Unhandled webhook event:', event);
      }

      return NextResponse.json({ success: true });
    };

    // Execute with timeout
    return await Promise.race([processingPromise(), timeoutPromise]);
  } catch (error: any) {
    console.error('Webhook processing error:', error);

    // Try to update error count if we have webhook info
    try {
      const payload = await request.text();
      const data = JSON.parse(payload);
      const repository = await GitHubRepository.findOne({
        repositoryId: data.repository?.id?.toString(),
      });

      if (repository) {
        const webhook = await GitHubWebhook.findOne({
          repositoryId: repository._id,
        });

        if (webhook) {
          webhook.errorCount += 1;
          webhook.lastError = {
            message: error.message,
            timestamp: new Date(),
            statusCode: 500,
          };
          await webhook.save();
        }
      }
    } catch (updateError) {
      console.error('Failed to update webhook error stats:', updateError);
    }

    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Helper functions for webhook handling
async function handleIssueEvent(data: any, repository: any) {
  const { action, issue } = data;

  // Only handle issue creation if auto-create is enabled
  if (action === 'opened' && repository.syncSettings.autoCreateTasks) {
    try {
      // Check if task already exists
      const existingTask = await Task.findOne({
        'metadata.githubIssue.id': issue.id,
        userId: repository.userId,
      });

      if (existingTask) {
        console.log('Task already exists for issue:', issue.number);
        return;
      }

      // Find or create GitHub project
      let project = await Project.findOne({
        userId: repository.userId,
        name: 'GitHub Issues',
      });

      if (!project) {
        project = new Project({
          userId: repository.userId,
          organizationId: repository.organizationId,
          name: 'GitHub Issues',
          description: 'Tasks created from GitHub issues',
          color: '#6366f1',
          isDefault: false,
        });
        await project.save();
      }

      // Create task from GitHub issue
      const task = new Task({
        userId: repository.userId,
        organizationId: repository.organizationId,
        projectId: project._id,
        title: issue.title,
        description: issue.body || '',
        status: 'To Do',
        priority: 'Medium',
        tags: issue.labels?.map((label: any) => label.name) || [],
        metadata: {
          githubIssue: {
            id: issue.id,
            number: issue.number,
            url: issue.html_url,
            state: issue.state,
            repository: repository.fullName,
            createdAt: issue.created_at,
            updatedAt: issue.updated_at,
            author: {
              login: issue.user.login,
              avatarUrl: issue.user.avatar_url,
            },
          },
        },
      });

      await task.save();

      await NotificationService.createNotification({
        userId: repository.userId,
        title: 'New Task from GitHub',
        description: `Task "${task.title}" was created from GitHub issue #${issue.number}`,
        type: 'task',
        link: `/tasks/${task._id}`,
      });

      console.log('Created task from GitHub issue:', issue.number);
    } catch (error) {
      console.error('Failed to create task from GitHub issue:', error);
    }
  }

  // Handle issue updates
  if (action === 'closed' || action === 'reopened') {
    try {
      const task = await Task.findOne({
        'metadata.githubIssue.id': issue.id,
        userId: repository.userId,
      });

      if (task) {
        task.status = action === 'closed' ? 'Completed' : 'To Do';
        task.metadata.githubIssue.state = issue.state;
        task.metadata.githubIssue.updatedAt = issue.updated_at;
        await task.save();

        await NotificationService.createNotification({
          userId: repository.userId,
          title: 'Task Updated from GitHub',
          description: `Task "${task.title}" was ${action} based on GitHub issue #${issue.number}`,
          type: 'task',
          link: `/tasks/${task._id}`,
        });

        console.log('Updated task from GitHub issue:', issue.number);
      }
    } catch (error) {
      console.error('Failed to update task from GitHub issue:', error);
    }
  }
}

async function handlePullRequestEvent(data: any, repository: any) {
  const { action, pull_request } = data;

  // Only handle if PR sync is enabled
  if (!repository.syncSettings.syncPullRequests) {
    return;
  }

  console.log('Handling pull request event:', action, pull_request.number);

  // Add PR handling logic here if needed
  // For now, we'll just log the event
}
