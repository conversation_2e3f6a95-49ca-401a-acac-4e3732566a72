'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  Crown,
  Zap,
  Shield,
  HardDrive,
  Users,
  Database,
  Puzzle,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { PRICING_PLANS } from '@/constant/PricingPlans';

export const SubscriptionDashboard: React.FC = () => {
  const {
    subscription,
    usage,
    currentPlan,
    planStatus,
    billingCycle,
    isLoading,
    formatPrice,
    getUsagePercentage,
    formatUsageDisplay,
  } = useSubscription();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">No subscription information available</p>
        </CardContent>
      </Card>
    );
  }

  const getPlanIcon = () => {
    switch (currentPlan) {
      case 'free':
        return <HardDrive className="h-5 w-5 text-gray-500" />;
      case 'basic':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-5 w-5 text-orange-500" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      paused: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: AlertTriangle },
      cancelled: { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle },
      past_due: { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const currentPlanConfig = PRICING_PLANS.find(p => p.id === currentPlan);

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getPlanIcon()}
              <div>
                <CardTitle className="flex items-center gap-2">
                  {currentPlanConfig?.name || currentPlan} Plan
                  {getStatusBadge(planStatus)}
                </CardTitle>
                <CardDescription>{currentPlanConfig?.description}</CardDescription>
              </div>
            </div>
            {currentPlan !== 'free' && subscription.amount && (
              <div className="text-right">
                <p className="text-2xl font-bold">{formatPrice(subscription.amount)}</p>
                <p className="text-sm text-gray-500">
                  {billingCycle === 'monthly'
                    ? 'per month'
                    : billingCycle === 'yearly'
                      ? 'per year'
                      : ''}
                </p>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Users Usage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Team Members</span>
                </div>
                <span className="text-sm text-gray-600">{formatUsageDisplay('users')}</span>
              </div>
              <Progress value={getUsagePercentage('users')} className="h-2" />
            </div>

            {/* Projects Usage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Projects</span>
                </div>
                <span className="text-sm text-gray-600">{formatUsageDisplay('projects')}</span>
              </div>
              <Progress value={getUsagePercentage('projects')} className="h-2" />
            </div>

            {/* Integrations Usage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Puzzle className="h-4 w-4 text-purple-500" />
                  <span className="font-medium">Integrations</span>
                </div>
                <span className="text-sm text-gray-600">{formatUsageDisplay('integrations')}</span>
              </div>
              <Progress value={getUsagePercentage('integrations')} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Plan Features */}
      <Card>
        <CardHeader>
          <CardTitle>Plan Features</CardTitle>
          <CardDescription>What's included in your current plan</CardDescription>
        </CardHeader>
        <CardContent>
          {currentPlanConfig && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {currentPlanConfig.features.slice(0, 8).map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className={`${feature.included ? 'text-green-500' : 'text-gray-300'}`}>
                    {feature.included ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertTriangle className="h-4 w-4" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div
                      className={`text-sm font-medium ${feature.included ? 'text-gray-900 dark:text-white' : 'text-gray-400'}`}
                    >
                      {feature.name}
                    </div>
                    {feature.limit && <div className="text-xs text-gray-500">{feature.limit}</div>}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upgrade Recommendation */}
      {usage?.recommendations.shouldUpgrade && (
        <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
          <CardHeader>
            <CardTitle className="text-yellow-800 dark:text-yellow-200">
              Upgrade Recommended
            </CardTitle>
            <CardDescription className="text-yellow-700 dark:text-yellow-300">
              {usage.recommendations.reasons.join(', ')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="bg-yellow-600 hover:bg-yellow-700 text-white">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade to {usage.recommendations.recommendedPlan}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
