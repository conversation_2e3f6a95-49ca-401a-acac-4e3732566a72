'use client';

import { memo, useState } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Activity, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';

interface StatusNodeData {
  title: string;
  statuses: Array<{ label: string; color: string }>;
  currentStatus: string;
}

export const StatusNode = memo(({ id, data, selected }: NodeProps<StatusNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const handleDoubleClick = () => {
    startEditing(id, { ...data, nodeType: 'status' });
  };

  const getStatusColor = (color: string) => {
    switch (color) {
      case 'red':
        return 'bg-red-500';
      case 'green':
        return 'bg-green-500';
      case 'blue':
        return 'bg-blue-500';
      case 'yellow':
        return 'bg-yellow-500';
      case 'purple':
        return 'bg-purple-500';
      case 'orange':
        return 'bg-orange-500';
      case 'gray':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getCurrentStatusIndex = () => {
    if (!data.statuses || !Array.isArray(data.statuses)) {
      return -1;
    }
    return data.statuses.findIndex(status => status.label === data.currentStatus);
  };

  return (
    <Card
      className={`min-w-[250px] bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-purple-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-purple-600" />
            <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          </div>
          {isHovered && (
            <Edit3
              className="h-3 w-3 text-gray-400 cursor-pointer hover:text-gray-600"
              onClick={() => startEditing(id, { ...data, nodeType: 'status' })}
            />
          )}
        </div>

        <div className="bg-white/70 rounded-md p-3 border border-purple-100">
          <div className="flex items-center justify-between mb-2">
            <div className="h-1 flex-1 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-purple-500 rounded-full"
                style={{
                  width: `${data.statuses && data.statuses.length > 0 ? ((getCurrentStatusIndex() + 1) / data.statuses.length) * 100 : 0}%`,
                  transition: 'width 0.3s ease-in-out',
                }}
              />
            </div>
          </div>

          <div className="flex justify-between mt-2">
            {data.statuses && Array.isArray(data.statuses) ? (
              data.statuses.map((status, index) => {
                const isCurrent = status.label === data.currentStatus;

                return (
                  <div
                    key={index}
                    className={`flex flex-col items-center ${isCurrent ? 'scale-110' : ''}`}
                    style={{ width: `${100 / data.statuses.length}%` }}
                  >
                    <div
                      className={`w-3 h-3 rounded-full ${getStatusColor(status.color)} ${
                        isCurrent ? 'ring-2 ring-purple-300 ring-offset-2' : ''
                      }`}
                    />
                    <span
                      className={`text-xs mt-1 text-center ${
                        isCurrent ? 'font-medium text-purple-800' : 'text-gray-500'
                      }`}
                    >
                      {status.label}
                    </span>
                  </div>
                );
              })
            ) : (
              <div className="text-xs text-gray-500 text-center">No statuses defined</div>
            )}
          </div>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />
    </Card>
  );
});

StatusNode.displayName = 'StatusNode';
