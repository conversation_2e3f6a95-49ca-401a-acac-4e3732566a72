'use client';

import React from 'react';
import { Check, AlertCircle, Clock, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface SaveStatusIndicatorProps {
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  saveError: string | null;
  lastSaved: Date | null;
  className?: string;
  compact?: boolean;
}

export function SaveStatusIndicator({
  hasUnsavedChanges,
  isSaving,
  saveError,
  lastSaved,
  className,
  compact = false,
}: SaveStatusIndicatorProps) {
  const getStatusContent = () => {
    if (isSaving) {
      return {
        icon: <Loader2 className="w-3 h-3 animate-spin" />,
        text: compact ? 'Saving...' : 'Saving changes...',
        color: 'text-primary',
      };
    }

    if (saveError) {
      return {
        icon: <AlertCircle className="w-3 h-3" />,
        text: compact ? 'Save failed' : 'Failed to save',
        color: 'text-destructive',
      };
    }

    if (hasUnsavedChanges) {
      return {
        icon: <Clock className="w-3 h-3" />,
        text: compact ? 'Unsaved' : 'Unsaved changes',
        color: 'text-warning',
      };
    }

    if (lastSaved) {
      return {
        icon: <Check className="w-3 h-3" />,
        text: compact ? 'Saved' : `Saved ${formatDistanceToNow(lastSaved, { addSuffix: true })}`,
        color: 'text-success',
      };
    }

    return {
      icon: <Check className="w-3 h-3" />,
      text: compact ? 'Saved' : 'All changes saved',
      color: 'text-success',
    };
  };

  const status = getStatusContent();

  return (
    <div
      className={cn(
        'flex items-center gap-1 text-xs font-medium theme-transition',
        status.color,
        className
      )}
      title={saveError || status.text}
    >
      {status.icon}
      <span>{status.text}</span>
    </div>
  );
}
