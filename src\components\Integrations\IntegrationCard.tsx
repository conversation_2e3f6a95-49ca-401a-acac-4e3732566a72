'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Check, Plus, RefreshCw, ArrowRight, X, ExternalLink } from 'lucide-react';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { AVAILABLE_INTEGRATIONS } from '@/constant/Integration';

const cardVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { delay: i * 0.05, duration: 0.3 },
  }),
};

interface ConnectedIntegrationCardProps {
  integration: any;
  index: number;
  onDisconnect: () => void;
  onSync: () => void;
  onOpenHub?: () => void;
}

export const ConnectedIntegrationCard: React.FC<ConnectedIntegrationCardProps> = ({
  integration,
  index,
  onDisconnect,
  onSync,
  onOpenHub,
}) => {
  const integrationMeta =
    AVAILABLE_INTEGRATIONS[integration.provider as keyof typeof AVAILABLE_INTEGRATIONS];
  if (!integrationMeta) return null;

  return (
    <motion.div custom={index} variants={cardVariants} initial="hidden" animate="visible">
      <Card className="theme-surface-elevated hover-reveal glow-on-hover h-full flex flex-col">
        <CardHeader className="pb-3 border-b border-border/20">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-3">
              <div className="bg-primary/10 p-2 rounded-lg">
                <integrationMeta.icon className="h-4 w-4 text-primary" />
              </div>
              <CardTitle className="text-base theme-text-primary">{integrationMeta.name}</CardTitle>
            </div>
            <Badge variant="secondary" className="status-indicator-completed text-xs">
              <Check className="mr-1 h-3 w-3" />
              Connected
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pt-3 flex-grow">
          <p className="text-sm theme-text-secondary mb-4">{integrationMeta.description}</p>
          <div className="space-y-2 theme-surface p-3 rounded-md border border-border/20">
            <div className="flex justify-between text-xs">
              <span className="theme-text-secondary font-medium">Last synced</span>
              <span className="theme-text-primary font-medium">
                {integration.lastSyncedAt
                  ? formatDistanceToNow(new Date(integration.lastSyncedAt), { addSuffix: true })
                  : 'Never'}
              </span>
            </div>
            {integration.workspaceName && (
              <div className="flex justify-between text-xs">
                <span className="theme-text-secondary font-medium">Workspace</span>
                <span
                  className="theme-text-primary font-medium truncate max-w-[120px]"
                  title={integration.workspaceName}
                >
                  {integration.workspaceName}
                </span>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="flex flex-col pt-3 gap-2 border-t border-border/20">
          <div className="flex justify-between w-full gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 theme-button-ghost"
              onClick={onDisconnect}
            >
              <X className="mr-1 h-3 w-3" />
              Disconnect
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 theme-button-ghost"
              onClick={onSync}
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              Sync
            </Button>
          </div>
          {(integration.provider === 'notion' || integration.provider === 'github') &&
            onOpenHub && (
              <Button
                variant="default"
                size="sm"
                className="w-full theme-button-primary"
                onClick={onOpenHub}
              >
                <ArrowRight className="mr-1 h-3 w-3" />
                Open {integrationMeta.name} Hub
              </Button>
            )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

interface AvailableIntegrationCardProps {
  integration: any;
  index: number;
  onConnect: () => void;
}

export const AvailableIntegrationCard: React.FC<AvailableIntegrationCardProps> = ({
  integration,
  index,
  onConnect,
}) => (
  <motion.div
    key={integration.id}
    custom={index}
    variants={cardVariants}
    initial="hidden"
    animate="visible"
  >
    <Card className="theme-surface-elevated hover-reveal glow-on-hover h-full flex flex-col">
      <CardHeader className="pb-3 border-b border-border/20">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            <div className="bg-primary/10 p-2 rounded-lg">
              <integration.icon className="h-4 w-4 text-primary" />
            </div>
            <CardTitle className="text-base theme-text-primary">{integration.name}</CardTitle>
          </div>
          {integration.popular && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
              Popular
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-3 flex-grow">
        <p className="text-sm theme-text-secondary">{integration.description}</p>
        {integration.category && (
          <div className="mt-3">
            <Badge variant="outline" className="theme-surface text-xs">
              {integration.category.charAt(0).toUpperCase() + integration.category.slice(1)}
            </Badge>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3 border-t border-border/20">
        <Button
          variant={integration.available ? 'default' : 'outline'}
          className={`w-full text-sm ${integration.available ? 'theme-button-primary' : 'theme-button-ghost'}`}
          disabled={!integration.available}
          onClick={onConnect}
        >
          {integration.available ? (
            <>
              <Plus className="mr-1 h-3 w-3" />
              Connect
            </>
          ) : (
            'Coming Soon'
          )}
        </Button>
      </CardFooter>
    </Card>
  </motion.div>
);

interface FeaturedIntegrationCardProps {
  integration: any;
  onConnect: () => void;
  features: string[];
  learnMoreUrl?: string;
}

export const FeaturedIntegrationCard: React.FC<FeaturedIntegrationCardProps> = ({
  integration,
  onConnect,
  features,
  learnMoreUrl,
}) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3 }}
    className="mb-6"
  >
    <h3 className="text-base font-medium mb-3 flex items-center theme-text-primary">
      <Badge variant="outline" className="mr-2 bg-amber-50 text-amber-700 border-amber-200 text-xs">
        Featured
      </Badge>
      <span>Recommended for you</span>
    </h3>
    <Card className="theme-surface-elevated hover-reveal glow-on-hover border-2 border-primary/20">
      <div className="bg-primary/5 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="theme-surface p-2 rounded-lg">
            <integration.icon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-bold theme-text-primary">{integration.name}</h3>
            <p className="text-xs theme-text-secondary mt-1">{integration.description}</p>
          </div>
        </div>
        <Badge variant="default" className="bg-primary text-primary-foreground text-xs">
          Most Popular
        </Badge>
      </div>
      <CardContent className="pt-4">
        <div className="grid grid-cols-2 gap-2 text-xs">
          {features.map(feature => (
            <div key={feature} className="flex items-center">
              <Check className="h-3 w-3 text-success mr-2" />
              <span className="theme-text-secondary">{feature}</span>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t border-border/20 theme-surface p-3">
        {learnMoreUrl && (
          <a
            href={learnMoreUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-primary flex items-center hover:underline theme-transition"
          >
            Learn more
            <ExternalLink className="h-3 w-3 ml-1" />
          </a>
        )}
        <Button onClick={onConnect} size="sm" className="group theme-button-primary">
          <Plus className="mr-1 h-3 w-3 group-hover:rotate-90 theme-transition" />
          Connect {integration.name}
        </Button>
      </CardFooter>
    </Card>
  </motion.div>
);
