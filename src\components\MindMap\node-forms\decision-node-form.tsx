'use client';

import type React from 'react';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { X, Plus, Save } from 'lucide-react';

interface DecisionNodeData {
  title: string;
  options: string[];
  selected: string;
  status: 'pending' | 'active' | 'resolved';
  conditions: Array<{ handle: string; label: string }>;
}

interface DecisionNodeFormProps {
  data: DecisionNodeData;
  onSave: (data: Partial<DecisionNodeData>) => void;
  onCancel: () => void;
}

export function DecisionNodeForm({ data, onSave, onCancel }: DecisionNodeFormProps) {
  // Provide default values for all fields to handle null/undefined data
  const [formData, setFormData] = useState<DecisionNodeData>({
    title: data?.title || 'New Decision',
    options: data?.options ? [...data.options] : ['Option 1', 'Option 2'],
    selected: data?.selected || '',
    status: data?.status || 'pending',
    conditions: data?.conditions
      ? [...data.conditions]
      : [
          { handle: 'true', label: 'True' },
          { handle: 'false', label: 'False' },
        ],
  });

  const handleChange = (field: keyof DecisionNodeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, `Option ${prev.options.length + 1}`],
    }));
  };

  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  const handleOptionChange = (index: number, value: string) => {
    setFormData(prev => {
      const newOptions = [...prev.options];
      newOptions[index] = value;
      return { ...prev, options: newOptions };
    });
  };

  const handleAddCondition = () => {
    const newHandle = String.fromCharCode(97 + formData.conditions.length);
    setFormData(prev => ({
      ...prev,
      conditions: [
        ...prev.conditions,
        { handle: newHandle, label: `Condition ${prev.conditions.length + 1}` },
      ],
    }));
  };

  const handleRemoveCondition = (index: number) => {
    setFormData(prev => ({
      ...prev,
      conditions: prev.conditions.filter((_, i) => i !== index),
    }));
  };

  const handleConditionChange = (index: number, value: string) => {
    setFormData(prev => {
      const newConditions = [...prev.conditions];
      newConditions[index] = { ...newConditions[index], label: value };
      return { ...prev, conditions: newConditions };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="min-w-[350px] p-6">
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-semibold text-gray-700">
              Decision Title
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleChange('title', e.target.value)}
              className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
              placeholder="Enter decision title..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status" className="text-sm font-semibold text-gray-700">
              Status
            </Label>
            <Select value={formData.status} onValueChange={value => handleChange('status', value)}>
              <SelectTrigger className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">🟡 Pending</SelectItem>
                <SelectItem value="active">🔵 Active</SelectItem>
                <SelectItem value="resolved">🟢 Resolved</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-semibold text-gray-700">Decision Options</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddOption}
                className="h-8 px-3 text-xs border-gray-300 hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200"
              >
                <Plus className="h-3 w-3 mr-1" /> Add Option
              </Button>
            </div>

            <div className="space-y-2">
              {formData.options.map((option, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                    {index + 1}
                  </div>
                  <Input
                    value={option}
                    onChange={e => handleOptionChange(index, e.target.value)}
                    className="flex-1 h-8 border-gray-200 bg-white"
                    placeholder={`Option ${index + 1}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveOption(index)}
                    className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600 transition-colors duration-200"
                    disabled={formData.options.length <= 1}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="selected" className="text-sm font-semibold text-gray-700">
              Selected Option
            </Label>
            <Select
              value={formData.selected}
              onValueChange={value => handleChange('selected', value)}
            >
              <SelectTrigger className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200">
                <SelectValue placeholder="Choose the selected option..." />
              </SelectTrigger>
              <SelectContent>
                {formData.options.map((option, index) => (
                  <SelectItem key={index} value={option}>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                        {index + 1}
                      </div>
                      {option}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-semibold text-gray-700">Connection Labels</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddCondition}
                className="h-8 px-3 text-xs border-gray-300 hover:bg-green-50 hover:border-green-300 transition-colors duration-200"
              >
                <Plus className="h-3 w-3 mr-1" /> Add Label
              </Button>
            </div>

            <div className="space-y-2">
              {formData.conditions.map((condition, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                  <span className="flex-shrink-0 bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-mono font-medium">
                    {condition.handle}
                  </span>
                  <Input
                    value={condition.label}
                    onChange={e => handleConditionChange(index, e.target.value)}
                    className="flex-1 h-8 border-gray-200 bg-white"
                    placeholder={`Connection ${index + 1} label`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveCondition(index)}
                    className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600 transition-colors duration-200"
                    disabled={formData.conditions.length <= 1}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-4 py-2 border-gray-300 hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
          >
            <Save className="h-4 w-4 mr-2" /> Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
}
