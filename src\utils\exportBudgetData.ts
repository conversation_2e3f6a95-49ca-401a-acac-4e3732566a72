import { IBudget, IExpense } from '../models/Budget';
import { CURRENCY_OPTIONS } from '../constant/Currency';
import { toast } from 'sonner';

declare module 'jspdf' {
  interface jsPDF {
    lastAutoTable?: {
      finalY: number;
    };
  }
}

const formatDate = (date: Date): string => {
  return new Date(date).toISOString().split('T')[0];
};

export const generateBudgetCSV = (budget: IBudget, expenses: IExpense[]): string => {
  const currencySymbol = CURRENCY_OPTIONS.find(c => c.value === budget.currency)?.symbol || '$';

  let csv = 'Budget Summary\n';
  csv += `Project ID,${budget.projectId}\n`;
  csv += `Total Budget,${currencySymbol}${budget.totalBudget}\n`;
  csv += `Spent Amount,${currencySymbol}${budget.spentAmount}\n`;
  csv += `Remaining Amount,${currencySymbol}${budget.totalBudget - budget.spentAmount}\n`;
  csv += `Currency,${budget.currency}\n`;
  csv += `Status,${budget.status}\n\n`;
  csv += 'Budget Categories\n';
  csv += 'Name,Allocated Amount,Spent Amount,Remaining,Utilization %\n';

  budget.categories.forEach(category => {
    const utilization =
      category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0;
    csv += `${category.name},${currencySymbol}${category.allocatedAmount},${currencySymbol}${category.spentAmount},${currencySymbol}${category.allocatedAmount - category.spentAmount},${utilization.toFixed(1)}%\n`;
  });

  csv += '\n';

  csv += 'Expenses\n';
  csv += 'Date,Category,Amount,Description,Vendor,Receipt Number\n';

  expenses.forEach(expense => {
    const date = formatDate(expense.date);
    const description = expense.description ? `"${expense.description.replace(/"/g, '""')}"` : '';
    const vendor = expense.vendor || '';
    const receiptNumber = expense.receiptNumber || '';

    csv += `${date},${expense.category},${currencySymbol}${expense.amount},${description},${vendor},${receiptNumber}\n`;
  });

  return csv;
};

export const exportBudgetToCSV = (budget: IBudget, expenses: IExpense[]): void => {
  const csv = generateBudgetCSV(budget, expenses);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `budget_${budget.projectId}_${formatDate(new Date())}.csv`);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportBudgetToPDF = async (budget: IBudget, expenses: IExpense[]): Promise<void> => {
  try {
    const { jsPDF } = await import('jspdf');
    const autoTable = (await import('jspdf-autotable')).default;

    const doc = new jsPDF();
    const currencySymbol = CURRENCY_OPTIONS.find(c => c.value === budget.currency)?.symbol || '$';

    doc.setFontSize(20);
    doc.text('Budget Report', 14, 22);
    doc.setFontSize(16);
    doc.text('Budget Summary', 14, 35);

    autoTable(doc, {
      startY: 40,
      head: [['Item', 'Value']],
      body: [
        ['Project ID', `${budget.projectId}`],
        ['Total Budget', `${currencySymbol}${budget.totalBudget.toLocaleString()}`],
        ['Spent Amount', `${currencySymbol}${budget.spentAmount.toLocaleString()}`],
        [
          'Remaining Amount',
          `${currencySymbol}${(budget.totalBudget - budget.spentAmount).toLocaleString()}`,
        ],
        ['Currency', budget.currency],
        ['Status', budget.status],
      ],
    });
    doc.setFontSize(16);
    const categoriesY = doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 15 : 100;
    doc.text('Budget Categories', 14, categoriesY);

    const categoryRows = budget.categories.map(category => {
      const utilization =
        category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0;
      return [
        category.name,
        `${currencySymbol}${category.allocatedAmount.toLocaleString()}`,
        `${currencySymbol}${category.spentAmount.toLocaleString()}`,
        `${currencySymbol}${(category.allocatedAmount - category.spentAmount).toLocaleString()}`,
        `${utilization.toFixed(1)}%`,
      ];
    });

    autoTable(doc, {
      startY: doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 20 : categoriesY + 5,
      head: [['Name', 'Allocated', 'Spent', 'Remaining', 'Utilization']],
      body: categoryRows,
    });

    doc.setFontSize(16);
    const expensesY = doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 15 : 200;
    doc.text('Recent Expenses', 14, expensesY);

    const expenseRows = expenses
      .slice(0, 10)
      .map(expense => [
        formatDate(expense.date),
        expense.category,
        `${currencySymbol}${expense.amount.toLocaleString()}`,
        expense.description || '',
        expense.vendor || '',
      ]);

    autoTable(doc, {
      startY: doc.lastAutoTable?.finalY ? doc.lastAutoTable.finalY + 20 : expensesY + 5,
      head: [['Date', 'Category', 'Amount', 'Description', 'Vendor']],
      body: expenseRows,
    });
    const pageCount = (doc as any).internal.pages.length - 1;
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.text(
        `Generated on ${new Date().toLocaleDateString()} | Page ${i} of ${pageCount}`,
        doc.internal.pageSize.width / 2,
        doc.internal.pageSize.height - 10,
        { align: 'center' }
      );
    }
    doc.save(`budget_${budget.projectId}_${formatDate(new Date())}.pdf`);
  } catch (error) {
    toast.error('Failed to generate PDF. Make sure jspdf and jspdf-autotable are installed.');
    throw new Error('Failed to generate PDF. Make sure jspdf and jspdf-autotable are installed.');
  }
};
