import crypto from 'crypto';
import { GitHubService } from '@/services/GitHub.service';
import { Integration } from '@/models/Integration';
import { NotificationService } from '@/services/Notification.service';

export interface StateData {
  userId: string;
  organizationId?: string;
  popup: boolean;
  timestamp: number;
  nonce: string;
}

export class GitHubAuthHelper {
  static generateOAuthUrl(
    userId: string,
    organizationId?: string,
    isPopup: boolean = false
  ): {
    url: string;
    state: string;
  } {
    const appName = process.env.GITHUB_APP_NAME || 'TaskFluxio GitHub Integration';
    const state = crypto.randomBytes(32).toString('hex');

    const stateData: StateData = {
      userId,
      organizationId,
      popup: isPopup,
      timestamp: Date.now(),
      nonce: state,
    };

    const encodedState = Buffer.from(JSON.stringify(stateData)).toString('base64');
    const callbackUrl = `${process.env.NEXTAUTH_URL}/api/github/installation/callback`;
    const installUrl = `https://github.com/apps/${encodeURIComponent(appName)}/installations/new?state=${encodedState}&setup_action=install&redirect_uri=${encodeURIComponent(callbackUrl)}`;

    return {
      url: installUrl,
      state: encodedState,
    };
  }

  static parseState(state: string): StateData {
    try {
      const stateData = JSON.parse(Buffer.from(state, 'base64').toString());

      if (!stateData.userId) {
        throw new Error('User ID not found in state');
      }

      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      if (stateData.timestamp < oneHourAgo) {
        throw new Error('State has expired');
      }

      return stateData;
    } catch (error) {
      throw new Error('Invalid state parameter');
    }
  }

  static async handleInstallationCallback(
    installationId: string,
    stateData: StateData
  ): Promise<void> {
    const { userId, organizationId } = stateData;

    const appId = process.env.GITHUB_APP_ID;
    const privateKey = process.env.GITHUB_APP_PRIVATE_KEY;

    if (!appId || !privateKey) {
      throw new Error('GitHub App not configured');
    }

    // Generate JWT and get installation data
    const appJWT = GitHubService.generateAppJWT(appId, privateKey);
    const installationData = await GitHubService.getInstallation(appJWT, installationId);
    const installationToken = await GitHubService.getInstallationToken(appJWT, installationId);

    const existingIntegration = await Integration.findOne({
      userId,
      provider: 'github',
    });

    const integrationData = {
      accessToken: installationToken.token,
      refreshToken: installationToken.token,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      metadata: {
        installationId,
        appId,
        account: installationData.account,
        permissions: installationData.permissions,
        repositorySelection: installationData.repository_selection,
      },
      lastSyncedAt: new Date(),
    };

    if (existingIntegration) {
      Object.assign(existingIntegration, integrationData);
      await existingIntegration.save();
    } else {
      const newIntegrationData: any = {
        userId,
        provider: 'github',
        ...integrationData,
      };

      if (
        organizationId &&
        organizationId.trim() !== '' &&
        organizationId !== 'undefined' &&
        organizationId !== 'null'
      ) {
        newIntegrationData.organizationId = organizationId;
      }

      await Integration.create(newIntegrationData);
    }

    await NotificationService.createNotification({
      userId,
      title: 'GitHub Connected',
      description: `Successfully connected GitHub account: ${installationData.account.login}`,
      type: 'integration',
      link: '/github-hub',
    });
  }

  static generateSuccessPopupHTML(installationId: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>GitHub Connected</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              text-align: center;
              padding: 50px 20px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              margin: 0;
              min-height: 100vh;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            }
            .container {
              background: rgba(255, 255, 255, 0.1);
              padding: 40px;
              border-radius: 20px;
              backdrop-filter: blur(10px);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            .success-icon {
              font-size: 48px;
              margin-bottom: 20px;
            }
            h2 {
              margin: 0 0 10px 0;
              font-size: 24px;
              font-weight: 600;
            }
            p {
              margin: 10px 0;
              opacity: 0.9;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="success-icon">✅</div>
            <h2>GitHub Connected Successfully!</h2>
            <p>Your GitHub integration is now active.</p>
            <p>This window will close automatically...</p>
          </div>
          <script>
            console.log('GitHub success page loaded, sending message to parent...');
            
            try {
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({
                  type: 'GITHUB_OAUTH_SUCCESS',
                  data: { 
                    connected: true,
                    installationId: '${installationId}'
                  }
                }, '*');
                
                console.log('Success message sent to parent window');
              } else {
                console.log('No parent window available');
              }

              // Close popup after a short delay
              setTimeout(() => {
                console.log('Closing popup window');
                window.close();
              }, 1000);
              
            } catch (error) {
              console.error('Error communicating with parent window:', error);
              setTimeout(() => {
                window.location.href = '/integrations';
              }, 3000);
            }
          </script>
        </body>
      </html>
    `;
  }

  static generateErrorPopupHTML(errorMessage: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>GitHub Connection Failed</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              text-align: center;
              padding: 50px 20px;
              background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
              color: white;
              margin: 0;
              min-height: 100vh;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            }
            .container {
              background: rgba(255, 255, 255, 0.1);
              padding: 40px;
              border-radius: 20px;
              backdrop-filter: blur(10px);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            .error-icon {
              font-size: 48px;
              margin-bottom: 20px;
            }
            h2 {
              margin: 0 0 10px 0;
              font-size: 24px;
              font-weight: 600;
            }
            p {
              margin: 10px 0;
              opacity: 0.9;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="error-icon">❌</div>
            <h2>GitHub Connection Failed</h2>
            <p>Error: ${errorMessage}</p>
            <p>This window will close automatically...</p>
          </div>
          <script>
            console.log('GitHub error page loaded, sending error to parent...');
            
            try {
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({
                  type: 'GITHUB_OAUTH_ERROR',
                  error: '${errorMessage || 'Failed to connect GitHub'}'
                }, '*');
                
                console.log('Error message sent to parent window');
              }

              setTimeout(() => {
                console.log('Closing error popup window');
                window.close();
              }, 3000);
              
            } catch (e) {
              console.error('Error communicating with parent window:', e);
              setTimeout(() => {
                window.location.href = '/integrations';
              }, 4000);
            }
          </script>
        </body>
      </html>
    `;
  }
}
