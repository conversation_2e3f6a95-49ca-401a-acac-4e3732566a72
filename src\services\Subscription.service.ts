import axios from 'axios';
import { PRICING_PLANS, PricingPlan } from '@/constant/PricingPlans';

interface UsageCheck {
  isWithinLimit: boolean;
  currentUsage: number;
  limit: number | 'unlimited';
  percentage: number;
  isNearLimit: boolean;
  isAtLimit: boolean;
  isOverLimit: boolean;
}

// API Response Types
export interface SubscriptionResponse {
  subscription: {
    id: string;
    organizationId: string;
    plan: string;
    status: string;
    billingCycle: string;
    amount: number;
    currentPeriodEnd: string;
    features: Record<string, boolean>;
    usage: {
      projectsCreated: number;
      usersInvited: number;
      // Removed storage field as per requirements
      integrationsConnected: number;
      automationsCreated: number;
      apiCallsThisMonth: number;
    };
  };
}

export interface UsageSummaryResponse {
  usage: {
    organizationId: string;
    planId: string;
    metrics: {
      projects: number;
      users: number;
      integrations: number;
      automations: number;
      apiCalls: number;
    };
    limits: Record<
      string,
      {
        current: number;
        limit: number | 'unlimited';
        percentage: number;
        isNearLimit: boolean;
        isAtLimit: boolean;
        isOverLimit: boolean;
      }
    >;
    recommendations: {
      shouldUpgrade: boolean;
      recommendedPlan: string | null;
      reasons: string[];
    };
  };
}

export interface CreateSubscriptionRequest {
  planId: 'free' | 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'yearly';
}

export interface UpdateSubscriptionRequest {
  planId?: 'free' | 'basic' | 'professional' | 'enterprise';
  billingCycle?: 'monthly' | 'yearly';
  status?: 'active' | 'suspended' | 'cancelled' | 'past_due' | 'trialing' | 'paused';
}

export interface PaymentVerificationRequest {
  razorpay_payment_id: string;
  razorpay_subscription_id: string;
  razorpay_signature: string;
}

export class SubscriptionService {
  private static readonly baseURL = '/api/subscriptions';

  /**
   * Get current subscription
   */
  static async getSubscription(): Promise<SubscriptionResponse> {
    try {
      const response = await axios.get(this.baseURL);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch subscription');
    }
  }

  /**
   * Get usage summary
   */
  static async getUsageSummary(): Promise<UsageSummaryResponse> {
    try {
      const response = await axios.get(`${this.baseURL}/usage`);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch usage data');
    }
  }

  /**
   * Create subscription order
   */
  static async createSubscription(data: CreateSubscriptionRequest): Promise<any> {
    try {
      const response = await axios.post(`${this.baseURL}/create`, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create subscription');
    }
  }

  /**
   * Verify payment
   */
  static async verifyPayment(data: PaymentVerificationRequest): Promise<any> {
    try {
      const response = await axios.post(`${this.baseURL}/verify`, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Payment verification failed');
    }
  }

  /**
   * Update subscription
   */
  static async updateSubscription(data: UpdateSubscriptionRequest): Promise<SubscriptionResponse> {
    try {
      const response = await axios.put(this.baseURL, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to update subscription');
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post(`${this.baseURL}/cancel`);
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to cancel subscription');
    }
  }

  /**
   * Pause subscription
   */
  static async pauseSubscription(reason?: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post(`${this.baseURL}/pause`, { reason });
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to pause subscription');
    }
  }

  /**
   * Get plan configuration by plan ID
   */
  static getPlanConfig(planId: string): PricingPlan | null {
    return PRICING_PLANS.find(plan => plan.id === planId) || null;
  }

  /**
   * Calculate plan pricing with discounts
   */
  static calculatePlanPrice(
    planId: string,
    billingCycle: 'monthly' | 'yearly'
  ): {
    originalPrice: number;
    finalPrice: number;
    discount: number;
    discountPercentage: number;
  } {
    const plan = this.getPlanConfig(planId);
    if (!plan) {
      throw new Error('Invalid plan ID');
    }

    const originalPrice = billingCycle === 'yearly' ? plan.price.yearly : plan.price.monthly;
    const discountPercentage = billingCycle === 'yearly' ? plan.price.yearlyDiscount : 0;
    const discount = billingCycle === 'yearly' ? plan.price.monthly * 12 - plan.price.yearly : 0;
    const finalPrice = originalPrice;

    return {
      originalPrice: billingCycle === 'yearly' ? plan.price.monthly * 12 : originalPrice,
      finalPrice,
      discount,
      discountPercentage,
    };
  }

  /**
   * Get plan features configuration
   */
  static getPlanFeatures(planId: string): Record<string, boolean> {
    const plan = this.getPlanConfig(planId);
    if (!plan) {
      return {};
    }

    const features: Record<string, boolean> = {
      // Core features
      unlimitedProjects: plan.limits.projects === 'unlimited',
      unlimitedUsers: plan.limits.users === 'unlimited',
      advancedCalendarViews: ['basic', 'professional', 'enterprise'].includes(planId),
      customTaskFields: ['basic', 'professional', 'enterprise'].includes(planId),

      // Analytics & Reporting
      basicReporting: ['basic', 'professional', 'enterprise'].includes(planId),
      advancedAnalytics: ['professional', 'enterprise'].includes(planId),
      exportCapabilities: ['basic', 'professional', 'enterprise'].includes(planId),

      // Collaboration
      teamCollaboration: ['professional', 'enterprise'].includes(planId),
      projectSharing: ['professional', 'enterprise'].includes(planId),
      permissionControls: ['professional', 'enterprise'].includes(planId),
      timeTracking: ['professional', 'enterprise'].includes(planId),

      // Integrations & Automation
      basicIntegrations: ['basic', 'professional', 'enterprise'].includes(planId),
      advancedIntegrations: ['professional', 'enterprise'].includes(planId),
      customIntegrations: planId === 'enterprise',
      basicAutomation: ['basic', 'professional', 'enterprise'].includes(planId),
      advancedAutomation: ['professional', 'enterprise'].includes(planId),
      apiAccess: planId === 'enterprise',

      // Support & Service
      emailSupport: ['basic', 'professional', 'enterprise'].includes(planId),
      prioritySupport: ['professional', 'enterprise'].includes(planId),
      phoneSupport: planId === 'enterprise',
      dedicatedAccountManager: planId === 'enterprise',

      // Security & Compliance
      basicSecurity: true, // All plans
      advancedSecurity: planId === 'enterprise',
      ssoEnabled: planId === 'enterprise',
      auditLogs: planId === 'enterprise',

      // Customization
      customBranding: ['professional', 'enterprise'].includes(planId),
      whiteLabel: planId === 'enterprise',
    };

    return features;
  }

  /**
   * Get plan limits configuration
   */
  static getPlanLimits(planId: string): Record<string, number | 'unlimited'> {
    const plan = this.getPlanConfig(planId);
    if (!plan) {
      return {};
    }

    return {
      projects: plan.limits.projects,
      users: plan.limits.users,
      integrations: plan.limits.integrations,
      automations: plan.limits.automations,
      apiCalls: plan.limits.apiCalls,
    };
  }

  /**
   * Check if usage is within plan limits
   */
  static checkUsageLimit(currentUsage: number, limit: number | 'unlimited'): UsageCheck {
    if (limit === 'unlimited') {
      return {
        isWithinLimit: true,
        currentUsage,
        limit,
        percentage: 0,
        isNearLimit: false,
        isAtLimit: false,
        isOverLimit: false,
      };
    }

    const percentage = (currentUsage / (limit as number)) * 100;
    return {
      isWithinLimit: currentUsage < limit,
      currentUsage,
      limit,
      percentage,
      isNearLimit: percentage >= 80,
      isAtLimit: currentUsage >= limit,
      isOverLimit: currentUsage > limit,
    };
  }

  /**
   * Validate plan upgrade/downgrade
   */
  static validatePlanChange(
    currentPlan: string,
    newPlan: string,
    currentUsage: Record<string, number>
  ): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    const newPlanConfig = this.getPlanConfig(newPlan);
    if (!newPlanConfig) {
      errors.push('Invalid target plan');
      return { isValid: false, errors, warnings };
    }

    const newLimits = newPlanConfig.limits;

    // Check if current usage exceeds new plan limits
    if (newLimits.projects !== 'unlimited' && currentUsage.projects > newLimits.projects) {
      errors.push(
        `Current project count (${currentUsage.projects}) exceeds new plan limit (${newLimits.projects})`
      );
    }

    if (newLimits.users !== 'unlimited' && currentUsage.users > newLimits.users) {
      errors.push(
        `Current user count (${currentUsage.users}) exceeds new plan limit (${newLimits.users})`
      );
    }

    // Removed storage validation as per requirements

    if (
      newLimits.integrations !== 'unlimited' &&
      currentUsage.integrations > newLimits.integrations
    ) {
      errors.push(
        `Current integration count (${currentUsage.integrations}) exceeds new plan limit (${newLimits.integrations})`
      );
    }

    // Add warnings for feature loss
    const currentFeatures = this.getPlanFeatures(currentPlan);
    const newFeatures = this.getPlanFeatures(newPlan);

    Object.keys(currentFeatures).forEach(feature => {
      if (currentFeatures[feature] && !newFeatures[feature]) {
        warnings.push(
          `You will lose access to ${feature.replace(/([A-Z])/g, ' $1').toLowerCase()}`
        );
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Calculate prorated amount for plan changes
   */
  static calculateProration(
    currentPlan: string,
    newPlan: string,
    billingCycle: 'monthly' | 'yearly',
    daysRemaining: number
  ): {
    creditAmount: number;
    chargeAmount: number;
    netAmount: number;
  } {
    const currentPricing = this.calculatePlanPrice(currentPlan, billingCycle);
    const newPricing = this.calculatePlanPrice(newPlan, billingCycle);

    const daysInCycle = billingCycle === 'yearly' ? 365 : 30;
    const dailyCurrentRate = currentPricing.finalPrice / daysInCycle;
    const dailyNewRate = newPricing.finalPrice / daysInCycle;

    const creditAmount = dailyCurrentRate * daysRemaining;
    const chargeAmount = dailyNewRate * daysRemaining;
    const netAmount = chargeAmount - creditAmount;

    return {
      creditAmount: Math.max(0, creditAmount),
      chargeAmount: Math.max(0, chargeAmount),
      netAmount,
    };
  }

  /**
   * Get upgrade recommendations based on usage
   */
  static getUpgradeRecommendations(
    currentPlan: string,
    usage: Record<string, number>
  ): {
    shouldUpgrade: boolean;
    recommendedPlan: string | null;
    reasons: string[];
  } {
    const currentLimits = this.getPlanLimits(currentPlan);
    const reasons: string[] = [];
    let recommendedPlan: string | null = null;

    // Check if hitting limits
    if (currentLimits.projects !== 'unlimited' && usage.projects >= currentLimits.projects * 0.8) {
      reasons.push('Approaching project limit');
    }

    if (currentLimits.users !== 'unlimited' && usage.users >= currentLimits.users * 0.8) {
      reasons.push('Approaching user limit');
    }

    // Removed storage limit check as per requirements

    // Recommend next tier
    const planOrder = ['free', 'basic', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);

    if (reasons.length > 0 && currentIndex < planOrder.length - 1) {
      recommendedPlan = planOrder[currentIndex + 1];
    }

    return {
      shouldUpgrade: reasons.length > 0,
      recommendedPlan,
      reasons,
    };
  }

  /**
   * Format price for display
   */
  static formatPrice(amount: number, currency: string = 'INR'): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Get billing cycle display text
   */
  static getBillingCycleText(cycle: 'monthly' | 'yearly'): string {
    return cycle === 'yearly' ? 'Annual' : 'Monthly';
  }

  /**
   * Calculate next billing date
   */
  static calculateNextBillingDate(currentDate: Date, billingCycle: 'monthly' | 'yearly'): Date {
    const nextDate = new Date(currentDate);

    if (billingCycle === 'yearly') {
      nextDate.setFullYear(nextDate.getFullYear() + 1);
    } else {
      nextDate.setMonth(nextDate.getMonth() + 1);
    }

    return nextDate;
  }
}
