'use client';

import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, Mail, User } from 'lucide-react';

interface TeamNodeData {
  title: string;
  role: string;
  email: string;
  status: 'active' | 'busy' | 'offline';
}

interface TeamNodeFormProps {
  data: TeamNodeData;
  onSave: (data: Partial<TeamNodeData>) => void;
  onCancel: () => void;
}

export function TeamNodeForm({ data, onSave, onCancel }: TeamNodeFormProps) {
  const [formData, setFormData] = useState<TeamNodeData>({
    title: data.title || 'New Team Member',
    role: data.role || '',
    email: data.email || '',
    status: data.status || 'active',
  });

  const handleChange = (field: keyof TeamNodeData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleSendEmail = () => {
    if (formData.email) {
      window.location.href = `mailto:${formData.email}`;
    }
  };

  return (
    <div className="min-w-[400px]">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Name</Label>
          <div className="flex gap-2 mt-1">
            <User className="h-4 w-4 mt-3 text-gray-400" />
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleChange('title', e.target.value)}
              placeholder="Enter team member name"
              className="flex-1"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="role">Role</Label>
          <Input
            id="role"
            value={formData.role}
            onChange={e => handleChange('role', e.target.value)}
            className="mt-1"
            placeholder="e.g. Developer, Designer, Manager"
          />
        </div>

        <div>
          <Label htmlFor="email">Email</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={e => handleChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className="flex-1"
            />
            {formData.email && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleSendEmail}
                className="px-3"
              >
                <Mail className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={value => handleChange('status', value as TeamNodeData['status'])}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="busy">Busy</SelectItem>
              <SelectItem value="offline">Offline</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
