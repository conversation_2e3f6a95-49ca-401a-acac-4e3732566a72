'use client';

import type React from 'react';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, Flag, Calendar } from 'lucide-react';

interface MilestoneNodeData {
  title: string;
  date: string;
  status: 'pending' | 'completed' | 'overdue';
}

interface MilestoneNodeFormProps {
  data: MilestoneNodeData;
  onSave: (data: Partial<MilestoneNodeData>) => void;
  onCancel: () => void;
}

export function MilestoneNodeForm({ data, onSave, onCancel }: MilestoneNodeFormProps) {
  const [formData, setFormData] = useState<MilestoneNodeData>({
    title: data.title || 'New Milestone',
    date: data.date || new Date().toISOString().split('T')[0],
    status: data.status || 'pending',
  });

  const handleChange = (field: keyof MilestoneNodeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const statusOptions = [
    { value: 'pending', label: '🟡 Pending', color: 'text-yellow-600' },
    { value: 'completed', label: '🟢 Completed', color: 'text-green-600' },
    { value: 'overdue', label: '🔴 Overdue', color: 'text-red-600' },
  ];

  // Format date for display
  const formatDateForInput = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch {
      return new Date().toISOString().split('T')[0];
    }
  };

  return (
    <div className="min-w-[350px] p-6">
      <div className="flex items-center gap-3 mb-6 pb-4 border-b border-gray-200">
        <Flag className="h-5 w-5 text-green-600" />
        <h3 className="text-lg font-semibold text-gray-900">Edit Milestone</h3>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-semibold text-gray-700">
              Milestone Title
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleChange('title', e.target.value)}
              className="h-10 border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all duration-200"
              placeholder="Enter milestone title..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="date" className="text-sm font-semibold text-gray-700">
              Target Date
            </Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="date"
                type="date"
                value={formatDateForInput(formData.date)}
                onChange={e => handleChange('date', e.target.value)}
                className="h-10 pl-10 border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all duration-200"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status" className="text-sm font-semibold text-gray-700">
              Status
            </Label>
            <Select value={formData.status} onValueChange={value => handleChange('status', value)}>
              <SelectTrigger className="h-10 border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all duration-200">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <span className={option.color}>{option.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Preview */}
          <div className="p-3 bg-green-50 rounded-md border border-green-200">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-green-600" />
              <span className="text-green-700 font-medium">
                Target:{' '}
                {new Date(formData.date).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </span>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-4 py-2 border-gray-300 hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="px-4 py-2 bg-green-600 hover:bg-green-700 transition-colors duration-200"
          >
            <Save className="h-4 w-4 mr-2" /> Save Milestone
          </Button>
        </div>
      </form>
    </div>
  );
}
