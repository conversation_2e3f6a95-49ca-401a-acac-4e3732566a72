import React from 'react';
import { TeamMember } from './ResourceAllocationGrid';
import { Card, CardContent, CardHeader } from '../ui/card';
import { ScrollArea, ScrollBar } from '../ui/scroll-area';
import { Badge } from '../ui/badge';
import { format } from 'date-fns';
import Image from 'next/image';

interface ResourceTimelineViewProps {
  members: TeamMember[];
}

export function ResourceTimelineView({ members }: ResourceTimelineViewProps) {
  // Generate dates for the next 30 days
  const today = new Date();
  const dates = Array.from({ length: 30 }, (_, i) => {
    const date = new Date();
    date.setDate(today.getDate() + i);
    return date;
  });

  // Get allocation status for a member on a specific date
  const getAllocationStatus = (member: TeamMember, date: Date) => {
    const activeAllocations = member.allocations.filter(allocation => {
      const startDate = new Date(allocation.startDate);
      const endDate = new Date(allocation.endDate);
      return date >= startDate && date <= endDate;
    });

    if (activeAllocations.length === 0) return null;

    const totalCapacity = activeAllocations.reduce(
      (sum, allocation) => sum + allocation.capacity,
      0
    );

    if (totalCapacity > 100) {
      return { status: 'overallocated', capacity: totalCapacity };
    } else if (totalCapacity >= 80) {
      return { status: 'full', capacity: totalCapacity };
    } else if (totalCapacity > 0) {
      return { status: 'partial', capacity: totalCapacity };
    }

    return null;
  };

  // Get color class based on allocation status
  const getStatusColorClass = (status: string | null) => {
    if (!status) return 'bg-gray-100';

    switch (status) {
      case 'overallocated':
        return 'bg-red-500';
      case 'full':
        return 'bg-amber-500';
      case 'partial':
        return 'bg-green-500';
      default:
        return 'bg-gray-100';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Resource Timeline</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Available</span>
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-amber-500"></div>
              <span>Full</span>
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Over-allocated</span>
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="w-full">
          <div className="min-w-[1200px]">
            {/* Timeline header */}
            <div className="flex border-b">
              <div className="w-48 p-2 font-medium">Team Member</div>
              {dates.map((date, index) => (
                <div
                  key={index}
                  className={`w-10 p-1 text-center text-xs border-r ${
                    date.getDay() === 0 || date.getDay() === 6 ? 'bg-gray-50' : ''
                  }`}
                >
                  <div>{format(date, 'd')}</div>
                  <div>{format(date, 'EEE')}</div>
                </div>
              ))}
            </div>

            {/* Timeline rows */}
            {members.map(member => (
              <div key={member.id} className="flex border-b hover:bg-gray-50">
                <div className="w-48 p-2 flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 overflow-hidden">
                    {member.avatar && (
                      <Image
                        width={32}
                        height={32}
                        src={member.avatar}
                        alt={member.name}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{member.name}</div>
                    <div className="text-xs text-gray-500">{member.role}</div>
                  </div>
                </div>

                {dates.map((date, index) => {
                  const allocationStatus = getAllocationStatus(member, date);
                  return (
                    <div
                      key={index}
                      className={`w-10 p-1 border-r ${
                        date.getDay() === 0 || date.getDay() === 6 ? 'bg-gray-50' : ''
                      }`}
                    >
                      {allocationStatus && (
                        <div
                          className={`w-full h-6 rounded-sm ${getStatusColorClass(allocationStatus.status)}`}
                          title={`${allocationStatus.capacity}% allocated`}
                        ></div>
                      )}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
