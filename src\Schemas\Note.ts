import * as Yup from 'yup';

export const noteSchema = Yup.object({
  title: Yup.string().min(1, 'Title is required').max(200, 'Title too long'),
  content: Yup.string().default(''),
  contentType: Yup.string()
    .oneOf(['markdown', 'rich-text', 'plain-text', 'editorjs', 'plate'])
    .default('plate'),
  tags: Yup.array().of(Yup.string()).default([]),
  category: Yup.string().default('general'),
  color: Yup.string()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid color')
    .default('#ffffff'),
  isPinned: Yup.boolean().default(false),
  isFavorite: Yup.boolean().default(false),
  isPublic: Yup.boolean().default(false),
  settings: Yup.object({
    allowComments: Yup.boolean().default(true),
    allowDownload: Yup.boolean().default(true),
    allowPrint: Yup.boolean().default(true),
    autoSave: Yup.boolean().default(true),
    fontSize: Yup.string().oneOf(['small', 'medium', 'large']).default('medium'),
    theme: Yup.string().oneOf(['light', 'dark', 'auto']).default('auto'),
  }).default({}),
});
