import React, { useMemo } from 'react';
import { startOfDay, format, parseISO } from 'date-fns';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar as CalendarIcon, Clock, Users, MapPin, Repeat, Link } from 'lucide-react';
import { IEvent } from '@/models/Event';

interface CalendarViewProps {
  events: IEvent[];
  currentDate: Date;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  onEventClick: (event: IEvent) => void;
  selectedEvent?: IEvent | null;
  isCreatingEvent?: boolean;
}
const PRIORITY_COLORS = {
  low: '#10b981',
  medium: '#f59e0b',
  high: '#ef4444',
};

const AgendaView: React.FC<CalendarViewProps> = ({
  events,
  currentDate,
  onEventClick,
  selectedEvent,
}) => {
  const sortedEvents = useMemo(() => {
    return events
      .filter(event => new Date(event.startTime) >= startOfDay(currentDate))
      .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
  }, [events, currentDate]);

  const groupedEvents = useMemo(() => {
    return sortedEvents.reduce(
      (groups, event) => {
        const date = format(new Date(event.startTime), 'yyyy-MM-dd');
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(event);
        return groups;
      },
      {} as Record<string, IEvent[]>
    );
  }, [sortedEvents]);

  if (sortedEvents.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 theme-text-secondary empty-state-container">
        <div className="text-center">
          <CalendarIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <div className="text-lg font-medium empty-state-title">No Upcoming Events</div>
          <div className="text-sm empty-state-description">
            No events scheduled for the selected period.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {Object.entries(groupedEvents).map(([date, dayEvents]) => (
        <Card key={date} className="overflow-hidden theme-surface-elevated theme-shadow-sm">
          <CardHeader className="pb-3 bg-secondary">
            <CardTitle className="text-lg flex items-center gap-2 theme-text-primary">
              <CalendarIcon className="w-5 h-5" />
              {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
              <Badge variant="secondary" className="ml-auto theme-badge-secondary">
                {dayEvents.length} {dayEvents.length === 1 ? 'event' : 'events'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-0">
              {dayEvents.map(event => {
                const isSelected = selectedEvent && String(event._id) === String(selectedEvent._id);

                return (
                  <div
                    key={String(event._id)}
                    className={`
                      flex items-center gap-4 p-4 border-b theme-border last:border-0
                      cursor-pointer hover:bg-accent/50 theme-transition
                      ${isSelected ? 'bg-primary/10 border-primary/20' : ''}
                    `}
                    onClick={() => onEventClick(event)}
                  >
                    <div
                      className="w-4 h-12 rounded theme-shadow-sm"
                      style={{ backgroundColor: event.colorId || '#3b82f6' }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="font-medium text-lg truncate theme-text-primary">
                            {event.title}
                          </div>
                          <div className="text-sm theme-text-secondary flex items-center gap-4 mt-1">
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {event.isAllDay
                                ? 'All day'
                                : `${format(new Date(event.startTime), 'HH:mm')} - ${format(new Date(event.endTime), 'HH:mm')}`}
                            </span>
                            {event.location && (
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {event.location}
                              </span>
                            )}
                            {event.attendees && event.attendees.length > 0 && (
                              <span className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {event.attendees.length} attendee
                                {event.attendees.length > 1 ? 's' : ''}
                              </span>
                            )}
                          </div>
                          {event.description && (
                            <div className="text-sm theme-text-secondary mt-2 line-clamp-2">
                              {event.description}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <Badge
                            variant="outline"
                            className="text-xs"
                            style={{
                              borderColor: PRIORITY_COLORS[event.priority],
                              color: PRIORITY_COLORS[event.priority],
                            }}
                          >
                            {event.priority}
                          </Badge>
                          {event.isRecurring && <Repeat className="w-4 h-4 theme-text-secondary" />}
                          {event.taskIds && event.taskIds.length > 0 && (
                            <Link className="w-4 h-4 theme-text-secondary" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default AgendaView;
