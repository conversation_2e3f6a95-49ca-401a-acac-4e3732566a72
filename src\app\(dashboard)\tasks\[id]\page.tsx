'use client';

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import FileUploader from '@/components/Global/FileUploader';
import TaskDependencyGraph from '@/components/Tasks/TaskDependencyGraph';
import {
  Calendar,
  AlertCircle,
  CheckCircle2,
  Circle,
  Edit,
  Save,
  X,
  ChevronLeft,
  MessageSquare,
  Paperclip,
  Play,
  Pause,
  Flag,
} from 'lucide-react';
import { TaskStatus, TaskPriority } from '@/components/Tasks/types';
import { TaskService } from '@/services/Task.service';

export default function TaskDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('details');
  const [isEditing, setIsEditing] = useState(false);
  const [editFormData, setEditFormData] = useState<any>({});
  const [newComment, setNewComment] = useState('');
  const [timeTracking, setTimeTracking] = useState<{
    isTracking: boolean;
    startTime: string | null;
  }>({ isTracking: false, startTime: null });

  // Fetch task details
  const {
    data: task,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['task', id],
    queryFn: () => TaskService.getTaskById(id as string),
    enabled: !!id,
  });

  // Fetch task dependencies
  const { data: dependencies } = useQuery({
    queryKey: ['task-dependencies', id],
    queryFn: () =>
      TaskService.getTaskDependencies
        ? TaskService.getTaskDependencies(id as string)
        : Promise.resolve([]),
    enabled: !!id,
  });

  // Fetch task comments and history
  const { data: taskActivity } = useQuery({
    queryKey: ['task-activity', id],
    queryFn: () =>
      TaskService.getTaskActivity ? TaskService.getTaskActivity(id as string) : Promise.resolve([]),
    enabled: !!id,
  });

  // Initialize edit form data
  useEffect(() => {
    if (task) {
      setEditFormData({
        name: task.name,
        description: task.description,
        status: task.status,
        priority: task.priority,
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '',
        estimatedTime: task.estimatedTime || 0,
      });
    }
  }, [task]);

  // Update task mutation
  const updateTaskMutation = useMutation({
    mutationFn: (updates: any) => TaskService.updateTask(id as string, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task', id] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      toast.success('Task updated successfully');
      setIsEditing(false);
    },
    onError: () => {
      toast.error('Failed to update task');
    },
  });

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: (comment: string) =>
      TaskService.addComment
        ? TaskService.addComment(id as string, { text: comment })
        : Promise.resolve(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task-activity', id] });
      toast.success('Comment added');
      setNewComment('');
    },
    onError: () => {
      toast.error('Failed to add comment');
    },
  });

  // Add attachment mutation
  const addAttachmentMutation = useMutation({
    mutationFn: (files: File[]) => {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      return TaskService.addAttachments
        ? TaskService.addAttachments(id as string, formData)
        : Promise.resolve();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task', id] });
      toast.success('Attachments uploaded');
    },
    onError: () => {
      toast.error('Failed to upload attachments');
    },
  });

  // Update time tracking mutation
  const updateTimeMutation = useMutation({
    mutationFn: (time: number) =>
      TaskService.updateLoggedTime
        ? TaskService.updateLoggedTime(id as string, time)
        : Promise.resolve(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task', id] });
      toast.success('Time logged successfully');
    },
    onError: () => {
      toast.error('Failed to log time');
    },
  });

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setEditFormData((prev: any) => ({ ...prev, [name]: value }));
  };

  // Handle save edits
  const handleSaveChanges = () => {
    updateTaskMutation.mutate(editFormData);
  };

  // Handle add comment
  const handleAddComment = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      addCommentMutation.mutate(newComment);
    }
  };

  // Handle file upload
  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      addAttachmentMutation.mutate(files);
    }
  };

  // Handle time tracking
  const handleTimeTracking = () => {
    const now = new Date();
    if (timeTracking.isTracking && timeTracking.startTime && task) {
      const elapsedHours =
        (now.getTime() - new Date(timeTracking.startTime).getTime()) / (1000 * 60 * 60);
      const newLoggedTime = (task.loggedTime || 0) + elapsedHours;
      updateTimeMutation.mutate(newLoggedTime);
      setTimeTracking({ isTracking: false, startTime: null });
    } else {
      setTimeTracking({ isTracking: true, startTime: now.toISOString() });
      toast.info('Time tracking started');
    }
  };

  // Format time (hours)
  const formatTime = (time?: number) => {
    if (time === undefined || time === null) return 'Not set';
    const hours = Math.floor(time);
    const minutes = Math.round((time - hours) * 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  // Get status icon
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case 'To Do':
        return <Circle className="h-5 w-5 text-slate-500" />;
      case 'In Progress':
        return <Play className="h-5 w-5 text-blue-500" />;
      case 'Review':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'Completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      default:
        return <Circle className="h-5 w-5" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-blue-100 text-blue-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  };

  if (isLoading) {
    return (
      <div className="p-8 flex items-center justify-center">
        <div className="animate-pulse text-lg">Loading task details...</div>
      </div>
    );
  }

  if (error || !task) {
    return (
      <div className="p-8">
        <div className="text-lg text-red-500">Error loading task details. Task may not exist.</div>
        <Button className="mt-4" onClick={() => router.push('/tasks')}>
          <ChevronLeft className="mr-2 h-4 w-4" /> Back to Tasks
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="p-6 space-y-6"
    >
      {/* Breadcrumb Navigation */}
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbItem>
            <BreadcrumbLink href="/tasks">Tasks</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/tasks?status=${task.status}`}>{task.status}</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink>{task.name}</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/tasks')}>
            <ChevronLeft className="mr-2 h-4 w-4" /> Back
          </Button>

          {!isEditing ? (
            <Button variant="outline" onClick={() => setIsEditing(true)}>
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleSaveChanges} disabled={updateTaskMutation.isPending}>
                <Save className="mr-2 h-4 w-4" /> Save
              </Button>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                <X className="mr-2 h-4 w-4" /> Cancel
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Task Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              {isEditing ? (
                <input
                  type="text"
                  name="name"
                  value={editFormData.name}
                  onChange={handleInputChange}
                  className="text-2xl font-bold bg-transparent border-b border-gray-300 focus:outline-none focus:border-blue-500"
                />
              ) : (
                <CardTitle className="text-2xl">{task.name}</CardTitle>
              )}

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  {getStatusIcon(task.status)}
                  <span className="text-sm font-medium">{task.status}</span>
                </div>

                <Badge className={getPriorityColor(task.priority)}>
                  <Flag className="mr-1 h-3 w-3" />
                  {task.priority}
                </Badge>

                {task.dueDate && (
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    {new Date(task.dueDate).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>

            <Button
              variant={timeTracking.isTracking ? 'destructive' : 'default'}
              onClick={handleTimeTracking}
              className="flex items-center gap-2"
            >
              {timeTracking.isTracking ? (
                <>
                  <Pause className="h-4 w-4" /> Stop Timer
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" /> Start Timer
                </>
              )}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="attachments">Files</TabsTrigger>
          <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Details */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  {isEditing ? (
                    <Textarea
                      name="description"
                      value={editFormData.description}
                      onChange={handleInputChange}
                      rows={6}
                      placeholder="Task description..."
                    />
                  ) : (
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {task.description || 'No description provided.'}
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Progress */}
              <Card>
                <CardHeader>
                  <CardTitle>Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Completion</span>
                        <span>{typeof task.progress === 'number' ? task.progress : 0}%</span>
                      </div>
                      <Progress
                        value={typeof task.progress === 'number' ? task.progress : 0}
                        className="h-2"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Estimated Time:</span>
                        <p className="font-medium">{formatTime(task.estimatedTime)}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Logged Time:</span>
                        <p className="font-medium">{formatTime(task.loggedTime)}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Task Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-600">Status</label>
                    {isEditing ? (
                      <select
                        name="status"
                        value={editFormData.status}
                        onChange={handleInputChange}
                        className="w-full mt-1 p-2 border rounded"
                      >
                        <option value="To Do">To Do</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Review">Review</option>
                        <option value="Completed">Completed</option>
                      </select>
                    ) : (
                      <p className="font-medium">{task.status}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm text-gray-600">Priority</label>
                    {isEditing ? (
                      <select
                        name="priority"
                        value={editFormData.priority}
                        onChange={handleInputChange}
                        className="w-full mt-1 p-2 border rounded"
                      >
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                      </select>
                    ) : (
                      <p className="font-medium">{task.priority}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm text-gray-600">Due Date</label>
                    {isEditing ? (
                      <input
                        type="date"
                        name="dueDate"
                        value={editFormData.dueDate}
                        onChange={handleInputChange}
                        className="w-full mt-1 p-2 border rounded"
                      />
                    ) : (
                      <p className="font-medium">
                        {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'Not set'}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm text-gray-600">Created</label>
                    <p className="font-medium">
                      {task.createdAt ? new Date(task.createdAt).toLocaleDateString() : 'Unknown'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Comments & Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddComment} className="mb-6">
                <Textarea
                  value={newComment}
                  onChange={e => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="mb-2"
                />
                <Button type="submit" disabled={!newComment.trim() || addCommentMutation.isPending}>
                  <MessageSquare className="mr-2 h-4 w-4" /> Add Comment
                </Button>
              </form>

              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {taskActivity?.map((activity: any, index: number) => (
                    <div key={index} className="flex gap-3 p-3 border rounded">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>{activity.user?.name?.[0] || 'U'}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium">{activity.user?.name || 'Unknown'}</span>
                          <span className="text-gray-500">
                            {new Date(activity.createdAt).toLocaleString()}
                          </span>
                        </div>
                        <p className="mt-1 text-sm">{activity.text || activity.description}</p>
                      </div>
                    </div>
                  )) || <p className="text-center text-gray-500 py-8">No activity yet</p>}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attachments">
          <Card>
            <CardHeader>
              <CardTitle>Attachments</CardTitle>
            </CardHeader>
            <CardContent>
              <FileUploader onChange={handleFileUpload} multiple />

              <div className="space-y-2">
                {task.attachments && task.attachments.length > 0 ? (
                  task.attachments.map((attachment: any, index: number) => (
                    <div key={index} className="flex items-center gap-2 p-2 border rounded">
                      <Paperclip className="h-4 w-4" />
                      <span className="flex-1">{attachment.filename || attachment.name}</span>
                      <Button variant="ghost" size="sm" asChild>
                        <a href={attachment.url} download target="_blank" rel="noopener noreferrer">
                          Download
                        </a>
                      </Button>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-gray-500 py-8">No attachments</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dependencies">
          <Card>
            <CardHeader>
              <CardTitle>Task Dependencies</CardTitle>
            </CardHeader>
            <CardContent>
              {dependencies && dependencies.length > 0 ? (
                <TaskDependencyGraph tasks={dependencies} onTaskClick={() => {}} />
              ) : (
                <p className="text-center text-gray-500 py-8">No dependencies</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
