'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useRazorpay } from '@/hooks/useRazorpay';
import { useSession } from 'next-auth/react';
import { Separator } from '@/components/ui/separator';
import { UpgradePrompt } from '@/components/Upgrade/UpgradePrompt';
import { SubscriptionService } from '@/services/Subscription.service';
import { PRICING_PLANS } from '@/constant/PricingPlans';
import {
  CreditCard,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Crown,
  Zap,
  Shield,
  HardDrive,
  Users,
  Database,
  Puzzle,
  Clock,
  ArrowUpRight,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { TransactionService } from '@/services/Transaction.service';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useSubscription } from '@/hooks/useSubscription';

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState('subscription');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { data: session } = useSession();
  const {
    usage,
    currentPlan,
    planStatus,
    billingCycle,
    isLoading,
    formatUsageDisplay,
    getUsagePercentage,
    cancelSubscription,
    pauseSubscription,
    isCancellingSubscription,
    isPausingSubscription,
  } = useSubscription();
  const { processSubscriptionPayment } = useRazorpay();

  // Fetch transaction history
  const { data: transactionData, isLoading: isLoadingTransactions } = useQuery({
    queryKey: ['transactions'],
    queryFn: () => TransactionService.getTransactions({ limit: 10 }),
    enabled: !!session?.user,
  });

  const currentPlanConfig = PRICING_PLANS.find(p => p.id === currentPlan);
  const pricing = currentPlanConfig
    ? SubscriptionService.calculatePlanPrice(currentPlan, billingCycle as 'monthly' | 'yearly')
    : null;

  const handleUpgrade = async (planId: string) => {
    try {
      await processSubscriptionPayment({
        planId: planId as 'basic' | 'professional' | 'enterprise',
        billingCycle: 'monthly',
        onSuccess: () => {
          setShowUpgradeModal(false);
          toast.success('Plan upgraded successfully!');
        },
        onFailure: error => {
          console.error('Upgrade failed:', error);
        },
      });
    } catch (error) {
      console.error('Upgrade error:', error);
    }
  };

  const handleCancelSubscription = async () => {
    if (
      window.confirm(
        'Are you sure you want to cancel your subscription? You will lose access to premium features.'
      )
    ) {
      try {
        await cancelSubscription();
        toast.success('Subscription cancelled successfully');
      } catch (error) {
        toast.error('Failed to cancel subscription');
      }
    }
  };

  const handlePauseSubscription = async () => {
    if (
      window.confirm('Are you sure you want to pause your subscription? You can resume it later.')
    ) {
      try {
        await pauseSubscription('User requested pause');
        toast.success('Subscription paused successfully');
      } catch (error) {
        toast.error('Failed to pause subscription');
      }
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <HardDrive className="h-5 w-5 text-gray-500" />;
      case 'basic':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-5 w-5 text-orange-500" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      paused: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: Clock },
      cancelled: { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle },
      past_due: { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6 px-4">
        <div className="space-y-6">
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-px w-full" />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-24" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <Skeleton className="h-px w-full" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent className="space-y-3">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 flex-1" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 px-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium theme-text-primary">Billing & Subscription</h3>
          <p className="text-sm theme-text-secondary">
            Manage your subscription, usage, and billing information
          </p>
        </div>
        {currentPlan !== 'free' && (
          <Button variant="outline" onClick={() => setShowUpgradeModal(true)}>
            <ArrowUpRight className="h-4 w-4 mr-2" />
            Upgrade Plan
          </Button>
        )}
      </div>
      <Separator className="theme-divider" />
      <Tabs defaultValue={activeTab} className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="usage">Usage & Limits</TabsTrigger>
          <TabsTrigger value="billing-history">Billing History</TabsTrigger>
        </TabsList>

        <TabsContent value="subscription" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Plan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getPlanIcon(currentPlan)}
                  Current Plan
                </CardTitle>
                <CardDescription>Your active subscription details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Plan</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold capitalize">
                      {currentPlanConfig?.name || currentPlan}
                    </span>
                    {currentPlan !== 'free' && getStatusBadge(planStatus)}
                  </div>
                </div>

                {currentPlan !== 'free' && pricing && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Price</span>
                      <span className="text-lg font-semibold">
                        {SubscriptionService.formatPrice(pricing.finalPrice)}
                        <span className="text-sm text-gray-500">
                          /{billingCycle === 'yearly' ? 'year' : 'month'}
                        </span>
                      </span>
                    </div>
                    <Separator />

                    <div className="space-y-2">
                      {currentPlan !== 'free' && planStatus === 'active' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handlePauseSubscription}
                          disabled={isPausingSubscription}
                          className="w-full"
                        >
                          <Clock className="h-4 w-4 mr-2" />
                          {isPausingSubscription ? 'Pausing...' : 'Pause Subscription'}
                        </Button>
                      )}

                      {currentPlan !== 'free' && (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={handleCancelSubscription}
                          disabled={isCancellingSubscription}
                          className="w-full"
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          {isCancellingSubscription ? 'Cancelling...' : 'Cancel Subscription'}
                        </Button>
                      )}
                    </div>
                  </>
                )}

                {currentPlan === 'free' && (
                  <div className="text-center py-4">
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      You're on the free plan. Upgrade to unlock more features!
                    </p>
                    <Button onClick={() => setShowUpgradeModal(true)}>
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade Now
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Plan Features */}
            <Card>
              <CardHeader>
                <CardTitle>Plan Features</CardTitle>
                <CardDescription>What's included in your current plan</CardDescription>
              </CardHeader>
              <CardContent>
                {currentPlanConfig && (
                  <div className="space-y-3">
                    {currentPlanConfig.features.slice(0, 8).map((feature, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className={`${feature.included ? 'text-green-500' : 'text-gray-300'}`}>
                          {feature.included ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <AlertTriangle className="h-4 w-4" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div
                            className={`text-sm font-medium ${feature.included ? 'text-gray-900 dark:text-white' : 'text-gray-400'}`}
                          >
                            {feature.name}
                          </div>
                          {feature.limit && (
                            <div className="text-xs text-gray-500">{feature.limit}</div>
                          )}
                        </div>
                      </div>
                    ))}

                    {currentPlanConfig.features.length > 8 && (
                      <div className="text-xs text-gray-500 text-center pt-2">
                        +{currentPlanConfig.features.length - 8} more features
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Usage Overview
              </CardTitle>
              <CardDescription>Your current usage across all features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {isLoading ? (
                <div className="space-y-6">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-4 w-4 rounded-full" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                        <Skeleton className="h-4 w-16" />
                      </div>
                      <Skeleton className="h-2 w-full rounded-full" />
                    </div>
                  ))}
                </div>
              ) : usage ? (
                <>
                  {/* Projects */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Database className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Projects</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {formatUsageDisplay('projects')}
                      </span>
                    </div>
                    <Progress value={getUsagePercentage('projects')} className="h-2" />
                  </div>

                  {/* Users */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-green-500" />
                        <span className="font-medium">Team Members</span>
                      </div>
                      <span className="text-sm text-gray-600">{formatUsageDisplay('users')}</span>
                    </div>
                    <Progress value={getUsagePercentage('users')} className="h-2" />
                  </div>

                  {/* Integrations */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Puzzle className="h-4 w-4 text-orange-500" />
                        <span className="font-medium">Integrations</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {formatUsageDisplay('integrations')}
                      </span>
                    </div>
                    <Progress value={getUsagePercentage('integrations')} className="h-2" />
                  </div>

                  {usage.recommendations.shouldUpgrade && (
                    <>
                      <Separator />
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="h-4 w-4 text-yellow-600" />
                          <span className="font-medium text-yellow-800 dark:text-yellow-200">
                            Upgrade Recommended
                          </span>
                        </div>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                          {usage.recommendations.reasons.join(', ')}
                        </p>
                        <Button
                          size="sm"
                          onClick={() => setShowUpgradeModal(true)}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white"
                        >
                          Upgrade to {usage.recommendations.recommendedPlan}
                        </Button>
                      </div>
                    </>
                  )}
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">Unable to load usage data</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing-history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Transaction History
              </CardTitle>
              <CardDescription>Your complete payment and billing history</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTransactions ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Skeleton className="w-10 h-10 rounded-lg" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-24" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </div>
                      <div className="space-y-2 text-right">
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : transactionData?.transactions && transactionData.transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactionData.transactions.map(transaction => (
                    <div
                      key={transaction._id?.toString()}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`p-2 rounded-lg ${
                            transaction.status === 'completed'
                              ? 'bg-green-100 dark:bg-green-900'
                              : transaction.status === 'failed'
                                ? 'bg-red-100 dark:bg-red-900'
                                : 'bg-yellow-100 dark:bg-yellow-900'
                          }`}
                        >
                          {transaction.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                          ) : transaction.status === 'failed' ? (
                            <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
                          ) : (
                            <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium">
                            {TransactionService.getTypeDisplayName(transaction.type)}
                          </div>
                          <div className="text-sm text-gray-500">{transaction.description}</div>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(
                              new Date((transaction as any).createdAt || transaction.initiatedAt),
                              { addSuffix: true }
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">
                          {TransactionService.formatAmount(transaction.amount)}
                        </div>
                        <Badge
                          className={`text-xs ${TransactionService.getStatusColor(transaction.status)}`}
                        >
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </Badge>
                        {transaction.invoiceNumber && (
                          <div className="text-xs text-gray-400 mt-1">
                            #{transaction.invoiceNumber}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                  {transactionData.pagination.totalPages > 1 && (
                    <div className="flex justify-center pt-4">
                      <Button variant="outline" size="sm">
                        View All Transactions
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="font-medium">No transactions yet</p>
                  <p className="text-sm">Your payment history will appear here</p>
                  {currentPlan === 'free' && (
                    <Button className="mt-4" onClick={() => setShowUpgradeModal(true)}>
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade to Get Started
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Upgrade Modal */}
      <UpgradePrompt
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={handleUpgrade}
        currentPlan={currentPlan}
        usageSummary={usage as any}
        triggerReason="recommendation"
      />
    </div>
  );
}
