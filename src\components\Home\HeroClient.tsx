'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// Types
interface HeroFallbackProps {
  error?: Error | null;
  resetErrorBoundary?: () => void;
}

interface MotionComponents {
  motion: any;
}

// Fallback component for server-side rendering and errors
const HeroFallback: React.FC<HeroFallbackProps> = ({ error = null }) => {
  return (
    <section className="px-6 py-24 flex flex-col items-center justify-center relative overflow-hidden min-h-screen">
      <div className="absolute top-20 left-20 w-3 h-3 bg-orange-500/40 rounded-full" />
      <div className="absolute top-40 right-32 w-2 h-8 bg-green-500/30 rounded-full" />
      <div className="absolute bottom-32 left-16 w-4 h-4 bg-purple-500/30 rotate-45" />
      <div className="absolute bottom-20 right-20 w-6 h-2 bg-pink-500/25 rounded-full" />

      <div className="text-center z-10 max-w-6xl mx-auto">
        <span className="px-6 py-2 bg-gradient-to-r from-orange-500 to-green-600 text-white rounded-full text-sm font-semibold inline-block mb-8 shadow-lg hover:scale-105 transition-transform cursor-pointer">
          🚀 #1 Project Management Software in India
        </span>

        <h1 className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-6 leading-tight text-foreground">
          Transform Your Team&apos;s{' '}
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-white to-green-500">
            Productivity Forever
          </span>
        </h1>

        {error && (
          <p className="text-sm text-red-500 mt-4">
            Animation unavailable - displaying static version
          </p>
        )}
      </div>
    </section>
  );
};

// Loading component
const HeroLoading: React.FC = () => {
  return (
    <section className="px-6 py-24 flex flex-col items-center justify-center relative overflow-hidden min-h-screen">
      <div className="absolute top-20 left-20 w-3 h-3 bg-orange-500/20 rounded-full animate-pulse" />
      <div className="absolute top-40 right-32 w-2 h-8 bg-green-500/20 rounded-full animate-pulse" />
      <div className="absolute bottom-32 left-16 w-4 h-4 bg-purple-500/20 rotate-45 animate-pulse" />
      <div className="absolute bottom-20 right-20 w-6 h-2 bg-pink-500/20 rounded-full animate-pulse" />

      <div className="text-center z-10 max-w-6xl mx-auto">
        <div className="px-6 py-2 bg-gradient-to-r from-orange-500/50 to-green-600/50 rounded-full text-sm font-semibold inline-block mb-8 animate-pulse">
          🚀 #1 Project Management Software in India
        </div>

        <h1 className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-6 leading-tight text-foreground opacity-75">
          Transform Your Team&apos;s{' '}
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-white to-green-500">
            Productivity Forever
          </span>
        </h1>
      </div>
    </section>
  );
};

// Main animated component with dynamic framer-motion loading
const HeroAnimated: React.FC = () => {
  const [motionComponents, setMotionComponents] = useState<MotionComponents | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadError, setLoadError] = useState<boolean>(false);

  const loadFramerMotion = useCallback(async () => {
    try {
      const { motion } = await import('framer-motion');
      setMotionComponents({ motion });
      setIsLoading(false);
    } catch (error) {
      console.warn('Failed to load framer-motion:', error);
      setLoadError(true);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    let isMounted = true;

    // Add a small delay to prevent flash of loading state
    const timer = setTimeout(() => {
      if (isMounted) {
        loadFramerMotion();
      }
    }, 100);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, [loadFramerMotion]);

  // Show loading state while importing framer-motion
  if (isLoading) {
    return <HeroLoading />;
  }

  // Show fallback if framer-motion failed to load
  if (loadError || !motionComponents) {
    return <HeroFallback error={loadError ? new Error('Failed to load animations') : null} />;
  }

  const { motion } = motionComponents;

  return (
    <section className="px-6 py-24 flex flex-col items-center justify-center relative overflow-hidden min-h-screen">
      <motion.div
        className="absolute top-20 left-20 w-3 h-3 bg-orange-500/40 rounded-full"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2, duration: 0.5 }}
        style={{
          animation: 'float 6s ease-in-out infinite',
        }}
      />
      <motion.div
        className="absolute top-40 right-32 w-2 h-8 bg-green-500/30 rounded-full"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2.5, duration: 0.5 }}
        style={{
          animation: 'float 4s ease-in-out infinite',
        }}
      />
      <motion.div
        className="absolute bottom-32 left-16 w-4 h-4 bg-purple-500/30 rotate-45"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 3, duration: 0.5 }}
        style={{
          animation: 'float 3s ease-in-out infinite',
        }}
      />
      <motion.div
        className="absolute bottom-20 right-20 w-6 h-2 bg-pink-500/25 rounded-full"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 3.5, duration: 0.5 }}
        style={{
          animation: 'float 6s ease-in-out infinite',
        }}
      />

      <motion.div
        className="text-center z-10 max-w-6xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <motion.span
          className="px-6 py-2 bg-gradient-to-r from-orange-500 to-green-600 text-white rounded-full text-sm font-semibold inline-block mb-8 shadow-lg cursor-pointer"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          🚀 #1 Project Management Software in India
        </motion.span>

        <motion.h1
          className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-6 leading-tight text-foreground"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          Transform Your Team&apos;s{' '}
          <motion.span
            className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-white to-green-500"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.8 }}
          >
            Productivity Forever
          </motion.span>
        </motion.h1>
      </motion.div>

      <style jsx>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }
      `}</style>
    </section>
  );
};

// Error boundary fallback component
const ErrorFallback: React.FC<HeroFallbackProps> = ({ error, resetErrorBoundary }) => {
  return <HeroFallback error={error} resetErrorBoundary={resetErrorBoundary} />;
};

// Main client wrapper component
const HeroClient: React.FC = () => {
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Always render fallback on server-side to prevent hydration mismatch
  if (!isClient) {
    return <HeroFallback />;
  }

  // Render animated version only on client-side with error boundary
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.warn('Hero component error:', error, errorInfo);
      }}
    >
      <HeroAnimated />
    </ErrorBoundary>
  );
};

export default HeroClient;
