import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { MindMapService } from '@/services/MindMap.service';
import { CreateMindMapRequest, MindMap, MindMapFilters } from '@/types/MindMapsTypes';

export const mindMapKeys = {
  all: ['mindMaps'] as const,
  lists: () => [...mindMapKeys.all, 'list'] as const,
  list: (filters: MindMapFilters) => [...mindMapKeys.lists(), filters] as const,
  details: () => [...mindMapKeys.all, 'detail'] as const,
  detail: (id: string) => [...mindMapKeys.details(), id] as const,
};

export const useMindMaps = (filters?: MindMapFilters) => {
  return useQuery({
    queryKey: mindMapKeys.list(filters || {}),
    queryFn: async () => {
      const response = await MindMapService.getMindMaps(filters);
      return response.mindMaps || response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useMindMap = (id: string) => {
  return useQuery({
    queryKey: mindMapKeys.detail(id),
    queryFn: async () => {
      const response = await MindMapService.getMindMap(id);
      return response.mindMap || response;
    },
    enabled: !!id,
  });
};

export const useCreateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateMindMapRequest) => MindMapService.createMindMap(data),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map created successfully',
      });
      return data;
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to create mind map',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MindMap> }) =>
      MindMapService.updateMindMap(id, data),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
      toast({
        title: 'Success',
        description: 'Mind map updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to update mind map',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => MindMapService.deleteMindMap(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to delete mind map',
        variant: 'destructive',
      });
    },
  });
};

export const useDuplicateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, title }: { id: string; title: string }) =>
      MindMapService.duplicateMindMap(id, title),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map duplicated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to duplicate mind map',
        variant: 'destructive',
      });
    },
  });
};
