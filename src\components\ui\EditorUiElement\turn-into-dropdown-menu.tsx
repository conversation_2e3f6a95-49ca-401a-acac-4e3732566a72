'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';
import type { TElement } from '@udecode/plate';

import { BlockquotePlugin } from '@udecode/plate-block-quote/react';
import { CodeBlockPlugin } from '@udecode/plate-code-block/react';
import { HEADING_KEYS } from '@udecode/plate-heading';
import { INDENT_LIST_KEYS, ListStyleType } from '@udecode/plate-indent-list';
import { TogglePlugin } from '@udecode/plate-toggle/react';
import { ParagraphPlugin, useEditorRef, useSelectionFragmentProp } from '@udecode/plate/react';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  Columns3Icon,
  FileCodeIcon,
  Heading1Icon,
  Heading2Icon,
  Heading3Icon,
  ListIcon,
  ListOrderedIcon,
  PilcrowIcon,
  QuoteIcon,
  SquareIcon,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { getBlockType, setBlockType, STRUCTURAL_TYPES } from '@/components/editor/transforms';

import { ToolbarButton } from './toolbar';

const turnIntoItems = [
  {
    icon: <PilcrowIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['paragraph'],
    label: 'Text',
    description: 'Regular paragraph text',
    color: 'text-gray-600 dark:text-gray-400',
    value: ParagraphPlugin.key,
  },
  {
    icon: <Heading1Icon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['title', 'h1'],
    label: 'Heading 1',
    description: 'Large section heading',
    color: 'text-red-600 dark:text-red-400',
    value: HEADING_KEYS.h1,
  },
  {
    icon: <Heading2Icon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['subtitle', 'h2'],
    label: 'Heading 2',
    description: 'Medium section heading',
    color: 'text-orange-600 dark:text-orange-400',
    value: HEADING_KEYS.h2,
  },
  {
    icon: <Heading3Icon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['subtitle', 'h3'],
    label: 'Heading 3',
    description: 'Small section heading',
    color: 'text-yellow-600 dark:text-yellow-400',
    value: HEADING_KEYS.h3,
  },
  {
    icon: <ListIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['unordered', 'ul', '-'],
    label: 'Bulleted list',
    description: 'Unordered list with bullets',
    color: 'text-green-600 dark:text-green-400',
    value: ListStyleType.Disc,
  },
  {
    icon: <ListOrderedIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['ordered', 'ol', '1'],
    label: 'Numbered list',
    description: 'Ordered list with numbers',
    color: 'text-blue-600 dark:text-blue-400',
    value: ListStyleType.Decimal,
  },
  {
    icon: <SquareIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['checklist', 'task', 'checkbox', '[]'],
    label: 'To-do list',
    description: 'Interactive checklist',
    color: 'text-purple-600 dark:text-purple-400',
    value: INDENT_LIST_KEYS.todo,
  },
  {
    icon: <ChevronRightIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['collapsible', 'expandable'],
    label: 'Toggle list',
    description: 'Collapsible content block',
    color: 'text-indigo-600 dark:text-indigo-400',
    value: TogglePlugin.key,
  },
  {
    icon: <FileCodeIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['```'],
    label: 'Code',
    description: 'Code block with syntax highlighting',
    color: 'text-pink-600 dark:text-pink-400',
    value: CodeBlockPlugin.key,
  },
  {
    icon: <QuoteIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    keywords: ['citation', 'blockquote', '>'],
    label: 'Quote',
    description: 'Highlighted quotation',
    color: 'text-teal-600 dark:text-teal-400',
    value: BlockquotePlugin.key,
  },
  {
    icon: <Columns3Icon className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: '3 columns',
    description: 'Three column layout',
    color: 'text-cyan-600 dark:text-cyan-400',
    value: 'action_three_columns',
  },
];

export function TurnIntoDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);

  const value = useSelectionFragmentProp({
    defaultValue: ParagraphPlugin.key,
    structuralTypes: STRUCTURAL_TYPES,
    getProp: node => getBlockType(node as TElement),
  });
  const selectedItem = React.useMemo(
    () =>
      turnIntoItems.find(item => item.value === (value ?? ParagraphPlugin.key)) ?? turnIntoItems[0],
    [value]
  );

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Turn into"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <span className="hidden md:inline font-medium text-xs sm:text-sm theme-text-primary">
              {selectedItem.label}
            </span>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[200px] sm:min-w-[240px] p-1 sm:p-2 max-h-80 overflow-y-auto theme-scrollbar"
        onCloseAutoFocus={e => {
          e.preventDefault();
          editor.tf.focus();
        }}
        align="start"
        sideOffset={8}
      >
        {turnIntoItems.map(({ icon, label, description, color, value: itemValue }) => (
          <DropdownMenuItem
            key={itemValue}
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
              value === itemValue && 'theme-surface'
            )}
            onSelect={() => {
              setBlockType(editor, itemValue);
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', color)}>{icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">{label}</span>
                {value === itemValue && <CheckIcon className="h-3 w-3 theme-text-accent" />}
              </div>
              <p className="text-xs theme-text-secondary">{description}</p>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
