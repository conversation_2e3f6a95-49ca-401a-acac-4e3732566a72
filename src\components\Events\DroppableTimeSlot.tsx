import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { isToday } from 'date-fns';

interface DroppableTimeSlotProps {
  date: Date;
  hour: number;
  minute: number;
  viewType: 'month' | 'week' | 'day';
  onClick: (date: Date) => void;
  children: React.ReactNode;
  className?: string;
}

const DroppableTimeSlot: React.FC<DroppableTimeSlotProps> = ({
  date,
  hour,
  minute,
  viewType,
  onClick,
  children,
  className = '',
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id: `timeslot-${date.getTime()}-${hour || 0}-${minute || 0}`,
    data: {
      type: 'timeslot',
      date,
      hour,
      minute,
    },
  });
  const canDrop = true;
  return (
    <div
      ref={setNodeRef}
      className={`
          theme-transition cursor-pointer
          ${isOver && canDrop ? 'bg-primary/10 border-primary/30' : ''}
          ${isOver && !canDrop ? 'bg-destructive/10 border-destructive/30' : ''}
          ${!isOver ? 'hover:bg-accent/50' : ''}
          ${isToday(date) && viewType === 'month' ? 'bg-primary/5' : ''}
          ${className}
        `}
      onClick={() => onClick(date)}
    >
      {children}
    </div>
  );
};

export default DroppableTimeSlot;
