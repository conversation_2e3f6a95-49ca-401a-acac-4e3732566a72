'use client';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { LayoutGrid, Calendar, List } from 'lucide-react';
import { ProjectService } from '@/services/Project.service';
import ProjectKanban from '@/components/IndividualProject/ProjectKanban';
import ProjectAnalytics from '@/components/IndividualProject/ProjectAnalytics';
import ResourceAllocationGrid from '@/components/Projects/ResourceAllocationGrid';
import BudgetTracker from '@/components/Projects/BudgetTracker';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { motion } from 'framer-motion';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';

export default function ProjectPage() {
  const { projectId } = useParams();
  const [project, setProject] = useState<any>(null);
  const [viewMode, setViewMode] = useState('kanban');

  const { data: projectData, isLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: async () => {
      if (!projectId) {
        return { project: null };
      }
      const response = await ProjectService.getProjectById(projectId as string);
      return response || { project: null };
    },
    enabled: !!projectId,
  });

  useEffect(() => {
    if (projectData?.project) {
      setProject(projectData.project);
    }
  }, [projectData]);

  const handleViewModeChange = (mode: string) => {
    setViewMode(mode);
  };

  if (isLoading) {
    return <LoadingSpinner variant="wave" label="Loading..." />;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-4 rounded-md py-2 h-full w-full"
    >
      <div className="flex flex-col gap-4 items-start justify-between w-full">
        <div className="flex items-center theme-surface-elevated rounded-lg p-1 border border-border/50">
          <TooltipProvider>
            {[
              { mode: 'kanban', icon: LayoutGrid, label: 'Kanban Board' },
              { mode: 'analytics', icon: List, label: 'Analytics View' },
              { mode: 'resources', icon: Calendar, label: 'Resources View' },
              { mode: 'budget', icon: Calendar, label: 'Budget Tracker' },
            ].map(item => (
              <Tooltip key={item.mode}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`relative rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                      viewMode === item.mode
                        ? 'bg-background text-foreground shadow-sm border border-border/50'
                        : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                    }`}
                    onClick={() => handleViewModeChange(item.mode)}
                  >
                    <item.icon className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">
                      {item.mode.charAt(0).toUpperCase() + item.mode.slice(1)}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="theme-surface-elevated border border-border">
                  <p className="theme-text-primary">{item.label}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </div>
        <div className="w-full h-full">
          {viewMode === 'kanban' && <ProjectKanban project={project} />}
          {viewMode === 'analytics' && <ProjectAnalytics project={project} />}
          {viewMode === 'resources' && <ResourceAllocationGrid projectId={projectId as string} />}
          {viewMode === 'budget' && <BudgetTracker projectId={projectId as string} />}
        </div>
      </div>
    </motion.div>
  );
}
