'use client';

import React from 'react';

import { createPlatePlugin } from '@udecode/plate/react';

import { FixedToolbar } from '@/components/ui/EditorUiElement/fixed-toolbar';
import { FixedToolbarButtons } from '@/components/ui/EditorUiElement/fixed-toolbar-buttons';

export const FixedToolbarPlugin = createPlatePlugin({
  key: 'fixed-toolbar',
  render: {
    beforeEditable: () =>
      React.createElement(FixedToolbar, null, React.createElement(FixedToolbarButtons)),
  },
});
