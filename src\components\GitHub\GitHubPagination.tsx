'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface GitHubPaginationProps {
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
  totalItems?: number;
  itemsPerPage?: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
  className?: string;
  showInfo?: boolean;
  showFirstLast?: boolean;
}

const GitHubPagination: React.FC<GitHubPaginationProps> = ({
  currentPage,
  totalPages,
  hasMore,
  totalItems,
  itemsPerPage = 30,
  onPageChange,
  isLoading = false,
  className,
  showInfo = true,
  showFirstLast = true,
}) => {
  const getVisiblePages = (): (number | string)[] => {
    const delta = 2;
    const range: number[] = [];
    const rangeWithDots: (number | string)[] = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  if ((totalPages <= 1 && !hasMore) || (!totalItems && !hasMore)) return null;

  const visiblePages = getVisiblePages();
  const startItem = Math.max(1, (currentPage - 1) * itemsPerPage + 1);
  const endItem = Math.min(currentPage * itemsPerPage, totalItems || currentPage * itemsPerPage);
  const safeStartItem = totalItems ? startItem : 0;
  const safeEndItem = totalItems ? endItem : 0;

  const buttonVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 },
  };

  const pageVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className={cn(
        'flex flex-col lg:flex-row items-center justify-between gap-6 py-6 px-6 rounded-2xl theme-surface-elevated border border-border/50 backdrop-blur-sm shadow-lg shadow-black/5 dark:shadow-black/20',
        'bg-gradient-to-r from-background/50 via-background to-background/50',
        className
      )}
    >
      {/* Pagination Info */}
      {showInfo && totalItems && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="flex items-center gap-3"
        >
          <div className="flex items-center gap-3 text-sm theme-text-secondary">
            <div className="relative">
              <div className="w-2 h-2 rounded-full bg-primary/60 animate-pulse"></div>
              <div className="absolute inset-0 w-2 h-2 rounded-full bg-primary/30 animate-ping"></div>
            </div>
            <span className="font-medium">
              Showing{' '}
              <span className="text-primary font-bold bg-primary/10 px-2 py-1 rounded-md">
                {safeStartItem}
              </span>{' '}
              to{' '}
              <span className="text-primary font-bold bg-primary/10 px-2 py-1 rounded-md">
                {safeEndItem}
              </span>{' '}
              of{' '}
              <span className="text-primary font-bold bg-primary/10 px-2 py-1 rounded-md">
                {totalItems}
              </span>{' '}
              repositories
            </span>
          </div>
        </motion.div>
      )}

      {/* Pagination Controls */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="flex items-center gap-2"
      >
        {/* First Page */}
        {showFirstLast && totalPages > 5 && (
          <motion.div variants={buttonVariants} initial="initial" whileHover="hover" whileTap="tap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1 || isLoading}
              className={cn(
                'h-10 w-10 p-0 rounded-xl border-2 theme-transition shadow-sm',
                'hover:shadow-lg hover:shadow-primary/20 hover:scale-105 hover:border-primary/50 active:scale-95',
                'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
                'theme-button-secondary hover:theme-surface-elevated',
                'transition-all duration-200 ease-out'
              )}
            >
              <ChevronsLeft className="h-4 w-4" />
              <span className="sr-only">First page</span>
            </Button>
          </motion.div>
        )}

        {/* Previous Page */}
        <motion.div variants={buttonVariants} initial="initial" whileHover="hover" whileTap="tap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1 || isLoading}
            className={cn(
              'h-10 w-10 p-0 rounded-xl border-2 theme-transition shadow-sm',
              'hover:shadow-lg hover:shadow-primary/20 hover:scale-105 hover:border-primary/50 active:scale-95',
              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
              'theme-button-secondary hover:theme-surface-elevated',
              'transition-all duration-200 ease-out'
            )}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous page</span>
          </Button>
        </motion.div>

        {/* Page Numbers */}
        <div className="flex items-center gap-1 mx-2">
          <AnimatePresence mode="wait">
            {visiblePages.map((page, index) => {
              if (page === '...') {
                return (
                  <motion.div
                    key={`ellipsis-${index}`}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex h-10 w-10 items-center justify-center theme-text-secondary"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">More pages</span>
                  </motion.div>
                );
              }

              const pageNumber = page as number;
              const isActive = pageNumber === currentPage;

              return (
                <motion.div
                  key={pageNumber}
                  variants={pageVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={{ duration: 0.2 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant={isActive ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onPageChange(pageNumber)}
                    disabled={isLoading}
                    className={cn(
                      'h-10 w-10 p-0 rounded-xl border-2 theme-transition shadow-sm font-semibold',
                      'hover:shadow-lg active:scale-95 transition-all duration-200 ease-out',
                      'disabled:opacity-50 disabled:cursor-not-allowed',
                      isActive
                        ? 'bg-gradient-to-br from-primary to-primary/80 text-primary-foreground border-primary shadow-lg shadow-primary/30 hover:shadow-xl hover:shadow-primary/40 hover:from-primary/90 hover:to-primary/70'
                        : 'theme-button-secondary hover:theme-surface-elevated hover:border-primary/40 hover:shadow-primary/10'
                    )}
                  >
                    {isLoading && isActive ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      pageNumber
                    )}
                  </Button>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {/* Next Page */}
        <motion.div variants={buttonVariants} initial="initial" whileHover="hover" whileTap="tap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={(!hasMore && currentPage >= totalPages) || isLoading}
            className={cn(
              'h-10 w-10 p-0 rounded-xl border-2 theme-transition shadow-sm',
              'hover:shadow-lg hover:shadow-primary/20 hover:scale-105 hover:border-primary/50 active:scale-95',
              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
              'theme-button-secondary hover:theme-surface-elevated',
              'transition-all duration-200 ease-out'
            )}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next page</span>
          </Button>
        </motion.div>

        {/* Last Page */}
        {showFirstLast && totalPages > 5 && (
          <motion.div variants={buttonVariants} initial="initial" whileHover="hover" whileTap="tap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages || isLoading}
              className={cn(
                'h-10 w-10 p-0 rounded-xl border-2 theme-transition shadow-sm',
                'hover:shadow-lg hover:shadow-primary/20 hover:scale-105 hover:border-primary/50 active:scale-95',
                'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
                'theme-button-secondary hover:theme-surface-elevated',
                'transition-all duration-200 ease-out'
              )}
            >
              <ChevronsRight className="h-4 w-4" />
              <span className="sr-only">Last page</span>
            </Button>
          </motion.div>
        )}
      </motion.div>

      {/* Loading indicator */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="flex items-center gap-2 text-sm theme-text-secondary lg:hidden"
        >
          <Loader2 className="h-4 w-4 animate-spin text-primary" />
          <span className="font-medium">Loading repositories...</span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default GitHubPagination;
