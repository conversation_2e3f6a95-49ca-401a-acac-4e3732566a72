import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Task } from '@/models/Task';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import Note from '@/models/Note';
import { logger } from 'hono/logger';
import { SearchCache } from '@/lib/redis';

// Define user data type
interface UserData {
  id: string;
  name: string;
  email: string;
  image: string;
  organizationId?: string;
}

interface CustomVariables {
  user?: UserData;
}

const app = new Hono<{ Variables: CustomVariables }>().basePath('/api/search');

app.use('*', logger());

// Auth middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData: UserData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: (session.user as any).organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.get('/', async c => {
  try {
    const user = c.get('user') as UserData | undefined;
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const { q, type, status, priority, tags, project, page = '1', limit = '20' } = c.req.query();
    const startTime = Date.now();

    await connectDB();

    const pageNum = parseInt(page) || 1;
    const limitNum = Math.min(parseInt(limit) || 20, 100); // Cap at 100 results
    const skip = (pageNum - 1) * limitNum;
    const sanitizedQuery = sanitizeQuery(q || '');
    const searchTerms = sanitizedQuery.split(/\s+/).filter(Boolean);

    // If no search query, return empty results
    if (!sanitizedQuery.trim() || searchTerms.length === 0) {
      return c.json({
        results: [],
        total: 0,
        page: pageNum,
        totalPages: 0,
        searchTime: Date.now() - startTime,
      });
    }

    // Check Redis cache first
    const cacheKey = SearchCache.generateCacheKey(user.organizationId || '', sanitizedQuery, {
      type,
      status,
      priority,
      tags,
      project,
      page,
      limit,
    });

    const cachedResult = await SearchCache.get(cacheKey);
    if (cachedResult) {
      // Track search for analytics
      SearchCache.trackSearch(user.organizationId || '', sanitizedQuery);

      return c.json({
        ...cachedResult,
        searchTime: Date.now() - startTime,
        cached: true,
      });
    }

    const contentTypes = type ? type.split(',') : ['task', 'project', 'note', 'user'];
    type SearchResult = ReturnType<typeof formatResult>;
    const results: SearchResult[] = [];

    // Build base filters for organization
    const baseFilters = { organizationId: user.organizationId };
    const additionalFilters = buildAdditionalFilters({ status, priority, tags, project });

    // Search each content type with proper error handling
    if (contentTypes.includes('task')) {
      try {
        const taskQuery = buildSearchQuery(
          baseFilters,
          additionalFilters,
          sanitizedQuery,
          searchTerms
        );
        const tasks = await Task.find(taskQuery)
          .populate('assignedTo', 'name')
          .populate('projectId', 'name')
          .skip(skip)
          .limit(limitNum)
          .lean();

        tasks.forEach(task => {
          results.push(formatResult(task, 'task', searchTerms));
        });
      } catch (error) {
        console.error('Error searching tasks:', error);
      }
    }

    if (contentTypes.includes('project')) {
      try {
        const projectQuery = buildSearchQuery(
          baseFilters,
          additionalFilters,
          sanitizedQuery,
          searchTerms
        );
        const projects = await Project.find(projectQuery).skip(skip).limit(limitNum).lean();

        projects.forEach(project => {
          results.push(formatResult(project, 'project', searchTerms));
        });
      } catch (error) {
        console.error('Error searching projects:', error);
      }
    }

    if (contentTypes.includes('note')) {
      try {
        const noteQuery = buildSearchQuery(baseFilters, {}, sanitizedQuery, searchTerms);
        const notes = await Note.find(noteQuery)
          .populate('userId', 'name')
          .skip(skip)
          .limit(limitNum)
          .lean();

        notes.forEach(note => {
          results.push(formatResult(note, 'note', searchTerms));
        });
      } catch (error) {
        console.error('Error searching notes:', error);
      }
    }

    if (contentTypes.includes('user')) {
      try {
        const userQuery = buildSearchQuery(baseFilters, {}, sanitizedQuery, searchTerms);
        const users = await User.find(userQuery).skip(skip).limit(limitNum).lean();

        users.forEach(userDoc => {
          results.push(formatResult(userDoc, 'user', searchTerms));
        });
      } catch (error) {
        console.error('Error searching users:', error);
      }
    }

    // Sort by relevance
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    const total = await getTotalCount(
      baseFilters,
      additionalFilters,
      sanitizedQuery,
      searchTerms,
      contentTypes
    );

    const searchResult = {
      results,
      total,
      page: pageNum,
      totalPages: Math.ceil(total / limitNum),
      searchTime: Date.now() - startTime,
    };

    // Cache the result
    await SearchCache.set(cacheKey, searchResult);

    // Track search for analytics
    SearchCache.trackSearch(user.organizationId || '', sanitizedQuery);

    return c.json(searchResult);
  } catch (error) {
    console.error('Global search error:', error);
    return c.json(
      {
        error: 'Search failed. Please try again.',
        results: [],
        total: 0,
        page: 1,
        totalPages: 0,
        searchTime: 0,
      },
      500
    );
  }
});

// Helper functions
function sanitizeQuery(query: string): string {
  return (
    query
      ?.trim()
      .replace(/[<>;"'\\]/g, '')
      .substring(0, 500) || ''
  );
}

function buildAdditionalFilters(filters: any) {
  const query: any = {};

  if (filters.status) query.status = { $in: filters.status.split(',') };
  if (filters.priority) query.priority = { $in: filters.priority.split(',') };
  if (filters.tags) query.tags = { $in: filters.tags.split(',') };
  if (filters.project) query.projectId = filters.project;

  return query;
}

function buildSearchQuery(
  baseFilters: any,
  additionalFilters: any,
  _sanitizedQuery: string,
  searchTerms: string[]
) {
  const query = { ...baseFilters, ...additionalFilters };

  // Build search conditions using regex for each search term
  if (searchTerms.length > 0) {
    const searchConditions = searchTerms.map(term => {
      const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      return {
        $or: [
          { name: { $regex: escapedTerm, $options: 'i' } },
          { title: { $regex: escapedTerm, $options: 'i' } },
          { description: { $regex: escapedTerm, $options: 'i' } },
          { content: { $regex: escapedTerm, $options: 'i' } },
          { email: { $regex: escapedTerm, $options: 'i' } },
          { bio: { $regex: escapedTerm, $options: 'i' } },
        ],
      };
    });

    // All search terms must match (AND logic)
    if (searchConditions.length === 1) {
      query.$or = searchConditions[0].$or;
    } else {
      query.$and = searchConditions;
    }
  }

  return query;
}

function formatResult(doc: any, type: string, searchTerms: string[]) {
  try {
    const relevanceScore = calculateRelevance(doc, searchTerms);
    const highlights = generateHighlights(doc, searchTerms);

    const baseResult = {
      id: doc._id?.toString() || doc.id?.toString() || '',
      type,
      relevanceScore,
      highlights,
    };

    switch (type) {
      case 'task':
        return {
          ...baseResult,
          title: doc.name || 'Untitled Task',
          description: doc.description || '',
          url: `/tasks/${doc._id}`,
          metadata: {
            status: doc.status || 'To Do',
            priority: doc.priority || 'Medium',
            assignee: doc.assignedTo?.[0]?.name || null,
            projectName: doc.projectId?.name || null,
            createdAt: doc.createdAt,
          },
        };
      case 'project':
        return {
          ...baseResult,
          title: doc.name || 'Untitled Project',
          description: doc.description || '',
          url: `/projects/${doc._id}`,
          metadata: {
            status: doc.status || 'Planning',
            membersCount: doc.members?.length || 0,
            createdAt: doc.createdAt,
          },
        };
      case 'note':
        return {
          ...baseResult,
          title: doc.title || 'Untitled Note',
          description: doc.content?.substring(0, 150) || '',
          url: `/notes/${doc._id}`,
          metadata: {
            createdBy: doc.userId?.name || doc.createdBy?.name || 'Unknown',
            createdAt: doc.createdAt,
            tags: doc.tags || [],
          },
        };
      case 'user':
        return {
          ...baseResult,
          title: doc.name || 'Unknown User',
          description: `${doc.email || ''} - ${doc.systemRole || doc.role || 'User'}`,
          url: `/users/${doc._id}`,
          metadata: {
            role: doc.systemRole || doc.role || 'User',
            email: doc.email || '',
            createdAt: doc.createdAt,
          },
        };
      default:
        return {
          ...baseResult,
          title: 'Unknown Item',
          description: '',
          url: '#',
          metadata: {},
        };
    }
  } catch (error) {
    console.error('Error formatting result:', error);
    return {
      id: doc._id?.toString() || 'unknown',
      type,
      title: 'Error formatting result',
      description: '',
      url: '#',
      relevanceScore: 0,
      highlights: {},
      metadata: {},
    };
  }
}

function calculateRelevance(doc: any, searchTerms: string[]): number {
  if (!searchTerms.length) return 1;

  try {
    let score = 0;
    const title = (doc.name || doc.title || '').toLowerCase();
    const content = (doc.description || doc.content || doc.bio || '').toLowerCase();
    const email = (doc.email || '').toLowerCase();

    searchTerms.forEach(term => {
      const termLower = term.toLowerCase();

      // Higher score for title matches
      if (title.includes(termLower)) {
        score += 3;
      }

      // Medium score for content matches
      if (content.includes(termLower)) {
        score += 2;
      }

      // Lower score for email matches
      if (email.includes(termLower)) {
        score += 1;
      }
    });

    return Math.min(score / searchTerms.length, 10);
  } catch (error) {
    console.error('Error calculating relevance:', error);
    return 1;
  }
}

function generateHighlights(doc: any, searchTerms: string[]) {
  if (!searchTerms.length) return {};

  try {
    const highlights: any = {};
    const title = doc.name || doc.title || '';
    const content = doc.description || doc.content || '';

    searchTerms.forEach(term => {
      const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(${escapedTerm})`, 'gi');

      if (title && title.match(regex)) {
        highlights.title = highlights.title || [];
        highlights.title.push(title.replace(regex, '<mark>$1</mark>'));
      }

      if (content && content.match(regex)) {
        highlights.content = highlights.content || [];
        const truncatedContent = content.substring(0, 200);
        highlights.content.push(truncatedContent.replace(regex, '<mark>$1</mark>'));
      }
    });

    return highlights;
  } catch (error) {
    console.error('Error generating highlights:', error);
    return {};
  }
}

async function getTotalCount(
  baseFilters: any,
  additionalFilters: any,
  sanitizedQuery: string,
  searchTerms: string[],
  contentTypes: string[]
): Promise<number> {
  let total = 0;

  try {
    if (contentTypes.includes('task')) {
      const taskQuery = buildSearchQuery(
        baseFilters,
        additionalFilters,
        sanitizedQuery,
        searchTerms
      );
      total += await Task.countDocuments(taskQuery);
    }
    if (contentTypes.includes('project')) {
      const projectQuery = buildSearchQuery(
        baseFilters,
        additionalFilters,
        sanitizedQuery,
        searchTerms
      );
      total += await Project.countDocuments(projectQuery);
    }
    if (contentTypes.includes('note')) {
      const noteQuery = buildSearchQuery(baseFilters, {}, sanitizedQuery, searchTerms);
      total += await Note.countDocuments(noteQuery);
    }
    if (contentTypes.includes('user')) {
      const userQuery = buildSearchQuery(baseFilters, {}, sanitizedQuery, searchTerms);
      total += await User.countDocuments(userQuery);
    }
  } catch (error) {
    console.error('Error counting search results:', error);
  }

  return total;
}

// Add endpoint for popular searches
app.get('/popular', async c => {
  try {
    const user = c.get('user') as UserData | undefined;
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const popularSearches = await SearchCache.getPopularSearches(user.organizationId || '');

    return c.json({
      searches: popularSearches,
    });
  } catch (error) {
    console.error('Popular searches error:', error);
    return c.json({ error: 'Failed to get popular searches' }, 500);
  }
});

// Add endpoint for popular searches
app.get('/popular', async c => {
  try {
    const user = c.get('user') as UserData | undefined;
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const popularSearches = await SearchCache.getPopularSearches(user.organizationId || '');

    return c.json({
      searches: popularSearches,
    });
  } catch (error) {
    console.error('Popular searches error:', error);
    return c.json({ error: 'Failed to get popular searches' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
