'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Star,
  GitFork,
  ExternalLink,
  Plus,
  Minus,
  RefreshCw,
  Lock,
  Globe,
  AlertCircle,
  Calendar,
  Settings,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { WebhookStatus } from './WebhookStatus';
import { WebhookManagementModal } from './WebhookManagementModal';

interface Repository {
  id?: number;
  name?: string;
  full_name?: string;
  description?: string | null;
  private?: boolean;
  html_url?: string;
  language?: string | null;
  stargazers_count?: number;
  forks_count?: number;
  open_issues_count?: number;
  updated_at?: string;
  isConnected?: boolean;
}

interface RepositoryCardProps {
  repository: Repository;
  index: number;
  isConnected: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
  isLoading: boolean;
  className?: string;
  showWebhookManagement?: boolean;
}

export const RepositoryCard: React.FC<RepositoryCardProps> = ({
  repository,
  index,
  isConnected,
  onConnect,
  onDisconnect,
  isLoading,
  className,
  showWebhookManagement = true,
}) => {
  const languageColors: Record<string, string> = {
    JavaScript: '#f1e05a',
    TypeScript: '#2b7489',
    Python: '#3572A5',
    Java: '#b07219',
    'C++': '#f34b7d',
    'C#': '#239120',
    PHP: '#4F5D95',
    Ruby: '#701516',
    Go: '#00ADD8',
    Rust: '#dea584',
    Swift: '#ffac45',
    Kotlin: '#F18E33',
    Dart: '#00B4AB',
    HTML: '#e34c26',
    CSS: '#1572B6',
    Vue: '#4FC08D',
    React: '#61DAFB',
  };

  const getLanguageColor = (language?: string | null) => {
    if (!language) return '#6b7280';
    return languageColors[language] || '#6b7280';
  };

  const [isOpen, setIsOpen] = React.useState(false);

  const safeRepo = {
    id: repository?.id ?? 0,
    name: repository?.name ?? 'Unknown Repository',
    full_name: repository?.full_name ?? 'unknown/repository',
    description: repository?.description ?? null,
    private: repository?.private ?? false,
    html_url: repository?.html_url ?? '#',
    language: repository?.language ?? null,
    stargazers_count: repository?.stargazers_count ?? 0,
    forks_count: repository?.forks_count ?? 0,
    open_issues_count: repository?.open_issues_count ?? 0,
    updated_at: repository?.updated_at ?? new Date().toISOString(),
    isConnected: repository?.isConnected ?? false,
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Recently';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05, duration: 0.3 }}
      className={cn('h-full w-full', className)}
    >
      <Card className="theme-surface-elevated hover-reveal glow-on-hover h-full flex flex-col theme-transition w-full">
        <CardHeader className="p-3 sm:p-4 lg:p-5 border-b border-border/20 flex-shrink-0">
          <div className="flex items-start justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
              <div className="bg-primary/10 p-1.5 sm:p-2 rounded-lg theme-transition flex-shrink-0">
                <GitFork className="h-4 w-4 sm:h-5 sm:w-5 text-primary theme-transition" />
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <CardTitle
                    className={cn(
                      'text-sm sm:text-base lg:text-lg font-semibold theme-text-primary truncate',
                      'group-hover:text-primary transition-colors duration-200'
                    )}
                  >
                    {safeRepo.name}
                  </CardTitle>
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="flex items-center gap-2 flex-shrink-0"
                  >
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className={cn(
                              'w-2 h-2 rounded-full transition-all duration-200',
                              isConnected
                                ? 'bg-green-500 shadow-lg shadow-green-500/50'
                                : 'bg-gray-400 dark:bg-gray-500'
                            )}
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{isConnected ? 'Connected' : 'Available'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {/* Webhook Status Indicator */}
                    {isConnected && showWebhookManagement && (
                      <WebhookStatus
                        repositoryId={repository.id?.toString() || ''}
                        size="sm"
                        isConnected={isConnected}
                      />
                    )}
                  </motion.div>
                </div>
                <p className="text-xs sm:text-sm theme-text-secondary truncate font-medium">
                  {safeRepo.full_name}
                </p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-3 sm:p-4 lg:p-5 pt-3 flex-grow flex flex-col">
          <p className="text-xs sm:text-sm theme-text-secondary mb-3 sm:mb-4 line-clamp-2 min-h-[2.5rem] flex-shrink-0">
            {safeRepo.description || 'No description available'}
          </p>

          <div className="flex flex-wrap items-center gap-2 sm:gap-3 text-xs sm:text-sm theme-text-secondary mb-3 sm:mb-4 flex-shrink-0">
            <div className="flex items-center flex-wrap gap-2 sm:gap-3 lg:gap-4">
              {safeRepo.language && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center whitespace-nowrap">
                        <div
                          className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full mr-1 sm:mr-1.5 flex-shrink-0"
                          style={{ backgroundColor: getLanguageColor(safeRepo.language) }}
                        />
                        <span className="text-xs sm:text-sm">{safeRepo.language}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Primary language: {safeRepo.language}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center whitespace-nowrap">
                      <Star className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 sm:mr-1.5 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">{safeRepo.stargazers_count}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{safeRepo.stargazers_count} stars</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center whitespace-nowrap">
                      <GitFork className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 sm:mr-1.5 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">{safeRepo.forks_count}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{safeRepo.forks_count} forks</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Badge
                variant="outline"
                className={cn(
                  'text-xs sm:text-sm theme-transition flex-shrink-0 px-1.5 sm:px-2 py-0.5',
                  safeRepo.private
                    ? 'text-orange-600 dark:text-orange-400 border-orange-500/20'
                    : 'text-green-600 dark:text-green-400 border-green-500/20'
                )}
              >
                {safeRepo.private ? (
                  <>
                    <Lock className="h-2 w-2 sm:h-2.5 sm:w-2.5" />
                    <span className="ml-1 hidden sm:inline">Private</span>
                  </>
                ) : (
                  <>
                    <Globe className="h-2 w-2 sm:h-2.5 sm:w-2.5" />
                    <span className="ml-1 hidden sm:inline">Public</span>
                  </>
                )}
              </Badge>
            </div>
          </div>

          <div className="flex flex-wrap items-center justify-between gap-2 text-xs theme-text-secondary mt-auto">
            <div className="flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1">
              <Calendar className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">Updated {formatTimeAgo(safeRepo.updated_at)}</span>
            </div>
            {safeRepo.open_issues_count > 0 && (
              <Badge variant="outline" className="text-xs flex-shrink-0 px-1.5 py-0.5">
                <AlertCircle className="mr-1 h-2 w-2" />
                <span className="whitespace-nowrap">{safeRepo.open_issues_count} issues</span>
              </Badge>
            )}
          </div>
        </CardContent>
        <div className="p-2 sm:p-3 border-t border-border/20 flex flex-col sm:flex-row items-center justify-between gap-2 flex-shrink-0">
          <div className="flex items-center gap-2 flex-1">
            <a
              href={safeRepo.html_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-primary hover:text-primary/80 flex items-center theme-transition"
            >
              <span className="hidden sm:inline">View on GitHub</span>
              <span className="sm:hidden">GitHub</span>
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>

            {isConnected && showWebhookManagement && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 text-xs"
                      onClick={() => setIsOpen(true)}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Configure Webhook</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          <Button
            size="sm"
            variant={isConnected ? 'outline' : 'default'}
            onClick={isConnected ? onDisconnect : onConnect}
            disabled={isLoading}
            className={cn(
              'text-xs theme-transition w-full sm:w-auto',
              isConnected ? 'theme-button-secondary' : 'theme-button-primary'
            )}
          >
            {isLoading ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : isConnected ? (
              <>
                <Minus className="mr-1 h-3 w-3" />
                Disconnect
              </>
            ) : (
              <>
                <Plus className="mr-1 h-3 w-3" />
                Connect
              </>
            )}
          </Button>
        </div>
      </Card>
      {/* Webhook Management Modal */}
      <WebhookManagementModal
        repositoryId={repository.id?.toString() || ''}
        repositoryName={safeRepo.full_name}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        isConnected={isConnected}
      />
    </motion.div>
  );
};
