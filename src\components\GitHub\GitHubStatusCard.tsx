'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Github, CheckCircle, AlertCircle, ExternalLink, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface GitHubStatusCardProps {
  status: {
    connected: boolean;
    user?: {
      installationId: string;
      appId: string;
      account: {
        login: string;
        id: number;
        avatar_url: string;
        html_url: string;
        type: string;
      };
      permissions: {
        issues: string;
        contents: string;
        metadata: string;
        pull_requests: string;
      };
      repositorySelection: string;
    };
    lastSyncedAt?: string;
  };
  onRefresh?: () => void;
  isLoading?: boolean;
  className?: string;
}

export const GitHubStatusCard: React.FC<GitHubStatusCardProps> = ({
  status,
  onRefresh,
  isLoading = false,
  className,
}) => {
  if (!status?.connected || !status?.user) {
    return (
      <Card className={cn('theme-surface-elevated border-destructive/20', className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="bg-destructive/10 p-3 rounded-full">
              <AlertCircle className="h-6 w-6 text-destructive theme-transition" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold theme-text-primary">GitHub Not Connected</h3>
              <p className="text-sm theme-text-secondary">
                Connect your GitHub account to access repositories and issues
              </p>
            </div>
            <Badge variant="destructive" className="theme-transition">
              Disconnected
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { account } = status.user;
  const userInitials = account.login.slice(0, 2).toUpperCase();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="theme-surface-elevated hover-reveal glow-on-hover border-success/20">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-success/10 p-2 rounded-lg theme-transition">
                <Github className="h-5 w-5 text-success theme-transition" />
              </div>
              <CardTitle className="text-lg theme-text-primary flex items-center gap-2">
                GitHub
                <Badge variant="default" className="status-indicator-completed text-xs">
                  <CheckCircle className="mr-1 h-3 w-3" />
                  Connected
                </Badge>
              </CardTitle>
            </div>
            <div className="flex items-center gap-2">
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRefresh}
                  disabled={isLoading}
                  className="theme-button-ghost"
                >
                  <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12 ring-2 ring-success/20 theme-transition">
              <AvatarImage src={account.avatar_url} alt={account.login} />
              <AvatarFallback className="bg-success/10 text-success font-semibold">
                {userInitials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold theme-text-primary">{account.login}</h4>
                <a
                  href={account.html_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary/80 theme-transition"
                >
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
              <p className="text-sm theme-text-secondary">ID: {account.id}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
