import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { format as formatDate } from 'date-fns';
import { activityLogApiService, ExportParams } from '@/services/ActivityLogApi.service';
import { useToast } from '@/hooks/use-toast';

export interface UseActivityLogExportOptions {
    onSuccess?: (format: string) => void;
    onError?: (error: Error) => void;
}

export interface UseActivityLogExportReturn {
    exportLogs: (format: 'csv' | 'json' | 'xlsx', params?: Partial<ExportParams>) => Promise<void>;
    isExporting: boolean;
    error: Error | null;
}

export const useActivityLogExport = (
    options: UseActivityLogExportOptions = {}
): UseActivityLogExportReturn => {
    const { toast } = useToast();
    const { onSuccess, onError } = options;

    const mutation = useMutation({
        mutationFn: async ({ format, params }: { format: 'csv' | 'json' | 'xlsx'; params: ExportParams }) => {
            const blob = await activityLogApiService.exportLogs(params);
            return { blob, format };
        },
        onSuccess: ({ blob, format }) => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activity-logs-${formatDate(new Date(), 'yyyy-MM-dd')}.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            toast({
                title: 'Export Successful',
                description: `Activity logs exported as ${format.toUpperCase()}`,
            });

            onSuccess?.(format);
        },
        onError: (error: Error) => {
            console.error('Export failed:', error);
            toast({
                title: 'Export Failed',
                description: error.message || 'Failed to export activity logs',
                variant: 'destructive',
            });

            onError?.(error);
        },
        retry: 2,
        retryDelay: 1000,
    });

    const exportLogs = useCallback(
        async (format: 'csv' | 'json' | 'xlsx', params: Partial<ExportParams> = {}) => {
            const exportParams: ExportParams = {
                format,
                ...params,
            };

            await mutation.mutateAsync({ format, params: exportParams });
        },
        [mutation]
    );

    return {
        exportLogs,
        isExporting: mutation.isPending,
        error: mutation.error as Error | null,
    };
};