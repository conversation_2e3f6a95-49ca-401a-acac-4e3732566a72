import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import ActivityLogService from '@/services/ActivityLog.service';

export interface ActivityLogEntry {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  action: string;
  actionType:
    | 'create'
    | 'update'
    | 'delete'
    | 'login'
    | 'logout'
    | 'view'
    | 'share'
    | 'export'
    | 'invite'
    | 'join'
    | 'leave';
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'mindmap'
    | 'event'
    | 'report'
    | 'file';
  resourceId?: string;
  resourceName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  organizationId: string;
  projectId?: string;
  changes?: Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }>;
}

export interface ActivityLogFilters {
  userId?: string;
  actionType?: string;
  resourceType?: string;
  resourceId?: string;
  projectId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface ActivityLogOptions {
  enableAutoTracking?: boolean;
  autoTrackActions?: string[];
  batchSize?: number;
  debounceDelay?: number;
}

export interface ActivityLogStats {
  totalActions: number;
  todayActions: number;
  weekActions: number;
  monthActions: number;
  topUsers: Array<{
    userId: string;
    userName: string;
    actionCount: number;
  }>;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  topResources: Array<{
    resourceType: string;
    count: number;
  }>;
}

const defaultOptions: ActivityLogOptions = {
  enableAutoTracking: true,
  autoTrackActions: ['create', 'update', 'delete', 'login', 'logout'],
  batchSize: 50,
  debounceDelay: 500,
};

export const useActivityLog = (options: ActivityLogOptions = {}) => {
  const { data: session } = useSession();
  const opts = useMemo(() => ({ ...defaultOptions, ...options }), [options]);

  const [logs, setLogs] = useState<ActivityLogEntry[]>([]);
  const [filters, setFilters] = useState<ActivityLogFilters>({});
  const [stats, setStats] = useState<ActivityLogStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);

  const batchQueue = useRef<ActivityLogEntry[]>([]);
  const batchTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const filteredLogs = useMemo(() => {
    return logs.filter(log => {
      if (filters.userId && log.userId !== filters.userId) return false;
      if (filters.actionType && log.actionType !== filters.actionType) return false;
      if (filters.resourceType && log.resourceType !== filters.resourceType) return false;
      if (filters.resourceId && log.resourceId !== filters.resourceId) return false;
      if (filters.projectId && log.projectId !== filters.projectId) return false;
      if (filters.dateFrom && new Date(log.timestamp) < filters.dateFrom) return false;
      if (filters.dateTo && new Date(log.timestamp) > filters.dateTo) return false;
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (
          !log.action.toLowerCase().includes(searchLower) &&
          !log.description.toLowerCase().includes(searchLower) &&
          !log.userName.toLowerCase().includes(searchLower) &&
          !log.resourceName?.toLowerCase().includes(searchLower)
        )
          return false;
      }
      return true;
    });
  }, [logs, filters]);

  const mapFiltersToBackend = useCallback((uiFilters: ActivityLogFilters) => {
    const backendFilters: any = {};
    if (uiFilters.userId) backendFilters.userId = uiFilters.userId;
    if (uiFilters.actionType) backendFilters.actions = [uiFilters.actionType];
    if (uiFilters.resourceType) backendFilters.resourceTypes = [uiFilters.resourceType];
    if (uiFilters.resourceId) backendFilters.resourceId = uiFilters.resourceId;
    if (uiFilters.projectId) backendFilters['metadata.projectId'] = uiFilters.projectId;
    if (uiFilters.dateFrom) backendFilters.startDate = uiFilters.dateFrom;
    if (uiFilters.dateTo) backendFilters.endDate = uiFilters.dateTo;
    if (uiFilters.search) backendFilters.search = uiFilters.search;
    if (uiFilters.limit) backendFilters.limit = uiFilters.limit;
    if (uiFilters.offset) backendFilters.offset = uiFilters.offset;
    return backendFilters;
  }, []);

  const logAction = useCallback(
    async (
      action: ActivityLogEntry['actionType'],
      resourceType: ActivityLogEntry['resourceType'],
      resourceId?: string,
      description?: string,
      metadata?: Record<string, any>,
      changes?: ActivityLogEntry['changes']
    ) => {
      if (!session?.user) return;

      const logEntry: Omit<ActivityLogEntry, 'id' | 'timestamp'> = {
        userId: session.user.id!,
        userName: session.user.name || '',
        userEmail: session.user.email || '',
        userAvatar: session.user.image,
        action: action,
        actionType: action,
        resourceType,
        resourceId,
        resourceName: metadata?.resourceName,
        description:
          description || `${action} ${resourceType}${resourceId ? ` (${resourceId})` : ''}`,
        metadata,
        organizationId: session.user.organizationId || '',
        projectId: metadata?.projectId,
        changes,
        ipAddress: metadata?.ipAddress,
        userAgent:
          metadata?.userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : ''),
      };

      batchQueue.current.push({
        ...logEntry,
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
      });

      if (batchQueue.current.length >= opts.batchSize!) {
        processBatch();
      } else if (!batchTimer.current) {
        batchTimer.current = setTimeout(processBatch, 2000);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [session?.user, opts.batchSize]
  );

  const processBatch = useCallback(async () => {
    if (batchQueue.current.length === 0) return;

    const batch = [...batchQueue.current];
    batchQueue.current = [];

    if (batchTimer.current) {
      clearTimeout(batchTimer.current);
      batchTimer.current = null;
    }

    try {
      const result = await (ActivityLogService as any).createBulkLogs(
        batch.map(entry => ({
          ...entry,
          id: undefined,
          timestamp: undefined,
        })),
        session?.user?.id || ''
      );

      if (result?.success && result.data) {
        setLogs(prev => [...result.data, ...prev]);
      }
    } catch (error: any) {
      toast.error('Error processing batch logs:', error);
      batchQueue.current.unshift(...batch);
    }
  }, [session?.user?.id]);

  const loadLogs = useCallback(
    async (reset = false) => {
      if (isLoading) return;

      setIsLoading(true);

      try {
        const offset = reset ? 0 : currentPage * (filters.limit || 50);
        const backendFilters = mapFiltersToBackend(filters);
        backendFilters.limit = filters.limit || 50;
        backendFilters.offset = offset;

        const result = await (ActivityLogService as any).getLogs(backendFilters);

        if (result && Array.isArray(result)) {
          if (reset) {
            setLogs(result);
            setCurrentPage(0);
          } else {
            setLogs(prev => [...prev, ...result]);
            setCurrentPage(prev => prev + 1);
          }
          setHasNextPage(result.length === (filters.limit || 50));
        }
      } catch (error: any) {
        toast.error('Error loading activity logs:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [isLoading, currentPage, filters, mapFiltersToBackend]
  );

  const loadStats = useCallback(async () => {
    try {
      const result = await (ActivityLogService as any).getAnalytics(
        session?.user?.id,
        session?.user?.organizationId
      );

      if (result) {
        setStats(result);
      }
    } catch (error: any) {
      toast.error('Error loading activity stats:', error);
    }
  }, [session?.user?.id, session?.user?.organizationId]);

  const updateFilters = useCallback((newFilters: Partial<ActivityLogFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(0);
    setHasNextPage(true);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
    setCurrentPage(0);
    setHasNextPage(true);
  }, []);

  const refresh = useCallback(() => {
    loadLogs(true);
    loadStats();
  }, [loadLogs, loadStats]);

  const loadMore = useCallback(() => {
    if (!isLoading && hasNextPage) {
      loadLogs(false);
    }
  }, [isLoading, hasNextPage, loadLogs]);

  const exportLogs = useCallback(
    async (format: 'csv' | 'json' | 'xlsx' = 'csv'): Promise<Blob | null> => {
      try {
        const result = await (ActivityLogService as any).exportLogs(
          mapFiltersToBackend(filters),
          format,
          session?.user?.organizationId || '',
          session?.user?.id || ''
        );

        if (result && result.data) {
          return result.data;
        } else {
          toast.error('Failed to export activity logs');
          return null;
        }
      } catch (error: any) {
        toast.error('Error exporting activity logs:', error);
        return null;
      }
    },
    [filters, session?.user?.organizationId, session?.user?.id, mapFiltersToBackend]
  );

  const deleteLogs = useCallback(
    async (logIds: string[]) => {
      try {
        const result = await (ActivityLogService as any).deleteLogs(
          logIds,
          session?.user?.id || ''
        );

        if (result && result.success) {
          setLogs(prev => prev.filter(log => !logIds.includes(log.id)));
          toast.success(`Deleted ${logIds.length} log entries`);
        } else {
          toast.error('Failed to delete log entries');
        }
      } catch (error) {
        toast.error('Failed to delete log entries');
      }
    },
    [session?.user?.id]
  );

  const getLogById = useCallback(
    (logId: string): ActivityLogEntry | undefined => {
      return logs.find(log => log.id === logId);
    },
    [logs]
  );

  const getLogsByResource = useCallback(
    (resourceType: string, resourceId: string): ActivityLogEntry[] => {
      return logs.filter(log => log.resourceType === resourceType && log.resourceId === resourceId);
    },
    [logs]
  );

  const getLogsByUser = useCallback(
    (userId: string): ActivityLogEntry[] => {
      return logs.filter(log => log.userId === userId);
    },
    [logs]
  );

  const getRecentActivity = useCallback(
    (hours = 24): ActivityLogEntry[] => {
      const cutoff = new Date();
      cutoff.setHours(cutoff.getHours() - hours);
      return logs.filter(log => new Date(log.timestamp) >= cutoff);
    },
    [logs]
  );

  const trackPageView = useCallback(
    (pageName: string, metadata?: Record<string, any>) => {
      logAction('view', 'user', undefined, `Viewed ${pageName}`, {
        ...metadata,
        pageName,
        url: typeof window !== 'undefined' ? window.location.href : '',
      });
    },
    [logAction]
  );

  const trackUserAction = useCallback(
    (action: string, description?: string, metadata?: Record<string, any>) => {
      logAction(action as any, 'user', session?.user?.id, description, metadata);
    },
    [logAction, session?.user?.id]
  );

  const trackResourceAction = useCallback(
    (
      action: ActivityLogEntry['actionType'],
      resourceType: ActivityLogEntry['resourceType'],
      resourceId: string,
      resourceName?: string,
      changes?: ActivityLogEntry['changes'],
      metadata?: Record<string, any>
    ) => {
      logAction(
        action,
        resourceType,
        resourceId,
        undefined,
        {
          ...metadata,
          resourceName,
        },
        changes
      );
    },
    [logAction]
  );

  useEffect(() => {
    if (!opts.enableAutoTracking || !session?.user?.id) return;

    const trackAction = (
      action: string,
      resourceType: string,
      resourceId?: string,
      description?: string,
      metadata?: Record<string, any>
    ) => {
      if (opts.autoTrackActions?.includes(action)) {
        logAction(action as any, resourceType as any, resourceId, description, metadata);
      }
    };

    const handler = (event: any) => {
      const { action, resourceType, resourceId, description, metadata } = event.detail;
      trackAction(action, resourceType, resourceId, description, metadata);
    };

    window.addEventListener('TaskFluxio:action', handler);

    return () => {
      window.removeEventListener('TaskFluxio:action', handler);
    };
  }, [opts.enableAutoTracking, opts.autoTrackActions, session?.user?.id, logAction]);

  useEffect(() => {
    if (session?.user?.organizationId) {
      loadLogs(true);
      loadStats();
    }
  }, [session?.user?.organizationId, loadLogs, loadStats]);

  return {
    logs: filteredLogs,
    allLogs: logs,
    stats,
    filters,
    isLoading,
    hasNextPage,
    currentPage,
    logAction,
    loadLogs,
    loadStats,
    refresh,
    loadMore,
    updateFilters,
    clearFilters,
    exportLogs,
    deleteLogs,
    getLogById,
    getLogsByResource,
    getLogsByUser,
    getRecentActivity,
    trackPageView,
    trackUserAction,
    trackResourceAction,
  };
};

export default useActivityLog;
