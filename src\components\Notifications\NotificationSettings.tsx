'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useNotificationManager } from '@/hooks/useNotificationManager';
import { 
  Bell, 
  BellOff, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2,
  Send,
  Settings
} from 'lucide-react';

export function NotificationSettings() {
  const {
    isSupported,
    permission,
    isSubscribed,
    isLoading,
    error,
    serviceWorkerReady,
    subscribe,
    unsubscribe,
    sendTestNotification
  } = useNotificationManager();

  const [testLoading, setTestLoading] = useState(false);

  const handleToggleNotifications = async () => {
    if (isSubscribed) {
      await unsubscribe();
    } else {
      await subscribe();
    }
  };

  const handleTestNotification = async () => {
    setTestLoading(true);
    await sendTestNotification();
    setTestLoading(false);
  };

  const getPermissionBadge = () => {
    switch (permission) {
      case 'granted':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Granted</Badge>;
      case 'denied':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Denied</Badge>;
      default:
        return <Badge variant="secondary"><AlertTriangle className="h-3 w-3 mr-1" />Not Requested</Badge>;
    }
  };

  const getStatusIcon = () => {
    if (!isSupported) return <XCircle className="h-5 w-5 text-red-500" />;
    if (!serviceWorkerReady) return <Loader2 className="h-5 w-5 text-yellow-500 animate-spin" />;
    if (isSubscribed) return <Bell className="h-5 w-5 text-green-500" />;
    return <BellOff className="h-5 w-5 text-gray-500" />;
  };

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-500" />
            Push Notifications Not Supported
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your browser doesn't support push notifications. Please use a modern browser like Chrome, Firefox, or Safari.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Settings Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon()}
            Push Notifications
          </CardTitle>
          <CardDescription>
            Get notified about important updates and activities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Permission Status</Label>
              {getPermissionBadge()}
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium">Service Worker</Label>
              <Badge variant={serviceWorkerReady ? "default" : "secondary"}>
                {serviceWorkerReady ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Ready</>
                ) : (
                  <><Loader2 className="h-3 w-3 mr-1 animate-spin" />Loading</>
                )}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium">Subscription Status</Label>
              <Badge variant={isSubscribed ? "default" : "secondary"}>
                {isSubscribed ? (
                  <><Bell className="h-3 w-3 mr-1" />Active</>
                ) : (
                  <><BellOff className="h-3 w-3 mr-1" />Inactive</>
                )}
              </Badge>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Main Toggle */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label className="text-base font-medium">Enable Push Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications when you're not actively using the app
              </p>
            </div>
            <Switch
              checked={isSubscribed}
              onCheckedChange={handleToggleNotifications}
              disabled={isLoading || !serviceWorkerReady || permission === 'denied'}
            />
          </div>

          {/* Permission Denied Help */}
          {permission === 'denied' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Notifications are blocked. To enable them:
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Click the lock icon in your browser's address bar</li>
                  <li>Change notifications from "Block" to "Allow"</li>
                  <li>Refresh this page</li>
                </ol>
              </AlertDescription>
            </Alert>
          )}

          {/* Test Notification */}
          {isSubscribed && (
            <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
              <div className="space-y-1">
                <Label className="text-base font-medium">Test Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Send a test notification to verify everything is working
                </p>
              </div>
              <Button
                onClick={handleTestNotification}
                disabled={testLoading}
                variant="outline"
                size="sm"
              >
                {testLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send Test
              </Button>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleToggleNotifications}
              disabled={isLoading || !serviceWorkerReady}
              className="flex-1"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : isSubscribed ? (
                <BellOff className="h-4 w-4 mr-2" />
              ) : (
                <Bell className="h-4 w-4 mr-2" />
              )}
              {isSubscribed ? 'Disable Notifications' : 'Enable Notifications'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notification Types Card */}
      {isSubscribed && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Notification Types
            </CardTitle>
            <CardDescription>
              Choose what types of notifications you want to receive
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Task Assignments</Label>
                  <p className="text-sm text-muted-foreground">When you're assigned to a new task</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Project Updates</Label>
                  <p className="text-sm text-muted-foreground">Important project announcements</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Mentions</Label>
                  <p className="text-sm text-muted-foreground">When someone mentions you in comments</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>System Announcements</Label>
                  <p className="text-sm text-muted-foreground">Important system updates and maintenance</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Deadline Reminders</Label>
                  <p className="text-sm text-muted-foreground">Reminders about upcoming deadlines</p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Technical Info Card (for debugging) */}
      {process.env.NODE_ENV === 'development' && isSubscribed && (
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>Browser Support:</strong> {isSupported ? 'Yes' : 'No'}</div>
              <div><strong>Permission:</strong> {permission}</div>
              <div><strong>Service Worker:</strong> {serviceWorkerReady ? 'Ready' : 'Not Ready'}</div>
              <div><strong>Subscribed:</strong> {isSubscribed ? 'Yes' : 'No'}</div>
              {error && <div><strong>Error:</strong> {error}</div>}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
