{"version": 2, "functions": {"src/app/api/webhooks/github/route.ts": {"maxDuration": 15}, "src/app/api/notes/[[...route]]/route.ts": {"maxDuration": 15}, "src/app/api/storage/[[...route]]/route.ts": {"maxDuration": 15}, "src/app/api/[[...route]]/route.ts": {"maxDuration": 10}, "src/app/api/search/[[...route]]/route.ts": {"maxDuration": 10}, "src/app/api/mind-maps/[[...route]]/route.ts": {"maxDuration": 10}, "src/app/api/invitations/[[...route]]/route.ts": {"maxDuration": 10}}, "headers": []}