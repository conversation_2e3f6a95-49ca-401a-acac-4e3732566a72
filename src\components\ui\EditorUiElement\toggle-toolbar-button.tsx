'use client';

import * as React from 'react';

import { useToggleToolbarButton, useToggleToolbarButtonState } from '@udecode/plate-toggle/react';
import { ListCollapseIcon } from 'lucide-react';

import { ToolbarButton } from './toolbar';
import { cn } from '@/lib/utils';

export function ToggleToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const state = useToggleToolbarButtonState();
  const { props: buttonProps } = useToggleToolbarButton(state);

  return (
    <ToolbarButton {...props} {...buttonProps} tooltip="Toggle">
      <div className={cn('theme-transition', 'text-blue-600 dark:text-blue-400')}>
        <ListCollapseIcon className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
