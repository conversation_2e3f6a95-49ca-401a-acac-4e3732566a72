import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { StatCard } from '@/components/Global/StatCard';
import { ChartCard } from '@/components/Global/ChartCard';
import {
    Activity,
    TrendingUp,
    Users,
    Database,
    BarChart3,
} from 'lucide-react';
import { format as formatDate } from 'date-fns';
import { useActivityAnalyticsWithDateRange } from '@/hooks/useActivityAnalytics';
import {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';

import { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';

interface ActivityLogAnalyticsProps {
    userId?: string;
    organizationId?: string;
    dateRange?: { start?: Date; end?: Date };
    className?: string;
}
const CHART_COLORS = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
];

export const ActivityLogAnalytics: React.FC<ActivityLogAnalyticsProps> = ({
    userId,
    organizationId,
    dateRange,
    className = '',
}) => {
    // Only pass dateRange if both start and end are defined
    const validDateRange = dateRange?.start && dateRange?.end
        ? { start: dateRange.start, end: dateRange.end }
        : undefined;

    const {
        data,
        isLoading,
        totalActions,
        mostActiveAction,
        mostActiveResource,
        activeUsersCount
    } = useActivityAnalyticsWithDateRange({
        userId,
        organizationId,
        dateRange: validDateRange,
    });

    if (isLoading) {
        return (
            <div className={`flex items-center justify-center h-96 ${className}`}>
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    if (!data) {
        return (
            <div className={`flex flex-col items-center justify-center py-16 text-muted-foreground ${className}`}>
                <Activity className="w-12 h-12 mb-4 text-muted-foreground/50" />
                <p className="text-lg">No analytics data available</p>
                <p className="text-sm text-muted-foreground mt-1">Try adjusting your filters</p>
            </div>
        );
    }

    // Prepare data for charts
    const actionBreakdownChartData = Object.entries(data.actionBreakdown).map(([name, value], index) => ({
        name,
        value,
        color: CHART_COLORS[index % CHART_COLORS.length], // Cycle through colors
    }));

    const resourceBreakdownChartData = Object.entries(data.resourceBreakdown).map(([name, value], index) => ({
        name,
        value,
        color: CHART_COLORS[index % CHART_COLORS.length],
    }));

    // Daily Activity Chart Data
    let dailyActivityChartData: { date: string; count: number }[] = [];
    if (data.dailyActivity && data.dailyActivity.length > 0) {
        const validData = data.dailyActivity.filter(item => {
            const date = new Date(item.date);
            return !isNaN(date.getTime());
        });
        const sortedData = [...validData].sort((a, b) => {
            return new Date(a.date).getTime() - new Date(b.date).getTime();
        });
        dailyActivityChartData = sortedData.map(item => ({
            date: formatDate(new Date(item.date), 'MMM dd'),
            count: item.count,
        }));
    }

    // Top Users Data
    const topUsersData = data.topUsers.slice(0, 10);

    return (
        <div className={`space-y-8 ${className}`}>

            <section>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                    <StatCard
                        title="Total Actions"
                        value={totalActions.toLocaleString()}
                        icon={Activity}
                        trend={{
                            value: totalActions,
                            isPositive: totalActions > 0,
                        }}
                        className="border-0 bg-gradient-to-br from-blue-50/50 to-blue-100/30 dark:from-blue-950/20 dark:to-blue-900/10 shadow-sm hover:shadow-md transition-all duration-200"
                    />
                    <StatCard
                        title="Most Active Resource"
                        value={mostActiveResource || 'N/A'}
                        icon={Database}
                        trend={{
                            value: data.resourceBreakdown[mostActiveResource || ''] || 0,
                            isPositive: true,
                        }}
                        className="border-0 bg-gradient-to-br from-emerald-50/50 to-emerald-100/30 dark:from-emerald-950/20 dark:to-emerald-900/10 shadow-sm hover:shadow-md transition-all duration-200"
                    />
                    <StatCard
                        title="Most Common Action"
                        value={mostActiveAction || 'N/A'}
                        icon={TrendingUp}
                        trend={{
                            value: data.actionBreakdown[mostActiveAction || ''] || 0,
                            isPositive: true,
                        }}
                        className="border-0 bg-gradient-to-br from-orange-50/50 to-orange-100/30 dark:from-orange-950/20 dark:to-orange-900/10 shadow-sm hover:shadow-md transition-all duration-200"
                    />
                    <StatCard
                        title="Active Users"
                        value={activeUsersCount.toString()}
                        icon={Users}
                        trend={{
                            value: activeUsersCount,
                            isPositive: activeUsersCount > 1,
                        }}
                        className="border-0 bg-gradient-to-br from-purple-50/50 to-purple-100/30 dark:from-purple-950/20 dark:to-purple-900/10 shadow-sm hover:shadow-md transition-all duration-200"
                    />
                </div>
            </section>

            {/* Charts Section */}
            <section>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                    {/* Action Breakdown Chart */}
                    <ChartCard
                        title="Actions Breakdown"
                        className="theme-shadow theme-transition"
                    >
                        {actionBreakdownChartData.length > 0 ? (
                            <ChartContainer
                                config={Object.fromEntries(
                                    actionBreakdownChartData.map((item) => [
                                        item.name,
                                        {
                                            label: item.name,
                                            color: item.color,
                                        },
                                    ])
                                )}
                                className="aspect-square max-h-[300px] w-full"
                            >
                                <PieChart>
                                    <ChartTooltip
                                        cursor={false}
                                        content={<ChartTooltipContent hideLabel />}
                                    />
                                    <Pie
                                        data={actionBreakdownChartData}
                                        dataKey="value"
                                        nameKey="name"
                                        innerRadius={60}
                                    >
                                        {actionBreakdownChartData.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Pie>
                                </PieChart>
                            </ChartContainer>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                                <Activity className="w-10 h-10 mb-2 text-muted-foreground/50" />
                                <p>No action data available</p>
                            </div>
                        )}
                    </ChartCard>

                    {/* Resources Breakdown Chart */}
                    <ChartCard
                        title="Resources Breakdown"
                        className="theme-shadow theme-transition"
                    >
                        {resourceBreakdownChartData.length > 0 ? (
                            <ChartContainer
                                config={Object.fromEntries(
                                    resourceBreakdownChartData.map((item) => [
                                        item.name,
                                        {
                                            label: item.name,
                                            color: item.color,
                                        },
                                    ])
                                )}
                                className="aspect-square max-h-[300px] w-full"
                            >
                                <PieChart>
                                    <ChartTooltip
                                        cursor={false}
                                        content={<ChartTooltipContent hideLabel />}
                                    />
                                    <Pie
                                        data={resourceBreakdownChartData}
                                        dataKey="value"
                                        nameKey="name"
                                        innerRadius={60}
                                    >
                                        {resourceBreakdownChartData.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Pie>
                                </PieChart>
                            </ChartContainer>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                                <Database className="w-10 h-10 mb-2 text-muted-foreground/50" />
                                <p>No resource data available</p>
                            </div>
                        )}
                    </ChartCard>
                </div>
            </section>

            {/* Detailed Insights Section */}
            <section>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                    {/* Daily Activity Chart */}
                    <ChartCard
                        title="Daily Activity"
                        className="theme-shadow theme-transition"
                    >
                        {dailyActivityChartData.length > 0 ? (
                            <ChartContainer
                                config={{
                                    count: {
                                        label: "Actions",
                                        color: "hsl(var(--chart-1))",
                                    },
                                }}
                                className="aspect-square max-h-[300px] w-full"
                            >
                                <BarChart
                                    accessibilityLayer
                                    data={dailyActivityChartData}
                                    margin={{
                                        top: 20,
                                        right: 30, // Increased for Y-axis labels
                                        left: 20,
                                        bottom: 20, // Increased for X-axis labels
                                    }}
                                >
                                    <CartesianGrid vertical={false} />
                                    <XAxis
                                        dataKey="date"
                                        tickLine={false}
                                        tickMargin={10}
                                        axisLine={false}
                                        tickFormatter={(value) => value}
                                    />
                                    <YAxis
                                        tickLine={false}
                                        axisLine={false}
                                        tickMargin={8}
                                        tickFormatter={(value) => `${value}`}
                                    />
                                    <ChartTooltip
                                        cursor={false}
                                        content={<ChartTooltipContent indicator="dashed" />}
                                    />
                                    <Bar
                                        dataKey="count"
                                        fill="var(--color-count)"
                                        radius={4} // Rounded corners
                                    />
                                </BarChart>
                            </ChartContainer>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                                <BarChart3 className="w-10 h-10 mb-2 text-muted-foreground/50" />
                                <p>No daily activity data</p>
                                <p className="text-sm mt-1">for the selected period</p>
                            </div>
                        )}
                    </ChartCard>

                    {/* Top Users List */}
                    <Card className="theme-shadow theme-transition">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="w-5 h-5 text-primary" />
                                Most Active Users
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {topUsersData.length > 0 ? (
                                <div className="space-y-3">
                                    {topUsersData.map((user, index) => (
                                        <div
                                            key={user.userId}
                                            className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 theme-transition-fast"
                                        >
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-bold text-primary">
                                                    {index + 1}
                                                </div>
                                                <span className="font-medium">{user.userName}</span>
                                            </div>
                                            <Badge variant="secondary" className="text-xs">
                                                {user.count} actions
                                            </Badge>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                                    <Users className="w-10 h-10 mb-2 text-muted-foreground/50" />
                                    <p>No user activity data</p>
                                    <p className="text-sm mt-1">for the selected period</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </section>
        </div>
    );
};