'use client';

import * as React from 'react';

import { useIndentButton } from '@udecode/plate-indent/react';
import { Indent } from 'lucide-react';
import { ToolbarButton } from './toolbar';
import { cn } from '@/lib/utils';

export function IndentToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const { props: buttonProps } = useIndentButton();

  return (
    <ToolbarButton {...props} {...buttonProps} tooltip="Indent">
      <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
        <Indent className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
