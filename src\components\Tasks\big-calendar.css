/* Custom styles for React Big Calendar */

/* Calendar container */
.rbc-calendar {
  font-family: var(--font-outfit), sans-serif;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Header */
.rbc-toolbar {
  display: none !important; /* Hide the default toolbar */
}

/* Keep these styles for other components that might use these classes */
.rbc-toolbar button {
  color: #475569;
  border-color: #e2e8f0;
  background-color: white;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.rbc-toolbar button:hover {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #3b82f6;
}

.rbc-toolbar button.rbc-active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.rbc-toolbar button.rbc-active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Month view */
.rbc-month-view {
  border-radius: 0.5rem;
  border-color: #e2e8f0;
  overflow: hidden;
}

.rbc-month-header {
  background-color: #f8fafc;
}

.rbc-header {
  padding: 0.75rem 0;
  font-weight: 600;
  color: #475569;
  border-color: #e2e8f0;
}

.rbc-month-row {
  border-color: #e2e8f0;
}

.rbc-day-bg {
  transition: background-color 0.2s;
}

.rbc-day-bg:hover {
  background-color: #f1f5f9;
}

.rbc-off-range-bg {
  background-color: #f8fafc;
}

.rbc-today {
  background-color: #eff6ff;
}

/* Events */
.rbc-event {
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: none !important;
}

.rbc-event:hover {
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 10;
}

.rbc-event-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.rbc-event-content {
  font-weight: 500;
  line-height: 1.2;
}

/* Week view */
.rbc-time-view {
  border-radius: 0.5rem;
  border-color: #e2e8f0;
  overflow: hidden;
}

.rbc-time-header {
  border-color: #e2e8f0;
}

.rbc-time-header-content {
  border-color: #e2e8f0;
}

.rbc-time-content {
  border-color: #e2e8f0;
}

.rbc-time-slot {
  color: #64748b;
}

.rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
}

/* Day view */
.rbc-day-slot .rbc-event {
  border-radius: 0.375rem;
  margin-right: 10px;
  border-left: 3px solid rgba(255, 255, 255, 0.5) !important;
}

/* Add a subtle animation for day/week view events */
.rbc-day-slot .rbc-event {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Agenda view */
.rbc-agenda-view table.rbc-agenda-table {
  border-color: #e2e8f0;
}

.rbc-agenda-view table.rbc-agenda-table thead > tr > th {
  border-color: #e2e8f0;
  background-color: #f8fafc;
  color: #475569;
  padding: 0.75rem;
}

.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {
  border-color: #e2e8f0;
  padding: 0.75rem;
}

.rbc-agenda-time-cell {
  font-size: 0.875rem;
  color: #64748b;
}

.rbc-agenda-date-cell {
  font-size: 0.875rem;
  font-weight: 500;
  color: #334155;
}

.rbc-agenda-event-cell {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Popup */
.rbc-overlay {
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #e2e8f0;
}

.rbc-overlay-header {
  padding: 0.75rem;
  background-color: #f8fafc;
  border-color: #e2e8f0;
  color: #334155;
  font-weight: 600;
}
