'use client';

import type React from 'react';

import { memo, useState } from 'react';
import { <PERSON>le, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Diamond, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';

interface DecisionNodeData {
  title: string;
  options?: string[];
  selected?: string;
  status: 'pending' | 'active' | 'resolved';
  conditions?: Array<{ handle: string; label: string }>;
}

export const DecisionNode = memo(({ id, data, selected }: NodeProps<DecisionNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    active: 'bg-blue-100 text-blue-800 border-blue-200',
    resolved: 'bg-green-100 text-green-800 border-green-200',
  };

  const handleDoubleClick = () => {
    startEditing(id, { ...data, nodeType: 'decision' });
  };

  // Default conditions for decision node (True/False)
  const defaultConditions = [
    { handle: 'true', label: 'True' },
    { handle: 'false', label: 'False' },
  ];

  const conditions = data?.conditions || defaultConditions;

  return (
    <div
      className="relative"
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Input handle at the top */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
        style={{ top: '-6px', left: '50%', transform: 'translateX(-50%)' }}
      />

      <Card
        className={`min-w-[180px] bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200 shadow-lg transition-all duration-200 ${
          selected
            ? 'ring-2 ring-yellow-500 shadow-xl scale-105'
            : 'hover:shadow-xl hover:scale-102'
        }`}
        style={{
          clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
          transform: 'rotate(0deg)',
        }}
      >
        <div className="p-6 flex flex-col items-center justify-center min-h-[120px]">
          <div className="flex items-center justify-center space-x-2 mb-3">
            <Diamond className="h-4 w-4 text-yellow-600" />
            <h3 className="font-semibold text-gray-900 text-sm text-center">
              {data?.title || 'Decision'}
            </h3>
            {isHovered && (
              <Edit3
                className="h-3 w-3 text-gray-400 ml-1 cursor-pointer"
                onClick={() => startEditing(id, { ...data, nodeType: 'decision' })}
              />
            )}
          </div>

          {data?.options && data.options.length > 0 && (
            <div className="space-y-1 mb-2">
              {data.options.map((option, index) => (
                <div
                  key={index}
                  className={`text-xs px-2 py-1 rounded text-center ${
                    option === data.selected
                      ? 'bg-yellow-200 text-yellow-800 font-medium'
                      : 'text-gray-600'
                  }`}
                >
                  {option}
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-center">
            <Badge
              variant="outline"
              className={`text-xs ${statusColors[data?.status || 'pending']}`}
            >
              {data?.status || 'pending'}
            </Badge>
          </div>
        </div>
      </Card>

      {/* Output handles positioned at proper angles */}
      {conditions.map((condition, index) => {
        let position: Position;
        let style: React.CSSProperties = {};
        let labelStyle: React.CSSProperties = {};

        if (conditions.length === 2) {
          // For True/False decisions, position at left and right sides of diamond
          if (condition.handle === 'true' || index === 0) {
            // True - right side of diamond
            position = Position.Right;
            style = {
              right: '-6px',
              top: '50%',
              transform: 'translateY(-50%)',
            };
            labelStyle = {
              right: '-45px',
              top: '50%',
              transform: 'translateY(-50%)',
            };
          } else {
            // False - left side of diamond
            position = Position.Left;
            style = {
              left: '-6px',
              top: '50%',
              transform: 'translateY(-50%)',
            };
            labelStyle = {
              left: '-45px',
              top: '50%',
              transform: 'translateY(-50%)',
            };
          }
        } else {
          // For multiple conditions, distribute around the diamond
          const angle = (index * 360) / conditions.length;
          const isRight = angle >= 315 || angle < 45;
          const isBottom = angle >= 45 && angle < 135;
          const isLeft = angle >= 135 && angle < 225;

          if (isRight) {
            position = Position.Right;
            style = { right: '-6px', top: '50%', transform: 'translateY(-50%)' };
          } else if (isBottom) {
            position = Position.Bottom;
            style = { bottom: '-6px', left: '50%', transform: 'translateX(-50%)' };
          } else if (isLeft) {
            position = Position.Left;
            style = { left: '-6px', top: '50%', transform: 'translateY(-50%)' };
          } else {
            position = Position.Top;
            style = { top: '-6px', left: '50%', transform: 'translateX(-50%)' };
          }
        }

        return (
          <div key={condition.handle} className="relative">
            <Handle
              id={condition.handle}
              type="source"
              position={position}
              className="w-3 h-3 bg-yellow-500 border-2 border-white"
              style={style}
            />
            {isHovered && (
              <div
                className="absolute bg-white/90 backdrop-blur-sm text-xs px-2 py-1 rounded border border-gray-200 shadow-sm whitespace-nowrap z-10"
                style={labelStyle}
              >
                {condition.label}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
});

DecisionNode.displayName = 'DecisionNode';
