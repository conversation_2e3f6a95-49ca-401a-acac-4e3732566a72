@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Geist';
  src: url('/fonts/GeistVF.woff') format('woff');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Geist Mono';
  src: url('/fonts/GeistMonoVF.woff') format('woff');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}
body {
  font-family: '<PERSON>eist', Arial, Helvetica, sans-serif;
}
*{
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 30% 58%;
    --success-foreground: 0 0% 98%;
    --warning: 40 74% 58%;
    --warning-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --brand: oklch(0.623 0.214 259.815);
  }
  .dark {
      --background: 0 0% 1.5%;
        --foreground: 0 0% 95%;
        --card: 0 0% 2%;
        --card-foreground: 0 0% 95%;
        --popover: 0 0% 2.5%;
        --popover-foreground: 0 0% 95%;
        --primary: 0 0% 95%;
        --primary-foreground: 0 0% 5%;
        --secondary: 0 0% 6%;
        --secondary-foreground: 0 0% 90%;
        --muted: 0 0% 8%;
        --muted-foreground: 0 0% 55%;
        --accent: 0 0% 10%;
        --accent-foreground: 0 0% 90%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 30% 58%;
    --success-foreground: 0 0% 98%;
    --warning: 40 74% 58%;
    --warning-foreground: 0 0% 98%;
    --border: 0 0% 12%;
      --input: 0 0% 8%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    /* Much darker sidebar colors */
      --sidebar-background: 0 0% 2%;
      --sidebar-foreground: 0 0% 85%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 6%;
      --sidebar-accent-foreground: 0 0% 85%;
      --sidebar-border: 0 0% 8%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --brand: oklch(0.707 0.165 254.624);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
/* Custom Scrollbar */
.scrollbar-custom {
  scrollbar-width: thin;
    scrollbar-color: #4A90E2 #F1F1F1;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
    height: 6px;
  }

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #4A90E2, #357ABD);
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background 0.3s ease, transform 0.2s ease;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #5AA0F2, #468BCF);
    transform: scale(1.1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #F1F1F1;
    border-radius: 10px;
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.05);
  }
    @supports (scrollbar-color: auto) {
      .scrollbar-custom {
        scrollbar-color: linear-gradient(180deg, #4A90E2, #357ABD) #F1F1F1;
      }
}



/* Sidebar Styles */
.sidebar {
  width: 240px; /* Default width */
  transition: width 0.3s ease-in-out;
}

.sidebar.collapsed {
  width: 70px;
}

@media (max-width: 767px) {
  .sidebar {
      width: 240px;
      position: fixed;
      transform: translateX(-100%);
  }

  .sidebar.mobile-open {
      transform: translateX(0);
  }
}
.task-bar {
  transition: opacity 0.2s ease;
}

.task-bar:hover {
  opacity: 0.9;
}
.gradient-text {
  @apply bg-clip-text text-transparent;
  background-image: linear-gradient(to right, #a855f7, #ec4899);
}
/* Grid Pattern Background */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .bg-grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced hover effects */
.hover-reveal {
  transition: all 0.3s ease;
}

.hover-reveal:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .hover-reveal:hover {
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}

/* Subtle glow effects */
.glow-on-hover {
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.15);
}

.dark .glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Advanced button effects */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Floating animation */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse animation for stats */
@keyframes pulse-glow {

  0%,
  100% {
    box-shadow: 0 0 5px rgba(168, 85, 247, 0.3);
  }

  50% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 3D perspective effects */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Enhanced hover effects for Indian theme */
.indian-hover {
  transition: all 0.3s ease;
}

.indian-hover:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 10px 25px rgba(255, 165, 0, 0.15),
    0 0 20px rgba(0, 128, 0, 0.1);
}

/* Floating keyframes */
@keyframes float-slow {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-10px) rotate(120deg);
  }

  66% {
    transform: translateY(5px) rotate(240deg);
  }
}

@keyframes float-medium {

  0%,
  100% {
    transform: translateY(0px) scale(1);
  }

  50% {
    transform: translateY(-15px) scale(1.1);
  }
}

@keyframes float-fast {

  0%,
  100% {
    transform: translateX(0px) rotate(0deg);
  }

  25% {
    transform: translateX(10px) rotate(90deg);
  }

  50% {
    transform: translateX(0px) rotate(180deg);
  }

  75% {
    transform: translateX(-10px) rotate(270deg);
  }
}

.float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

/* Gradient text animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.gradient-animate {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Card tilt effect */
.card-tilt {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.card-tilt:hover {
  transform: rotateY(5deg) rotateX(5deg);
}
/* Custom scrollbar for timeline components */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.5);
}

/* Animation for timeline items */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Remove spinner buttons from number inputs */
input[type=number] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}
/* Animation classes for Shadcn UI */
@keyframes enter {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes exit {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

/* Prevent text selection during drag operations */
.dnd-dragging,
.dnd-dragging * {
  cursor: grabbing !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important;
  }

  /* Ensure proper cursor for draggable elements */
  .cursor-grab {
    cursor: grab !important;
  }

  .cursor-grab:active {
    cursor: grabbing !important;
  }

  .cursor-default {
    cursor: default !important;
}

.animate-in {
  animation: enter 0.2s ease-out;
}

.animate-out {
  animation: exit 0.2s ease-in;
}

/* Responsive grid utilities for repository cards */
.repo-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (max-width: 640px) {
  .repo-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .repo-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .repo-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) {
  .repo-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

/* Ensure consistent card heights */
.repo-card {
  height: 100%;
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.repo-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Responsive breakpoint for extra small screens */
@media (max-width: 480px) {
  .xs\:hidden {
    display: none !important;
  }

  .xs\:inline {
    display: inline !important;
  }

  .xs\:block {
    display: block !important;
  }

  .xs\:flex {
    display: flex !important;
  }
}

/* Enhanced tab styling for better dark mode visibility */
.tabs-enhanced [data-state="active"] {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid hsl(var(--primary)) !important;
}

.dark .tabs-enhanced [data-state="active"] {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1) !important;
  border: 1px solid hsl(var(--primary)) !important;
}

/* Compact header styles */
.github-header-compact {
  min-height: 60px;
}

.github-tabs-compact {
  min-height: 44px;
}
.fade-in {
  opacity: 0;
  animation: enter 0.2s ease-out forwards;
}

.slide-in-from-top {
  transform: translateY(-100%);
}

.slide-in-from-bottom {
  transform: translateY(100%);
}

.slide-in-from-left {
  transform: translateX(-100%);
}

.slide-in-from-right {
  transform: translateX(100%);
}

.slide-out-to-top {
  transform: translateY(-100%);
}

.slide-out-to-bottom {
  transform: translateY(100%);
}

.slide-out-to-left {
  transform: translateX(-100%);
}

.slide-out-to-right {
  transform: translateX(100%);
}
/* Animation delay utility classes */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-700 {
  animation-delay: 700ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1500 {
  animation-delay: 1500ms;
}

.animation-delay-2000 {
  animation-delay: 2000ms;
}
/* ===== GLOBAL DARK THEME UTILITY CLASSES ===== */

/* Sidebar Component Classes */
.sidebar-container {
  @apply bg-sidebar text-sidebar-foreground border-sidebar-border;
}

.sidebar-item {
  @apply text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-all duration-200;
}

.sidebar-item-active {
  @apply bg-sidebar-primary/10 text-sidebar-primary font-medium shadow-sm;
}

.sidebar-section-label {
  @apply text-sidebar-foreground/60 text-xs uppercase font-semibold tracking-wider;
}

.sidebar-badge-primary {
  @apply bg-sidebar-primary/15 text-sidebar-primary;
}

.sidebar-badge-secondary {
  @apply bg-destructive/20 text-destructive;
}

.sidebar-tooltip {
  @apply bg-popover text-popover-foreground shadow-lg border border-border;
}

.sidebar-chevron {
  @apply text-sidebar-foreground/50;
}

.sidebar-sub-item {
  @apply text-sidebar-foreground/70 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground border-sidebar-border;
}

.sidebar-sub-item-active {
  @apply bg-sidebar-primary/5 text-sidebar-primary font-medium;
}

/* User Profile Classes */
.user-profile-container {
  @apply border-sidebar-border shadow-sm;
}

.user-profile-section {
  @apply border-sidebar-border;
}

.user-profile-name {
  @apply text-sidebar-foreground font-semibold;
}

.user-profile-email {
  @apply text-sidebar-foreground/60;
}

.user-profile-status-indicator {
  @apply bg-sidebar shadow-sm border border-sidebar-border;
}

.user-profile-chevron {
  @apply text-sidebar-foreground/50 hover:text-sidebar-primary transition-colors duration-200;
}

.user-profile-signout-btn {
  @apply text-sidebar-foreground/80 hover:text-destructive bg-popover hover:bg-destructive/10 border border-border transition-colors duration-200;
}

/* Logo Classes */
.logo-container {
  @apply border-sidebar-border;
}

.logo-text {
  @apply text-sidebar-foreground font-bold;
}

.logo-toggle-btn {
  @apply bg-sidebar-accent hover:bg-sidebar-accent/80 transition-all duration-200 shadow-sm;
}

.logo-toggle-icon {
  @apply text-sidebar-foreground/70;
}

/* General Component Classes */
.theme-surface {
  @apply bg-card text-card-foreground border border-border;
}

.theme-surface-elevated {
  @apply bg-popover text-popover-foreground border border-border;
}
.theme-toggle {
  @apply bg-popover text-popover-foreground border border-border;
}
.theme-button-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200;
}

.theme-button-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-colors duration-200;
}

.theme-button-ghost {
  @apply hover:bg-accent hover:text-accent-foreground transition-colors duration-200;
}

.theme-input {
  @apply bg-input border border-border text-foreground placeholder:text-muted-foreground;
}

.theme-text-primary {
  @apply text-foreground;
}

.theme-text-secondary {
  @apply text-muted-foreground;
}

.theme-text-accent {
  @apply text-accent-foreground;
}

.theme-border {
  @apply border-border;
}

.theme-divider {
  @apply border-t border-border;
}

/* Interactive States */
.theme-hover-surface {
  @apply hover:bg-accent/50 transition-colors duration-200;
}

.theme-hover-primary {
  @apply hover:bg-primary/10 hover:text-primary transition-colors duration-200;
}

.theme-active-primary {
  @apply bg-primary/10 text-primary;
}

/* Badge Classes */
.theme-badge-primary {
  @apply bg-primary/15 text-primary px-2 py-0.5 text-xs font-medium rounded-full;
}

.theme-badge-secondary {
  @apply bg-secondary/50 text-secondary-foreground px-2 py-0.5 text-xs font-medium rounded-full;
}

.theme-badge-destructive {
  @apply bg-destructive/20 text-destructive px-2 py-0.5 text-xs font-medium rounded-full;
}

.theme-badge-success {
  @apply bg-success/20 text-success px-2 py-0.5 text-xs font-medium rounded-full;
}

.theme-badge-warning {
  @apply bg-warning/20 text-warning px-2 py-0.5 text-xs font-medium rounded-full;
}

/* Scrollbar Classes */
.theme-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted)) hsl(var(--background));
}

.theme-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.theme-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 10px;
}

.theme-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 10px;
}

.theme-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Enhanced image and media styles */
.plate-image-element {
  position: relative;
}

.plate-image-element img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Mobile-friendly resize handles */
@media (max-width: 640px) {
  .plate-resizable-handle {
    width: 32px !important;
    height: 32px !important;
  }

  .plate-resizable-handle::after {
    opacity: 0.7 !important;
    width: 4px !important;
  }
}

/* Touch-friendly media popover */
@media (max-width: 768px) {
  .plate-media-popover {
    transform: scale(1.1);
    transform-origin: center;
  }

  .plate-media-popover button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Drag and drop visual feedback */
.plate-editor-container.drag-over {
  background-color: hsl(var(--primary) / 0.05);
  border: 2px dashed hsl(var(--primary) / 0.3);
  border-radius: 8px;
}

.plate-editor-container.drag-over::before {
  content: "Drop images here to upload";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: hsl(var(--primary));
  font-weight: 500;
  z-index: 10;
  pointer-events: none;
}
/* Shadow Classes */
.theme-shadow-sm {
  box-shadow: 0 1px 2px 0 hsl(var(--foreground) / 0.05);
}

.theme-shadow {
  box-shadow: 0 1px 3px 0 hsl(var(--foreground) / 0.1), 0 1px 2px -1px hsl(var(--foreground) / 0.1);
}

.theme-shadow-md {
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1), 0 2px 4px -2px hsl(var(--foreground) / 0.1);
}

.theme-shadow-lg {
  box-shadow: 0 10px 15px -3px hsl(var(--foreground) / 0.1), 0 4px 6px -4px hsl(var(--foreground) / 0.1);
}

/* Focus States */
.theme-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Transition Classes */
.theme-transition {
  @apply transition-all duration-200 ease-in-out;
}

.theme-transition-fast {
  @apply transition-all duration-150 ease-in-out;
}

.theme-transition-slow {
  @apply transition-all duration-300 ease-in-out;
}

/* Chart and Data Visualization Classes */
.chart-tooltip {
  @apply bg-popover text-popover-foreground border border-border shadow-lg rounded-md;
}

.chart-legend {
  @apply text-muted-foreground;
}

.chart-grid {
  stroke: hsl(var(--border));
  stroke-opacity: 0.5;
}

.chart-axis {
  @apply text-muted-foreground;
}

/* Loading and Empty State Classes */
.loading-skeleton {
  @apply bg-muted animate-pulse rounded;
}

.empty-state-container {
  @apply flex flex-col items-center justify-center py-16 theme-text-secondary;
}

.empty-state-icon {
  @apply w-16 h-16 rounded-full theme-surface flex items-center justify-center mb-4;
}

.empty-state-title {
  @apply text-lg font-medium theme-text-primary mb-1;
}

.empty-state-description {
  @apply text-sm theme-text-secondary max-w-md text-center;
}

/* Status and Priority Indicator Classes */
.status-indicator-todo {
  @apply bg-muted/50 text-muted-foreground;
}

.status-indicator-in-progress {
  @apply bg-primary/20 text-primary;
}

.status-indicator-completed {
  @apply bg-success/20 text-success;
}

.priority-indicator-low {
  @apply bg-success/20 text-success;
}

.priority-indicator-medium {
  @apply bg-warning/20 text-warning;
}

.priority-indicator-high {
  @apply bg-destructive/20 text-destructive;
}

/* Card and Panel Variants */
.dashboard-card {
  @apply theme-surface-elevated rounded-lg p-4 theme-shadow-sm;
}

.dashboard-card-header {
  @apply flex items-center justify-between mb-4;
}

.dashboard-card-title {
  @apply text-lg font-semibold theme-text-primary;
}

.dashboard-card-description {
  @apply text-sm theme-text-secondary;
}

/* Interactive Element States */
.interactive-hover {
  @apply hover:bg-accent/50 hover:text-accent-foreground theme-transition cursor-pointer;
}

.interactive-active {
  @apply bg-accent text-accent-foreground;
}

.interactive-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Form and Input Variants */
.form-input {
  @apply theme-input rounded-md px-3 py-2 theme-focus;
}

.form-label {
  @apply text-sm font-medium theme-text-primary;
}

.form-error {
  @apply text-sm text-destructive;
}

.form-helper {
  @apply text-sm theme-text-secondary;
}