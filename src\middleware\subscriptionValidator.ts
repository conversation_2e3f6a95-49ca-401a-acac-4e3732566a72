import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Subscription } from '@/models/Subscription';
import { Project } from '@/models/Project';
import { Organization } from '@/models/organization';
import { Integration } from '@/models/Integration';
import { SubscriptionService } from '@/services/Subscription.service';

export interface ValidationResult {
  allowed: boolean;
  reason?: string;
  currentUsage?: number;
  limit?: number | 'unlimited';
  upgradeRequired?: boolean;
  recommendedPlan?: string;
}

export interface SubscriptionLimits {
  projects: number | 'unlimited';
  users: number | 'unlimited';
  integrations: number | 'unlimited';
  automations: number | 'unlimited';
  apiCalls: number | 'unlimited';
}

export class SubscriptionValidator {
  /**
   * Validate if user can perform an action based on their subscription
   */
  static async validateAction(
    organizationId: string,
    action:
      | 'create_project'
      | 'invite_user'
      | 'create_integration'
      | 'create_automation'
      | 'api_call'
  ): Promise<ValidationResult> {
    try {
      await connectDB();

      // Get subscription
      const subscription = await Subscription.findOne({ organizationId });
      if (!subscription) {
        return { allowed: false, reason: 'No subscription found' };
      }

      // Get current usage
      const currentUsage = await this.getCurrentUsage(organizationId);
      const limits = this.getPlanLimits(subscription.plan);

      switch (action) {
        case 'create_project':
          return this.validateProjectCreation(currentUsage.projects, limits.projects);

        case 'invite_user':
          return this.validateUserInvitation(currentUsage.users, limits.users);

        case 'create_integration':
          return this.validateIntegrationCreation(currentUsage.integrations, limits.integrations);

        case 'create_automation':
          return this.validateAutomationCreation(currentUsage.automations, limits.automations);

        case 'api_call':
          return this.validateApiCall(currentUsage.apiCalls, limits.apiCalls);

        default:
          return { allowed: false, reason: 'Unknown action' };
      }
    } catch (error) {
      console.error('Subscription validation error:', error);
      return { allowed: false, reason: 'Validation failed' };
    }
  }

  /**
   * Get current usage for an organization
   */
  private static async getCurrentUsage(organizationId: string): Promise<{
    projects: number;
    users: number;
    integrations: number;
    automations: number;
    apiCalls: number;
  }> {
    const [projectCount, organization, integrationCount, subscription] = await Promise.all([
      Project.countDocuments({ organizationId }),
      Organization.findById(organizationId),
      Integration.countDocuments({ organizationId, status: 'connected' }),
      Subscription.findOne({ organizationId }),
    ]);

    return {
      projects: projectCount,
      users: organization?.members?.length || 0,
      integrations: integrationCount,
      automations: subscription?.usage?.automationsCreated || 0,
      apiCalls: subscription?.usage?.apiCallsThisMonth || 0,
    };
  }

  /**
   * Get plan limits
   */
  private static getPlanLimits(planId: string): SubscriptionLimits {
    const planConfig = SubscriptionService.getPlanConfig(planId);
    if (!planConfig) {
      // Default to free plan limits
      return {
        projects: 3,
        users: 1,
        integrations: 0,
        automations: 0,
        apiCalls: 0,
      };
    }

    return {
      projects: planConfig.limits.projects,
      users: planConfig.limits.users,
      integrations: planConfig.limits.integrations,
      automations: planConfig.limits.automations,
      apiCalls: planConfig.limits.apiCalls,
    };
  }

  /**
   * Validate project creation
   */
  private static validateProjectCreation(
    currentProjects: number,
    limit: number | 'unlimited'
  ): ValidationResult {
    if (limit === 'unlimited') {
      return { allowed: true };
    }

    if (currentProjects >= limit) {
      return {
        allowed: false,
        reason: `Project limit reached (${limit}). Upgrade your plan to create more projects.`,
        currentUsage: currentProjects,
        limit,
        upgradeRequired: true,
        recommendedPlan: this.getRecommendedPlan('projects'),
      };
    }

    return { allowed: true, currentUsage: currentProjects, limit };
  }

  /**
   * Validate user invitation
   */
  private static validateUserInvitation(
    currentUsers: number,
    limit: number | 'unlimited'
  ): ValidationResult {
    if (limit === 'unlimited') {
      return { allowed: true };
    }

    if (currentUsers >= limit) {
      return {
        allowed: false,
        reason: `User limit reached (${limit}). Upgrade your plan to invite more users.`,
        currentUsage: currentUsers,
        limit,
        upgradeRequired: true,
        recommendedPlan: this.getRecommendedPlan('users'),
      };
    }

    return { allowed: true, currentUsage: currentUsers, limit };
  }

  private static validateIntegrationCreation(
    currentIntegrations: number,
    limit: number | 'unlimited'
  ): ValidationResult {
    if (limit === 'unlimited') {
      return { allowed: true };
    }

    if (currentIntegrations >= limit) {
      return {
        allowed: false,
        reason: `Integration limit reached (${limit}). Upgrade your plan to connect more integrations.`,
        currentUsage: currentIntegrations,
        limit,
        upgradeRequired: true,
        recommendedPlan: this.getRecommendedPlan('integrations'),
      };
    }

    return { allowed: true, currentUsage: currentIntegrations, limit };
  }

  /**
   * Validate automation creation
   */
  private static validateAutomationCreation(
    currentAutomations: number,
    limit: number | 'unlimited'
  ): ValidationResult {
    if (limit === 'unlimited') {
      return { allowed: true };
    }

    if (currentAutomations >= limit) {
      return {
        allowed: false,
        reason: `Automation limit reached (${limit}). Upgrade your plan to create more automations.`,
        currentUsage: currentAutomations,
        limit,
        upgradeRequired: true,
        recommendedPlan: this.getRecommendedPlan('automations'),
      };
    }

    return { allowed: true, currentUsage: currentAutomations, limit };
  }

  /**
   * Validate API call
   */
  private static validateApiCall(
    currentApiCalls: number,
    limit: number | 'unlimited'
  ): ValidationResult {
    if (limit === 'unlimited') {
      return { allowed: true };
    }

    if (currentApiCalls >= limit) {
      return {
        allowed: false,
        reason: `API call limit reached for this month (${limit}). Upgrade your plan for more API calls.`,
        currentUsage: currentApiCalls,
        limit,
        upgradeRequired: true,
        recommendedPlan: this.getRecommendedPlan('apiCalls'),
      };
    }

    return { allowed: true, currentUsage: currentApiCalls, limit };
  }

  /**
   * Get recommended plan based on limit type
   */
  private static getRecommendedPlan(limitType: string): string {
    // Simple logic - recommend next tier
    // In a real app, this could be more sophisticated
    switch (limitType) {
      case 'projects':
      case 'users':
        return 'basic';
      case 'storage':
      case 'integrations':
        return 'professional';
      case 'automations':
      case 'apiCalls':
        return 'enterprise';
      default:
        return 'basic';
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  static async middleware(
    action:
      | 'create_project'
      | 'invite_user'
      | 'create_integration'
      | 'create_automation'
      | 'api_call'
  ): Promise<NextResponse | null> {
    try {
      const session = await getServerSession(authOptions);

      if (!session?.user?.organizationId) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const validation = await this.validateAction(session.user.organizationId, action);

      if (!validation.allowed) {
        return NextResponse.json(
          {
            error: validation.reason,
            upgradeRequired: validation.upgradeRequired,
            recommendedPlan: validation.recommendedPlan,
            currentUsage: validation.currentUsage,
            limit: validation.limit,
          },
          { status: 403 }
        );
      }

      return null; // Allow the request to continue
    } catch (error) {
      console.error('Subscription middleware error:', error);
      return NextResponse.json({ error: 'Validation failed' }, { status: 500 });
    }
  }
}
