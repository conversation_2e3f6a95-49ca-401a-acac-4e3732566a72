import { Search } from 'lucide-react';
import { useEffect } from 'react';

interface SearchTriggerProps {
  onOpen: () => void;
}

export const SearchTrigger = ({ onOpen }: SearchTriggerProps) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        onOpen();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onOpen]);

  return (
    <button
      onClick={onOpen}
      className="flex items-center gap-2 px-3 py-2 theme-surface hover-reveal rounded-lg theme-transition text-sm theme-text-secondary min-w-[200px] justify-between glow-on-hover"
    >
      <div className="flex items-center gap-2">
        <Search className="w-4 h-4" />
        <span>Search...</span>
      </div>
      <kbd className="hidden sm:inline-flex items-center gap-1 px-2 py-1 theme-surface-elevated theme-border border rounded text-xs font-mono theme-text-primary theme-shadow-sm">
        <span>⌘</span>K
      </kbd>
    </button>
  );
};
