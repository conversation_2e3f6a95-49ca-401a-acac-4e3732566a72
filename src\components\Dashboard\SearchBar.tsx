import React, { useState, useCallback } from 'react';
import { Search, X } from 'lucide-react';

interface SearchBarProps {
  placeholder?: string;
  onSearch: (_query: string) => void;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  onSearch,
  className = '',
}) => {
  const [query, setQuery] = useState('');

  const handleSearch = useCallback(() => {
    onSearch(query);
  }, [query, onSearch]);

  const handleClear = useCallback(() => {
    setQuery('');
    onSearch('');
  }, [onSearch]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleSearch();
      }
    },
    [handleSearch]
  );

  return (
    <div className={`relative ${className}`}>
      <input
        type="text"
        value={query}
        onChange={e => setQuery(e.target.value)}
        onKeyUp={handleKeyPress}
        placeholder={placeholder}
        className="w-full py-2 pl-10 pr-10 text-sm theme-text-primary theme-surface-elevated theme-border border rounded-full theme-focus theme-transition glow-on-hover"
      />
      <div className="absolute inset-y-0 left-0 flex items-center pl-3">
        <Search
          className="w-5 h-5 theme-text-secondary cursor-pointer hover:text-primary theme-transition"
          onClick={handleSearch}
        />
      </div>
      {query && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <X
            className="w-5 h-5 theme-text-secondary cursor-pointer hover:text-destructive theme-transition"
            onClick={handleClear}
          />
        </div>
      )}
    </div>
  );
};
