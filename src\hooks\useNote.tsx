'use client';

import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { NoteService } from '@/services/Note.service';
import { Note, CreateNoteData } from '@/types/Note';

export const noteColors = [
  { name: 'Dark', value: '#1f2937' },
  { name: 'Yellow', value: '#92400e' },
  { name: 'Orange', value: '#9a3412' },
  { name: 'Red', value: '#991b1b' },
  { name: 'Pink', value: '#9d174d' },
  { name: 'Purple', value: '#5b21b6' },
  { name: 'Blue', value: '#1e40af' },
  { name: 'Green', value: '#166534' },
  { name: 'Gray', value: '#374151' },
];

export const noteCategories = [
  'general',
  'work',
  'personal',
  'ideas',
  'meeting',
  'research',
  'todo',
  'project',
];

export type FilterMode = 'all' | 'pinned' | 'archived' | 'favorites' | 'active';
export type SortBy = 'updatedAt' | 'createdAt' | 'title' | 'wordCount';
export type SortOrder = 'asc' | 'desc';

export interface NoteFilters {
  searchQuery: string;
  category: string;
  tags: string[];
  filterMode: FilterMode;
  sortBy: SortBy;
  sortOrder: SortOrder;
  currentPage: number;
}

export const useNote = () => {
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<NoteFilters>({
    searchQuery: '',
    category: '',
    tags: [],
    filterMode: 'all',
    sortBy: 'updatedAt',
    sortOrder: 'desc',
    currentPage: 1,
  });

  const [selectedNotes, setSelectedNotes] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const {
    data: notesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['notes', filters],
    queryFn: () =>
      NoteService.getNotes({
        page: filters.currentPage,
        search: filters.searchQuery,
        category: filters.category,
        tags: filters.tags,
        filter: filters.filterMode,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      }),
    staleTime: 30000,
  });

  const getNoteQueryKey = useCallback((noteId: string) => ['note', noteId], []);

  const createNoteMutation = useMutation({
    mutationFn: (data: CreateNoteData) => NoteService.createNote(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note created successfully');
    },
    onError: (error: Error) => {
      toast.error('Failed to create note', { description: error.message });
    },
  });

  const updateNoteMutation = useMutation({
    mutationFn: ({ noteId, ...data }: { noteId: string } & Partial<Note>) =>
      NoteService.updateNote(noteId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['note'] });
      toast.success('Note updated successfully');
    },
    onError: (error: Error) => {
      toast.error('Failed to update note', { description: error.message });
    },
  });

  const deleteNoteMutation = useMutation({
    mutationFn: ({ noteId, permanent = false }: { noteId: string; permanent?: boolean }) =>
      NoteService.deleteNote(noteId, permanent),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note deleted successfully');
      setSelectedNotes([]);
    },
    onError: (error: Error) => {
      toast.error('Failed to delete note', { description: error.message });
    },
  });

  const bulkActionMutation = useMutation({
    mutationFn: ({ action, noteIds, data }: { action: string; noteIds: string[]; data?: any }) =>
      NoteService.bulkAction(action, noteIds, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success(`${variables.action} completed successfully`);
      setSelectedNotes([]);
      setIsSelectionMode(false);
    },
    onError: (error: Error) => {
      toast.error('Failed to perform action', { description: error.message });
    },
  });

  const updateFilters = useCallback((newFilters: Partial<NoteFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      searchQuery: '',
      category: '',
      tags: [],
      filterMode: 'all',
      sortBy: 'updatedAt',
      sortOrder: 'desc',
      currentPage: 1,
    });
  }, []);

  const toggleNoteSelection = useCallback((noteId: string) => {
    setSelectedNotes(prev =>
      prev.includes(noteId) ? prev.filter(id => id !== noteId) : [...prev, noteId]
    );
  }, []);

  const selectAllNotes = useCallback(() => {
    const notes = notesData?.notes || [];
    if (selectedNotes.length === notes.length) {
      setSelectedNotes([]);
    } else {
      setSelectedNotes(notes.map((note: Note) => note._id));
    }
  }, [notesData?.notes, selectedNotes.length]);

  const clearSelection = useCallback(() => {
    setSelectedNotes([]);
    setIsSelectionMode(false);
  }, []);

  const handleBulkAction = useCallback(
    (action: string, data?: any) => {
      if (selectedNotes.length === 0) {
        toast.error('Please select notes first');
        return;
      }
      bulkActionMutation.mutate({ action, noteIds: selectedNotes, data });
    },
    [selectedNotes, bulkActionMutation]
  );

  const quickAction = useCallback(
    (noteId: string, field: keyof Note) => {
      const note = notesData?.notes?.find((n: Note) => n._id === noteId);
      if (!note) return;

      const updateData = { [field]: !note[field] };
      updateNoteMutation.mutate({ noteId, ...updateData });
    },
    [notesData?.notes, updateNoteMutation]
  );

  return {
    // Data
    notes: notesData?.notes || [],
    pagination: notesData?.pagination || {},
    availableFilters: notesData?.filters || { categories: [], tags: [] },

    // State
    filters,
    selectedNotes,
    isSelectionMode,

    // Loading states
    isLoading,
    isCreating: createNoteMutation.isPending,
    isUpdating: updateNoteMutation.isPending,
    isDeleting: deleteNoteMutation.isPending,
    isBulkActionLoading: bulkActionMutation.isPending,

    // Actions
    createNote: createNoteMutation.mutate,
    updateNote: updateNoteMutation.mutate,
    deleteNote: deleteNoteMutation.mutate,
    bulkAction: handleBulkAction,
    quickAction,

    // Filters and selection
    updateFilters,
    resetFilters,
    toggleNoteSelection,
    selectAllNotes,
    clearSelection,
    setIsSelectionMode,

    // Utilities
    refetch,
    getNoteQueryKey,
    error,
  };
};
