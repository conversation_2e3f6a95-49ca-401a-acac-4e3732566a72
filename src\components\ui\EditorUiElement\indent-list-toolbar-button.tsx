'use client';

import * as React from 'react';

import { ListStyleType, toggleIndentList } from '@udecode/plate-indent-list';
import { useEditorRef } from '@udecode/plate/react';
import { List, ListOrdered, ChevronDownIcon } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

const numberedListItems: Record<
  string,
  {
    icon: React.ReactNode;
    label: string;
    description: string;
    color: string;
    listStyleType: ListStyleType;
  }
> = {
  decimal: {
    icon: <ListOrdered className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Decimal',
    description: 'Numbered list (1, 2, 3)',
    color: 'text-blue-600 dark:text-blue-400',
    listStyleType: ListStyleType.Decimal,
  },
  lowerAlpha: {
    icon: <ListOrdered className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Lower Alpha',
    description: 'Alphabetical list (a, b, c)',
    color: 'text-green-600 dark:text-green-400',
    listStyleType: ListStyleType.LowerAlpha,
  },
  upperAlpha: {
    icon: <ListOrdered className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Upper Alpha',
    description: 'Alphabetical list (A, B, C)',
    color: 'text-purple-600 dark:text-purple-400',
    listStyleType: ListStyleType.UpperAlpha,
  },
  lowerRoman: {
    icon: <ListOrdered className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Lower Roman',
    description: 'Roman numerals (i, ii, iii)',
    color: 'text-orange-600 dark:text-orange-400',
    listStyleType: ListStyleType.LowerRoman,
  },
  upperRoman: {
    icon: <ListOrdered className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Upper Roman',
    description: 'Roman numerals (I, II, III)',
    color: 'text-red-600 dark:text-red-400',
    listStyleType: ListStyleType.UpperRoman,
  },
};

const bulletListItems: Record<
  string,
  {
    icon: React.ReactNode;
    label: string;
    description: string;
    color: string;
    listStyleType: ListStyleType;
  }
> = {
  disc: {
    icon: <List className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Disc',
    description: 'Default bullet points',
    color: 'text-blue-600 dark:text-blue-400',
    listStyleType: ListStyleType.Disc,
  },
  circle: {
    icon: <List className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Circle',
    description: 'Hollow circle bullets',
    color: 'text-green-600 dark:text-green-400',
    listStyleType: ListStyleType.Circle,
  },
  square: {
    icon: <List className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Square',
    description: 'Square bullet points',
    color: 'text-purple-600 dark:text-purple-400',
    listStyleType: ListStyleType.Square,
  },
};

export function NumberedIndentListToolbarButton() {
  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Numbered list"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', numberedListItems.decimal.color)}>
              {numberedListItems.decimal.icon}
            </div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        {Object.entries(numberedListItems).map(([key, item]) => (
          <DropdownMenuItem
            key={key}
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg'
            )}
            onSelect={() => {
              toggleIndentList(editor, {
                listStyleType: item.listStyleType,
              });
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', item.color)}>{item.icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">{item.label}</span>
              </div>
              <p className="text-xs theme-text-secondary">{item.description}</p>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function BulletedIndentListToolbarButton() {
  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Bulleted list"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', bulletListItems.disc.color)}>
              {bulletListItems.disc.icon}
            </div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        {Object.entries(bulletListItems).map(([key, item]) => (
          <DropdownMenuItem
            key={key}
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg'
            )}
            onSelect={() => {
              toggleIndentList(editor, {
                listStyleType: item.listStyleType,
              });
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', item.color)}>{item.icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">{item.label}</span>
              </div>
              <p className="text-xs theme-text-secondary">{item.description}</p>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
