'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface GitHubSkeletonProps {
  className?: string;
  count?: number;
}

// Repository Card Skeleton
export const RepositoryCardSkeleton: React.FC<GitHubSkeletonProps> = ({ className, count = 6 }) => {
  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05, duration: 0.3 }}
        >
          <Card className="theme-surface-elevated h-full flex flex-col">
            <CardHeader className="pb-3 border-b border-border/20">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3 flex-1">
                  <Skeleton className="h-8 w-8 rounded-lg loading-skeleton" />
                  <div className="min-w-0 flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4 loading-skeleton" />
                    <Skeleton className="h-3 w-1/2 loading-skeleton" />
                  </div>
                </div>
                <Skeleton className="h-6 w-20 rounded-full loading-skeleton" />
              </div>
            </CardHeader>
            <CardContent className="pt-3 flex-grow">
              <div className="space-y-3">
                <Skeleton className="h-3 w-full loading-skeleton" />
                <Skeleton className="h-3 w-4/5 loading-skeleton" />
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Skeleton className="h-2 w-2 rounded-full loading-skeleton" />
                      <Skeleton className="h-3 w-12 loading-skeleton" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <Skeleton className="h-3 w-3 loading-skeleton" />
                      <Skeleton className="h-3 w-6 loading-skeleton" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <Skeleton className="h-3 w-3 loading-skeleton" />
                      <Skeleton className="h-3 w-6 loading-skeleton" />
                    </div>
                  </div>
                  <Skeleton className="h-5 w-16 rounded loading-skeleton" />
                </div>
              </div>
            </CardContent>
            <div className="p-3 border-t border-border/20 flex items-center justify-between">
              <Skeleton className="h-3 w-24 loading-skeleton" />
              <Skeleton className="h-8 w-20 rounded loading-skeleton" />
            </div>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

// Connected Repository Card Skeleton
export const ConnectedRepositoryCardSkeleton: React.FC<GitHubSkeletonProps> = ({
  className,
  count = 4,
}) => {
  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05, duration: 0.3 }}
        >
          <Card className="theme-surface-elevated h-full flex flex-col">
            <CardHeader className="pb-3 border-b border-border/20">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3 flex-1">
                  <Skeleton className="h-8 w-8 rounded-lg loading-skeleton" />
                  <div className="min-w-0 flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4 loading-skeleton" />
                    <Skeleton className="h-3 w-1/2 loading-skeleton" />
                  </div>
                </div>
                <Skeleton className="h-6 w-24 rounded-full loading-skeleton" />
              </div>
            </CardHeader>
            <CardContent className="pt-3 flex-grow">
              <div className="space-y-3">
                <Skeleton className="h-3 w-full loading-skeleton" />
                <Skeleton className="h-3 w-4/5 loading-skeleton" />
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-24 loading-skeleton" />
                    <Skeleton className="h-5 w-16 rounded loading-skeleton" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-20 loading-skeleton" />
                    <Skeleton className="h-3 w-16 loading-skeleton" />
                  </div>
                </div>
              </div>
            </CardContent>
            <div className="p-3 border-t border-border/20 space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-3 w-24 loading-skeleton" />
                <Skeleton className="h-8 w-20 rounded loading-skeleton" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-8 flex-1 rounded loading-skeleton" />
                <Skeleton className="h-8 flex-1 rounded loading-skeleton" />
              </div>
            </div>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

// Issue Card Skeleton
export const IssueCardSkeleton: React.FC<GitHubSkeletonProps> = ({ className, count = 8 }) => {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.03, duration: 0.3 }}
        >
          <Card className="theme-surface-elevated hover-reveal">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Skeleton className="h-6 w-6 rounded loading-skeleton mt-1" />
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-5 w-4/5 loading-skeleton" />
                      <Skeleton className="h-4 w-full loading-skeleton" />
                      <Skeleton className="h-4 w-3/4 loading-skeleton" />
                    </div>
                    <Skeleton className="h-6 w-16 rounded-full loading-skeleton ml-4" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Skeleton className="h-4 w-4 rounded-full loading-skeleton" />
                        <Skeleton className="h-3 w-16 loading-skeleton" />
                      </div>
                      <Skeleton className="h-3 w-20 loading-skeleton" />
                    </div>
                    <div className="flex gap-2">
                      <Skeleton className="h-5 w-12 rounded loading-skeleton" />
                      <Skeleton className="h-5 w-16 rounded loading-skeleton" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

// Pull Request Card Skeleton
export const PullRequestCardSkeleton: React.FC<GitHubSkeletonProps> = ({
  className,
  count = 6,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.03, duration: 0.3 }}
        >
          <Card className="theme-surface-elevated hover-reveal">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Skeleton className="h-6 w-6 rounded loading-skeleton mt-1" />
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-5 w-4/5 loading-skeleton" />
                      <Skeleton className="h-4 w-full loading-skeleton" />
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-3 w-16 loading-skeleton" />
                        <Skeleton className="h-3 w-4 loading-skeleton" />
                        <Skeleton className="h-3 w-20 loading-skeleton" />
                      </div>
                    </div>
                    <Skeleton className="h-6 w-16 rounded-full loading-skeleton ml-4" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Skeleton className="h-4 w-4 rounded-full loading-skeleton" />
                        <Skeleton className="h-3 w-16 loading-skeleton" />
                      </div>
                      <Skeleton className="h-3 w-20 loading-skeleton" />
                    </div>
                    <div className="flex gap-2">
                      <Skeleton className="h-5 w-12 rounded loading-skeleton" />
                      <Skeleton className="h-5 w-16 rounded loading-skeleton" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

// GitHub Hub Header Skeleton
export const GitHubHubHeaderSkeleton: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={cn(
        'flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-3',
        className
      )}
    >
      <div className="flex items-center gap-3">
        <Skeleton className="h-10 w-10 rounded-lg loading-skeleton" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-48 loading-skeleton" />
          <Skeleton className="h-4 w-32 loading-skeleton" />
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-9 w-20 rounded loading-skeleton" />
        <Skeleton className="h-9 w-24 rounded loading-skeleton" />
      </div>
    </div>
  );
};

// GitHub Status Skeleton
export const GitHubStatusSkeleton: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Card className={cn('theme-surface-elevated', className)}>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full loading-skeleton" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-48 loading-skeleton" />
            <Skeleton className="h-4 w-32 loading-skeleton" />
          </div>
          <Skeleton className="h-6 w-20 rounded-full loading-skeleton" />
        </div>
      </CardContent>
    </Card>
  );
};
