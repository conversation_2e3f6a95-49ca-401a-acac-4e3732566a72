import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';

// Store active SSE connections
const connections = new Map<string, {
  controller: ReadableStreamDefaultController;
  userId: string;
  organizationId?: string;
  lastPing: number;
}>();

// Cleanup inactive connections every 30 seconds
setInterval(() => {
  const now = Date.now();
  const timeout = 60000; // 1 minute timeout
  
  for (const [id, connection] of connections.entries()) {
    if (now - connection.lastPing > timeout) {
      try {
        connection.controller.close();
      } catch (error) {
        // Connection already closed
      }
      connections.delete(id);
    }
  }
}, 30000);

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  const organizationId = (session.user as any).organizationId;
  
  // Create a unique connection ID
  const connectionId = `${userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const stream = new ReadableStream({
    start(controller) {
      // Store the connection
      connections.set(connectionId, {
        controller,
        userId,
        organizationId,
        lastPing: Date.now()
      });

      // Send initial connection message
      const data = JSON.stringify({
        type: 'connected',
        message: 'Successfully connected to SSE',
        userId,
        timestamp: new Date().toISOString()
      });
      
      controller.enqueue(`data: ${data}\n\n`);

      // Send periodic ping to keep connection alive
      const pingInterval = setInterval(() => {
        try {
          const connection = connections.get(connectionId);
          if (connection) {
            connection.lastPing = Date.now();
            const pingData = JSON.stringify({
              type: 'ping',
              timestamp: new Date().toISOString()
            });
            controller.enqueue(`data: ${pingData}\n\n`);
          } else {
            clearInterval(pingInterval);
          }
        } catch (error) {
          clearInterval(pingInterval);
          connections.delete(connectionId);
        }
      }, 30000); // Ping every 30 seconds

      // Clean up on connection close
      request.signal.addEventListener('abort', () => {
        clearInterval(pingInterval);
        connections.delete(connectionId);
        try {
          controller.close();
        } catch (error) {
          // Connection already closed
        }
      });
    },
    cancel() {
      connections.delete(connectionId);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

// Function to broadcast notification to a specific user
export function broadcastToUser(userId: string, data: any) {
  const message = JSON.stringify({
    type: 'notification',
    ...data,
    timestamp: new Date().toISOString()
  });

  let sentCount = 0;
  for (const [connectionId, connection] of connections.entries()) {
    if (connection.userId === userId) {
      try {
        connection.controller.enqueue(`data: ${message}\n\n`);
        sentCount++;
      } catch (error) {
        // Connection is closed, remove it
        connections.delete(connectionId);
      }
    }
  }
  
  return sentCount > 0;
}

// Function to broadcast notification to an organization
export function broadcastToOrganization(organizationId: string, data: any) {
  const message = JSON.stringify({
    type: 'notification',
    ...data,
    timestamp: new Date().toISOString()
  });

  let sentCount = 0;
  for (const [connectionId, connection] of connections.entries()) {
    if (connection.organizationId === organizationId) {
      try {
        connection.controller.enqueue(`data: ${message}\n\n`);
        sentCount++;
      } catch (error) {
        // Connection is closed, remove it
        connections.delete(connectionId);
      }
    }
  }
  
  return sentCount > 0;
}

// Function to get active connections count
export function getActiveConnectionsCount(): number {
  return connections.size;
}

// Function to get user connection status
export function isUserConnected(userId: string): boolean {
  for (const connection of connections.values()) {
    if (connection.userId === userId) {
      return true;
    }
  }
  return false;
}

// Function to broadcast to all connected users
export function broadcastToAll(data: any) {
  const message = JSON.stringify({
    type: 'broadcast',
    ...data,
    timestamp: new Date().toISOString()
  });

  let sentCount = 0;
  for (const [connectionId, connection] of connections.entries()) {
    try {
      connection.controller.enqueue(`data: ${message}\n\n`);
      sentCount++;
    } catch (error) {
      // Connection is closed, remove it
      connections.delete(connectionId);
    }
  }
  
  return sentCount;
}
