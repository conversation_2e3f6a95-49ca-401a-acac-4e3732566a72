import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { CheckCircle2, Circle, AlertCircle, Clock } from 'lucide-react';
import { motion } from 'framer-motion';

const statusCount = 4;
const tasksPerStatus = 3;

const TaskListSkeleton: React.FC = () => {
  const statusIcons = [
    <Circle key="todo" className="h-4 w-4 theme-text-secondary" />,
    <Clock key="in-progress" className="h-4 w-4 text-blue-500" />,
    <AlertCircle key="pending" className="h-4 w-4 text-yellow-500" />,
    <CheckCircle2 key="completed" className="h-4 w-4 text-green-500" />,
  ];

  return (
    <div className="space-y-6">
      {/* Task Tables Grouped by Status */}
      {Array.from({ length: statusCount }).map((_, statusIndex) => (
        <motion.div
          key={statusIndex}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: statusIndex * 0.1, duration: 0.3 }}
        >
          <Card className="theme-border theme-shadow-sm">
            <CardHeader className="py-3 px-4 theme-surface-elevated sticky top-0 z-10">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                {statusIcons[statusIndex]}
                <Skeleton className="h-5 w-20 loading-skeleton" />
                <Badge variant="secondary" className="ml-1">
                  <Skeleton className="h-4 w-4 loading-skeleton" />
                </Badge>
              </CardTitle>
            </CardHeader>
            <ScrollArea className="max-h-[500px]">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="sticky top-0 z-10 theme-surface-elevated">
                    <TableRow className="theme-surface-elevated hover-reveal">
                      <TableHead className="w-8"></TableHead>
                      <TableHead className="min-w-[200px]">
                        <Skeleton className="h-4 w-12 loading-skeleton" />
                      </TableHead>
                      <TableHead className="hidden md:table-cell min-w-[120px]">
                        <Skeleton className="h-4 w-16 loading-skeleton" />
                      </TableHead>
                      <TableHead className="hidden sm:table-cell min-w-[100px]">
                        <Skeleton className="h-4 w-16 loading-skeleton" />
                      </TableHead>
                      <TableHead className="hidden lg:table-cell min-w-[120px]">
                        <Skeleton className="h-4 w-20 loading-skeleton" />
                      </TableHead>
                      <TableHead className="hidden xl:table-cell min-w-[100px]">
                        <Skeleton className="h-4 w-16 loading-skeleton" />
                      </TableHead>
                      <TableHead className="hidden sm:table-cell min-w-[80px]">
                        <Skeleton className="h-4 w-16 loading-skeleton" />
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: tasksPerStatus }).map((_, taskIndex) => (
                      <TableRow
                        key={taskIndex}
                        className="group hover-reveal theme-transition cursor-pointer"
                      >
                        <TableCell className="w-8">
                          <Skeleton className="h-4 w-4 rounded-full loading-skeleton" />
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Skeleton className="h-4 w-32 loading-skeleton" />
                              <Skeleton className="h-5 w-16 rounded-full loading-skeleton" />
                            </div>
                            <Skeleton className="h-3 w-48 loading-skeleton" />
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-6 w-6 rounded-full loading-skeleton" />
                            <Skeleton className="h-4 w-20 loading-skeleton" />
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <div className="flex items-center gap-1 text-xs theme-text-secondary">
                            <Skeleton className="h-3 w-3 loading-skeleton" />
                            <Skeleton className="h-3 w-16 loading-skeleton" />
                          </div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="flex items-center gap-1 text-xs theme-text-secondary">
                            <Skeleton className="h-3 w-3 loading-skeleton" />
                            <Skeleton className="h-3 w-20 loading-skeleton" />
                          </div>
                        </TableCell>
                        <TableCell className="hidden xl:table-cell">
                          <div className="text-center">
                            <Skeleton className="h-3 w-8 mx-auto loading-skeleton" />
                            <span className="text-xs theme-text-secondary">
                              <Skeleton className="h-3 w-8 mx-auto mt-1 loading-skeleton" />
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-2 w-12 rounded-full loading-skeleton" />
                            <Skeleton className="h-3 w-8 loading-skeleton" />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </ScrollArea>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

export default TaskListSkeleton;
