'use client';

import React, { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Edit,
  Hash,
  Pin,
  Star,
  Archive,
  Trash2,
  MoreHorizontal,
  Clock,
  Eye,
  FileText,
  Calendar,
  ArchiveRestore,
  StarOff,
  Copy,
  Loader2,
  Share2,
} from 'lucide-react';
import { NoteService } from '@/services/Note.service';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import MarkdownRenderer from '@/components/Notes/MarkdownRenderer';
import { motion } from 'framer-motion';
import NoteViewSkeleton from '@/components/Notes/NoteViewSkeleton';
import NoteExportModal from '@/components/Notes/NoteExportModal';
import NoteShareModal from '@/components/Notes/NoteShareModal';
import { FcExport } from 'react-icons/fc';

export default function NoteViewPage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  const noteId = params.id as string;

  const { data: note, isLoading: isLoadingNote } = useQuery({
    queryKey: ['note', noteId],
    queryFn: async () => {
      const data = await NoteService.getNoteById(noteId);
      return data.note;
    },
    enabled: !!noteId,
  });

  const deleteNoteMutation = useMutation({
    mutationFn: () => NoteService.deleteNote(noteId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note deleted successfully');
      router.push('/notes');
    },
    onError: (error: Error) => {
      toast.error('Failed to delete note', { description: error.message });
    },
  });

  const pinNoteMutation = useMutation({
    mutationFn: () => NoteService.togglePin(noteId),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['note', noteId] });
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error('Failed to update pin status', { description: error.message });
    },
  });

  const favoriteNoteMutation = useMutation({
    mutationFn: () => NoteService.toggleFavorite(noteId),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['note', noteId] });
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error('Failed to update favorite status', { description: error.message });
    },
  });

  const archiveNoteMutation = useMutation({
    mutationFn: () => NoteService.toggleArchive(noteId),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['note', noteId] });
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error('Failed to update archive status', { description: error.message });
    },
  });

  const handleQuickAction = (action: 'pin' | 'favorite' | 'archive') => {
    if (!note) return;

    switch (action) {
      case 'pin':
        pinNoteMutation.mutate();
        break;
      case 'favorite':
        favoriteNoteMutation.mutate();
        break;
      case 'archive':
        archiveNoteMutation.mutate();
        break;
    }
  };

  const handleEdit = () => {
    router.push(`/notes/editor?id=${noteId}`);
  };

  const duplicateNoteMutation = useMutation({
    mutationFn: () => NoteService.duplicateNote(noteId),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note duplicated successfully');
      router.push(`/notes/view/${data.note._id}`);
    },
    onError: (error: Error) => {
      toast.error('Failed to duplicate note', { description: error.message });
    },
  });

  const handleDuplicate = () => {
    duplicateNoteMutation.mutate();
  };

  if (isLoadingNote) {
    return <NoteViewSkeleton />;
  }

  if (!note) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Note not found</h2>
          <p className="text-muted-foreground mb-4">
            The note you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push('/notes')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Notes
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className="theme-surface h-full w-full flex flex-col rounded-md"
    >
      {/* Modern Header */}
      <div className="theme-divider theme-surface-elevated backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/notes')}
              className="theme-button-ghost rounded-lg px-3 py-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2 text-primary" />
              <span className="font-medium">Back to Notes</span>
            </Button>

            <Separator orientation="vertical" className="h-6 theme-border" />

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <h1 className="text-xl font-bold theme-text-primary tracking-tight">
                  {note.title}
                </h1>
                <div className="flex items-center gap-1.5">
                  {note.isPinned && (
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10">
                      <Pin className="h-3.5 w-3.5 text-primary" />
                    </div>
                  )}
                  {note.isFavorite && (
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-warning/20">
                      <Star className="h-3.5 w-3.5 text-warning fill-current" />
                    </div>
                  )}
                  {note.isArchived && (
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-muted">
                      <Archive className="h-3.5 w-3.5 theme-text-secondary" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="theme-button-primary rounded-lg px-4 py-2"
            >
              <Edit className="h-4 w-4 mr-2" />
              <span className="font-medium">Edit</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="theme-button-ghost rounded-xl px-3 py-2 hover:scale-105 transition-all duration-200"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-64 theme-surface-elevated theme-border rounded-2xl theme-shadow-lg backdrop-blur-sm border-0 p-2"
              >
                <div className="space-y-1">
                  <DropdownMenuItem
                    onClick={() => handleQuickAction('pin')}
                    className="interactive-hover rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                        <Pin className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-sm">
                          {note.isPinned ? 'Unpin Note' : 'Pin Note'}
                        </span>
                        <span className="text-xs theme-text-secondary opacity-70">
                          {note.isPinned ? 'Remove from pinned' : 'Keep at top'}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => handleQuickAction('favorite')}
                    className="interactive-hover rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-warning/10 group-hover:bg-warning/20 transition-colors">
                        {note.isFavorite ? (
                          <StarOff className="h-4 w-4 text-warning" />
                        ) : (
                          <Star className="h-4 w-4 text-warning" />
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-sm">
                          {note.isFavorite ? 'Remove from Favorites' : 'Add to Favorites'}
                        </span>
                        <span className="text-xs theme-text-secondary opacity-70">
                          {note.isFavorite ? 'Remove star rating' : 'Mark as favorite'}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => handleQuickAction('archive')}
                    className="interactive-hover rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-success/10 group-hover:bg-success/20 transition-colors">
                        {note.isArchived ? (
                          <ArchiveRestore className="h-4 w-4 text-success" />
                        ) : (
                          <Archive className="h-4 w-4 theme-text-secondary" />
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-sm">
                          {note.isArchived ? 'Unarchive Note' : 'Archive Note'}
                        </span>
                        <span className="text-xs theme-text-secondary opacity-70">
                          {note.isArchived ? 'Restore to active' : 'Move to archive'}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                </div>

                <DropdownMenuSeparator className="theme-divider mx-0 my-3 opacity-50" />

                <div className="space-y-1">
                  <DropdownMenuItem asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsShareModalOpen(true)}
                      className="theme-button-ghost rounded-xl px-4 py-3 w-full justify-start h-auto cursor-pointer transition-all duration-200 hover:scale-[0.98] group"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-1.5 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <Share2 className="h-4 w-4 text-primary" />
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="font-semibold text-sm">Share</span>
                          <span className="text-xs theme-text-secondary opacity-70">
                            Send to others
                          </span>
                        </div>
                      </div>
                    </Button>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={handleDuplicate}
                    disabled={duplicateNoteMutation.isPending}
                    className="interactive-hover rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                        {duplicateNoteMutation.isPending ? (
                          <Loader2 className="h-4 w-4 text-primary animate-spin" />
                        ) : (
                          <Copy className="h-4 w-4 text-primary" />
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-sm">
                          {duplicateNoteMutation.isPending ? 'Duplicating...' : 'Duplicate Note'}
                        </span>
                        <span className="text-xs theme-text-secondary opacity-70">
                          Create a copy
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                </div>

                <DropdownMenuSeparator className="theme-divider mx-0 my-3 opacity-50" />

                <DropdownMenuItem
                  onClick={() => deleteNoteMutation.mutate()}
                  className="theme-transition hover:bg-destructive/10 text-destructive rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 rounded-lg bg-destructive/10 group-hover:bg-destructive/20 transition-colors">
                      <Trash2 className="h-4 w-4" />
                    </div>
                    <div className="flex flex-col">
                      <span className="font-semibold text-sm">Delete Note</span>
                      <span className="text-xs opacity-70">Permanently remove</span>
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setIsExportModalOpen(true)}
                  className="interactive-hover rounded-xl mx-0 my-0 px-4 py-3 cursor-pointer transition-all duration-200 hover:scale-[0.98] group disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 rounded-lg bg-success/10 group-hover:bg-success/20 transition-colors">
                      <FcExport className="h-4 w-4" />
                    </div>
                    <div className="flex flex-col">
                      <span className="font-semibold text-sm">Export</span>
                      <span className="text-xs opacity-70">Export note</span>
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden h-full">
        <div className="flex-1 flex flex-col p-6">
          <MarkdownRenderer
            content={note.content}
            settings={note.settings}
            className="flex-1 overflow-y-auto theme-scrollbar h-full"
          />
        </div>

        <div className="w-80 h-full overflow-y-auto theme-scrollbar theme-divider theme-surface-elevated">
          {/* Category & Tags */}
          <Card className="m-4 dashboard-card">
            <CardHeader className="pb-3">
              <CardTitle className="dashboard-card-title text-sm flex items-center gap-2">
                <Hash className="h-4 w-4 text-primary" />
                Category & Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="form-label text-xs">Category</label>
                <div className="mt-2 px-3 py-2.5 theme-surface rounded-lg text-sm theme-text-primary font-medium">
                  {note?.category?.charAt(0).toUpperCase() + note?.category?.slice(1)}
                </div>
              </div>
              {note.tags && note.tags.length > 0 && (
                <div>
                  <label className="form-label text-xs">Tags</label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {note.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary" className="theme-badge-primary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Note Information */}
          <Card className="m-4 dashboard-card">
            <CardHeader className="pb-3">
              <CardTitle className="dashboard-card-title text-sm flex items-center gap-2">
                <FileText className="h-4 w-4 text-success" />
                Note Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-3.5 w-3.5 theme-text-secondary" />
                <span className="theme-text-secondary text-xs">Created:</span>
                <span className="theme-text-primary text-xs font-medium">
                  {new Date(note.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-3.5 w-3.5 theme-text-secondary" />
                <span className="theme-text-secondary text-xs">Updated:</span>
                <span className="theme-text-primary text-xs font-medium">
                  {new Date(note.updatedAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Hash className="h-3.5 w-3.5 theme-text-secondary" />
                <span className="theme-text-secondary text-xs">Version:</span>
                <span className="theme-text-primary text-xs font-medium">{note.version}</span>
              </div>
              {note.metadata && (
                <>
                  <div className="flex items-center gap-2 text-sm">
                    <FileText className="h-3.5 w-3.5 theme-text-secondary" />
                    <span className="theme-text-secondary text-xs">Word Count:</span>
                    <span className="theme-text-primary text-xs font-medium">
                      {note.metadata.wordCount}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-3.5 w-3.5 theme-text-secondary" />
                    <span className="theme-text-secondary text-xs">Reading Time:</span>
                    <span className="theme-text-primary text-xs font-medium">
                      {note.metadata.readingTime} min
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Eye className="h-3.5 w-3.5 theme-text-secondary" />
                    <span className="theme-text-secondary text-xs">Views:</span>
                    <span className="theme-text-primary text-xs font-medium">
                      {note.metadata.viewCount}
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <NoteShareModal
        noteId={noteId}
        noteTitle={note.title}
        isOpen={isShareModalOpen}
        setIsOpen={setIsShareModalOpen}
      />
      <NoteExportModal
        isOpen={isExportModalOpen}
        setIsOpen={setIsExportModalOpen}
        note={{
          _id: note._id,
          title: note.title,
          content: note.content,
          category: note.category,
          tags: note.tags,
          createdAt: note.createdAt,
          updatedAt: note.updatedAt,
        }}
      />
    </motion.div>
  );
}
