'use client';

import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Settings,
  Zap,
  <PERSON>apOff,
  GitPullRequest,
  Bug,
  Save,
  X,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import Modal from '../Global/Modal';
import { ConnectedRepository } from '@/types/GitHubTypes';
import { GitHubService } from '@/services/GitHub.service';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

interface RepositorySettingsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  repository: ConnectedRepository;
}

const validationSchema = Yup.object({
  autoCreateTasks: Yup.boolean(),
  syncIssues: Yup.boolean(),
  syncPullRequests: Yup.boolean(),
});

export const RepositorySettingsModal: React.FC<RepositorySettingsModalProps> = ({
  isOpen,
  onOpenChange,
  repository,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const queryClient = useQueryClient();

  const formik = useFormik({
    initialValues: {
      autoCreateTasks: repository.syncSettings?.autoCreateTasks || false,
      syncIssues: repository.syncSettings?.syncIssues || false,
      syncPullRequests: repository.syncSettings?.syncPullRequests || false,
    },
    validationSchema,
    onSubmit: async values => {
      setIsLoading(true);
      setError(null);
      setSuccess(false);

      try {
        await GitHubService.updateRepositorySettings(repository.repositoryId, {
          autoCreateTasks: values.autoCreateTasks,
          syncIssues: values.syncIssues,
          syncPullRequests: values.syncPullRequests,
        });

        await queryClient.invalidateQueries({ queryKey: ['github', 'connected-repositories'] });

        setSuccess(true);
        toast.success('Repository settings updated successfully');
        setTimeout(() => {
          onOpenChange(false);
          setSuccess(false);
        }, 1500);
      } catch (err: any) {
        const errorMessage = err.message || 'Failed to update repository settings';
        setError(errorMessage);
        toast.error('Failed to update settings', {
          description: errorMessage,
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  const handleClose = () => {
    if (!isLoading) {
      setError(null);
      setSuccess(false);
      formik.resetForm();
      onOpenChange(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg" className="theme-surface-elevated">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="bg-blue-500/10 p-2 rounded-lg">
            <Settings className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold theme-text-primary">Repository Settings</h2>
            <p className="text-sm theme-text-secondary truncate">
              Configure sync settings for {repository.name}
            </p>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert className="mb-4 border-destructive/20 bg-destructive/5">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <AlertDescription className="text-destructive">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 border-success/20 bg-success/5">
            <CheckCircle className="h-4 w-4 text-success" />
            <AlertDescription className="text-success">
              Settings updated successfully!
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Sync Settings */}
          <div className="space-y-4">
            <div>
              <h3 className="text-base font-medium theme-text-primary mb-3">Sync Settings</h3>
              <p className="text-sm theme-text-secondary mb-4">
                Configure what data should be synchronized from this repository.
              </p>
            </div>

            {/* Auto Create Tasks */}
            <div className="flex items-center justify-between p-4 theme-surface rounded-lg theme-transition">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                {formik.values.autoCreateTasks ? (
                  <Zap className="h-5 w-5 text-success flex-shrink-0" />
                ) : (
                  <ZapOff className="h-5 w-5 theme-text-secondary flex-shrink-0" />
                )}
                <div className="flex-1 min-w-0">
                  <Label className="text-sm font-medium theme-text-primary">
                    Auto-create tasks from issues
                  </Label>
                  <p className="text-xs theme-text-secondary mt-1">
                    Automatically create tasks when new issues are opened
                  </p>
                </div>
              </div>
              <Switch
                checked={formik.values.autoCreateTasks}
                onCheckedChange={checked => formik.setFieldValue('autoCreateTasks', checked)}
                disabled={isLoading}
              />
            </div>

            {/* Sync Issues */}
            <div className="flex items-center justify-between p-4 theme-surface rounded-lg theme-transition">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <Bug className="h-5 w-5 text-orange-600 dark:text-orange-400 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <Label className="text-sm font-medium theme-text-primary">Sync issues</Label>
                  <p className="text-xs theme-text-secondary mt-1">
                    Keep issues synchronized with the repository
                  </p>
                </div>
              </div>
              <Switch
                checked={formik.values.syncIssues}
                onCheckedChange={checked => formik.setFieldValue('syncIssues', checked)}
                disabled={isLoading}
              />
            </div>

            {/* Sync Pull Requests */}
            <div className="flex items-center justify-between p-4 theme-surface rounded-lg theme-transition">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <GitPullRequest className="h-5 w-5 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <Label className="text-sm font-medium theme-text-primary">
                    Sync pull requests
                  </Label>
                  <p className="text-xs theme-text-secondary mt-1">
                    Keep pull requests synchronized with the repository
                  </p>
                </div>
              </div>
              <Switch
                checked={formik.values.syncPullRequests}
                onCheckedChange={checked => formik.setFieldValue('syncPullRequests', checked)}
                disabled={isLoading}
              />
            </div>
          </div>

          <Separator className="theme-divider" />

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || success}
              className="px-4 font-medium theme-button-primary"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : success ? (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Saved!
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
