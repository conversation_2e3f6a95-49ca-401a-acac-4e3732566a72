import React, { memo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { format as formatDate } from 'date-fns';
import { UserAvatar } from '@/components/ui/UserAvatar';
import { ActivityLog } from '@/services/ActivityLogApi.service';

interface ActivityLogSidebarProps {
  log: ActivityLog | null;
}


const ACTION_COLORS = {
  create: 'bg-green-100 text-green-800 border-green-200',
  update: 'bg-blue-100 text-blue-800 border-blue-200',
  delete: 'bg-red-100 text-red-800 border-red-200',
  login: 'bg-purple-100 text-purple-800 border-purple-200',
  logout: 'bg-gray-100 text-gray-800 border-gray-200',
  view: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  export: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  import: 'bg-teal-100 text-teal-800 border-teal-200',
  share: 'bg-pink-100 text-pink-800 border-pink-200',
  archive: 'bg-orange-100 text-orange-800 border-orange-200',
};

const RESOURCE_TYPE_COLORS = {
  task: 'bg-blue-50 text-blue-700',
  project: 'bg-green-50 text-green-700',
  note: 'bg-purple-50 text-purple-700',
  user: 'bg-orange-50 text-orange-700',
  organization: 'bg-red-50 text-red-700',
  integration: 'bg-cyan-50 text-cyan-700',
  report: 'bg-indigo-50 text-indigo-700',
  timeline: 'bg-pink-50 text-pink-700',
  notification: 'bg-yellow-50 text-yellow-700',
  file: 'bg-gray-50 text-gray-700',
  comment: 'bg-teal-50 text-teal-700',
  budget: 'bg-violet-50 text-violet-700',
};

const ActivityLogSidebar: React.FC<ActivityLogSidebarProps> = memo(({ log }) => {
  if (!log) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        Select an activity log to view details
      </div>
    );
  }

  const timestamp = new Date(log.timestamp);
  const isValidDate = !isNaN(timestamp.getTime());

  const actionColorClass =
    ACTION_COLORS[log.action as keyof typeof ACTION_COLORS] ||
    'bg-gray-100 text-gray-800 border-gray-200';

  const resourceColorClass =
    RESOURCE_TYPE_COLORS[log.resourceType as keyof typeof RESOURCE_TYPE_COLORS] ||
    'bg-gray-50 text-gray-700';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Timestamp</Label>
          <p className="text-sm text-muted-foreground mt-1">
            {isValidDate ? formatDate(timestamp, 'PPpp') : 'Invalid date'}
          </p>
        </div>

        <div>
          <Label className="text-sm font-medium">User</Label>
          <div className="flex items-center gap-3 mt-2">
            <UserAvatar
              src={log.userImage}
              alt={log.userName || 'User'}
              size="md"
              fallbackText={log.userName || log.userEmail || '?'}
            />
            <div>
              <p className="text-sm font-medium">{log.userName || 'Unknown User'}</p>
              {log.userEmail && <p className="text-xs text-muted-foreground">{log.userEmail}</p>}
            </div>
          </div>
        </div>
      </div>

      {/* Action & Resource */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label className="text-sm font-medium">Action</Label>
          <div className="mt-2">
            <Badge variant="outline" className={`${actionColorClass} flex items-center gap-1 w-fit`}>
              {log.action}
            </Badge>
          </div>
        </div>

        <div>
          <Label className="text-sm font-medium">Resource</Label>
          <div className="mt-2">
            <Badge variant="secondary" className={resourceColorClass}>
              {log.resourceType}
            </Badge>
            {log.resourceName && (
              <p className="text-xs text-muted-foreground mt-1">{log.resourceName}</p>
            )}
          </div>
        </div>
      </div>

      {/* Description */}
      <div>
        <Label className="text-sm font-medium">Description</Label>
        <p className="text-sm text-muted-foreground mt-1">{log.description}</p>
      </div>

      {/* Technical Details */}
      <div className="space-y-4">
        {log.ipAddress && (
          <div>
            <Label className="text-sm font-medium">IP Address</Label>
            <p className="text-sm text-muted-foreground mt-1">{log.ipAddress}</p>
          </div>
        )}

        {log.deviceInfo && (
          <div>
            <Label className="text-sm font-medium">Device Info</Label>
            <div className="text-sm text-muted-foreground mt-1 space-y-1">
              <p>Browser: {log.deviceInfo.browser}</p>
              <p>OS: {log.deviceInfo.os}</p>
              <p>
                Device:{' '}
                {log.deviceInfo.isMobile
                  ? 'Mobile'
                  : log.deviceInfo.isTablet
                    ? 'Tablet'
                    : 'Desktop'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

ActivityLogSidebar.displayName = 'ActivityLogSidebar';

export default ActivityLogSidebar;
