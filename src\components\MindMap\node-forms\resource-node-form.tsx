'use client';

import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, ExternalLink } from 'lucide-react';

interface ResourceNodeData {
  title: string;
  type: 'document' | 'image' | 'video' | 'link';
  url: string;
}

interface ResourceNodeFormProps {
  data: ResourceNodeData;
  onSave: (data: Partial<ResourceNodeData>) => void;
  onCancel: () => void;
}

export function ResourceNodeForm({ data, onSave, onCancel }: ResourceNodeFormProps) {
  const [formData, setFormData] = useState<ResourceNodeData>({
    title: data.title || 'New Resource',
    type: data.type || 'link',
    url: data.url || '',
  });

  const handleChange = (field: keyof ResourceNodeData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleTestUrl = () => {
    if (formData.url) {
      window.open(formData.url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className="min-w-[400px]">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Resource Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => handleChange('title', e.target.value)}
            className="mt-1"
            placeholder="Enter resource title"
          />
        </div>

        <div>
          <Label htmlFor="type">Resource Type</Label>
          <Select
            value={formData.type}
            onValueChange={value => handleChange('type', value as ResourceNodeData['type'])}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select resource type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="link">Link</SelectItem>
              <SelectItem value="document">Document</SelectItem>
              <SelectItem value="image">Image</SelectItem>
              <SelectItem value="video">Video</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="url">URL</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="url"
              value={formData.url}
              onChange={e => handleChange('url', e.target.value)}
              placeholder="https://example.com"
              className="flex-1"
            />
            {formData.url && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleTestUrl}
                className="px-3"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
