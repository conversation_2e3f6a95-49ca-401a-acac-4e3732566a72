import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { events } = await request.json();

    // Log PWA analytics events
    console.log('PWA Analytics Events:', {
      count: events?.length || 0,
      events,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      processed: events?.length || 0,
    });
  } catch (error) {
    console.error('PWA analytics error:', error);
    return NextResponse.json({ error: 'Failed to process analytics' }, { status: 500 });
  }
}
