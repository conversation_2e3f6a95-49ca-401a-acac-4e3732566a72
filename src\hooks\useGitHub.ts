import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GitHubService } from '@/services/GitHub.service';
import { toast } from 'sonner';

export interface GitHubFilters {
  searchQuery: string;
  state: 'open' | 'closed' | 'all';
  currentPage: number;
  repositoryId?: string;
}

export function useGitHub() {
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<GitHubFilters>({
    searchQuery: '',
    state: 'open',
    currentPage: 1,
  });
  const [repositoryPage, setRepositoryPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      if (searchQuery !== debouncedSearchQuery) {
        setRepositoryPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery, debouncedSearchQuery]);

  // GitHub connection status
  const {
    data: githubStatus,
    isLoading: isStatusLoading,
    error: statusError,
    refetch: refetchStatus,
  } = useQuery({
    queryKey: ['github-status'],
    queryFn: async () => {
      const response = await fetch('/api/github/status');
      if (!response.ok) {
        throw new Error('Failed to fetch GitHub status');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Available repositories
  const {
    data: availableRepositories,
    isLoading: isAvailableLoading,
    error: availableError,
    refetch: refetchAvailable,
  } = useQuery({
    queryKey: ['github-repositories-available', repositoryPage, debouncedSearchQuery],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: repositoryPage.toString(),
        per_page: '30',
      });

      if (debouncedSearchQuery.trim()) {
        params.append('search', debouncedSearchQuery.trim());
      }

      const response = await fetch(`/api/github/repositories/available?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch available repositories');
      }
      return response.json();
    },
    enabled: githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // Connected repositories
  const {
    data: connectedRepositories,
    isLoading: isConnectedLoading,
    error: connectedError,
    refetch: refetchConnected,
  } = useQuery({
    queryKey: ['github-repositories-connected'],
    queryFn: () => GitHubService.getConnectedRepositories(),
    enabled: githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // Repository issues (single repository)
  const {
    data: repositoryIssues,
    isLoading: isIssuesLoading,
    error: issuesError,
    refetch: refetchIssues,
  } = useQuery({
    queryKey: [
      'github-issues',
      filters.repositoryId,
      filters.currentPage,
      filters.state,
      filters.searchQuery,
    ],
    queryFn: () =>
      GitHubService.getRepositoryIssuesClient(
        filters.repositoryId!,
        filters.currentPage,
        filters.state,
        filters.searchQuery
      ),
    enabled: !!filters.repositoryId && githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // Repository pull requests (single repository)
  const {
    data: repositoryPullRequests,
    isLoading: isPullsLoading,
    error: pullsError,
    refetch: refetchPulls,
  } = useQuery({
    queryKey: [
      'github-pulls',
      filters.repositoryId,
      filters.currentPage,
      filters.state,
      filters.searchQuery,
    ],
    queryFn: () =>
      GitHubService.getRepositoryPullRequestsClient(
        filters.repositoryId!,
        filters.currentPage,
        filters.state
      ),
    enabled: !!filters.repositoryId && githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // All issues from connected repositories
  const {
    data: allIssues,
    isLoading: isAllIssuesLoading,
    error: allIssuesError,
    refetch: refetchAllIssues,
  } = useQuery({
    queryKey: ['github-all-issues', filters.currentPage, filters.state, filters.searchQuery],
    queryFn: () =>
      GitHubService.getAllIssues(filters.currentPage, filters.state, filters.searchQuery),
    enabled: githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // All pull requests from connected repositories
  const {
    data: allPullRequests,
    isLoading: isAllPullsLoading,
    error: allPullsError,
    refetch: refetchAllPulls,
  } = useQuery({
    queryKey: ['github-all-pulls', filters.currentPage, filters.state],
    queryFn: () => GitHubService.getAllPullRequests(filters.currentPage, filters.state),
    enabled: githubStatus?.connected,
    staleTime: 1000 * 60 * 2,
  });

  // Connect repository mutation
  const connectRepositoryMutation = useMutation({
    mutationFn: (repositoryId: string) => GitHubService.connectRepository(repositoryId),
    onSuccess: () => {
      toast.success('Repository connected successfully');
      queryClient.invalidateQueries({ queryKey: ['github-repositories-available'] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories-connected'] });
    },
    onError: (error: any) => {
      toast.error('Failed to connect repository', {
        description: error.message,
      });
    },
  });

  // Disconnect repository mutation
  const disconnectRepositoryMutation = useMutation({
    mutationFn: (repositoryId: string) => GitHubService.disconnectRepository(repositoryId),
    onSuccess: () => {
      toast.success('Repository disconnected successfully');
      queryClient.invalidateQueries({ queryKey: ['github-repositories-available'] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories-connected'] });
    },
    onError: (error: any) => {
      toast.error('Failed to disconnect repository', {
        description: error.message,
      });
    },
  });

  // Create task from issue mutation
  const createTaskFromIssueMutation = useMutation({
    mutationFn: ({ repositoryId, issueNumber }: { repositoryId: string; issueNumber: number }) =>
      GitHubService.createTaskFromIssue(repositoryId, issueNumber),
    onSuccess: data => {
      toast.success('Task created successfully', {
        description: `Created task "${data.task.title}" from GitHub issue`,
      });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      toast.error('Failed to create task', {
        description: error.message,
      });
    },
  });

  // Disconnect GitHub integration
  const disconnectGitHubMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/github/disconnect', {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to disconnect GitHub');
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success('GitHub disconnected successfully');
      queryClient.invalidateQueries({ queryKey: ['github-status'] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories-available'] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories-connected'] });
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
    },
    onError: (error: any) => {
      toast.error('Failed to disconnect GitHub', {
        description: error.message,
      });
    },
  });

  // Helper functions
  const connectRepository = (repositoryId: string) => {
    connectRepositoryMutation.mutate(repositoryId);
  };

  const disconnectRepository = (repositoryId: string) => {
    disconnectRepositoryMutation.mutate(repositoryId);
  };

  const createTaskFromIssue = (repositoryId: string, issueNumber: number) => {
    createTaskFromIssueMutation.mutate({ repositoryId, issueNumber });
  };

  const disconnectGitHub = () => {
    disconnectGitHubMutation.mutate();
  };

  const updateFilters = (newFilters: Partial<GitHubFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const refreshAll = () => {
    refetchStatus();
    refetchAvailable();
    refetchConnected();
    refetchAllIssues();
    refetchAllPulls();
    if (filters.repositoryId) {
      refetchIssues();
      refetchPulls();
    }
  };

  return {
    // Data
    githubStatus,
    availableRepositories,
    connectedRepositories,
    repositoryIssues,
    repositoryPullRequests,
    allIssues,
    allPullRequests,
    filters,
    repositoryPage,
    searchQuery,

    // Loading states
    isStatusLoading,
    isAvailableLoading,
    isConnectedLoading,
    isIssuesLoading,
    isPullsLoading,
    isAllIssuesLoading,
    isAllPullsLoading,
    isConnecting: connectRepositoryMutation.isPending,
    isDisconnecting: disconnectRepositoryMutation.isPending,
    isCreatingTask: createTaskFromIssueMutation.isPending,
    isDisconnectingGitHub: disconnectGitHubMutation.isPending,

    // Error states
    statusError,
    availableError,
    connectedError,
    issuesError,
    pullsError,
    allIssuesError,
    allPullsError,

    // Actions
    connectRepository,
    disconnectRepository,
    createTaskFromIssue,
    disconnectGitHub,
    updateFilters,
    refreshAll,
    setRepositoryPage,
    setSearchQuery,

    // Refetch functions
    refetchStatus,
    refetchAvailable,
    refetchConnected,
    refetchIssues,
    refetchPulls,
    refetchAllIssues,
    refetchAllPulls,
  };
}
