'use client';

import * as React from 'react';

import type { UseEmojiPickerType } from '@udecode/plate-emoji/react';

export type EmojiPickerSearchBarProps = {
  children: React.ReactNode;
} & Pick<UseEmojiPickerType, 'i18n' | 'searchValue' | 'setSearch'>;

export function EmojiPickerSearchBar({
  children,
  i18n,
  searchValue,
  setSearch,
}: EmojiPickerSearchBarProps) {
  return (
    <div className="flex items-center px-3 py-2">
      <div className="relative flex grow items-center">
        <input
          className="block w-full appearance-none rounded-full theme-input theme-focus theme-transition px-10 py-2.5 text-sm shadow-sm border-2 border-border/30 hover:border-border/60 focus:border-primary/50 bg-background/50 backdrop-blur-sm"
          value={searchValue}
          onChange={event => setSearch(event.target.value)}
          placeholder={i18n.search}
          aria-label="Search"
          autoComplete="off"
          type="text"
          autoFocus
        />
        {children}
      </div>
    </div>
  );
}
