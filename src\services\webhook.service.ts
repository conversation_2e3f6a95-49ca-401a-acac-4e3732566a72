import axios from 'axios';

export interface WebhookDetails {
  id: string;
  githubWebhookId: string;
  events: string[];
  active: boolean;
  deliveryCount: number;
  errorCount: number;
  lastDeliveryAt?: string;
  lastError?: {
    message: string;
    timestamp: string;
    statusCode?: number;
  };
  config: {
    url: string;
    contentType: string;
    insecureSsl: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface WebhookResponse {
  webhook: WebhookDetails | null;
}

export interface CreateWebhookResponse {
  success: boolean;
  webhook: {
    id: string;
    githubWebhookId: string;
    events: string[];
    active: boolean;
    deliveryCount: number;
    errorCount: number;
    lastDeliveryAt?: string;
  };
}

export class WebhookService {
  private static BASE_URL = '/api/github';

  // Create webhook for repository
  static async createWebhook(
    repositoryId: string,
    events: string[] = ['issues', 'pull_request']
  ): Promise<CreateWebhookResponse> {
    try {
      const response = await axios.post(`${this.BASE_URL}/repositories/${repositoryId}/webhook`, {
        events,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create webhook');
    }
  }

  // Get webhook details for repository
  static async getWebhookDetails(repositoryId: string): Promise<WebhookResponse> {
    try {
      const response = await axios.get(`${this.BASE_URL}/repositories/${repositoryId}/webhook`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch webhook details');
    }
  }

  // Delete webhook for repository
  static async deleteWebhook(repositoryId: string): Promise<{ success: boolean }> {
    try {
      const response = await axios.delete(`${this.BASE_URL}/repositories/${repositoryId}/webhook`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to delete webhook');
    }
  }

  // Format webhook status for display
  static formatWebhookStatus(webhook: WebhookDetails | null): {
    status: 'active' | 'inactive' | 'error' | 'none';
    statusText: string;
    statusColor: string;
    deliveryInfo: string;
  } {
    if (!webhook) {
      return {
        status: 'none',
        statusText: 'No Webhook',
        statusColor: 'text-gray-500',
        deliveryInfo: 'Webhook not configured',
      };
    }

    if (!webhook.active) {
      return {
        status: 'inactive',
        statusText: 'Inactive',
        statusColor: 'text-orange-500',
        deliveryInfo: 'Webhook is disabled',
      };
    }

    if (webhook.errorCount > 0 && webhook.lastError) {
      return {
        status: 'error',
        statusText: 'Error',
        statusColor: 'text-red-500',
        deliveryInfo: `Last error: ${webhook.lastError.message}`,
      };
    }

    return {
      status: 'active',
      statusText: 'Active',
      statusColor: 'text-green-500',
      deliveryInfo: `${webhook.deliveryCount} deliveries`,
    };
  }

  // Get webhook health score
  static getWebhookHealth(webhook: WebhookDetails | null): {
    score: number;
    label: string;
    color: string;
  } {
    if (!webhook) {
      return { score: 0, label: 'No Webhook', color: 'text-gray-500' };
    }

    if (!webhook.active) {
      return { score: 0, label: 'Inactive', color: 'text-orange-500' };
    }

    const totalDeliveries = webhook.deliveryCount;
    const errorCount = webhook.errorCount;

    if (totalDeliveries === 0) {
      return { score: 100, label: 'New', color: 'text-blue-500' };
    }

    const successRate = ((totalDeliveries - errorCount) / totalDeliveries) * 100;

    if (successRate >= 95) {
      return { score: Math.round(successRate), label: 'Excellent', color: 'text-green-500' };
    } else if (successRate >= 85) {
      return { score: Math.round(successRate), label: 'Good', color: 'text-yellow-500' };
    } else if (successRate >= 70) {
      return { score: Math.round(successRate), label: 'Fair', color: 'text-orange-500' };
    } else {
      return { score: Math.round(successRate), label: 'Poor', color: 'text-red-500' };
    }
  }
}
