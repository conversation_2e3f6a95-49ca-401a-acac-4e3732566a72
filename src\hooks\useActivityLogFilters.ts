import { useState, useCallback, useMemo } from 'react';
import { startOfDay, endOfDay, subDays, subMonths } from 'date-fns';

export interface ActivityLogFilters {
    search: string;
    userId?: string;
    actions: string[];
    resourceTypes: string[];
    dateRange: {
        start?: Date;
        end?: Date;
    };
    source?: string;
}

export interface FilterActions {
    updateSearch: (search: string) => void;
    updateUserId: (userId?: string) => void;
    updateActions: (actions: string[]) => void;
    updateResourceTypes: (types: string[]) => void;
    updateDateRange: (range: { start?: Date; end?: Date }) => void;
    updateSource: (source?: string) => void;
    clearFilters: () => void;
    setQuickDateRange: (range: { start: Date; end: Date }) => void;
}

export interface UseActivityLogFiltersReturn {
    filters: ActivityLogFilters;
    actions: FilterActions;
    hasActiveFilters: boolean;
    getApiParams: () => Record<string, any>;
}

const initialFilters: ActivityLogFilters = {
    search: '',
    actions: [],
    resourceTypes: [],
    dateRange: {},
};

export const QUICK_DATE_RANGES = [
    {
        label: 'Today',
        getValue: () => ({ start: startOfDay(new Date()), end: endOfDay(new Date()) }),
    },
    {
        label: 'Yesterday',
        getValue: () => ({
            start: startOfDay(subDays(new Date(), 1)),
            end: endOfDay(subDays(new Date(), 1)),
        }),
    },
    {
        label: 'Last 7 days',
        getValue: () => ({ start: startOfDay(subDays(new Date(), 7)), end: endOfDay(new Date()) }),
    },
    {
        label: 'Last 30 days',
        getValue: () => ({ start: startOfDay(subDays(new Date(), 30)), end: endOfDay(new Date()) }),
    },
    {
        label: 'This month',
        getValue: () => ({ start: startOfDay(subMonths(new Date(), 1)), end: endOfDay(new Date()) }),
    },
];

export const useActivityLogFilters = (): UseActivityLogFiltersReturn => {
    const [filters, setFilters] = useState<ActivityLogFilters>(initialFilters);

    const updateSearch = useCallback((search: string) => {
        setFilters(prev => ({ ...prev, search }));
    }, []);

    const updateUserId = useCallback((userId?: string) => {
        setFilters(prev => ({ ...prev, userId }));
    }, []);

    const updateActions = useCallback((actions: string[]) => {
        setFilters(prev => ({ ...prev, actions }));
    }, []);

    const updateResourceTypes = useCallback((resourceTypes: string[]) => {
        setFilters(prev => ({ ...prev, resourceTypes }));
    }, []);

    const updateDateRange = useCallback((dateRange: { start?: Date; end?: Date }) => {
        setFilters(prev => ({ ...prev, dateRange }));
    }, []);

    const updateSource = useCallback((source?: string) => {
        setFilters(prev => ({ ...prev, source }));
    }, []);

    const clearFilters = useCallback(() => {
        setFilters(initialFilters);
    }, []);

    const setQuickDateRange = useCallback((range: { start: Date; end: Date }) => {
        updateDateRange(range);
    }, [updateDateRange]);

    const actions: FilterActions = useMemo(() => ({
        updateSearch,
        updateUserId,
        updateActions,
        updateResourceTypes,
        updateDateRange,
        updateSource,
        clearFilters,
        setQuickDateRange,
    }), [
        updateSearch,
        updateUserId,
        updateActions,
        updateResourceTypes,
        updateDateRange,
        updateSource,
        clearFilters,
        setQuickDateRange,
    ]);

    const hasActiveFilters = useMemo(() => {
        return (
            filters.search !== '' ||
            filters.userId !== undefined ||
            filters.actions.length > 0 ||
            filters.resourceTypes.length > 0 ||
            filters.dateRange.start !== undefined ||
            filters.dateRange.end !== undefined ||
            filters.source !== undefined
        );
    }, [filters]);

    const getApiParams = useCallback(() => {
        const params: Record<string, any> = {};

        if (filters.search) params.search = filters.search;
        if (filters.userId) params.filterUserId = filters.userId;
        if (filters.actions.length > 0) params.actions = filters.actions;
        if (filters.resourceTypes.length > 0) params.resourceTypes = filters.resourceTypes;
        if (filters.dateRange.start) params.startDate = filters.dateRange.start;
        if (filters.dateRange.end) params.endDate = filters.dateRange.end;
        if (filters.source) params.source = filters.source;

        return params;
    }, [filters]);

    return {
        filters,
        actions,
        hasActiveFilters,
        getApiParams,
    };
};