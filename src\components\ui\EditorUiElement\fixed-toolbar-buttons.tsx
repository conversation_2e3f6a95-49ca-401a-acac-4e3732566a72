'use client';

import * as React from 'react';

import {
  BoldPlugin,
  CodePlugin,
  ItalicPlugin,
  StrikethroughPlugin,
  UnderlinePlugin,
} from '@udecode/plate-basic-marks/react';
import { FontBackgroundColorPlugin, FontColorPlugin } from '@udecode/plate-font/react';
import { HighlightPlugin } from '@udecode/plate-highlight/react';
import { useEditorReadOnly } from '@udecode/plate/react';
import {
  BaselineIcon,
  BoldIcon,
  Code2Icon,
  HighlighterIcon,
  ItalicIcon,
  PaintBucketIcon,
  StrikethroughIcon,
  UnderlineIcon,
} from 'lucide-react';

import { MoreDropdownMenu } from '@/components/ui/EditorUiElement/more-dropdown-menu';

import { AlignDropdownMenu } from './align-dropdown-menu';
import { ColorDropdownMenu } from './color-dropdown-menu';
import { EmojiDropdownMenu } from './emoji-dropdown-menu';
import { FontSizeToolbarButton } from './font-size-toolbar-button';
import { RedoToolbarButton, UndoToolbarButton } from './history-toolbar-button';
import { ImportToolbarButton } from './import-toolbar-button';
import {
  BulletedIndentListToolbarButton,
  NumberedIndentListToolbarButton,
} from './indent-list-toolbar-button';
import { IndentTodoToolbarButton } from './indent-todo-toolbar-button';
import { IndentToolbarButton } from './indent-toolbar-button';
import { LineHeightDropdownMenu } from './line-height-dropdown-menu';
import { LinkToolbarButton } from './link-toolbar-button';
import { MarkToolbarButton } from './mark-toolbar-button';
import { ModeDropdownMenu } from './mode-dropdown-menu';
import { OutdentToolbarButton } from './outdent-toolbar-button';
import { TableDropdownMenu } from './table-dropdown-menu';
import { ToggleToolbarButton } from './toggle-toolbar-button';
import { ToolbarGroup } from './toolbar';
import { TurnIntoDropdownMenu } from './turn-into-dropdown-menu';
import { MediaToolbarButton } from './media-toolbar-button';
import { ImagePlugin } from '@udecode/plate-media/react';

export function FixedToolbarButtons() {
  const readOnly = useEditorReadOnly();

  return (
    <div className="flex w-full overflow-x-auto scrollbar-hide">
      {!readOnly && (
        <>
          <ToolbarGroup>
            <UndoToolbarButton />
            <RedoToolbarButton />
          </ToolbarGroup>

          <ToolbarGroup className="hidden md:flex">
            <ImportToolbarButton />
          </ToolbarGroup>

          <ToolbarGroup className="hidden sm:flex">
            <TurnIntoDropdownMenu />
            <FontSizeToolbarButton />
          </ToolbarGroup>

          <ToolbarGroup>
            <MarkToolbarButton nodeType={BoldPlugin.key} tooltip="Bold (⌘+B)">
              <BoldIcon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-700 dark:text-gray-200" />
            </MarkToolbarButton>

            <MarkToolbarButton nodeType={ItalicPlugin.key} tooltip="Italic (⌘+I)">
              <ItalicIcon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-700 dark:text-gray-200" />
            </MarkToolbarButton>

            <MarkToolbarButton nodeType={UnderlinePlugin.key} tooltip="Underline (⌘+U)">
              <UnderlineIcon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-700 dark:text-gray-200" />
            </MarkToolbarButton>

            <MarkToolbarButton nodeType={StrikethroughPlugin.key} tooltip="Strikethrough (⌘+⇧+M)">
              <StrikethroughIcon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-700 dark:text-gray-200" />
            </MarkToolbarButton>

            <MarkToolbarButton nodeType={CodePlugin.key} tooltip="Code (⌘+E)">
              <Code2Icon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-700 dark:text-gray-200" />
            </MarkToolbarButton>

            <div className="hidden sm:flex">
              <ColorDropdownMenu nodeType={FontColorPlugin.key} tooltip="Text color">
                <BaselineIcon className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 dark:text-red-400" />
              </ColorDropdownMenu>

              <ColorDropdownMenu
                nodeType={FontBackgroundColorPlugin.key}
                tooltip="Background color"
              >
                <PaintBucketIcon className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-500 dark:text-yellow-400" />
              </ColorDropdownMenu>
            </div>
          </ToolbarGroup>

          <ToolbarGroup>
            <AlignDropdownMenu />
            <NumberedIndentListToolbarButton />
            <BulletedIndentListToolbarButton />
            <IndentTodoToolbarButton />
            <ToggleToolbarButton />
          </ToolbarGroup>

          <ToolbarGroup>
            <MediaToolbarButton nodeType={ImagePlugin.key} />
          </ToolbarGroup>

          <ToolbarGroup>
            <LinkToolbarButton />
            <TableDropdownMenu />
            <EmojiDropdownMenu />
          </ToolbarGroup>

          <ToolbarGroup className="hidden sm:flex">
            <LineHeightDropdownMenu />
            <OutdentToolbarButton />
            <IndentToolbarButton />
          </ToolbarGroup>

          <ToolbarGroup>
            <MoreDropdownMenu />
          </ToolbarGroup>
        </>
      )}

      <div className="grow" />

      <ToolbarGroup>
        <MarkToolbarButton nodeType={HighlightPlugin.key} tooltip="Highlight">
          <div className="theme-transition text-gray-600 dark:text-gray-400">
            <HighlighterIcon className="h-3 w-3 sm:h-4 sm:w-4" />
          </div>
        </MarkToolbarButton>
      </ToolbarGroup>

      <ToolbarGroup>
        <ModeDropdownMenu />
      </ToolbarGroup>
    </div>
  );
}
