'use client';

import React, { useState, } from 'react';
import { useSession } from 'next-auth/react';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ActivityLogViewer } from '@/components/ActivityLog/ActivityLogViewer';
import { ActivityLogAnalytics } from '@/components/ActivityLog/ActivityLogAnalytics';
import { ActivityLogFilters } from '@/components/ActivityLog/ActivityLogFilters';
import { useActivityLogFilters } from '@/hooks/useActivityLogFilters';
import { Activity, BarChart3 } from 'lucide-react';
import { motion } from 'framer-motion';

const ActivityLogsPage = () => {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState<'logs' | 'analytics'>('logs');
  const { filters, actions: filterActions, getApiParams } = useActivityLogFilters();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-3 rounded-lg py-2 h-full w-full overflow-y-auto custom-scrollbar"
    >
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-3">
        <div>
          <h2 className="text-xl font-bold theme-text-primary">Activity Logs</h2>
          <p className="theme-text-secondary mt-1 text-sm">
            Monitor user actions and system events across your organization
          </p>
        </div>
      </div>

      {/* Filters */}
      <ActivityLogFilters
        filters={filters}
        actions={filterActions}
        className="mb-2"
      />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={value => setActiveTab(value as 'logs' | 'analytics')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Activity Logs
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="space-y-6">
          <ActivityLogViewer
            userId={session?.user?.id}
            organizationId={session?.user?.organizationId || undefined}
            filters={getApiParams()}
            showExport={true}
            className="mt-6"
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <ActivityLogAnalytics
            userId={session?.user?.id || undefined}
            organizationId={session?.user?.organizationId || undefined}
            dateRange={filters.dateRange}
          />
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default ActivityLogsPage;
