'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface SSEConnection {
  eventSource: EventSource | null;
  isConnected: boolean;
  connectionError: string | null;
  lastPing: number;
}

// Global SSE connection to prevent multiple connections
let globalSSE: SSEConnection | null = null;
let connectionCount = 0;

export function useSSE() {
  const { data: session } = useSession();
  const sseRef = useRef<EventSource | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const mountedRef = useRef(true);

  const handleNotification = useCallback((data: any) => {
    queryClient.invalidateQueries({ queryKey: ['notifications'] });
    queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });

    if (data.notification) {
      toast.success(data.notification.title, {
        description: data.notification.description,
        duration: 5000,
      });
    }
  }, [queryClient]);

  const connectSSE = useCallback(() => {
    if (!session?.user?.id || !mountedRef.current) return;

    // Clean up existing timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Use global SSE if it exists and is connected
    if (globalSSE && globalSSE.eventSource && globalSSE.isConnected) {
      sseRef.current = globalSSE.eventSource;
      setIsConnected(true);
      setConnectionError(null);
      connectionCount++;
      return globalSSE.eventSource;
    }

    // Close existing global SSE if it exists but is not connected
    if (globalSSE && globalSSE.eventSource) {
      globalSSE.eventSource.close();
      globalSSE = null;
    }

    try {
      const eventSource = new EventSource('/api/notifications/sse');
      
      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setIsConnected(true);
        setConnectionError(null);
        
        // Update global SSE
        globalSSE = {
          eventSource,
          isConnected: true,
          connectionError: null,
          lastPing: Date.now()
        };
        
        connectionCount++;
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'connected') {
            console.log('SSE connected:', data.message);
          } else if (data.type === 'ping') {
            // Update last ping time
            if (globalSSE) {
              globalSSE.lastPing = Date.now();
            }
          } else if (data.type === 'notification') {
            handleNotification(data);
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        setIsConnected(false);
        setConnectionError('Connection failed');
        
        if (globalSSE) {
          globalSSE.isConnected = false;
          globalSSE.connectionError = 'Connection failed';
        }

        // Attempt to reconnect after a delay
        if (mountedRef.current) {
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              console.log('Attempting to reconnect SSE...');
              connectSSE();
            }
          }, 5000); // Reconnect after 5 seconds
        }
      };

      sseRef.current = eventSource;
      return eventSource;
    } catch (error) {
      console.error('Failed to create SSE connection:', error);
      setConnectionError('Failed to connect');
      
      // Attempt to reconnect after a delay
      if (mountedRef.current) {
        reconnectTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current) {
            connectSSE();
          }
        }, 5000);
      }
    }
  }, [session?.user?.id, handleNotification]);

  useEffect(() => {
    mountedRef.current = true;
    connectSSE();

    return () => {
      mountedRef.current = false;
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      connectionCount--;
      
      // Only close the global SSE if this is the last component using it
      if (connectionCount <= 0 && globalSSE && globalSSE.eventSource) {
        globalSSE.eventSource.close();
        globalSSE = null;
        connectionCount = 0;
      }
      
      setIsConnected(false);
    };
  }, [connectSSE]);

  // Cleanup on session change
  useEffect(() => {
    if (!session?.user?.id && globalSSE && globalSSE.eventSource) {
      globalSSE.eventSource.close();
      globalSSE = null;
      connectionCount = 0;
      setIsConnected(false);
    }
  }, [session?.user?.id]);

  const disconnect = useCallback(() => {
    if (sseRef.current) {
      sseRef.current.close();
      sseRef.current = null;
    }
    
    if (globalSSE && globalSSE.eventSource) {
      globalSSE.eventSource.close();
      globalSSE = null;
    }
    
    connectionCount = 0;
    setIsConnected(false);
    setConnectionError(null);
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      if (mountedRef.current) {
        connectSSE();
      }
    }, 1000);
  }, [disconnect, connectSSE]);

  return {
    eventSource: sseRef.current,
    isConnected,
    connectionError,
    disconnect,
    reconnect
  };
}
