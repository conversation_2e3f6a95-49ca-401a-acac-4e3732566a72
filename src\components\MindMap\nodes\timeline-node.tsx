'use client';

import { memo, useState } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';

interface TimelineNodeData {
  title: string;
  startDate: string;
  endDate: string;
  status: 'scheduled' | 'active' | 'completed' | 'delayed';
}

export const TimelineNode = memo(({ id, data, selected }: NodeProps<TimelineNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const handleDoubleClick = () => {
    startEditing(id, { ...data, nodeType: 'timeline' });
  };

  const statusColors = {
    scheduled: 'bg-blue-100 text-blue-800',
    active: 'bg-green-100 text-green-800',
    completed: 'bg-gray-100 text-gray-800',
    delayed: 'bg-red-100 text-red-800',
  };

  const getDuration = () => {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <Card
      className={`min-w-[220px] bg-gradient-to-br from-red-50 to-pink-50 border-red-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-red-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-red-600" />
            <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          </div>
          {isHovered && (
            <Edit3
              className="h-3 w-3 text-gray-400 cursor-pointer hover:text-gray-600"
              onClick={() => startEditing(id, { ...data, nodeType: 'timeline' })}
            />
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>Start: {new Date(data.startDate).toLocaleDateString()}</span>
            <span>End: {new Date(data.endDate).toLocaleDateString()}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-xs text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              {getDuration()} days
            </div>

            <Badge variant="secondary" className={`text-xs ${statusColors[data.status]}`}>
              {data.status}
            </Badge>
          </div>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
});

TimelineNode.displayName = 'TimelineNode';
