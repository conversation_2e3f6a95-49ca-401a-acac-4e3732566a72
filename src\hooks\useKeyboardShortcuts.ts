import { useEffect } from 'react';
import { useMindMapStore } from '@/stores/mindMapStore';

export function useKeyboardShortcuts(isEditing: boolean) {
  const {
    selectedNodes,
    deleteSelectedNodes,
    duplicateSelectedNodes,
    isConnecting,
    endConnecting,
    undo,
    redo,
  } = useMindMapStore();

  useEffect(() => {
    if (!isEditing) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Delete selected nodes
      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (selectedNodes.length > 0) {
          event.preventDefault();
          deleteSelectedNodes();
        }
      }

      // Duplicate selected nodes
      if (event.key === 'd' && (event.ctrlKey || event.metaKey)) {
        if (selectedNodes.length > 0) {
          event.preventDefault();
          duplicateSelectedNodes();
        }
      }

      // Escape to cancel connection mode
      if (event.key === 'Escape') {
        if (isConnecting) {
          event.preventDefault();
          endConnecting();
        }
      }

      // Undo
      if (event.key === 'z' && (event.ctrlKey || event.metaKey) && !event.shiftKey) {
        event.preventDefault();
        undo();
      }

      // Redo
      if (
        (event.key === 'z' && (event.ctrlKey || event.metaKey) && event.shiftKey) ||
        (event.key === 'y' && (event.ctrlKey || event.metaKey))
      ) {
        event.preventDefault();
        redo();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    isEditing,
    selectedNodes,
    deleteSelectedNodes,
    duplicateSelectedNodes,
    isConnecting,
    endConnecting,
    undo,
    redo,
  ]);
}
