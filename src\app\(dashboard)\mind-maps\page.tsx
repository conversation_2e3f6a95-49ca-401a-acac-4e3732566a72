'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Grid,
  List,
  Plus,
  Search,
  Brain,
  Eye,
  Edit,
  Copy,
  Trash2,
  MoreVertical,
  Calendar,
  Tag,
  FileText,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useProjectStore } from '@/stores/projectsStore';
import { useMindMaps, useDeleteMindMap, useDuplicateMindMap } from '@/hooks/useMindMap';
import { MindMap, MindMapFilters } from '@/types/MindMapsTypes';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { EmptyState } from '@/components/Global/EmptyState';
import CreateMindMapModal from '@/components/MindMap/CreateMindMapModal';

export default function MindMapsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [projectFilter, setProjectFilter] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const { projects } = useProjectStore();

  // Build filters
  const filters: MindMapFilters = {
    search: searchTerm || undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    projectId: projectFilter !== 'all' ? projectFilter : undefined,
  };

  const { data: mindMaps, isLoading, error } = useMindMaps(filters);
  const deleteMutation = useDeleteMindMap();
  const duplicateMutation = useDuplicateMindMap();

  const handleEdit = (id: string) => {
    router.push(`/mind-maps/${id}/edit`);
  };

  const handleView = (id: string) => {
    router.push(`/mind-maps/${id}/view`);
  };

  const handleDuplicate = async (mindMap: MindMap) => {
    await duplicateMutation.mutateAsync({
      id: mindMap._id,
      title: `${mindMap.title} (Copy)`,
    });
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this mind map?')) {
      await deleteMutation.mutateAsync(id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'template':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-3 rounded-lg py-2 h-full w-full overflow-y-auto custom-scrollbar"
    >
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-3">
        <div>
          <h2 className="text-xl font-bold theme-text-primary">Mind Maps</h2>
          <p className="theme-text-secondary mt-1 text-sm">
            Create and manage your visual mind maps
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          New Mind Map
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search mind maps..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
              <SelectItem value="template">Template</SelectItem>
            </SelectContent>
          </Select>

          <Select value={projectFilter} onValueChange={setProjectFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Project" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Projects</SelectItem>
              {projects.map(project => (
                <SelectItem key={project._id} value={project._id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mt-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-600 dark:text-red-400">
              {error instanceof Error ? error.message : 'Failed to load mind maps'}
            </p>
            <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        ) : !mindMaps?.length ? (
          <EmptyState
            icon={<Brain />}
            title="No mind maps found"
            description={
              searchTerm || statusFilter !== 'all' || projectFilter !== 'all'
                ? 'No mind maps match your current filters'
                : 'Create your first mind map to get started'
            }
          />
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {(mindMaps || []).map((mindMap: MindMap) => (
              <Card
                key={mindMap._id}
                className="group hover:shadow-md transition-shadow cursor-pointer"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-sm font-medium truncate">
                        {mindMap.title}
                      </CardTitle>
                      {mindMap.description && (
                        <p className="text-xs theme-text-secondary mt-1 line-clamp-2">
                          {mindMap.description}
                        </p>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(mindMap._id)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(mindMap._id)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicate(mindMap)}>
                          <Copy className="w-4 h-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(mindMap._id)}
                          className="text-red-600 dark:text-red-400"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pt-0" onClick={() => handleView(mindMap._id)}>
                  <div className="space-y-3">
                    {/* Status and Stats */}
                    <div className="flex items-center justify-between">
                      <Badge className={getStatusColor(mindMap.status)}>{mindMap.status}</Badge>
                      <div className="flex items-center gap-2 text-xs theme-text-secondary">
                        <div className="flex items-center gap-1">
                          <FileText className="w-3 h-3" />
                          {mindMap.nodes?.length || 0}
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {mindMap.analytics?.viewCount || 0}
                        </div>
                      </div>
                    </div>

                    {/* Tags */}
                    {mindMap.tags && mindMap.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {mindMap.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {mindMap.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{mindMap.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Project and Date */}
                    <div className="flex items-center justify-between text-xs theme-text-secondary">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {formatDistanceToNow(new Date(mindMap.updatedAt), { addSuffix: true })}
                      </div>
                      {mindMap.projectId && (
                        <div className="flex items-center gap-1">
                          <Tag className="w-3 h-3" />
                          {projects.find(p => p._id === mindMap.projectId)?.name || 'Project'}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          /* List View */
          <div className="space-y-2">
            {(mindMaps || []).map((mindMap: MindMap) => (
              <Card key={mindMap._id} className="group hover:shadow-sm transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1 min-w-0">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <Brain className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3
                            className="font-medium truncate cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
                            onClick={() => handleView(mindMap._id)}
                          >
                            {mindMap.title}
                          </h3>
                          <Badge className={getStatusColor(mindMap.status)}>{mindMap.status}</Badge>
                        </div>
                        {mindMap.description && (
                          <p className="text-sm theme-text-secondary mt-1 truncate">
                            {mindMap.description}
                          </p>
                        )}
                        <div className="flex items-center gap-4 mt-2 text-xs theme-text-secondary">
                          <div className="flex items-center gap-1">
                            <FileText className="w-3 h-3" />
                            {mindMap.nodes?.length || 0} nodes
                          </div>
                          <div className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {mindMap.analytics?.viewCount || 0} views
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {formatDistanceToNow(new Date(mindMap.updatedAt), { addSuffix: true })}
                          </div>
                          {mindMap.projectId && (
                            <div className="flex items-center gap-1">
                              <Tag className="w-3 h-3" />
                              {projects.find(p => p._id === mindMap.projectId)?.name || 'Project'}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {mindMap.tags && mindMap.tags.length > 0 && (
                        <div className="flex gap-1">
                          {mindMap.tags.slice(0, 2).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleView(mindMap._id)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEdit(mindMap._id)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDuplicate(mindMap)}>
                            <Copy className="w-4 h-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(mindMap._id)}
                            className="text-red-600 dark:text-red-400"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <CreateMindMapModal
        showCreateDialog={showCreateDialog}
        setShowCreateDialog={setShowCreateDialog}
      />
    </motion.div>
  );
}
