import mongoose, { Schema, Document } from 'mongoose';

export interface ISubscription extends Document {
  organizationId: mongoose.Types.ObjectId;
  plan: 'free' | 'basic' | 'professional' | 'enterprise';
  status: 'active' | 'suspended' | 'cancelled' | 'past_due' | 'trialing' | 'paused';
  priceId?: string; // Razorpay price IDs
  customerId?: string; // Razorpay customer IDs
  subscriptionId?: string; // Razorpay subscription ID

  // Plan limits
  userLimit: number | 'unlimited';
  projectLimit: number | 'unlimited';
  integrationLimit: number | 'unlimited';
  automationLimit: number | 'unlimited';
  apiCallLimit: number | 'unlimited';

  // Billing
  billingCycle: 'monthly' | 'yearly';
  amount: number; // Amount in rupees (paise for Razorpay)
  originalAmount: number; // Original amount before discounts
  currency: string;
  discountPercentage?: number;

  // Periods
  currentPeriodStart: Date;
  currentPeriodEnd: Date | null;
  trialStart?: Date;
  trialEnd?: Date;
  pausedAt?: Date;
  pauseReason?: string;

  // Enhanced Features
  features: {
    // Core features
    unlimitedProjects: boolean;
    unlimitedUsers: boolean;
    advancedCalendarViews: boolean;
    customTaskFields: boolean;

    // Analytics & Reporting
    basicReporting: boolean;
    advancedAnalytics: boolean;
    exportCapabilities: boolean;

    // Collaboration
    teamCollaboration: boolean;
    projectSharing: boolean;
    permissionControls: boolean;
    timeTracking: boolean;

    // Integrations & Automation
    basicIntegrations: boolean;
    advancedIntegrations: boolean;
    customIntegrations: boolean;
    basicAutomation: boolean;
    advancedAutomation: boolean;
    apiAccess: boolean;

    // Support & Service
    emailSupport: boolean;
    prioritySupport: boolean;
    phoneSupport: boolean;
    dedicatedAccountManager: boolean;

    // Security & Compliance
    basicSecurity: boolean;
    advancedSecurity: boolean;
    ssoEnabled: boolean;
    auditLogs: boolean;

    // Customization
    customBranding: boolean;
    whiteLabel: boolean;
  };

  // Payment & Billing
  lastPaymentDate?: Date;
  nextPaymentDate?: Date;
  paymentMethod?: 'upi' | 'netbanking' | 'card' | 'wallet' | 'emi';
  paymentMethodDetails?: {
    last4?: string;
    brand?: string;
    bankName?: string;
    upiId?: string;
  };

  // Usage tracking
  usage: {
    projectsCreated: number;
    usersInvited: number;
    integrationsConnected: number;
    automationsCreated: number;
    apiCallsThisMonth: number;
    lastUsageReset: Date;
  };

  // Billing history reference
  invoices: mongoose.Types.ObjectId[];

  // Upgrade/Downgrade tracking
  planHistory: {
    previousPlan: string;
    changedAt: Date;
    reason: 'upgrade' | 'downgrade' | 'trial_end' | 'payment_failed' | 'user_request';
  }[];

  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const subscriptionSchema = new Schema<ISubscription>(
  {
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
      unique: true,
    },
    plan: {
      type: String,
      enum: ['free', 'basic', 'professional', 'enterprise'],
      default: 'free',
    },
    status: {
      type: String,
      enum: ['active', 'suspended', 'cancelled', 'past_due', 'trialing', 'paused'],
      default: 'active',
    },
    priceId: {
      type: String,
    },
    customerId: {
      type: String,
    },
    subscriptionId: {
      type: String,
    },

    // Removed storage fields as per requirements

    // Plan limits
    userLimit: {
      type: Schema.Types.Mixed, // Can be number or 'unlimited'
      default: 1, // Free plan limit
    },
    projectLimit: {
      type: Schema.Types.Mixed, // Can be number or 'unlimited'
      default: 3, // Free plan limit
    },
    integrationLimit: {
      type: Schema.Types.Mixed, // Can be number or 'unlimited'
      default: 0, // Free plan limit
    },
    automationLimit: {
      type: Schema.Types.Mixed, // Can be number or 'unlimited'
      default: 0, // Free plan limit
    },
    apiCallLimit: {
      type: Schema.Types.Mixed, // Can be number or 'unlimited'
      default: 0, // Free plan limit
    },

    // Billing
    billingCycle: {
      type: String,
      enum: ['monthly', 'yearly'],
      default: 'monthly',
    },
    amount: {
      type: Number,
      default: 0, // Free plan
    },
    originalAmount: {
      type: Number,
      default: 0,
    },
    currency: {
      type: String,
      default: 'INR',
    },
    discountPercentage: {
      type: Number,
      default: 0,
    },

    // Periods
    currentPeriodStart: {
      type: Date,
      default: Date.now,
    },
    currentPeriodEnd: {
      type: Date,
      default: null,
    },
    trialStart: {
      type: Date,
    },
    trialEnd: {
      type: Date,
    },
    pausedAt: {
      type: Date,
    },
    pauseReason: {
      type: String,
    },

    // Enhanced Features
    features: {
      // Core features
      unlimitedProjects: {
        type: Boolean,
        default: false,
      },
      unlimitedUsers: {
        type: Boolean,
        default: false,
      },
      advancedCalendarViews: {
        type: Boolean,
        default: false,
      },
      customTaskFields: {
        type: Boolean,
        default: false,
      },

      // Analytics & Reporting
      basicReporting: {
        type: Boolean,
        default: false,
      },
      advancedAnalytics: {
        type: Boolean,
        default: false,
      },
      exportCapabilities: {
        type: Boolean,
        default: false,
      },

      // Collaboration
      teamCollaboration: {
        type: Boolean,
        default: false,
      },
      projectSharing: {
        type: Boolean,
        default: false,
      },
      permissionControls: {
        type: Boolean,
        default: false,
      },
      timeTracking: {
        type: Boolean,
        default: false,
      },

      // Integrations & Automation
      basicIntegrations: {
        type: Boolean,
        default: false,
      },
      advancedIntegrations: {
        type: Boolean,
        default: false,
      },
      customIntegrations: {
        type: Boolean,
        default: false,
      },
      basicAutomation: {
        type: Boolean,
        default: false,
      },
      advancedAutomation: {
        type: Boolean,
        default: false,
      },
      apiAccess: {
        type: Boolean,
        default: false,
      },

      // Support & Service
      emailSupport: {
        type: Boolean,
        default: false,
      },
      prioritySupport: {
        type: Boolean,
        default: false,
      },
      phoneSupport: {
        type: Boolean,
        default: false,
      },
      dedicatedAccountManager: {
        type: Boolean,
        default: false,
      },

      // Security & Compliance
      basicSecurity: {
        type: Boolean,
        default: true,
      },
      advancedSecurity: {
        type: Boolean,
        default: false,
      },
      ssoEnabled: {
        type: Boolean,
        default: false,
      },
      auditLogs: {
        type: Boolean,
        default: false,
      },

      // Customization
      customBranding: {
        type: Boolean,
        default: false,
      },
      whiteLabel: {
        type: Boolean,
        default: false,
      },
    },

    // Payment & Billing
    lastPaymentDate: {
      type: Date,
    },
    nextPaymentDate: {
      type: Date,
    },
    paymentMethod: {
      type: String,
      enum: ['upi', 'netbanking', 'card', 'wallet', 'emi'],
    },
    paymentMethodDetails: {
      last4: String,
      brand: String,
      bankName: String,
      upiId: String,
    },

    // Usage tracking
    usage: {
      projectsCreated: {
        type: Number,
        default: 0,
      },
      usersInvited: {
        type: Number,
        default: 0,
      },
      integrationsConnected: {
        type: Number,
        default: 0,
      },
      automationsCreated: {
        type: Number,
        default: 0,
      },
      apiCallsThisMonth: {
        type: Number,
        default: 0,
      },
      lastUsageReset: {
        type: Date,
        default: Date.now,
      },
    },

    // Billing history reference
    invoices: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Invoice',
      },
    ],

    // Upgrade/Downgrade tracking
    planHistory: [
      {
        previousPlan: {
          type: String,
          required: true,
        },
        changedAt: {
          type: Date,
          default: Date.now,
        },
        reason: {
          type: String,
          enum: ['upgrade', 'downgrade', 'trial_end', 'payment_failed', 'user_request'],
          required: true,
        },
      },
    ],

    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  { timestamps: true }
);

subscriptionSchema.index({ status: 1 });

export const Subscription =
  mongoose.models.Subscription || mongoose.model<ISubscription>('Subscription', subscriptionSchema);
