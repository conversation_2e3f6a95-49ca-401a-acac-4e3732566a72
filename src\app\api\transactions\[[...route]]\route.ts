import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { Transaction } from '@/models/Transaction';
import { Subscription } from '@/models/Subscription';
// SubscriptionValidator not needed for transaction routes
import Razorpay from 'razorpay';
import crypto from 'crypto';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/transactions');

app.use('*', logger());

// Authentication middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    await connectDB();

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Auth middleware error:', error);
    return c.json({ error: 'Authentication failed' }, 401);
  }
  await next();
});

// Validation schemas
const createTransactionSchema = z.object({
  planId: z.enum(['basic', 'professional', 'enterprise']),
  billingCycle: z.enum(['monthly', 'yearly']),
  previousPlanId: z.enum(['free', 'basic', 'professional', 'enterprise']).optional(),
  type: z
    .enum(['subscription_payment', 'plan_upgrade', 'plan_downgrade', 'subscription_renewal'])
    .optional(),
  metadata: z
    .object({
      source: z.string().optional(),
      campaign: z.string().optional(),
    })
    .optional(),
});

const verifyPaymentSchema = z.object({
  transactionId: z.string(),
  razorpay_payment_id: z.string(),
  razorpay_order_id: z.string(),
  razorpay_signature: z.string(),
});

// Refund schema for future implementation
// const refundSchema = z.object({
//   transactionId: z.string(),
//   amount: z.number().optional(),
//   reason: z.string(),
// });

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

// GET /api/transactions - Get all transactions
app.get('/', async c => {
  const user = c.get('user');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const status = c.req.query('status');
    const type = c.req.query('type');
    const planId = c.req.query('planId');
    const startDate = c.req.query('startDate');
    const endDate = c.req.query('endDate');

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { organizationId: user.organizationId };

    if (status) filter.status = status;
    if (type) filter.type = type;
    if (planId) filter.planId = planId;

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const [transactions, total] = await Promise.all([
      Transaction.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('subscriptionId', 'plan status'),
      Transaction.countDocuments(filter),
    ]);

    return c.json({
      transactions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    console.error('Get transactions error:', error);
    return c.json({ error: 'Failed to fetch transactions' }, 500);
  }
});

// GET /api/transactions/:id - Get specific transaction
app.get('/:id', async c => {
  const user = c.get('user');
  const transactionId = c.req.param('id');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const transaction = await Transaction.findOne({
      $or: [{ _id: transactionId }, { transactionId: transactionId }],
      organizationId: user.organizationId,
    }).populate('subscriptionId', 'plan status');

    if (!transaction) {
      return c.json({ error: 'Transaction not found' }, 404);
    }

    return c.json({ transaction });
  } catch (error: any) {
    console.error('Get transaction error:', error);
    return c.json({ error: 'Failed to fetch transaction' }, 500);
  }
});

// POST /api/transactions - Create new transaction
app.post('/', zValidator('json', createTransactionSchema), async c => {
  const user = c.get('user');
  const transactionData = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Get current subscription
    const subscription = await Subscription.findOne({
      organizationId: user.organizationId,
    });

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404);
    }

    // Calculate amount based on plan and billing cycle
    const planPricing = {
      basic: { monthly: 9900, yearly: 99000 }, // ₹99/month, ₹990/year
      professional: { monthly: 19900, yearly: 199000 }, // ₹199/month, ₹1990/year
      enterprise: { monthly: 39900, yearly: 399000 }, // ₹399/month, ₹3990/year
    };

    const amount = planPricing[transactionData.planId][transactionData.billingCycle];

    // Calculate billing period
    const now = new Date();
    const billingPeriodEnd = new Date(now);
    if (transactionData.billingCycle === 'yearly') {
      billingPeriodEnd.setFullYear(billingPeriodEnd.getFullYear() + 1);
    } else {
      billingPeriodEnd.setMonth(billingPeriodEnd.getMonth() + 1);
    }

    // Generate transaction ID
    const transactionId = (Transaction as any).generateTransactionId();

    // Create Razorpay order
    const orderData = {
      amount,
      currency: 'INR',
      receipt: transactionId,
      notes: {
        organizationId: user.organizationId,
        planId: transactionData.planId,
        billingCycle: transactionData.billingCycle,
        transactionId,
      },
    };

    const razorpayOrder = await razorpay.orders.create(orderData);

    // Create transaction record
    const transaction = new Transaction({
      organizationId: user.organizationId,
      subscriptionId: subscription._id,
      transactionId,
      razorpayOrderId: razorpayOrder.id,
      type: transactionData.type || 'subscription_payment',
      status: 'pending',
      amount,
      currency: 'INR',
      planId: transactionData.planId,
      billingCycle: transactionData.billingCycle,
      previousPlanId: transactionData.previousPlanId || subscription.plan,
      paymentMethod: 'card', // Will be updated after payment
      billingPeriodStart: now,
      billingPeriodEnd,
      description: `${transactionData.planId.charAt(0).toUpperCase() + transactionData.planId.slice(1)} Plan - ${transactionData.billingCycle}`,
      invoiceNumber: (Transaction as any).generateInvoiceNumber(),
      metadata: {
        source: transactionData.metadata?.source || 'web',
        campaign: transactionData.metadata?.campaign,
        userAgent: c.req.header('user-agent'),
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
      },
    });

    await transaction.save();

    return c.json(
      {
        transaction,
        razorpayOrder,
      },
      201
    );
  } catch (error: any) {
    console.error('Create transaction error:', error);
    return c.json({ error: 'Failed to create transaction' }, 500);
  }
});

// POST /api/transactions/verify - Verify payment
app.post('/verify', zValidator('json', verifyPaymentSchema), async c => {
  const user = c.get('user');
  const paymentData = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Find transaction
    const transaction = await Transaction.findOne({
      transactionId: paymentData.transactionId,
      organizationId: user.organizationId,
    });

    if (!transaction) {
      return c.json({ error: 'Transaction not found' }, 404);
    }

    // Verify Razorpay signature
    const body = paymentData.razorpay_order_id + '|' + paymentData.razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(body.toString())
      .digest('hex');

    if (expectedSignature !== paymentData.razorpay_signature) {
      await transaction.markFailed('SIGNATURE_MISMATCH', 'Payment signature verification failed');
      return c.json({ error: 'Payment verification failed' }, 400);
    }

    // Get payment details from Razorpay
    const payment = await razorpay.payments.fetch(paymentData.razorpay_payment_id);

    // Update transaction
    await transaction.markCompleted({
      razorpay_payment_id: paymentData.razorpay_payment_id,
      razorpay_signature: paymentData.razorpay_signature,
    });

    // Update payment method details
    transaction.paymentMethod = payment.method as any;
    if (payment.card) {
      transaction.paymentMethodDetails = {
        last4: payment.card.last4,
        brand: payment.card.network,
      };
    } else if (payment.vpa) {
      transaction.paymentMethodDetails = {
        upiId: payment.vpa,
      };
    } else if (payment.bank) {
      transaction.paymentMethodDetails = {
        bankName: payment.bank,
      };
    }

    await transaction.save();

    // Update subscription
    const subscription = await Subscription.findById(transaction.subscriptionId);
    if (subscription) {
      subscription.plan = transaction.planId;
      subscription.status = 'active';
      subscription.billingCycle = transaction.billingCycle;
      subscription.amount = transaction.amount / 100; // Convert to rupees
      subscription.currentPeriodStart = transaction.billingPeriodStart;
      subscription.currentPeriodEnd = transaction.billingPeriodEnd;
      subscription.lastPaymentDate = new Date();
      subscription.nextPaymentDate = transaction.billingPeriodEnd;

      // Add to plan history
      if (transaction.previousPlanId && transaction.previousPlanId !== transaction.planId) {
        subscription.planHistory.push({
          previousPlan: transaction.previousPlanId,
          changedAt: new Date(),
          reason: transaction.type === 'plan_upgrade' ? 'upgrade' : 'user_request',
        });
      }

      await subscription.save();
    }

    return c.json({
      success: true,
      transaction,
      message: 'Payment verified successfully',
    });
  } catch (error: any) {
    console.error('Verify payment error:', error);
    return c.json({ error: 'Payment verification failed' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
