import { useState, useCallback, useEffect, useRef } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useMindMapStore } from '@/stores/mindMapStore';
import { MindMapService } from '@/services/MindMap.service';
import { UpdateMindMapRequest } from '@/types/MindMapsTypes';

// Query keys
export const mindMapKeys = {
  all: ['mindMaps'] as const,
  lists: () => [...mindMapKeys.all, 'list'] as const,
  list: (filters: any) => [...mindMapKeys.lists(), filters] as const,
  details: () => [...mindMapKeys.all, 'detail'] as const,
  detail: (id: string) => [...mindMapKeys.details(), id] as const,
};

// Context menu state
interface ContextMenuState {
  x: number;
  y: number;
  nodeId: string;
  nodeType: string;
}

// Confirm dialog state
interface ConfirmDialogState {
  isOpen: boolean;
  title: string;
  description: string;
  onConfirm: () => void;
}

// Save state
interface SaveState {
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  saveError: string | null;
  lastSaved: Date | null;
}

/**
 * Comprehensive Mind Map Editor Hook
 * Consolidates all mind map functionality into a single hook
 */
export function useMindMapEditor(mindMapId?: string) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const lastSavedDataRef = useRef<string | null>(null);

  // Store integration
  const {
    currentMindMap,
    setCurrentMindMap,
    addNode,
    updateNode,
    deleteNode,
    addConnection,
    deleteConnection,
    selectNode,
    clearNodeSelection,
    selectedNodes,
    isConnecting,
    connectingFrom,
    startConnecting,
    endConnecting,
    hasUnsavedChanges,
    markAsChanged,
    markAsSaved,
    undo,
    redo,
    duplicateSelectedNodes,
    deleteSelectedNodes,
  } = useMindMapStore();

  // Local state
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    isOpen: false,
    title: '',
    description: '',
    onConfirm: () => {},
  });

  const [saveState, setSaveState] = useState<SaveState>({
    hasUnsavedChanges: false,
    isSaving: false,
    saveError: null,
    lastSaved: null,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // Queries and mutations
  const mindMapQuery = useQuery({
    queryKey: mindMapKeys.detail(mindMapId || ''),
    queryFn: async () => {
      if (!mindMapId) return null;
      const response = await MindMapService.getMindMap(mindMapId);
      return response.mindMap || response;
    },
    enabled: !!mindMapId,
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMindMapRequest }) =>
      MindMapService.updateMindMap(id, data),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
      toast({
        title: 'Success',
        description: 'Mind map saved successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to save mind map',
        variant: 'destructive',
      });
    },
  });

  // Load mind map data into store when query succeeds
  useEffect(() => {
    if (mindMapQuery.data) {
      setCurrentMindMap(mindMapQuery.data);
      lastSavedDataRef.current = JSON.stringify({
        nodes: mindMapQuery.data.nodes,
        connections: mindMapQuery.data.connections,
        viewport: mindMapQuery.data.viewport,
      });
    }
  }, [mindMapQuery.data, setCurrentMindMap]);

  // Sync save state with store
  useEffect(() => {
    setSaveState(prev => ({
      ...prev,
      hasUnsavedChanges,
    }));
  }, [hasUnsavedChanges]);

  // Context Menu Functions
  const showContextMenu = useCallback((x: number, y: number, nodeId: string, nodeType: string) => {
    setContextMenu({ x, y, nodeId, nodeType });
  }, []);

  const hideContextMenu = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Confirm Dialog Functions
  const showConfirmDialog = useCallback(
    (title: string, description: string, onConfirm: () => void) => {
      setConfirmDialog({
        isOpen: true,
        title,
        description,
        onConfirm,
      });
    },
    []
  );

  const hideConfirmDialog = useCallback(() => {
    setConfirmDialog({
      isOpen: false,
      title: '',
      description: '',
      onConfirm: () => {},
    });
  }, []);

  // Node Operations
  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      const node = currentMindMap?.nodes.find(n => n.id === nodeId);
      const nodeTitle = node?.content.text || 'this node';

      showConfirmDialog(
        'Delete Node',
        `Are you sure you want to delete "${nodeTitle}"? This action cannot be undone and will also remove any connections to this node.`,
        async () => {
          setIsDeleting(true);
          try {
            deleteNode(nodeId);
            hideConfirmDialog();
            hideContextMenu();
          } finally {
            setIsDeleting(false);
          }
        }
      );
    },
    [currentMindMap, deleteNode, showConfirmDialog, hideConfirmDialog, hideContextMenu]
  );

  const handleDuplicateNode = useCallback(
    (_nodeId: string) => {
      duplicateSelectedNodes();
      hideContextMenu();
    },
    [duplicateSelectedNodes, hideContextMenu]
  );

  // Save Functions
  const save = useCallback(async () => {
    if (!currentMindMap || !currentMindMap._id) {
      setSaveState(prev => ({
        ...prev,
        saveError: 'No mind map data to save',
      }));
      return;
    }

    // Check if there are actually changes to save
    const currentData = JSON.stringify({
      nodes: currentMindMap.nodes,
      connections: currentMindMap.connections,
      viewport: currentMindMap.viewport,
    });

    if (currentData === lastSavedDataRef.current) {
      // No changes to save
      setSaveState(prev => ({
        ...prev,
        hasUnsavedChanges: false,
      }));
      return;
    }

    setSaveState(prev => ({
      ...prev,
      isSaving: true,
      saveError: null,
    }));

    try {
      await updateMutation.mutateAsync({
        id: currentMindMap._id,
        data: {
          nodes: currentMindMap.nodes,
          connections: currentMindMap.connections,
          viewport: currentMindMap.viewport,
        },
      });

      // Update the last saved data reference
      lastSavedDataRef.current = currentData;
      markAsSaved();

      setSaveState(prev => ({
        ...prev,
        hasUnsavedChanges: false,
        isSaving: false,
        saveError: null,
        lastSaved: new Date(),
      }));
    } catch (error) {
      console.error('Save failed:', error);

      let errorMessage = 'Failed to save mind map';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      setSaveState(prev => ({
        ...prev,
        isSaving: false,
        saveError: errorMessage,
      }));

      throw error;
    }
  }, [currentMindMap, updateMutation, markAsSaved]);

  return {
    // Data
    mindMap: currentMindMap,
    isLoading: mindMapQuery.isLoading,
    error: mindMapQuery.error,

    // Save state
    hasUnsavedChanges: saveState.hasUnsavedChanges || hasUnsavedChanges,
    isSaving: saveState.isSaving,
    saveError: saveState.saveError,
    lastSaved: saveState.lastSaved,

    // Node operations
    addNode,
    updateNode,
    deleteNode,
    selectNode,
    clearNodeSelection,
    selectedNodes,

    // Connection operations
    addConnection,
    deleteConnection,
    isConnecting,
    connectingFrom,
    startConnecting,
    endConnecting,

    // History operations
    undo,
    redo,
    duplicateSelectedNodes,
    deleteSelectedNodes,

    // Context menu
    contextMenu,
    showContextMenu,
    hideContextMenu,

    // Confirm dialog
    confirmDialog,
    showConfirmDialog,
    hideConfirmDialog,
    isDeleting,

    // Actions
    handleDeleteNode,
    handleDuplicateNode,
    save,

    // Store actions
    markAsChanged,
    markAsSaved,
  };
}
