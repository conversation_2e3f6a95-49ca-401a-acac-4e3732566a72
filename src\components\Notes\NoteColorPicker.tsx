'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { noteColors } from '@/hooks/useNote';

interface NoteColorPickerProps {
  selectedColor: string;
  onColorChange: (color: string) => void;
  className?: string;
}

const NoteColorPicker: React.FC<NoteColorPickerProps> = ({
  selectedColor,
  onColorChange,
  className,
}) => {
  return (
    <div className={cn('grid grid-cols-3 gap-2', className)}>
      {noteColors.map(color => (
        <Button
          key={color.value}
          variant="outline"
          className={cn(
            'h-12 w-full p-0 border-2 hover:scale-105 transition-transform',
            selectedColor === color.value && 'ring-2 ring-primary ring-offset-2'
          )}
          style={{ backgroundColor: color.value }}
          onClick={() => onColorChange(color.value)}
          title={color.name}
        >
          {selectedColor === color.value && <Check className="h-4 w-4 text-foreground" />}
        </Button>
      ))}
    </div>
  );
};

export default NoteColorPicker;
