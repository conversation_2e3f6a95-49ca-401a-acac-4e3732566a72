import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { getServerSession } from 'next-auth';
import MindMap from '@/models/MindMap';
import {
  getMindMapsHelper,
  createMindMapHelper,
  updateMindMapHelper,
  deleteMindMapHelper,
  duplicateMindMapHelper,
} from '../helpers';
import { logger } from 'hono/logger';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/mind-maps');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    await connectDB();
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// GET /api/mind-maps - Get all mind maps
app.get('/', async c => {
  try {
    const url = new URL(c.req.url);
    const user = c.get('user');
    const userId = user?.id;
    console.log('Mind maps API - User ID:', userId);

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const mindMaps = await getMindMapsHelper(url.searchParams, userId);

    if ((mindMaps as any).error) {
      const err = mindMaps as any;
      return c.json({ error: err.message }, err.status || 400);
    }
    return c.json({ mindMaps });
  } catch (error) {
    console.error('Error fetching mind maps:', error);
    return c.json({ error: 'Failed to fetch mind maps' }, 500);
  }
});

// POST /api/mind-maps - Create new mind map
app.post('/', async c => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    const userId = user?.id;
    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }
    const result = await createMindMapHelper(body, userId);
    if ((result as any).error) {
      const err = result as any;
      return c.json({ error: err.message }, err.status || 400);
    }
    return c.json({ mindMap: result }, 201);
  } catch (error) {
    console.error('Error creating mind map:', error);
    return c.json({ error: 'Failed to create mind map' }, 500);
  }
});

// GET /api/mind-maps/:id - Get single mind map
app.get('/:id', async c => {
  try {
    const id = c.req.param('id');
    console.log('Fetching mind map with ID:', id);

    const mindMap = await MindMap.findById(id).lean();
    console.log('Found mind map:', !!mindMap);

    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Increment view count
    await MindMap.findByIdAndUpdate(id, {
      $inc: { 'analytics.viewCount': 1 },
      $set: { 'analytics.lastViewed': new Date() },
    });

    return c.json({ mindMap });
  } catch (error) {
    console.error('Error fetching mind map:', error);
    return c.json({ error: 'Failed to fetch mind map' }, 500);
  }
});

// PUT /api/mind-maps/:id - Update mind map
app.put('/:id', async c => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const user = c.get('user');
    const userId = user?.id;
    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }
    const result = await updateMindMapHelper(id, body, userId);
    if ((result as any).error) {
      const err = result as any;
      return c.json({ error: err.message }, err.status || 400);
    }
    return c.json({ mindMap: result });
  } catch (error) {
    console.error('Error updating mind map:', error);
    return c.json({ error: 'Failed to update mind map' }, 500);
  }
});

// DELETE /api/mind-maps/:id - Delete mind map
app.delete('/:id', async c => {
  try {
    const id = c.req.param('id');
    const user = c.get('user');
    const userId = user?.id;
    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }
    const result = await deleteMindMapHelper(id, userId);
    if ((result as any).error) {
      const err = result as any;
      return c.json({ error: err.message }, err.status || 400);
    }
    return c.json(result);
  } catch (error) {
    console.error('Error deleting mind map:', error);
    return c.json({ error: 'Failed to delete mind map' }, 500);
  }
});

// POST /api/mind-maps/:id/duplicate - Duplicate mind map
app.post('/:id/duplicate', async c => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const { title } = body;
    const user = c.get('user');
    const userId = user?.id;
    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }
    if (!title?.trim()) {
      return c.json({ error: 'Title is required' }, 400);
    }
    const result = await duplicateMindMapHelper(id, title.trim(), userId);
    if ((result as any).error) {
      const err = result as any;
      return c.json({ error: err.message }, err.status || 400);
    }
    return c.json({ mindMap: result }, 201);
  } catch (error) {
    console.error('Error duplicating mind map:', error);
    return c.json({ error: 'Failed to duplicate mind map' }, 500);
  }
});

// POST /api/mind-maps/:id/export - Export mind map
app.post('/:id/export', async c => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const { format } = body;

    const mindMap = await MindMap.findById(id);
    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Update analytics
    if (mindMap.analytics) {
      mindMap.analytics.editCount++;
    }

    await mindMap.save();

    // Return the mind map data for export
    const exportData = {
      id: mindMap._id,
      title: mindMap.title,
      description: mindMap.description,
      nodes: mindMap.nodes,
      connections: mindMap.connections,
      viewport: mindMap.viewport,
      tags: mindMap.tags,
      createdAt: mindMap.createdAt,
      updatedAt: mindMap.updatedAt,
    };

    return c.json({
      message: 'Export data prepared',
      format,
      data: exportData,
    });
  } catch (error) {
    console.error('Error exporting mind map:', error);
    return c.json({ error: 'Failed to export mind map' }, 500);
  }
});

// POST /api/mind-maps/:id/nodes - Add node to mind map
app.post('/:id/nodes', async c => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const user = c.get('user');
    const userId = user?.id;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const mindMap = await MindMap.findById(id);
    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Check if user is the owner
    if (mindMap.userId.toString() !== userId) {
      return c.json({ error: 'Permission denied' }, 403);
    }

    const newNode = {
      id: body.id || `node-${Date.now()}`,
      type: body.type || 'text',
      position: body.position || { x: 0, y: 0 },
      size: body.size || { width: 200, height: 100 },
      content: body.content || { text: 'New Node' },
      parentId: body.parentId,
      childIds: body.childIds || [],
    };

    mindMap.nodes.push(newNode);
    mindMap.analytics.editCount++;
    mindMap.analytics.lastEdited = new Date();

    await mindMap.save();

    return c.json({ node: newNode });
  } catch (error) {
    console.error('Error adding node:', error);
    return c.json({ error: 'Failed to add node' }, 500);
  }
});

// PUT /api/mind-maps/:id/nodes/:nodeId - Update node
app.put('/:id/nodes/:nodeId', async c => {
  try {
    const id = c.req.param('id');
    const nodeId = c.req.param('nodeId');
    const body = await c.req.json();
    const user = c.get('user');
    const userId = user?.id;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const mindMap = await MindMap.findById(id);
    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Check if user is the owner
    if (mindMap.userId.toString() !== userId) {
      return c.json({ error: 'Permission denied' }, 403);
    }

    const nodeIndex = mindMap.nodes.findIndex(node => node.id === nodeId);
    if (nodeIndex === -1) {
      return c.json({ error: 'Node not found' }, 404);
    }

    mindMap.nodes[nodeIndex] = { ...mindMap.nodes[nodeIndex], ...body };
    mindMap.analytics.editCount++;
    mindMap.analytics.lastEdited = new Date();

    await mindMap.save();

    return c.json({ node: mindMap.nodes[nodeIndex] });
  } catch (error) {
    console.error('Error updating node:', error);
    return c.json({ error: 'Failed to update node' }, 500);
  }
});

// DELETE /api/mind-maps/:id/nodes/:nodeId - Delete node
app.delete('/:id/nodes/:nodeId', async c => {
  try {
    const id = c.req.param('id');
    const nodeId = c.req.param('nodeId');
    const user = c.get('user');
    const userId = user?.id;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const mindMap = await MindMap.findById(id);
    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Check if user is the owner
    if (mindMap.userId.toString() !== userId) {
      return c.json({ error: 'Permission denied' }, 403);
    }

    // Remove node
    mindMap.nodes = mindMap.nodes.filter(node => node.id !== nodeId);

    // Remove connections involving this node
    mindMap.connections = mindMap.connections.filter(
      conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
    );

    mindMap.analytics.editCount++;
    mindMap.analytics.lastEdited = new Date();

    await mindMap.save();

    return c.json({ message: 'Node deleted successfully' });
  } catch (error) {
    console.error('Error deleting node:', error);
    return c.json({ error: 'Failed to delete node' }, 500);
  }
});

// POST /api/mind-maps/:id/connections - Add connection to mind map
app.post('/:id/connections', async c => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const user = c.get('user');
    const userId = user?.id;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const mindMap = await MindMap.findById(id);
    if (!mindMap) {
      return c.json({ error: 'Mind map not found' }, 404);
    }

    // Check if user is the owner
    if (mindMap.userId.toString() !== userId) {
      return c.json({ error: 'Permission denied' }, 403);
    }

    const newConnection = {
      id: body.id || `conn-${Date.now()}`,
      sourceNodeId: body.sourceNodeId,
      targetNodeId: body.targetNodeId,
      type: body.type || 'smoothstep',
      style: body.style || { stroke: '#3b82f6', strokeWidth: 2 },
      label: body.label,
      labelStyle: body.labelStyle,
    };

    mindMap.connections.push(newConnection);
    mindMap.analytics.editCount++;
    mindMap.analytics.lastEdited = new Date();

    await mindMap.save();

    return c.json({ connection: newConnection });
  } catch (error) {
    console.error('Error adding connection:', error);
    return c.json({ error: 'Failed to add connection' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
