import React, { useMemo, useState } from 'react';
import { ConflictInfo, TeamMember } from './ResourceAllocationGrid';
import { IResourceAllocation } from '@/models/ResourceAllocation';
import { SKILL_CATEGORIES } from '@/constant/SkillCategory';
import { Card, CardContent, CardHeader } from '../ui/card';
import Image from 'next/image';
import {
  AlertTriangle,
  Calendar,
  ChevronDown,
  ChevronRight,
  Clock,
  HelpCircle,
  Settings,
} from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Progress } from '../ui/progress';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DndContext,
  closestCenter,
  DragEndEvent,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
} from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { DraggableAllocationCard } from './DraggableAllocationCard';

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' },
];

export const TeamMemberCard: React.FC<{
  member: TeamMember;
  onAllocationEdit: (allocation: IResourceAllocation) => void;
  onAllocationDelete: (allocationId: string) => void;
  conflicts: { [allocationId: string]: ConflictInfo[] };
  onViewSchedule: () => void;
  onEditMember: () => void;
}> = ({
  member,
  onAllocationEdit,
  onAllocationDelete,
  conflicts,
  onViewSchedule,
  onEditMember,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const onDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && over) {
      const oldIndex = member.allocations.findIndex(a => a.allocationId === active.id);
      const newIndex = member.allocations.findIndex(a => a.allocationId === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        // Update member.allocations with newAllocationOrder, handle save/update to backend
      }
    }
  };

  const utilizationColor =
    member.currentUtilization > 100
      ? 'text-red-500'
      : member.currentUtilization > 80
        ? 'text-yellow-500'
        : 'text-green-500';

  const skillsByCategory = useMemo(() => {
    const categorized: { [key: string]: string[] } = {};
    member.skills.forEach(skill => {
      const category =
        SKILL_CATEGORIES.find(cat => skill.toLowerCase().includes(cat.value))?.value || 'other';

      if (!categorized[category]) {
        categorized[category] = [];
      }
      categorized[category].push(skill);
    });
    return categorized;
  }, [member.skills]);

  // Get all conflicts for this member
  const memberConflicts = Object.values(conflicts).flat();
  const totalConflicts = memberConflicts.length;
  const highPriorityConflicts = memberConflicts.filter(c => c.severity === 'high').length;

  return (
    <Card
      className={`transition-all duration-200 ${member.isOverAllocated ? 'border-red-200 bg-red-50' : ''}`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                {member.avatar ? (
                  <Image
                    src={member.avatar}
                    alt={member.name}
                    width={48}
                    height={48}
                    className="w-12 h-12 rounded-full"
                  />
                ) : (
                  member.name.charAt(0).toUpperCase()
                )}
              </div>
              {member.isOverAllocated && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-2 w-2 text-white" />
                </div>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-lg">{member.name}</h3>
              <p className="text-sm text-muted-foreground">{member.email}</p>
              <p className="text-sm text-muted-foreground">{member.role}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {totalConflicts > 0 && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {highPriorityConflicts} critical
              </Badge>
            )}
            <Button variant="ghost" size="sm" onClick={onViewSchedule}>
              <Calendar className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onEditMember}>
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Capacity Overview */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${utilizationColor}`}>
              {member.currentUtilization}%
            </div>
            <p className="text-xs text-muted-foreground">Current Utilization</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{member.totalCapacity}%</div>
            <p className="text-xs text-muted-foreground">Total Capacity</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{member.availableHours}h</div>
            <p className="text-xs text-muted-foreground">Available Hours</p>
          </div>
        </div>

        {/* Utilization Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label className="text-sm">Capacity Utilization</Label>
            <span className="text-xs text-muted-foreground">
              {member.allocations.length} allocation{member.allocations.length !== 1 ? 's' : ''}
            </span>
          </div>
          <Progress
            value={Math.min(member.currentUtilization, 100)}
            className={`h-2 ${member.currentUtilization > 100 ? 'bg-red-100' : ''}`}
          />
          {member.currentUtilization > 100 && (
            <div className="mt-1 text-xs text-red-600">
              Over-allocated by {member.currentUtilization - 100}%
            </div>
          )}
        </div>

        {/* Skills Matrix */}
        <div>
          <Label className="text-sm mb-2 block">Skills</Label>
          <div className="flex flex-wrap gap-1">
            {Object.entries(skillsByCategory).map(([category, skills]) => {
              const categoryInfo = SKILL_CATEGORIES.find(c => c.value === category);
              const CategoryIcon = categoryInfo?.icon || HelpCircle;
              return skills.slice(0, 3).map((skill, index) => (
                <Badge
                  key={`${category}-${index}`}
                  variant="secondary"
                  className="text-xs flex items-center gap-1"
                >
                  <CategoryIcon className="h-3 w-3" />
                  {skill}
                </Badge>
              ));
            })}
            {member.skills.length > 6 && (
              <Badge variant="outline" className="text-xs">
                +{member.skills.length - 6} more
              </Badge>
            )}
          </div>
        </div>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="space-y-4 border-t pt-4"
            >
              {/* Current Allocations */}
              <div>
                <Label className="text-sm mb-3 block">Current Allocations</Label>
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={onDragEnd}
                >
                  <SortableContext
                    items={member.allocations.map(allocation => allocation.allocationId)}
                  >
                    <div className="space-y-2">
                      {member.allocations.map((allocation, index) => (
                        <DraggableAllocationCard
                          key={allocation.allocationId}
                          allocation={allocation}
                          conflicts={conflicts[allocation.allocationId] || []}
                          onEdit={onAllocationEdit}
                          onDelete={onAllocationDelete}
                          index={index}
                        />
                      ))}
                      {member.allocations.length === 0 && (
                        <div className="text-center py-4 text-muted-foreground text-sm">
                          No current allocations
                        </div>
                      )}
                    </div>
                  </SortableContext>
                </DndContext>
              </div>

              {/* Weekly Schedule Overview */}
              <div>
                <Label className="text-sm mb-3 block">Weekly Schedule</Label>
                <div className="grid grid-cols-7 gap-1 text-xs">
                  {DAYS_OF_WEEK.map(day => (
                    <div key={day.value} className="text-center">
                      <div className="font-medium text-muted-foreground mb-1">{day.short}</div>
                      <div className="h-8 bg-muted rounded flex items-center justify-center">
                        {/* This would show actual schedule data */}
                        <Clock className="h-3 w-3 text-muted-foreground" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cost Information */}
              <div className="flex justify-between items-center p-3 bg-muted rounded">
                <span className="text-sm font-medium">Estimated Cost</span>
                <span className="text-sm font-bold">${member.cost.toLocaleString()}/month</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
};
