/* eslint-disable no-console */
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { getUserPermissions as fetchUserPermissions } from '@/services/Permission.service';

export interface PermissionCheck {
  resource: string;
  action: string;
  resourceId?: string;
  organizationId?: string;
}

export interface PermissionMiddlewareConfig {
  enabled: boolean;
  publicRoutes: string[];
  adminOnlyRoutes: string[];
  bypassRoutes: string[];
  strictMode: boolean;
  cacheEnabled: boolean;
  cacheTTL: number;
}

export interface UserPermissions {
  userId: string;
  roles: string[];
  permissions: string[];
  organizationId?: string;
  isAdmin: boolean;
  isOwner: boolean;
}

class PermissionMiddleware {
  private config: PermissionMiddlewareConfig;
  private permissionCache: Map<string, { permissions: UserPermissions; timestamp: number }> =
    new Map();

  constructor(config: Partial<PermissionMiddlewareConfig> = {}) {
    this.config = {
      enabled: process.env.PERMISSION_MIDDLEWARE_ENABLED !== 'false',
      publicRoutes: [
        '/api/auth',
        '/api/health',
        '/api/public',
        '/api/webhooks',
        '/api/share-target',
        ...(config.publicRoutes || []),
      ],
      adminOnlyRoutes: [
        '/api/admin',
        '/api/organizations',
        '/api/permissions',
        '/api/roles',
        '/api/settings/general',
        '/api/activity-logs',
        ...(config.adminOnlyRoutes || []),
      ],
      bypassRoutes: [
        '/api/auth/callback',
        '/api/auth/signin',
        '/api/auth/signout',
        '/api/auth/session',
        '/_next',
        '/favicon.ico',
        ...(config.bypassRoutes || []),
      ],
      strictMode: config.strictMode !== false,
      cacheEnabled: config.cacheEnabled !== false,
      cacheTTL: config.cacheTTL || 300000, // 5 minutes
      ...config,
    };
  }

  private shouldBypass(request: NextRequest): boolean {
    const pathname = request.nextUrl.pathname;

    return this.config.bypassRoutes.some(route => pathname.startsWith(route));
  }

  private isPublicRoute(request: NextRequest): boolean {
    const pathname = request.nextUrl.pathname;

    return this.config.publicRoutes.some(route => pathname.startsWith(route));
  }

  private isAdminOnlyRoute(request: NextRequest): boolean {
    const pathname = request.nextUrl.pathname;

    return this.config.adminOnlyRoutes.some(route => pathname.startsWith(route));
  }

  private getCacheKey(userId: string, organizationId?: string): string {
    return `${userId}:${organizationId || 'no-org'}`;
  }

  /**
   * Get user permissions, using cache if enabled.
   * @param userId - The user ID (must be string, not null/undefined)
   * @param organizationId - The organization ID (optional)
   */
  public async getUserPermissions(
    userId: string,
    organizationId?: string
  ): Promise<UserPermissions | null> {
    if (!userId || typeof userId !== 'string') return null;
    const cacheKey = this.getCacheKey(userId, organizationId);

    // Check cache first
    if (this.config.cacheEnabled && this.permissionCache.has(cacheKey)) {
      const cached = this.permissionCache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < this.config.cacheTTL) {
        return cached.permissions;
      }
      this.permissionCache.delete(cacheKey);
    }

    try {
      // fetchUserPermissions expects userId as string, organizationId as string | undefined
      const orgId =
        organizationId && typeof organizationId === 'string' ? organizationId : undefined;
      const userPermissions = await fetchUserPermissions(userId, undefined, undefined, orgId);

      if (this.config.cacheEnabled && userPermissions) {
        this.permissionCache.set(cacheKey, {
          permissions: userPermissions,
          timestamp: Date.now(),
        });
      }

      return userPermissions;
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      return null;
    }
  }

  private async getUserSession(_request: NextRequest) {
    try {
      const session = await getServerSession(authOptions);
      return session;
    } catch (error) {
      console.error('Error getting user session:', error);
      return null;
    }
  }

  private determinePermissionCheck(request: NextRequest): PermissionCheck | null {
    const pathname = request.nextUrl.pathname;
    const method = request.method;
    const pathSegments = pathname.split('/').filter(Boolean);

    if (!pathSegments.includes('api')) {
      return null;
    }

    const apiIndex = pathSegments.indexOf('api');
    const resource = pathSegments[apiIndex + 1];

    if (!resource) {
      return null;
    }

    // Determine action based on HTTP method
    let action: string;
    switch (method.toUpperCase()) {
      case 'GET':
        action = 'read';
        break;
      case 'POST':
        action = 'create';
        break;
      case 'PUT':
      case 'PATCH':
        action = 'update';
        break;
      case 'DELETE':
        action = 'delete';
        break;
      default:
        action = 'access';
        break;
    }

    // Extract resource ID if present
    const resourceId = this.extractResourceId(pathSegments, request.nextUrl.searchParams);

    return {
      resource,
      action,
      resourceId,
    };
  }

  private extractResourceId(
    pathSegments: string[],
    searchParams: URLSearchParams
  ): string | undefined {
    // Look for UUID patterns in path
    const uuidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const numericPattern = /^\d+$/;

    for (const segment of pathSegments) {
      if (uuidPattern.test(segment) || numericPattern.test(segment)) {
        return segment;
      }
    }

    // Check common ID parameters
    const commonIdParams = ['id', 'taskId', 'projectId', 'noteId', 'userId', 'organizationId'];
    for (const param of commonIdParams) {
      const value = searchParams.get(param);
      if (value) {
        return value;
      }
    }

    return undefined;
  }

  /**
   * Check if a user has the required permission.
   * @param userPermissions - The user's permissions object
   * @param permissionCheck - The permission check object
   */
  public async checkPermission(
    userPermissions: UserPermissions,
    permissionCheck: PermissionCheck
  ): Promise<boolean> {
    const { resource, action, resourceId } = permissionCheck;

    // Super admin bypass
    if (userPermissions.isOwner) {
      return true;
    }

    // Check if user has admin role
    if (userPermissions.isAdmin) {
      // Admin can access most resources except owner-only operations
      const ownerOnlyActions = ['delete_organization', 'transfer_ownership', 'billing'];
      const isOwnerOnly = ownerOnlyActions.some(ownerAction =>
        `${resource}.${action}`.includes(ownerAction)
      );

      if (!isOwnerOnly) {
        return true;
      }
    }

    // Check specific permission
    const permissionString = `${resource}.${action}`;
    if (userPermissions.permissions.includes(permissionString)) {
      return true;
    }

    // Check wildcard permissions
    const wildcardPermission = `${resource}.*`;
    if (userPermissions.permissions.includes(wildcardPermission)) {
      return true;
    }

    // Resource-level permission check
    if (resourceId) {
      const resourcePermission = `${resource}.${action}.${resourceId}`;
      if (userPermissions.permissions.includes(resourcePermission)) {
        return true;
      }
    }

    // Role-based permission check
    for (const role of userPermissions.roles) {
      const rolePermission = `role.${role}.${resource}.${action}`;
      if (userPermissions.permissions.includes(rolePermission)) {
        return true;
      }
    }

    return false;
  }

  private createUnauthorizedResponse(message: string = 'Unauthorized'): NextResponse {
    return NextResponse.json(
      {
        error: 'Unauthorized',
        message,
        timestamp: new Date().toISOString(),
      },
      { status: 401 }
    );
  }

  private createForbiddenResponse(message: string = 'Forbidden'): NextResponse {
    return NextResponse.json(
      {
        error: 'Forbidden',
        message,
        timestamp: new Date().toISOString(),
      },
      { status: 403 }
    );
  }

  async middleware(request: NextRequest): Promise<NextResponse> {
    if (!this.config.enabled) {
      return NextResponse.next();
    }

    // Bypass routes that don't need permission checks
    if (this.shouldBypass(request)) {
      return NextResponse.next();
    }

    // Allow public routes
    if (this.isPublicRoute(request)) {
      return NextResponse.next();
    }

    // Get user session
    const session = await this.getUserSession(request);

    if (!session?.user?.id || typeof session.user.id !== 'string') {
      console.warn(`[PERMISSION] Authentication required for ${request.nextUrl.pathname}`);
      return this.createUnauthorizedResponse('Authentication required');
    }

    const userId = session.user.id;
    const organizationId =
      session.user.organizationId && typeof session.user.organizationId === 'string'
        ? session.user.organizationId
        : undefined;

    // Get user permissions
    const userPermissions = await this.getUserPermissions(userId, organizationId);

    if (!userPermissions) {
      console.warn(`[PERMISSION] Unable to determine user permissions for user ${userId}`);
      return this.createForbiddenResponse('Unable to determine user permissions');
    }

    // Check admin-only routes
    if (this.isAdminOnlyRoute(request)) {
      if (!userPermissions.isAdmin && !userPermissions.isOwner) {
        console.warn(
          `[PERMISSION] Admin access required for ${request.nextUrl.pathname} by user ${userId}`
        );
        return this.createForbiddenResponse('Admin access required');
      }
      return NextResponse.next();
    }

    // Determine required permission
    const permissionCheck = this.determinePermissionCheck(request);

    if (!permissionCheck) {
      // If we can't determine the permission, allow in non-strict mode
      if (!this.config.strictMode) {
        return NextResponse.next();
      }
      console.warn(
        `[PERMISSION] Cannot determine required permissions for ${request.nextUrl.pathname}`
      );
      return this.createForbiddenResponse('Cannot determine required permissions');
    }

    // Add organization context to permission check
    permissionCheck.organizationId = organizationId ?? undefined;

    // Check permission
    const hasPermission = await this.checkPermission(userPermissions, permissionCheck);

    if (!hasPermission) {
      console.warn(
        `[PERMISSION] Insufficient permissions for ${permissionCheck.resource}.${permissionCheck.action} by user ${userId}`
      );
      return this.createForbiddenResponse(
        `Insufficient permissions for ${permissionCheck.resource}.${permissionCheck.action}`
      );
    }

    // Add user context to request headers for downstream use
    const response = NextResponse.next();
    response.headers.set('x-user-id', userId);
    response.headers.set('x-user-roles', userPermissions.roles.join(','));
    response.headers.set('x-user-permissions', userPermissions.permissions.join(','));
    if (organizationId) {
      response.headers.set('x-organization-id', organizationId);
    }
    response.headers.set('x-is-admin', userPermissions.isAdmin.toString());
    response.headers.set('x-is-owner', userPermissions.isOwner.toString());

    return response;
  }

  // Utility methods for manual permission checks
  async checkUserPermission(
    userId: string,
    resource: string,
    action: string,
    resourceId?: string,
    organizationId?: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, organizationId);

    if (!userPermissions) {
      return false;
    }

    return this.checkPermission(userPermissions, {
      resource,
      action,
      resourceId,
      organizationId,
    });
  }

  async requirePermission(
    userId: string,
    resource: string,
    action: string,
    resourceId?: string,
    organizationId?: string
  ): Promise<void> {
    const hasPermission = await this.checkUserPermission(
      userId,
      resource,
      action,
      resourceId,
      organizationId
    );

    if (!hasPermission) {
      throw new Error(`Insufficient permissions for ${resource}.${action}`);
    }
  }

  // Cache management
  /**
   * Remove expired cache entries. Should be called periodically in production.
   */
  public clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.permissionCache.entries()) {
      if (now - value.timestamp >= this.config.cacheTTL) {
        this.permissionCache.delete(key);
      }
    }
  }

  updateConfig(newConfig: Partial<PermissionMiddlewareConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): PermissionMiddlewareConfig {
    return { ...this.config };
  }
}

// Singleton instance
let permissionMiddlewareInstance: PermissionMiddleware | null = null;

export function getPermissionMiddleware(
  config?: Partial<PermissionMiddlewareConfig>
): PermissionMiddleware {
  if (!permissionMiddlewareInstance) {
    permissionMiddlewareInstance = new PermissionMiddleware(config);
  }
  return permissionMiddlewareInstance;
}

// Middleware function for Next.js
export function createPermissionMiddleware(config?: Partial<PermissionMiddlewareConfig>) {
  const middleware = getPermissionMiddleware(config);

  return (request: NextRequest) => {
    return middleware.middleware(request);
  };
}

/**
 * Helper function to check permissions in API routes. Throws on failure.
 * @param request - NextRequest
 * @param resource - Resource name
 * @param action - Action name
 * @param resourceId - Resource ID (optional)
 * @returns UserPermissions if allowed
 */
export async function requirePermission(
  resource: string,
  action: string,
  resourceId?: string
): Promise<UserPermissions> {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id || typeof session.user.id !== 'string') {
    throw new Error('Authentication required');
  }

  const middleware = getPermissionMiddleware();
  const userId = session.user.id;
  const organizationId =
    session.user.organizationId && typeof session.user.organizationId === 'string'
      ? session.user.organizationId
      : undefined;
  if (!userId) {
    throw new Error('Authentication required');
  }
  const userPermissions = await middleware.getUserPermissions(userId, organizationId);

  if (!userPermissions) {
    throw new Error('Unable to determine user permissions');
  }

  const hasPermission = await middleware.checkPermission(userPermissions, {
    resource,
    action,
    resourceId,
    organizationId: organizationId ?? undefined,
  });

  if (!hasPermission) {
    throw new Error(`Insufficient permissions for ${resource}.${action}`);
  }

  return userPermissions;
}

/**
 * Helper function to check if user has permission (non-throwing)
 */
export async function hasPermission(
  resource: string,
  action: string,
  resourceId?: string
): Promise<boolean> {
  try {
    await requirePermission(resource, action, resourceId);
    return true;
  } catch (err) {
    return false;
  }
}

// Export default instance
export const permissionMiddleware = getPermissionMiddleware();
