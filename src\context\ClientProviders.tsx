'use client';

import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { SessionProvider } from 'next-auth/react';
import AuthProvider from '@/context/AuthProvider';
import Providers from '@/components/Providers/query-provider';

const ProvidersErrorFallback = ({
  error,
  resetErrorBoundary,
}: {
  error: Error;
  resetErrorBoundary: () => void;
}) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center">
      <div className="max-w-md mx-auto">
        <h2 className="text-xl font-semibold mb-4 text-destructive">
          Application Initialization Failed
        </h2>
        <p className="text-muted-foreground mb-6">
          The application failed to initialize properly. This might be due to a network issue or
          browser compatibility problem.
        </p>
        <div className="space-y-3">
          <button
            onClick={resetErrorBoundary}
            className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.reload()}
            className="w-full px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
          >
            Reload Page
          </button>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-muted-foreground">
              Error Details
            </summary>
            <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-h-32">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
};

const SessionProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ProvidersErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Session Provider Error:', error, errorInfo);
      }}
    >
      <SessionProvider
        refetchInterval={5 * 60}
        refetchOnWindowFocus={true}
        refetchWhenOffline={false}
      >
        {children}
      </SessionProvider>
    </ErrorBoundary>
  );
};

const AuthProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ProvidersErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Auth Provider Error:', error, errorInfo);
      }}
    >
      <AuthProvider>{children}</AuthProvider>
    </ErrorBoundary>
  );
};

const QueryProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ProvidersErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Query Provider Error:', error, errorInfo);
      }}
    >
      <Providers>{children}</Providers>
    </ErrorBoundary>
  );
};

export default function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      FallbackComponent={ProvidersErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Client Providers Error:', error, errorInfo);
      }}
    >
      <SessionProviderWrapper>
        <QueryProviderWrapper>
          <AuthProviderWrapper>{children}</AuthProviderWrapper>
        </QueryProviderWrapper>
      </SessionProviderWrapper>
    </ErrorBoundary>
  );
}
