import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { zValidator } from '@hono/zod-validator';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { PushNotificationService } from '@/services/PushNotification.service';
import { User } from '@/models/User';
import { connectDB } from '@/Utility/db';
import { z } from 'zod';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    systemRole?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/push/send');

app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      await connectDB();
      const user = await User.findById(session.user.id).select('systemRole');
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        systemRole: user?.systemRole || 'User',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Auth middleware error:', error);
  }
  await next();
});

// Schema for push notification payload
const pushNotificationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  body: z.string().min(1, 'Body is required'),
  icon: z.string().optional(),
  badge: z.string().optional(),
  data: z.object({
    url: z.string().optional(),
  }).optional(),
  actions: z.array(z.object({
    action: z.string(),
    title: z.string(),
    icon: z.string().optional(),
  })).optional(),
  tag: z.string().optional(),
  renotify: z.boolean().optional(),
  silent: z.boolean().optional(),
  requireInteraction: z.boolean().optional(),
});

// Schema for sending to specific users
const sendToUsersSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  notification: pushNotificationSchema,
});

// Schema for sending to all users
const sendToAllSchema = z.object({
  notification: pushNotificationSchema,
});

// GET - Check push notification configuration and status
app.get('/', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  // Only allow admins to check configuration
  if (user.systemRole !== 'Admin') {
    return c.json({ error: 'Forbidden - Admin access required' }, 403);
  }

  try {
    const configStatus = PushNotificationService.getConfigStatus();
    const stats = await PushNotificationService.getSubscriptionStats();
    const connectivity = await PushNotificationService.testPushServiceConnectivity();
    const recommendations = await PushNotificationService.getCleanupRecommendations();

    return c.json({
      configuration: configStatus,
      subscriptionStats: stats,
      connectivity,
      recommendations,
      isConfigured: PushNotificationService.isConfigured(),
    });
  } catch (error: any) {
    console.error('Push configuration check error:', error);
    return c.json(
      { error: 'Failed to check push notification configuration' },
      500
    );
  }
});

// POST - Send push notification to specific users
app.post('/users', zValidator('json', sendToUsersSchema), async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  // Only allow admins to send push notifications
  if (user.systemRole !== 'Admin') {
    return c.json({ error: 'Forbidden - Admin access required' }, 403);
  }

  try {
    if (!PushNotificationService.isConfigured()) {
      return c.json(
        { error: 'Push notifications are not configured' },
        500
      );
    }

    const { userIds, notification } = c.req.valid('json');

    const result = await PushNotificationService.sendToUsers(userIds, notification);

    return c.json({
      success: true,
      message: `Push notifications sent to ${result.successful} users, ${result.failed} failed`,
      results: {
        successful: result.successful,
        failed: result.failed,
        details: result.results,
      },
    });
  } catch (error: any) {
    console.error('Push notification send error:', error);
    return c.json(
      { error: 'Failed to send push notifications' },
      500
    );
  }
});

// POST - Send push notification to all subscribed users
app.post('/all', zValidator('json', sendToAllSchema), async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  // Only allow admins to send push notifications
  if (user.systemRole !== 'Admin') {
    return c.json({ error: 'Forbidden - Admin access required' }, 403);
  }

  try {
    if (!PushNotificationService.isConfigured()) {
      return c.json(
        { error: 'Push notifications are not configured' },
        500
      );
    }

    const { notification } = c.req.valid('json');

    const result = await PushNotificationService.sendToAllSubscribed(notification);

    return c.json({
      success: true,
      message: `Push notifications sent to ${result.successful} users, ${result.failed} failed`,
      results: {
        successful: result.successful,
        failed: result.failed,
      },
    });
  } catch (error: any) {
    console.error('Push notification broadcast error:', error);
    return c.json(
      { error: 'Failed to broadcast push notifications' },
      500
    );
  }
});

// POST - Send test push notification to current user
app.post('/test', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    if (!PushNotificationService.isConfigured()) {
      return c.json(
        { error: 'Push notifications are not configured' },
        500
      );
    }

    const testNotification = {
      title: 'Test Notification',
      body: `Hello ${user.name || 'User'}! This is a test push notification.`,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      data: {
        url: '/notifications',
      },
      tag: 'test-notification',
      requireInteraction: false,
    };

    const success = await PushNotificationService.sendToUser(user.id, testNotification);

    if (success) {
      return c.json({
        success: true,
        message: 'Test push notification sent successfully',
      });
    } else {
      return c.json({
        success: false,
        message: 'Failed to send test push notification. You may not have a valid push subscription.',
      }, 400);
    }
  } catch (error: any) {
    console.error('Test push notification error:', error);
    return c.json(
      { error: 'Failed to send test push notification' },
      500
    );
  }
});

export const GET = handle(app);
export const POST = handle(app);
