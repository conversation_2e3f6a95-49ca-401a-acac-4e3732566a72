import React, { useCallback } from 'react';
import {
  startOfWeek,
  startOfMonth,
  endOfWeek,
  endOfMonth,
  addDays,
  isSameDay,
  isToday,
} from 'date-fns';
import { IEvent } from '@/models/Event';
import DraggableEvent from './DraggableEvent';
import DroppableTimeSlot from './DroppableTimeSlot';

interface CalendarViewProps {
  events: IEvent[];
  currentDate: Date;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  onEventClick: (event: IEvent) => void;
  selectedEvent?: IEvent | null;
  isCreatingEvent?: boolean;
}

const MonthView: React.FC<CalendarViewProps> = ({
  events,
  currentDate,
  onEventClick,
  selectedEvent,
}) => {
  const startDate = startOfWeek(startOfMonth(currentDate));
  const endDate = endOfWeek(endOfMonth(currentDate));
  const days: Date[] = [];
  let day = new Date(startDate);
  while (day <= endDate) {
    days.push(new Date(day));
    day = addDays(day, 1);
  }

  const getDayEvents = useCallback(
    (date: Date) => {
      return events.filter(event => {
        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);
        return isSameDay(eventStart, date) || (eventStart <= date && eventEnd > date);
      });
    },
    [events]
  );

  return (
    <div className="grid grid-cols-7 gap-0 border theme-border theme-surface">
      {/* Header */}
      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
        <div
          key={day}
          className="p-3 bg-secondary border-b theme-border font-medium text-center text-sm theme-text-primary"
        >
          {day}
        </div>
      ))}

      {/* Days */}
      {days.map(day => {
        const dayEvents = getDayEvents(day);
        const isCurrentMonth = day.getMonth() === currentDate.getMonth();
        const isSelected =
          selectedEvent && dayEvents.some(e => String(e._id) === String(selectedEvent._id));

        return (
          <DroppableTimeSlot
            key={day.toISOString()}
            date={day}
            hour={0}
            minute={0}
            viewType="month"
            onClick={() => {}}
            className={`
              min-h-[120px] p-2 border-r border-b theme-border
              ${!isCurrentMonth ? 'bg-secondary text-muted-foreground' : ''}
              ${isSelected ? 'ring-2 ring-primary' : ''}
            `}
          >
            <div className="space-y-1">
              <div
                className={`text-sm font-medium ${isToday(day) ? 'text-primary font-bold' : 'theme-text-primary'}`}
              >
                {day.getDate()}
              </div>
              <div className="space-y-1">
                {dayEvents.slice(0, 3).map(event => (
                  <DraggableEvent
                    key={String(event._id)}
                    event={event}
                    viewType="month"
                    onClick={onEventClick}
                  />
                ))}
                {dayEvents.length > 3 && (
                  <div className="text-xs theme-text-secondary font-medium">
                    +{dayEvents.length - 3} more
                  </div>
                )}
              </div>
            </div>
          </DroppableTimeSlot>
        );
      })}
    </div>
  );
};

export default MonthView;
