'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Save, Loader2, Check, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SaveButtonProps {
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  saveError: string | null;
  onSave: () => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'lg' | 'default';
  variant?: 'default' | 'outline' | 'ghost';
}

export function SaveButton({
  hasUnsavedChanges,
  isSaving,
  saveError,
  onSave,
  disabled = false,
  className,
  size = 'sm',
}: SaveButtonProps) {
  const isDisabled = disabled || isSaving || (!hasUnsavedChanges && !saveError);

  const getButtonContent = () => {
    if (isSaving) {
      return (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Saving...
        </>
      );
    }

    if (saveError) {
      return (
        <>
          <AlertCircle className="w-4 h-4 mr-2" />
          Retry Save
        </>
      );
    }

    if (hasUnsavedChanges) {
      return (
        <>
          <Save className="w-4 h-4 mr-2" />
          Save
        </>
      );
    }

    return (
      <>
        <Check className="w-4 h-4 mr-2" />
        Saved
      </>
    );
  };

  const getButtonVariant = () => {
    if (saveError) return 'destructive';
    if (hasUnsavedChanges) return 'default';
    return 'outline';
  };

  const getButtonClasses = () => {
    const baseClasses = 'theme-transition';

    if (saveError) {
      return cn(baseClasses, 'theme-button-destructive hover:bg-destructive/90');
    }

    if (hasUnsavedChanges) {
      return cn(baseClasses, 'theme-button-primary');
    }

    return cn(baseClasses, 'theme-button-ghost');
  };

  return (
    <Button
      variant={getButtonVariant() as any}
      size={size}
      onClick={onSave}
      disabled={isDisabled}
      className={cn(getButtonClasses(), className)}
      aria-label={
        isSaving
          ? 'Saving changes...'
          : saveError
            ? 'Retry saving changes'
            : hasUnsavedChanges
              ? 'Save unsaved changes'
              : 'All changes saved'
      }
    >
      {getButtonContent()}
    </Button>
  );
}
