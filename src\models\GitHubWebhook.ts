import mongoose, { Schema, Document } from 'mongoose';

export interface IGitHubWebhook extends Document {
  repositoryId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  organizationId?: mongoose.Types.ObjectId;
  githubWebhookId: string; // GitHub webhook ID
  webhookUrl: string;
  secret: string;
  events: string[];
  active: boolean;
  lastDeliveryAt?: Date;
  deliveryCount: number;
  errorCount: number;
  lastError?: {
    message: string;
    timestamp: Date;
    statusCode?: number;
  };
  config: {
    url: string;
    contentType: string;
    insecureSsl: boolean;
  };
  metadata?: any;
}

const GitHubWebhookSchema = new Schema<IGitHubWebhook>(
  {
    repositoryId: {
      type: Schema.Types.ObjectId,
      ref: 'GitHubRepository',
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    organizationId: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
    },
    githubWebhookId: {
      type: String,
      required: true,
    },
    webhookUrl: {
      type: String,
      required: true,
    },
    secret: {
      type: String,
      required: true,
    },
    events: {
      type: [String],
      required: true,
      default: ['issues', 'pull_request'],
    },
    active: {
      type: Boolean,
      default: true,
    },
    lastDeliveryAt: {
      type: Date,
    },
    deliveryCount: {
      type: Number,
      default: 0,
    },
    errorCount: {
      type: Number,
      default: 0,
    },
    lastError: {
      message: {
        type: String,
      },
      timestamp: {
        type: Date,
      },
      statusCode: {
        type: Number,
      },
    },
    config: {
      url: {
        type: String,
        required: true,
      },
      contentType: {
        type: String,
        default: 'application/json',
      },
      insecureSsl: {
        type: Boolean,
        default: false,
      },
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
GitHubWebhookSchema.index({ repositoryId: 1, githubWebhookId: 1 }, { unique: true });
GitHubWebhookSchema.index({ userId: 1, active: 1 });
GitHubWebhookSchema.index({ organizationId: 1, active: 1 });

export const GitHubWebhook =
  mongoose.models.GitHubWebhook ||
  mongoose.model<IGitHubWebhook>('GitHubWebhook', GitHubWebhookSchema);
