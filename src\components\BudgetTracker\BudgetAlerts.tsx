'use client';

import { IBudget } from '@/models/Budget';
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON><PERSON><PERSON>gle, Bell, CheckCircle, Settings } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
interface BudgetAlert {
  type: 'error' | 'warning';
  message: string;
  category: string;
}

export const BudgetAlerts: React.FC<{
  budget: IBudget;
  onUpdateAlerts?: (alerts: BudgetAlert[]) => void;
}> = ({ budget, onUpdateAlerts }) => {
  const [_showSettings, setShowSettings] = useState(false);
  const alerts: BudgetAlert[] = [];

  // Check for over-budget categories
  budget.categories.forEach(category => {
    const utilization =
      category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0;
    if (utilization >= 100) {
      alerts.push({
        type: 'error',
        message: `${category.name} category is over budget by ${(category.spentAmount - category.allocatedAmount).toLocaleString()}`,
        category: category.name,
      });
    } else if (utilization >= budget.alerts.thresholdPercentage) {
      alerts.push({
        type: 'warning',
        message: `${category.name} category has reached ${utilization.toFixed(1)}% of allocated budget`,
        category: category.name,
      });
    }
  });

  // Check overall budget
  const overallUtilization =
    budget.totalBudget > 0 ? (budget.spentAmount / budget.totalBudget) * 100 : 0;
  if (overallUtilization >= 100) {
    alerts.push({
      type: 'error',
      message: `Total budget exceeded by ${(budget.spentAmount - budget.totalBudget).toLocaleString()}`,
      category: 'Overall',
    });
  } else if (overallUtilization >= budget.alerts.thresholdPercentage) {
    alerts.push({
      type: 'warning',
      message: `Overall budget utilization at ${overallUtilization.toFixed(1)}%`,
      category: 'Overall',
    });
  }

  // Notify parent component about alerts if callback is provided
  if (onUpdateAlerts) {
    onUpdateAlerts(alerts);
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Budget Alerts
          {alerts.length > 0 && <Badge variant="destructive">{alerts.length}</Badge>}
        </CardTitle>
        <Button variant="outline" size="sm" onClick={() => setShowSettings(true)}>
          <Settings className="h-4 w-4 mr-1" />
          Settings
        </Button>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p>All budgets are within limits</p>
          </div>
        ) : (
          <div className="space-y-2">
            {alerts.map((alert, index) => (
              <Alert key={index} variant={alert.type === 'error' ? 'destructive' : 'default'}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{alert.message}</AlertDescription>
              </Alert>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
