import { RedisHelper } from '../lib/redis';

export class CacheService {
  // Cache TTL constants (in seconds)
  private static readonly DEFAULT_TTL = 180; // Reduced from 300 to 180 (3 minutes)
  private static readonly SHORT_TTL = 60; // Reduced from 120 to 60 (1 minute)
  private static readonly MEDIUM_TTL = 300; // Reduced from 600 to 300 (5 minutes)
  private static readonly LONG_TTL = 900; // Reduced from 1800 to 900 (15 minutes)

  // Set cache with default TTL
  static async set(key: string, data: any, ttl: number = this.DEFAULT_TTL): Promise<boolean> {
    if (!RedisHelper.isConnected()) {
      console.warn('Redis not connected, skipping cache set');
      return false;
    }

    try {
      await RedisHelper.setJSON(key, data, ttl);
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  // Get cache
  static async get<T = any>(key: string): Promise<T | null> {
    if (!RedisHelper.isConnected()) {
      console.warn('Redis not connected, skipping cache get');
      return null;
    }

    try {
      return await RedisHelper.getJSON<T>(key);
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  // Delete cache
  static async delete(key: string): Promise<boolean> {
    if (!RedisHelper.isConnected()) {
      console.warn('Redis not connected, skipping cache delete');
      return false;
    }

    try {
      await RedisHelper.deleteByPattern(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  // Set with short TTL
  static async setShort(key: string, data: any): Promise<boolean> {
    return this.set(key, data, this.SHORT_TTL);
  }

  // Set with medium TTL
  static async setMedium(key: string, data: any): Promise<boolean> {
    return this.set(key, data, this.MEDIUM_TTL);
  }

  // Set with long TTL
  static async setLong(key: string, data: any): Promise<boolean> {
    return this.set(key, data, this.LONG_TTL);
  }

  // Generate cache key
  static generateKey(prefix: string, ...parts: string[]): string {
    return RedisHelper.generateKey(prefix, ...parts);
  }

  // Clear cache by pattern
  static async clearByPattern(pattern: string): Promise<boolean> {
    if (!RedisHelper.isConnected()) {
      console.warn('Redis not connected, skipping cache clear');
      return false;
    }

    try {
      await RedisHelper.deleteByPattern(pattern);
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  // User-specific cache methods
  static async setUserCache(
    userId: string,
    key: string,
    data: any,
    ttl?: number
  ): Promise<boolean> {
    const cacheKey = this.generateKey('user', userId, key);
    return this.set(cacheKey, data, ttl);
  }

  static async getUserCache<T = any>(userId: string, key: string): Promise<T | null> {
    const cacheKey = this.generateKey('user', userId, key);
    return this.get<T>(cacheKey);
  }

  static async clearUserCache(userId: string): Promise<boolean> {
    const pattern = this.generateKey('user', userId, '*');
    return this.clearByPattern(pattern);
  }

  // Project-specific cache methods
  static async setProjectCache(
    projectId: string,
    key: string,
    data: any,
    ttl?: number
  ): Promise<boolean> {
    const cacheKey = this.generateKey('project', projectId, key);
    return this.set(cacheKey, data, ttl);
  }

  static async getProjectCache<T = any>(projectId: string, key: string): Promise<T | null> {
    const cacheKey = this.generateKey('project', projectId, key);
    return this.get<T>(cacheKey);
  }

  static async clearProjectCache(projectId: string): Promise<boolean> {
    const pattern = this.generateKey('project', projectId, '*');
    return this.clearByPattern(pattern);
  }

  // Organization-specific cache methods
  static async setOrgCache(orgId: string, key: string, data: any, ttl?: number): Promise<boolean> {
    const cacheKey = this.generateKey('org', orgId, key);
    return this.set(cacheKey, data, ttl);
  }

  static async getOrgCache<T = any>(orgId: string, key: string): Promise<T | null> {
    const cacheKey = this.generateKey('org', orgId, key);
    return this.get<T>(cacheKey);
  }

  static async clearOrgCache(orgId: string): Promise<boolean> {
    const pattern = this.generateKey('org', orgId, '*');
    return this.clearByPattern(pattern);
  }

  // Check if Redis is available
  static isAvailable(): boolean {
    return RedisHelper.isConnected();
  }

  // Health check method
  static async healthCheck(): Promise<boolean> {
    if (!RedisHelper.isConnected()) {
      return false;
    }

    try {
      const testKey = 'health_check_' + Date.now();
      const testData = { timestamp: Date.now() };

      // Test set and get with timeout
      await this.set(testKey, testData, 10);
      const retrieved = await this.get(testKey);
      await this.delete(testKey);

      return retrieved !== null && retrieved.timestamp === testData.timestamp;
    } catch (error) {
      console.error('Cache health check failed:', error);
      return false;
    }
  }
}
