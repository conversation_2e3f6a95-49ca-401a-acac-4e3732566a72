'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink,
  Plus,
  User,
  Calendar,
  MessageSquare,
  Tag,
  GitFork,
  Lock,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { GitHubIssue } from '@/types/GitHubTypes';

interface GitHubIssueCardProps {
  issue: GitHubIssue;
  index: number;
  onCreateTask: (issueNumber: number) => void;
  isCreatingTask?: boolean;
  className?: string;
}

export const GitHubIssueCard: React.FC<GitHubIssueCardProps> = ({
  issue,
  index,
  onCreateTask,
  isCreatingTask = false,
  className,
}) => {
  const safeIssue = {
    id: issue?.id || 0,
    number: issue?.number || 0,
    title: issue?.title || 'Untitled Issue',
    body: issue?.body || '',
    state: issue?.state || 'open',
    html_url: issue?.html_url || '#',
    user: {
      login: issue?.user?.login || 'Unknown',
      avatar_url: issue?.user?.avatar_url || '',
    },
    assignees: issue?.assignees || [],
    labels: issue?.labels || [],
    created_at: issue?.created_at || new Date().toISOString(),
    updated_at: issue?.updated_at || new Date().toISOString(),
    closed_at: issue?.closed_at,
    comments: issue?.comments || 0,
    repository: issue?.repository
      ? {
          id: issue.repository.id || '',
          name: issue.repository.name || 'Unknown',
          fullName: issue.repository.fullName || '',
          htmlUrl: issue.repository.htmlUrl || '#',
          private: issue.repository.private || false,
          language: issue.repository.language,
        }
      : null,
  };

  const isOpen = safeIssue.state === 'open';
  const hasAssignees = safeIssue.assignees.length > 0;
  const hasLabels = safeIssue.labels.length > 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.02, duration: 0.3 }}
      className={className}
    >
      <Card className="theme-surface-elevated hover-reveal glow-on-hover theme-transition">
        <CardContent className="p-3">
          <div className="flex items-start gap-3">
            {/* Issue Status Icon - Compact */}
            <div className="mt-0.5 flex-shrink-0">
              {isOpen ? (
                <AlertCircle className="h-4 w-4 text-success theme-transition" />
              ) : (
                <CheckCircle className="h-4 w-4 text-purple-600 dark:text-purple-400 theme-transition" />
              )}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between gap-2 mb-1">
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  {safeIssue.repository && (
                    <Badge
                      variant="secondary"
                      className="text-xs shrink-0 theme-transition bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/20"
                    >
                      <GitFork className="h-2.5 w-2.5 mr-1" />
                      {safeIssue.repository.name}
                    </Badge>
                  )}
                  <Badge variant="outline" className="text-xs shrink-0 theme-transition">
                    #{safeIssue.number}
                  </Badge>
                  {safeIssue.repository?.private && (
                    <Badge variant="outline" className="text-xs shrink-0 theme-transition">
                      <Lock className="h-2 w-2 mr-1" />
                      Private
                    </Badge>
                  )}
                </div>

                <Badge
                  variant={isOpen ? 'default' : 'secondary'}
                  className={cn(
                    'text-xs shrink-0 theme-transition',
                    isOpen
                      ? 'bg-success/10 text-success border-success/20'
                      : 'bg-purple-500/10 text-purple-600 dark:text-purple-400 border-purple-500/20'
                  )}
                >
                  {isOpen ? 'Open' : 'Closed'}
                </Badge>
              </div>

              <h3 className="font-semibold theme-text-primary line-clamp-1 text-sm mb-1">
                {safeIssue.title}
              </h3>

              {safeIssue.body && (
                <p className="text-xs theme-text-secondary line-clamp-1 mb-2">
                  {safeIssue.body
                    .replace(/[#*`\n\r]/g, ' ')
                    .trim()
                    .substring(0, 100)}
                  {safeIssue.body.length > 100 && '...'}
                </p>
              )}

              {hasLabels && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {safeIssue.labels.slice(0, 2).map((label, idx) => (
                    <TooltipProvider key={label?.name || idx}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            variant="secondary"
                            className="text-xs px-1.5 py-0.5 theme-transition"
                            style={{
                              backgroundColor: label?.color ? `#${label.color}20` : undefined,
                              color: label?.color ? `#${label.color}` : undefined,
                              borderColor: label?.color ? `#${label.color}40` : undefined,
                            }}
                          >
                            <Tag className="h-2 w-2 mr-1" />
                            {label?.name || 'Unknown'}
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{label?.description || label?.name || 'Unknown'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                  {safeIssue.labels.length > 2 && (
                    <Badge variant="outline" className="text-xs theme-transition">
                      +{safeIssue.labels.length - 2}
                    </Badge>
                  )}
                </div>
              )}

              <div className="flex items-center justify-between gap-2">
                <div className="flex items-center gap-3 text-xs theme-text-secondary min-w-0 flex-1">
                  <div className="flex items-center gap-1 min-w-0">
                    <Avatar className="h-3.5 w-3.5 flex-shrink-0">
                      <AvatarImage src={safeIssue.user.avatar_url} alt={safeIssue.user.login} />
                      <AvatarFallback className="text-xs">
                        {safeIssue.user.login.slice(0, 1).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="truncate">{safeIssue.user.login}</span>
                  </div>

                  <div className="flex items-center gap-1 flex-shrink-0">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {formatDistanceToNow(new Date(safeIssue.created_at), { addSuffix: true })}
                    </span>
                  </div>

                  {safeIssue.comments > 0 && (
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <MessageSquare className="h-3 w-3" />
                      <span>{safeIssue.comments}</span>
                    </div>
                  )}

                  {hasAssignees && (
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <User className="h-3 w-3" />
                      <div className="flex -space-x-1">
                        {safeIssue.assignees.slice(0, 2).map((assignee, idx) => (
                          <TooltipProvider key={assignee?.login || idx}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Avatar className="h-4 w-4 ring-1 ring-background">
                                  <AvatarImage src={assignee?.avatar_url} alt={assignee?.login} />
                                  <AvatarFallback className="text-xs">
                                    {(assignee?.login || 'U').slice(0, 1).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>@{assignee?.login || 'Unknown'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                        {safeIssue.assignees.length > 2 && (
                          <div className="h-4 w-4 rounded-full bg-muted border border-background flex items-center justify-center">
                            <span className="text-xs theme-text-secondary">
                              +{safeIssue.assignees.length - 2}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2 flex-shrink-0">
                  <a
                    href={safeIssue.html_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-primary hover:text-primary/80 flex items-center theme-transition"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </a>

                  {isOpen && (
                    <Button
                      size="sm"
                      onClick={() => onCreateTask(safeIssue.number)}
                      disabled={isCreatingTask}
                      className="text-xs h-6 px-2 theme-button-primary"
                    >
                      {isCreatingTask ? (
                        <Clock className="h-3 w-3 animate-spin" />
                      ) : (
                        <>
                          <Plus className="mr-1 h-3 w-3" />
                          Task
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
