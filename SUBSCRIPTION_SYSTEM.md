# TaskMantra Subscription System

A comprehensive freemium subscription system built for the Indian market with Razorpay integration, usage tracking, and intelligent upgrade prompts.

## 🎯 Features

### ✅ **Pricing Strategy**
- **4 Tiers**: Free (₹0), Basic (₹99), Professional (₹199), Enterprise (₹399)
- **Indian Market Focus**: Competitive pricing, UPI/EMI support, 17% annual discount
- **Feature Matrix**: Detailed feature breakdown across all plans

### ✅ **Payment Integration**
- **Razorpay Integration**: Complete subscription and one-time payments
- **Indian Payment Methods**: UPI, Net Banking, Cards, Wallets, EMI
- **Secure Processing**: Payment verification and webhook handling

### ✅ **Usage Tracking**
- **Real-time Monitoring**: Track projects, users, storage, integrations
- **Limit Enforcement**: Prevent actions when limits are reached
- **Smart Recommendations**: AI-powered upgrade suggestions

### ✅ **Developer Experience**
- **Type Safety**: Complete TypeScript coverage
- **Error Handling**: Comprehensive error management
- **Validation**: Zod schemas for all API endpoints
- **Testing Ready**: Structured for easy testing

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Required Environment Variables
MONGODB_URI=mongodb://localhost:27017/taskmantra
NEXTAUTH_SECRET=your-secret-key
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_secret_key
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_your_key_id
```

### 2. Install Dependencies

```bash
npm install razorpay @tanstack/react-query zod
```

### 3. Basic Usage

```tsx
import { useSubscription } from '@/hooks/useSubscription';
import { useRazorpay } from '@/hooks/useRazorpay';

function PricingPage() {
  const { currentPlan, usage } = useSubscription();
  const { processSubscriptionPayment } = useRazorpay();

  const handleUpgrade = async (planId: string) => {
    await processSubscriptionPayment({
      planId: planId as 'basic' | 'professional' | 'enterprise',
      billingCycle: 'monthly',
      onSuccess: () => console.log('Payment successful'),
      onFailure: (error) => console.error('Payment failed', error),
    });
  };

  return (
    <div>
      <h1>Current Plan: {currentPlan}</h1>
      <button onClick={() => handleUpgrade('basic')}>
        Upgrade to Basic
      </button>
    </div>
  );
}
```

## 📁 File Structure

```
src/
├── app/api/subscriptions/          # Subscription API routes
├── components/
│   ├── Pricing/                    # Pricing page components
│   └── Upgrade/                    # Upgrade prompts and modals
├── hooks/
│   ├── useSubscription.ts          # Main subscription hook
│   ├── useRazorpay.ts             # Payment processing hook
│   └── useSubscriptionValidator.ts # Client-side validation
├── services/
│   ├── Subscription.service.ts     # API service layer
│   └── UsageTracking.service.ts    # Usage tracking utilities
├── middleware/
│   └── subscriptionValidator.ts    # Server-side validation
├── constant/
│   └── PricingPlans.ts            # Plan configurations
├── types/
│   └── subscription.ts            # TypeScript types
└── lib/
    └── env.ts                     # Environment validation
```

## 🔧 API Endpoints

### Subscription Management
- `GET /api/subscriptions` - Get current subscription
- `GET /api/subscriptions/usage` - Get usage summary
- `POST /api/subscriptions/create` - Create subscription order
- `POST /api/subscriptions/verify` - Verify payment
- `PUT /api/subscriptions` - Update subscription
- `POST /api/subscriptions/cancel` - Cancel subscription
- `POST /api/subscriptions/pause` - Pause subscription

### Usage Validation
```typescript
// Server-side validation in API routes
const validation = await SubscriptionValidator.validateAction(
  organizationId,
  'create_project'
);

if (!validation.allowed) {
  return NextResponse.json({
    error: validation.reason,
    upgradeRequired: validation.upgradeRequired,
  }, 403);
}
```

## 🎨 Components

### Pricing Page
```tsx
import { PricingPage } from '@/components/Pricing/PricingPage';

<PricingPage
  currentPlan="free"
  onPlanSelect={handlePlanSelect}
  showCurrentPlan={true}
/>
```

### Subscription Validator
```tsx
import { useSubscriptionValidator } from '@/hooks/useSubscriptionValidator';

function CreateProject() {
  const { checkAndNotify } = useSubscriptionValidator();

  const handleCreate = () => {
    if (!checkAndNotify('create_project')) {
      return; // User will see upgrade prompt
    }
    // Proceed with creation
  };
}
```

### Upgrade Prompt
```tsx
import { UpgradePrompt } from '@/components/Upgrade/UpgradePrompt';

<UpgradePrompt
  isOpen={showUpgrade}
  onClose={() => setShowUpgrade(false)}
  onUpgrade={handleUpgrade}
  currentPlan="free"
  triggerReason="limit_reached"
  limitType="projects"
/>
```

## 💳 Payment Flow

### 1. Create Subscription
```typescript
const { createSubscription } = useSubscription();

const orderData = await createSubscription({
  planId: 'basic',
  billingCycle: 'monthly'
});
```

### 2. Process Payment
```typescript
const { processSubscriptionPayment } = useRazorpay();

await processSubscriptionPayment({
  planId: 'basic',
  billingCycle: 'monthly',
  onSuccess: (response) => {
    // Payment successful
  },
  onFailure: (error) => {
    // Handle error
  }
});
```

### 3. Verify Payment
```typescript
const { verifyPayment } = useSubscription();

const result = await verifyPayment({
  razorpay_payment_id: 'pay_xxx',
  razorpay_subscription_id: 'sub_xxx',
  razorpay_signature: 'signature_xxx'
});
```

## 📊 Usage Tracking

### Client-side Validation
```typescript
const { validateAction } = useSubscriptionValidator();

const result = validateAction('create_project');
if (!result.allowed) {
  // Show upgrade prompt
}
```

### Server-side Enforcement
```typescript
// In API routes
const validation = await SubscriptionValidator.validateAction(
  organizationId,
  'create_project'
);
```

## 🔒 Security Features

- **Payment Verification**: Razorpay signature validation
- **Rate Limiting**: Built-in API rate limiting
- **Input Validation**: Zod schema validation
- **Authentication**: NextAuth.js integration
- **Authorization**: Organization-based access control

## 🧪 Testing

### Unit Tests
```typescript
// Test subscription validation
describe('SubscriptionValidator', () => {
  it('should allow action within limits', async () => {
    const result = await SubscriptionValidator.validateAction(
      'org_id',
      'create_project'
    );
    expect(result.allowed).toBe(true);
  });
});
```

### Integration Tests
```typescript
// Test payment flow
describe('Payment Flow', () => {
  it('should process subscription payment', async () => {
    const result = await processSubscriptionPayment({
      planId: 'basic',
      billingCycle: 'monthly'
    });
    expect(result).toBeDefined();
  });
});
```

## 🚀 Deployment

### Environment Variables
```bash
# Production
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_live_secret_key
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_your_key_id

# Optional
ENABLE_ANALYTICS=true
ENABLE_WEBHOOKS=true
```

### Database Indexes
```javascript
// MongoDB indexes for performance
db.subscriptions.createIndex({ organizationId: 1 });
db.subscriptions.createIndex({ plan: 1, status: 1 });
db.subscriptions.createIndex({ currentPeriodEnd: 1 });
```

## 📈 Analytics & Monitoring

- **Subscription Metrics**: MRR, ARR, churn rate
- **Usage Analytics**: Feature adoption, limit hits
- **Payment Tracking**: Success rates, failure reasons
- **User Behavior**: Upgrade patterns, cancellation reasons

## 🔄 Webhooks

```typescript
// Handle Razorpay webhooks
app.post('/api/webhooks/razorpay', async (req, res) => {
  const signature = req.headers['x-razorpay-signature'];
  const body = req.body;
  
  // Verify webhook signature
  const isValid = verifyWebhookSignature(body, signature);
  
  if (isValid) {
    // Process webhook event
    await handleSubscriptionEvent(body);
  }
});
```

## 🎯 Best Practices

1. **Always validate on server-side** before allowing actions
2. **Use TypeScript** for type safety
3. **Handle errors gracefully** with user-friendly messages
4. **Test payment flows** thoroughly in sandbox mode
5. **Monitor usage patterns** for optimization opportunities
6. **Keep pricing competitive** with market research
7. **Provide clear upgrade paths** with contextual prompts

## 🆘 Troubleshooting

### Common Issues

1. **Payment Failures**: Check Razorpay credentials and webhook URLs
2. **Validation Errors**: Ensure proper Zod schema validation
3. **Usage Tracking**: Verify subscription data is up-to-date
4. **Type Errors**: Use proper TypeScript types from `/types/subscription.ts`

### Debug Mode
```typescript
// Enable debug logging
const { subscription, usage } = useSubscription();
console.log('Current subscription:', subscription);
console.log('Usage summary:', usage);
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Test in sandbox mode first
4. Contact the development team

---

Built with ❤️ for the Indian market using modern web technologies.
