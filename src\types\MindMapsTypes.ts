export interface MindMapNode {
  id: string;
  type:
    | 'task'
    | 'milestone'
    | 'decision'
    | 'note'
    | 'resource'
    | 'timeline'
    | 'team'
    | 'project'
    | 'deadline'
    | 'status'
    | 'annotation'
    | 'process'
    | 'text';
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  content: {
    // Common properties
    text: string;
    title?: string;
    description?: string;

    // Styling properties
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    fontWeight?: string;

    // Task-specific properties
    priority?: 'low' | 'medium' | 'high';
    status?: string;
    assignee?: string;
    completed?: boolean;

    // Decision-specific properties
    options?: string[];
    selected?: string;
    conditions?: Array<{ handle: string; label: string }>;

    // Project-specific properties
    progress?: number;
    tasks?: Array<{ id: string; title: string; completed: boolean }>;

    // Resource-specific properties
    url?: string;
    type?: 'document' | 'image' | 'video' | 'link';

    // Timeline/Date-specific properties
    date?: string;
    timeRemaining?: string;
    urgency?: 'low' | 'medium' | 'high' | 'critical';

    // Team-specific properties
    members?: string[];

    // Process-specific properties
    steps?: Array<{ number: number; description: string }>;
    currentStep?: number;

    // Milestone-specific properties
    achieved?: boolean;
  };
  style?: {
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    opacity?: number;
    shadow?: boolean;
  };
  parentId?: string;
  childIds: string[];
  collapsed?: boolean;
  zIndex?: number;
  selected?: boolean;
  dragging?: boolean;
}

export interface MindMapConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  type: 'straight' | 'curved' | 'step' | 'smoothstep';
  style?: {
    stroke?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
    animated?: boolean;
  };
  label?: string;
  labelStyle?: {
    fontSize?: number;
    color?: string;
    backgroundColor?: string;
  };
}

export interface MindMapViewport {
  x: number;
  y: number;
  zoom: number;
}

export interface MindMapAnalytics {
  viewCount: number;
  editCount: number;
  lastViewed?: Date;
  lastEdited?: Date;
}

export interface MindMap {
  _id: string;
  title: string;
  description?: string;
  userId: string;
  projectId?: string;
  organizationId?: string;

  // Mind map content
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  viewport: MindMapViewport;

  // Metadata
  tags: string[];
  status: 'draft' | 'active' | 'archived' | 'template';

  // Analytics
  analytics: MindMapAnalytics;

  // Soft delete
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: string;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMindMapRequest {
  title: string;
  description?: string;
  projectId?: string;
  tags?: string[];
}

export interface UpdateMindMapRequest {
  title?: string;
  description?: string;
  nodes?: MindMapNode[];
  connections?: MindMapConnection[];
  viewport?: MindMapViewport;
  tags?: string[];
  status?: 'draft' | 'active' | 'archived' | 'template';
}

export interface MindMapFilters {
  projectId?: string;
  status?: string;
  search?: string;
  tags?: string[];
}
