'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Save, X } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface StatusItem {
  label: string;
  color: string;
}

interface StatusNodeData {
  title: string;
  statuses: StatusItem[];
  currentStatus: string;
}

interface StatusNodeFormProps {
  data: StatusNodeData;
  onSave: (d: Partial<StatusNodeData>) => void;
  onCancel: () => void;
}

export function StatusNodeForm({ data, onSave, onCancel }: StatusNodeFormProps) {
  const [formData, setFormData] = useState<StatusNodeData>({
    title: data.title || 'New Status',
    statuses: Array.isArray(data.statuses)
      ? [...data.statuses]
      : [
          { label: 'Not Started', color: 'gray' },
          { label: 'In Progress', color: 'blue' },
          { label: 'Completed', color: 'green' },
        ],
    currentStatus: data.currentStatus || 'Not Started',
  });

  const handleChange = (field: keyof StatusNodeData, value: any) =>
    setFormData(prev => ({ ...prev, [field]: value }));

  const handleAddStatus = () =>
    setFormData(prev => ({
      ...prev,
      statuses: [...prev.statuses, { label: `Stage ${prev.statuses.length + 1}`, color: 'gray' }],
    }));

  const handleStatusChange = (idx: number, field: keyof StatusItem, value: any) =>
    setFormData(prev => {
      const statuses = [...prev.statuses];
      statuses[idx] = { ...statuses[idx], [field]: value };
      return { ...prev, statuses };
    });

  const handleRemoveStatus = (idx: number) =>
    setFormData(prev => ({
      ...prev,
      statuses: prev.statuses.filter((_, i) => i !== idx),
      currentStatus:
        prev.currentStatus === prev.statuses[idx].label
          ? (prev.statuses[0]?.label ?? '')
          : prev.currentStatus,
    }));

  return (
    <div className="min-w-[320px]">
      <form
        className="space-y-4"
        onSubmit={e => {
          e.preventDefault();
          onSave(formData);
        }}
      >
        <div>
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => handleChange('title', e.target.value)}
          />
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Statuses</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddStatus}
              className="px-2 bg-transparent"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          {formData.statuses.map((s, i) => (
            <div key={i} className="flex items-center gap-2 mb-2">
              <Input
                value={s.label}
                onChange={e => handleStatusChange(i, 'label', e.target.value)}
                className="flex-1"
              />
              <Select value={s.color} onValueChange={v => handleStatusChange(i, 'color', v)}>
                <SelectTrigger className="w-[90px]">
                  <SelectValue placeholder="Color" />
                </SelectTrigger>
                <SelectContent>
                  {['gray', 'red', 'orange', 'yellow', 'green', 'blue', 'purple'].map(c => (
                    <SelectItem key={c} value={c}>
                      {c}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleRemoveStatus(i)}
                disabled={formData.statuses.length <= 1}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        <div>
          <Label>Current Status</Label>
          <Select
            value={formData.currentStatus}
            onValueChange={v => handleChange('currentStatus', v)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Pick status" />
            </SelectTrigger>
            <SelectContent>
              {formData.statuses.map(s => (
                <SelectItem key={s.label} value={s.label}>
                  {s.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
