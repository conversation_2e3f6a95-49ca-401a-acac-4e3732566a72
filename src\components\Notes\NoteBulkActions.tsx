'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  X,
  Pin,
  Star,
  Archive,
  Trash2,
  Download,
  Copy,
  Tag,
  Hash,
  Palette,
  MoreHorizontal,
  Check,
  CheckSquare,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ReactSelect from '@/components/Global/ReactSelect';
import { cn } from '@/lib/utils';
import { noteCategories } from '@/hooks/useNote';
import NoteColorPicker from './NoteColorPicker';

interface NoteBulkActionsProps {
  selectedCount: number;
  onClearSelection: () => void;
  onSelectAll: () => void;
  onBulkAction: (action: string, data?: any) => void;
  isLoading?: boolean;
  totalCount: number;
  className?: string;
}

const NoteBulkActions: React.FC<NoteBulkActionsProps> = ({
  selectedCount,
  onClearSelection,
  onSelectAll,
  onBulkAction,
  isLoading = false,
  totalCount,
  className,
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [showTagSelector, setShowTagSelector] = useState(false);

  const categoryOptions = noteCategories.map(cat => ({
    value: cat,
    label: cat.charAt(0).toUpperCase() + cat.slice(1),
  }));

  const handleColorChange = (color: string) => {
    onBulkAction('updateColor', { color });
    setShowColorPicker(false);
  };

  const handleCategoryChange = (category: string) => {
    onBulkAction('updateCategory', { category });
    setShowCategorySelector(false);
  };

  const handleTagsChange = (tags: string[]) => {
    onBulkAction('addTags', { tags });
    setShowTagSelector(false);
  };

  const exportOptions = [
    { value: 'json', label: 'JSON' },
    { value: 'csv', label: 'CSV' },
    { value: 'markdown', label: 'Markdown' },
  ];

  if (selectedCount === 0) return null;

  return (
    <Card className={cn('border-primary/20 bg-primary/5', className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between gap-4">
          {/* Selection Info */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Badge variant="default" className="gap-1">
                <CheckSquare className="h-3 w-3" />
                {selectedCount} selected
              </Badge>
              <Button variant="ghost" size="sm" onClick={onClearSelection} className="h-6 w-6 p-0">
                <X className="h-4 w-4" />
              </Button>
            </div>

            {selectedCount < totalCount && (
              <Button variant="ghost" size="sm" onClick={onSelectAll} className="text-xs">
                <Check className="h-3 w-3 mr-1" />
                Select all ({totalCount})
              </Button>
            )}
          </div>

          {/* Bulk Actions */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* Quick Actions */}
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('pin')}
                disabled={isLoading}
                className="h-8"
              >
                <Pin className="h-4 w-4 mr-1" />
                Pin
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('favorite')}
                disabled={isLoading}
                className="h-8"
              >
                <Star className="h-4 w-4 mr-1" />
                Favorite
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('archive')}
                disabled={isLoading}
                className="h-8"
              >
                <Archive className="h-4 w-4 mr-1" />
                Archive
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Modify Actions */}
            <div className="flex items-center gap-1">
              <DropdownMenu open={showColorPicker} onOpenChange={setShowColorPicker}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Palette className="h-4 w-4 mr-1" />
                    Color
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48">
                  <div className="p-2">
                    <NoteColorPicker selectedColor="" onColorChange={handleColorChange} />
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu open={showCategorySelector} onOpenChange={setShowCategorySelector}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Hash className="h-4 w-4 mr-1" />
                    Category
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48">
                  <div className="p-2">
                    <ReactSelect
                      options={categoryOptions}
                      value={null}
                      onChange={option => {
                        const singleOption = option as { value: string; label: string } | null;
                        if (singleOption) handleCategoryChange(singleOption.value);
                      }}
                      placeholder="Select category"
                    />
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu open={showTagSelector} onOpenChange={setShowTagSelector}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Tag className="h-4 w-4 mr-1" />
                    Tags
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64">
                  <div className="p-2">
                    <ReactSelect
                      isMulti
                      options={[]}
                      value={[]}
                      onChange={options => {
                        const multiOptions = options as { value: string; label: string }[] | null;
                        handleTagsChange(multiOptions?.map(opt => opt.value) || []);
                      }}
                      placeholder="Add tags"
                    />
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onBulkAction('duplicate')}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                {exportOptions.map(option => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => onBulkAction('export', { format: option.value })}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export as {option.label}
                  </DropdownMenuItem>
                ))}

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={() => onBulkAction('delete')}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => onBulkAction('delete', { permanent: true })}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Permanently
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NoteBulkActions;
