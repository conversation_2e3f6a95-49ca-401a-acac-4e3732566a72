'use client';

import * as React from 'react';

import type { TElement } from '@udecode/plate';

import { toUnitLess } from '@udecode/plate-font';
import { FontSizePlugin } from '@udecode/plate-font/react';
import { HEADING_KEYS } from '@udecode/plate-heading';
import { useEditorPlugin, useEditorSelector } from '@udecode/plate/react';
import { Minus, Plus } from 'lucide-react';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

const DEFAULT_FONT_SIZE = '16';

const FONT_SIZE_MAP = {
  [HEADING_KEYS.h1]: '36',
  [HEADING_KEYS.h2]: '24',
  [HEADING_KEYS.h3]: '20',
} as const;

const FONT_SIZES = [
  '8',
  '9',
  '10',
  '12',
  '14',
  '16',
  '18',
  '24',
  '30',
  '36',
  '48',
  '60',
  '72',
  '96',
] as const;

export function FontSizeToolbarButton() {
  const [inputValue, setInputValue] = React.useState(DEFAULT_FONT_SIZE);
  const [isFocused, setIsFocused] = React.useState(false);
  const { api, editor } = useEditorPlugin(FontSizePlugin);

  const cursorFontSize = useEditorSelector(editor => {
    const fontSize = editor.api.marks()?.[FontSizePlugin.key];

    if (fontSize) {
      return toUnitLess(fontSize as string);
    }

    const [block] = editor.api.block<TElement>() || [];

    if (!block?.type) return DEFAULT_FONT_SIZE;

    return block.type in FONT_SIZE_MAP
      ? FONT_SIZE_MAP[block.type as keyof typeof FONT_SIZE_MAP]
      : DEFAULT_FONT_SIZE;
  }, []);

  const handleInputChange = () => {
    const newSize = toUnitLess(inputValue);

    if (Number.parseInt(newSize) < 1 || Number.parseInt(newSize) > 100) {
      editor.tf.focus();

      return;
    }
    if (newSize !== toUnitLess(cursorFontSize)) {
      api.fontSize.setMark(`${newSize}px`);
    }

    editor.tf.focus();
  };

  const handleFontSizeChange = (delta: number) => {
    const newSize = Number(displayValue) + delta;
    api.fontSize.setMark(`${newSize}px`);
    editor.tf.focus();
  };

  const displayValue = isFocused ? inputValue : cursorFontSize;

  return (
    <div className="flex h-7 items-center gap-0 rounded-lg theme-surface border theme-border overflow-hidden shadow-sm">
      <ToolbarButton
        onClick={() => handleFontSizeChange(-1)}
        className="h-full rounded-none border-0 theme-button-ghost hover:theme-surface-elevated theme-transition px-1.5"
      >
        <Minus className="h-3 w-3 theme-text-secondary" />
      </ToolbarButton>

      <div className="h-full w-px theme-border-subtle"></div>

      <Popover open={isFocused} modal={false}>
        <PopoverTrigger asChild>
          <input
            className={cn(
              'h-full w-10 shrink-0 bg-transparent px-1 text-center text-sm font-medium',
              'theme-text-primary theme-transition border-0 outline-none',
              'hover:theme-surface-elevated focus:theme-surface-elevated',
              'selection:bg-blue-500/20'
            )}
            value={displayValue}
            onBlur={() => {
              setIsFocused(false);
              handleInputChange();
            }}
            onChange={e => setInputValue(e.target.value)}
            onFocus={() => {
              setIsFocused(true);
              setInputValue(toUnitLess(cursorFontSize));
            }}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleInputChange();
              }
            }}
            data-plate-focus="true"
            type="text"
          />
        </PopoverTrigger>
        <PopoverContent
          className="w-auto min-w-[80px] p-1 theme-surface-elevated border theme-border shadow-lg"
          onOpenAutoFocus={e => e.preventDefault()}
          sideOffset={4}
        >
          <div className="max-h-48 overflow-y-auto theme-scrollbar">
            {FONT_SIZES.map(size => (
              <button
                key={size}
                className={cn(
                  'flex h-8 w-full items-center justify-center text-sm font-medium rounded-md',
                  'theme-button-ghost theme-transition px-2 min-w-[60px]',
                  'hover:theme-surface-elevated theme-text-primary',
                  size === displayValue && 'theme-surface theme-text-accent'
                )}
                onClick={() => {
                  api.fontSize.setMark(`${size}px`);
                  setIsFocused(false);
                }}
                type="button"
              >
                {size}px
              </button>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      <div className="h-full w-px theme-border-subtle"></div>

      <ToolbarButton
        onClick={() => handleFontSizeChange(1)}
        className="h-full rounded-none border-0 theme-button-ghost hover:theme-surface-elevated theme-transition px-1.5"
      >
        <Plus className="h-3 w-3 theme-text-secondary" />
      </ToolbarButton>
    </div>
  );
}
