import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Search, Calendar as CalendarIcon } from 'lucide-react';
import { format as formatDate } from 'date-fns';
import {
    ActivityLogFilters as FilterType,
    FilterActions,
    QUICK_DATE_RANGES
} from '@/hooks/useActivityLogFilters';
import { useAvailableActions, useAvailableResourceTypes } from '@/hooks/useActivityLogs';

interface ActivityLogFiltersProps {
    filters: FilterType;
    actions: FilterActions;
    className?: string;
}

export const ActivityLogFilters: React.FC<ActivityLogFiltersProps> = ({
    filters,
    actions,
    className = '',
}) => {
    const { data: availableActions } = useAvailableActions();
    const { data: availableResourceTypes } = useAvailableResourceTypes();

    const actionsList: string[] = Array.isArray(availableActions) ? availableActions : [];
    const resourceTypesList: string[] = Array.isArray(availableResourceTypes) ? availableResourceTypes : [];

    return (
        <Card className={className}>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Search */}
                    <div className="space-y-2">
                        <Label>Search</Label>
                        <div className="relative">
                            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search descriptions..."
                                value={filters.search}
                                onChange={e => actions.updateSearch(e.target.value)}
                                className="pl-9"
                            />
                        </div>
                    </div>

                    {/* Date Range */}
                    <div className="space-y-2">
                        <Label>Date Range</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {filters.dateRange.start && filters.dateRange.end
                                        ? `${formatDate(filters.dateRange.start, 'MMM dd')} - ${formatDate(filters.dateRange.end, 'MMM dd')}`
                                        : 'Select date range'}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                                <div className="p-3 space-y-2">
                                    <div className="grid grid-cols-2 gap-2">
                                        {QUICK_DATE_RANGES.map(range => (
                                            <Button
                                                key={range.label}
                                                variant="outline"
                                                size="sm"
                                                onClick={() => actions.setQuickDateRange(range.getValue())}
                                                className="text-xs"
                                            >
                                                {range.label}
                                            </Button>
                                        ))}
                                    </div>
                                    <Separator />
                                    <Calendar
                                        mode="range"
                                        selected={{
                                            from: filters.dateRange.start,
                                            to: filters.dateRange.end,
                                        }}
                                        onSelect={range => {
                                            actions.updateDateRange({
                                                start: range?.from,
                                                end: range?.to,
                                            });
                                        }}
                                        numberOfMonths={2}
                                    />
                                </div>
                            </PopoverContent>
                        </Popover>
                    </div>

                    {/* Actions Filter */}
                    <div className="space-y-2">
                        <Label>Actions</Label>
                        <Select
                            value={filters.actions.join(',')}
                            onValueChange={value => actions.updateActions(value ? value.split(',') : [])}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select actions" />
                            </SelectTrigger>
                            <SelectContent>
                                {actionsList.map(action => (
                                    <SelectItem key={action} value={action}>
                                        {action}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Clear Filters */}
                    <div className="space-y-2">
                        <Label>Quick Actions</Label>
                        <Button
                            variant="outline"
                            onClick={actions.clearFilters}
                            className="w-full"
                            disabled={!filters}
                        >
                            Clear All Filters
                        </Button>
                    </div>
                </div>

                {/* Resource Types Filter */}
                <div className="space-y-2">
                    <Label>Resource Types</Label>
                    <div className="flex flex-wrap gap-2">
                        {resourceTypesList.map(resourceType => {
                            const isSelected = filters.resourceTypes.includes(resourceType);
                            return (
                                <Button
                                    key={resourceType}
                                    variant={isSelected ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => {
                                        const newResourceTypes = isSelected
                                            ? filters.resourceTypes.filter(rt => rt !== resourceType)
                                            : [...filters.resourceTypes, resourceType];
                                        actions.updateResourceTypes(newResourceTypes);
                                    }}
                                >
                                    {resourceType}
                                </Button>
                            );
                        })}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};