import { NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET() {
  try {
    // Read version from package.json
    const packagePath = join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(readFileSync(packagePath, 'utf8'));

    // Fetch changelog from external source or file
    let changelog: { type?: string }[] = [];
    try {
      const changelogResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/changelog`, {
        next: { revalidate: 3600 }, // Cache for 1 hour
      });
      if (changelogResponse.ok) {
        const changelogData = await changelogResponse.json();
        changelog = changelogData.changelog || [];
      }
    } catch {
      // Fallback to local changelog if external fetch fails
      try {
        const changelogPath = join(process.cwd(), 'CHANGELOG.json');
        const changelogData = JSON.parse(readFileSync(changelogPath, 'utf8'));
        changelog = changelogData.changelog || [];
      } catch {
        changelog = [];
      }
    }

    // Determine if update is critical based on version or external config
    const isCritical =
      process.env.FORCE_CRITICAL_UPDATE === 'true' ||
      changelog.some(item => item.type === 'security');

    const updateInfo = {
      version: packageJson.version,
      releaseDate: new Date().toLocaleDateString(),
      critical: isCritical,
      size: process.env.APP_BUNDLE_SIZE || 'Unknown',
      changelog,
    };

    return NextResponse.json(updateInfo);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch version info' }, { status: 500 });
  }
}
