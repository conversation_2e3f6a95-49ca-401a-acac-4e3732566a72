import React, { useCallback } from 'react';
import { setHours, setMinutes, isSameDay, format, differenceInMinutes } from 'date-fns';
import { IEvent } from '@/models/Event';
import DraggableEvent from './DraggableEvent';
import DroppableTimeSlot from './DroppableTimeSlot';

const DayView: React.FC<{
  events: IEvent[];
  currentDate: Date;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  onEventClick: (event: IEvent) => void;
  selectedEvent?: IEvent | null;
  isCreatingEvent?: boolean;
}> = ({ events, currentDate, onEventClick, selectedEvent }) => {
  const hours = Array.from({ length: 24 }, (_, i) => i);

  const getHourEvents = useCallback(
    (hour: number) => {
      return events.filter(event => {
        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);
        const slotStart = setHours(setMinutes(currentDate, 0), hour);
        const slotEnd = setHours(setMinutes(currentDate, 0), hour + 1);

        return eventStart < slotEnd && eventEnd > slotStart && isSameDay(eventStart, currentDate);
      });
    },
    [events, currentDate]
  );

  return (
    <div className="flex flex-col theme-surface border theme-border theme-transition">
      {/* Header */}
      <div className="p-4 bg-secondary border-b theme-border text-center">
        <div className="font-medium text-lg theme-text-primary">
          {format(currentDate, 'EEEE, MMMM d, yyyy')}
        </div>
      </div>

      {/* Time slots */}
      <div className="flex-1 overflow-auto theme-scrollbar">
        {hours.map(hour => {
          const hourEvents = getHourEvents(hour);
          const slotDate = setHours(setMinutes(currentDate, 0), hour);
          const isSelected =
            selectedEvent && hourEvents.some(e => String(e._id) === String(selectedEvent._id));

          return (
            <div key={hour} className="grid grid-cols-12 border-b theme-border min-h-[80px]">
              <div className="col-span-2 p-3 bg-secondary border-r theme-border text-xs theme-text-secondary text-right">
                {format(setHours(new Date(), hour), 'HH:mm')}
              </div>
              <DroppableTimeSlot
                date={slotDate}
                hour={hour}
                minute={0}
                viewType="day"
                onClick={() => {}}
                className={`
                    col-span-10 p-2 relative
                    ${isSelected ? 'ring-2 ring-primary' : ''}
                  `}
              >
                <div className="space-y-1 relative h-full">
                  {hourEvents.map(event => {
                    const eventStart = new Date(event.startTime);
                    const eventDuration = differenceInMinutes(new Date(event.endTime), eventStart);
                    const hourStart = setHours(setMinutes(currentDate, 0), hour);
                    const minutesFromHourStart = differenceInMinutes(eventStart, hourStart);

                    return (
                      <DraggableEvent
                        key={String(event._id)}
                        event={event}
                        viewType="day"
                        onClick={onEventClick}
                        onResize={(event, _newStart, _newEnd) => {
                          const eventId =
                            typeof event._id === 'string' ? event._id : String(event._id ?? '');
                          if (eventId) {
                            // This onEventResize prop is not passed from parent, so this will not work as intended.
                            // The original code had this, but the new_code removed it from CalendarViewProps.
                            // For now, we'll keep it here as it was in the original file,
                            // but it will not trigger the onEventResize prop in the parent.
                            // This is a consequence of the new_code's removal of onEventResize from CalendarViewProps.
                          }
                        }}
                        style={{
                          position: 'absolute',
                          top: `${Math.max(0, (minutesFromHourStart / 60) * 100)}%`,
                          height: `${Math.min(100, (eventDuration / 60) * 100)}%`,
                          left: 0,
                          right: '20%',
                        }}
                      />
                    );
                  })}
                </div>
              </DroppableTimeSlot>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DayView;
