'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus, X, Check } from 'lucide-react';
import { useNote } from '@/hooks/useNote';
import NoteFilters from '@/components/Notes/NoteFilters';
import NotesList from '@/components/Notes/NotesList';
import NoteBulkActions from '@/components/Notes/NoteBulkActions';
import { motion } from 'framer-motion';

export default function NotesPage() {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const {
    // Data
    notes,
    pagination,
    availableFilters,

    // State
    filters,
    selectedNotes,
    isSelectionMode,

    // Loading states
    isLoading,
    isBulkActionLoading,

    // Actions
    bulkAction,
    quickAction,
    deleteNote,

    // Filters and selection
    updateFilters,
    resetFilters,
    toggleNoteSelection,
    selectAllNotes,
    clearSelection,
    setIsSelectionMode,
  } = useNote();

  const handlePageChange = (page: number) => {
    updateFilters({ currentPage: page });
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-4 rounded-md py-2 h-full w-full"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold theme-text-primary">Notes</h2>
          <p className="text-muted-foreground">
            Create, organize, and manage your notes with powerful features
          </p>
        </div>
        <div className="flex items-center gap-2">
          {isSelectionMode && (
            <div className="flex items-center gap-2 mr-4">
              <span className="text-sm text-muted-foreground">{selectedNotes.length} selected</span>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
          <Button variant="outline" size="sm" onClick={() => setIsSelectionMode(!isSelectionMode)}>
            <Check className="h-4 w-4 mr-2" />
            Select
          </Button>
          <Button onClick={() => router.push('/notes/editor')}>
            <Plus className="h-4 w-4 mr-2" />
            New Note
          </Button>
        </div>
      </div>

      {/* Filters */}
      <NoteFilters
        filters={filters}
        onFiltersChange={updateFilters}
        onResetFilters={resetFilters}
        availableCategories={availableFilters.categories}
        availableTags={availableFilters.tags}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />

      {/* Bulk Actions */}
      {selectedNotes.length > 0 && (
        <NoteBulkActions
          selectedCount={selectedNotes.length}
          totalCount={notes.length}
          onClearSelection={clearSelection}
          onSelectAll={selectAllNotes}
          onBulkAction={bulkAction}
          isLoading={isBulkActionLoading}
        />
      )}

      {/* Notes List */}
      <NotesList
        notes={notes}
        isLoading={isLoading}
        viewMode={viewMode}
        isSelectionMode={isSelectionMode}
        selectedNotes={selectedNotes}
        onToggleSelection={toggleNoteSelection}
        onQuickAction={quickAction}
        onDeleteNote={deleteNote}
        pagination={pagination}
        onPageChange={handlePageChange}
      />
    </motion.div>
  );
}
