'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useQueryClient, useQuery, useMutation } from '@tanstack/react-query';
import { NotificationClientService } from '@/services/NotificationClient.service';
import { useSSE } from './useSSE';

export type NotificationType = {
  _id: string;
  userId: string;
  title: string;
  description: string;
  type: 'mention' | 'task' | 'team' | 'system' | 'onboarding';
  read: boolean;
  link: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
};

export interface UseNotificationsReturn {
  notifications: NotificationType[];
  unreadCount: number;
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
  requestNotificationPermission: () => Promise<boolean>;
}

export function useNotifications(): UseNotificationsReturn {
  const { data: session } = useSession();
  const [error] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { eventSource, isConnected } = useSSE();

  // Fetch notifications
  const {
    data: notificationsData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => NotificationClientService.getNotificationsClient(0, 50),
    enabled: !!session?.user,
    refetchInterval: 30000, // Refetch every 30 seconds as fallback
  });

  // Fetch unread count
  const { data: unreadCount = 0 } = useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: () => NotificationClientService.getUnreadCountClient(),
    enabled: !!session?.user,
    refetchInterval: 30000,
  });

  // SSE event handlers
  useEffect(() => {
    if (!eventSource || !isConnected) return;

    const handleNotification = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'notification') {
          // Invalidate queries to refetch notifications
          queryClient.invalidateQueries({ queryKey: ['notifications'] });
          queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });

          // Show browser notification if permission granted and document is not visible
          // (Push notifications handle the case when document is not visible)
          if (Notification.permission === 'granted' && data.notification && !document.hidden) {
            try {
              const notification = new Notification(data.notification.title || 'New Notification', {
                body: data.notification.description || 'You have a new notification',
                icon: '/icons/icon-192x192.png',
                badge: '/icons/badge-72x72.png',
                tag: data.notification._id || 'notification',
                requireInteraction: false,
                silent: false,
              });

              // Handle notification click
              notification.onclick = () => {
                window.focus();
                if (data.notification.link) {
                  window.location.href = data.notification.link;
                } else {
                  window.location.href = '/notifications';
                }
                notification.close();
              };

              // Auto close after 5 seconds
              setTimeout(() => {
                notification.close();
              }, 5000);
            } catch (error) {
              console.error('Failed to show browser notification:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    eventSource.addEventListener('message', handleNotification);

    return () => {
      eventSource.removeEventListener('message', handleNotification);
    };
  }, [eventSource, isConnected, queryClient]);

  // Mutations
  const markAsReadMutation = useMutation({
    mutationFn: (id: string) => NotificationClientService.markAsReadClient(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
    },
  });

  const markAllAsReadMutation = useMutation({
    mutationFn: () => NotificationClientService.markAllAsReadClient(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
    },
  });

  const deleteNotificationMutation = useMutation({
    mutationFn: (id: string) => NotificationClientService.deleteNotificationClient(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
    },
  });

  const clearAllNotificationsMutation = useMutation({
    mutationFn: () => NotificationClientService.clearAllNotificationsClient(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
    },
  });

  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    try {
      if (!('Notification' in window)) {
        console.warn('Browser does not support notifications');
        return false;
      }

      if (Notification.permission === 'granted') {
        return true;
      }

      if (Notification.permission === 'denied') {
        console.warn('Notification permission denied by user');
        return false;
      }

      const permission = await Notification.requestPermission();
      const granted = permission === 'granted';

      if (granted) {
        // Show a test notification to confirm it works
        new Notification('Notifications Enabled', {
          body: 'You will now receive push notifications for new updates',
          icon: '/logo.png',
        });
      }

      return granted;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }, []);

  return {
    notifications: notificationsData?.notifications || [],
    unreadCount,
    isLoading,
    isConnected,
    error,
    markAsRead: (id: string) => markAsReadMutation.mutateAsync(id),
    markAllAsRead: () => markAllAsReadMutation.mutateAsync(),
    deleteNotification: (id: string) => deleteNotificationMutation.mutateAsync(id),
    clearAllNotifications: () => clearAllNotificationsMutation.mutateAsync(),
    refetch,
    hasMore: notificationsData?.hasMore || false,
    loadMore: () => { }, // Simplified - can be enhanced later
    requestNotificationPermission,
  };
}
