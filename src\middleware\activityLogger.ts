import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import ActivityLogService from '@/services/ActivityLog.service';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { toast } from 'sonner';

export interface ActivityLogData {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  organizationId?: string;
  success: boolean;
  duration?: number;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface ActivityLoggerConfig {
  enabled: boolean;
  excludePaths?: string[];
  excludeActions?: string[];
  logLevel: 'minimal' | 'standard' | 'detailed';
  batchSize: number;
  flushInterval: number;
}

class ActivityLogger {
  private config: ActivityLoggerConfig;
  private logQueue: ActivityLogData[] = [];
  private flushTimer?: ReturnType<typeof setInterval>;
  private activityService;

  constructor(config: Partial<ActivityLoggerConfig> = {}) {
    this.config = {
      enabled: process.env.ACTIVITY_LOGGING_ENABLED !== 'false',
      excludePaths: [
        '/api/auth',
        '/api/health',
        '/_next',
        '/favicon.ico',
        '/api/activity-logs',
        ...(config.excludePaths || []),
      ],
      excludeActions: ['health_check', 'ping', 'options', ...(config.excludeActions || [])],
      logLevel:
        ((config.logLevel || process.env.ACTIVITY_LOG_LEVEL) as
          | 'minimal'
          | 'standard'
          | 'detailed') || 'standard',
      batchSize: config.batchSize || 50,
      flushInterval: config.flushInterval || 30000, // 30 seconds
      ...config,
    };

    this.activityService = ActivityLogService.getInstance();
    this.setupFlushTimer();
  }

  private setupFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flushLogs().catch(err => {
        toast.error('Error flushing logs:', err);
      });
    }, this.config.flushInterval);
  }

  private shouldLog(request: NextRequest): boolean {
    if (!this.config.enabled) return false;

    const pathname = request.nextUrl.pathname;

    // Check excluded paths
    return !this.config.excludePaths!.some(excludePath => pathname.startsWith(excludePath));
  }

  private extractRequestInfo(request: NextRequest) {
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0]?.trim() || realIp || 'unknown';

    return {
      ipAddress,
      userAgent: request.headers.get('user-agent') || 'unknown',
      method: request.method,
      pathname: request.nextUrl.pathname,
      searchParams: Object.fromEntries(request.nextUrl.searchParams.entries()),
      referer: request.headers.get('referer'),
      origin: request.headers.get('origin'),
    };
  }

  private async getUserInfo(_request: NextRequest) {
    try {
      const session = await getServerSession(authOptions);
      return {
        userId: session?.user?.id,
        userEmail: session?.user?.email,
        organizationId: session?.user?.organizationId ?? undefined,
        // sessionId removed, not present in session.user
      };
    } catch (error: any) {
      toast.error('Error getting user session for activity log:', error);
      return {};
    }
  }

  private determineAction(method: string, pathname: string): string {
    const pathSegments = pathname.split('/').filter(Boolean);

    if (pathSegments.includes('api')) {
      const apiIndex = pathSegments.indexOf('api');
      const resource = pathSegments[apiIndex + 1] || 'unknown';

      switch (method.toUpperCase()) {
        case 'GET':
          return `${resource}.read`;
        case 'POST':
          return `${resource}.create`;
        case 'PUT':
        case 'PATCH':
          return `${resource}.update`;
        case 'DELETE':
          return `${resource}.delete`;
        default:
          return `${resource}.${method.toLowerCase()}`;
      }
    }

    // For page routes
    const resource = pathSegments[pathSegments.length - 1] || 'home';
    return `page.${resource}.visit`;
  }

  private determineResource(pathname: string): string {
    const pathSegments = pathname.split('/').filter(Boolean);

    if (pathSegments.includes('api')) {
      const apiIndex = pathSegments.indexOf('api');
      return pathSegments[apiIndex + 1] || 'api';
    }

    return pathSegments[pathSegments.length - 1] || 'home';
  }

  private extractResourceId(
    pathname: string,
    searchParams: Record<string, any>
  ): string | undefined {
    const pathSegments = pathname.split('/').filter(Boolean);

    // Look for UUID patterns in path
    const uuidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const numericPattern = /^\d+$/;

    for (const segment of pathSegments) {
      if (uuidPattern.test(segment) || numericPattern.test(segment)) {
        return segment;
      }
    }

    // Check common ID parameters
    return searchParams.id || searchParams.taskId || searchParams.projectId || searchParams.noteId;
  }

  async logActivity(data: Partial<ActivityLogData>): Promise<void> {
    if (!this.config.enabled) return;

    const action = data.action || 'unknown';
    if (this.config.excludeActions!.includes(action)) return;

    const logEntry: ActivityLogData = {
      action,
      resource: data.resource || 'unknown',
      resourceId: data.resourceId,
      userId: data.userId,
      organizationId: data.organizationId ?? undefined,
      details: data.details || {},
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      sessionId: data.sessionId ?? undefined,
      success: data.success !== false, // Default to true
      duration: data.duration,
      errorMessage: data.errorMessage,
      metadata: {
        timestamp: new Date().toISOString(),
        logLevel: this.config.logLevel,
        ...data.metadata,
      },
    };

    // Add to queue
    this.logQueue.push(logEntry);

    // Flush if queue is full
    if (this.logQueue.length >= this.config.batchSize) {
      await this.flushLogs();
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logQueue.length === 0) return;

    const logsToFlush = [...this.logQueue];
    this.logQueue = [];

    try {
      await this.activityService.batchCreateLogs(logsToFlush);
    } catch (error: any) {
      toast.error('Failed to flush activity logs:', error);

      // Re-add failed logs to queue if it's not too big
      if (this.logQueue.length < this.config.batchSize * 2) {
        this.logQueue.unshift(...logsToFlush);
      }
    }
  }

  async middleware(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now();

    if (!this.shouldLog(request)) {
      return NextResponse.next();
    }

    const requestInfo = this.extractRequestInfo(request);
    const userInfo = await this.getUserInfo(request);

    const action = this.determineAction(requestInfo.method, requestInfo.pathname);
    const resource = this.determineResource(requestInfo.pathname);
    const resourceId = this.extractResourceId(requestInfo.pathname, requestInfo.searchParams);

    // Continue with the request
    const response = NextResponse.next();

    // Log the activity asynchronously
    setImmediate(async () => {
      const duration = Date.now() - startTime;
      const success = response.status < 400;

      await this.logActivity({
        action,
        resource,
        resourceId,
        userId: userInfo.userId,
        organizationId: userInfo.organizationId ?? undefined,
        // sessionId removed, not present in userInfo
        ipAddress: requestInfo.ipAddress,
        userAgent: requestInfo.userAgent,
        success,
        duration,
        details: {
          method: requestInfo.method,
          pathname: requestInfo.pathname,
          searchParams: requestInfo.searchParams,
          statusCode: response.status,
          referer: requestInfo.referer,
          origin: requestInfo.origin,
        },
        metadata: {
          userEmail: userInfo.userEmail,
          logLevel: this.config.logLevel,
        },
      });
    });

    return response;
  }

  // Utility methods for manual logging
  async logUserAction(
    userId: string,
    action: string,
    resource: string,
    details?: Record<string, any>,
    organizationId?: string
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      resource,
      details,
      organizationId,
      success: true,
    });
  }

  async logError(
    error: Error,
    context: {
      userId?: string;
      action: string;
      resource: string;
      resourceId?: string;
      organizationId?: string;
      details?: Record<string, any>;
    }
  ): Promise<void> {
    await this.logActivity({
      ...context,
      success: false,
      errorMessage: error.message,
      details: {
        ...context.details,
        errorStack: error.stack,
        errorName: error.name,
      },
    });
  }

  async logSystemEvent(event: string, details?: Record<string, any>): Promise<void> {
    await this.logActivity({
      action: `system.${event}`,
      resource: 'system',
      details,
      success: true,
    });
  }

  // Configuration methods
  updateConfig(newConfig: Partial<ActivityLoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.setupFlushTimer();
  }

  getConfig(): ActivityLoggerConfig {
    return { ...this.config };
  }

  async shutdown(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    // Flush remaining logs
    await this.flushLogs();
  }
}

// Singleton instance
let activityLoggerInstance: ActivityLogger | null = null;

export function getActivityLogger(config?: Partial<ActivityLoggerConfig>): ActivityLogger {
  if (!activityLoggerInstance) {
    activityLoggerInstance = new ActivityLogger(config);
  }

  return activityLoggerInstance;
}

// Middleware function for Next.js
export function createActivityLoggerMiddleware(config?: Partial<ActivityLoggerConfig>) {
  const logger = getActivityLogger(config);

  return (request: NextRequest) => {
    return logger.middleware(request);
  };
}

// Export default instance
export const activityLogger = getActivityLogger();
