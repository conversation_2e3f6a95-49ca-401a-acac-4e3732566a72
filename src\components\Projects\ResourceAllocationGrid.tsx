'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  closestCenter,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { Users, Plus, Search, Grid3X3, List, Calendar } from 'lucide-react';
import { IResourceAllocation } from '@/models/ResourceAllocation';
import { TeamMemberCard } from './TeamMemberCard';
import { SortableTeamMemberCard } from './SortableTeamMemberCard';
import { ResourceAllocationForm } from './ResourceAllocationForm';
import TeamMemberCardSkeleton from './TeamMemberCardSkeleton';
import { ResourceTimelineView } from './ResourceTimelineView';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MemberScheduleView } from './MemberScheduleView';
import { MemberEditForm } from './MemberEditForm';

interface ResourceAllocationGridProps {
  projectId?: string;
  showAllProjects?: boolean;
  onAllocationUpdate?: () => void;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  skills: string[];
  role: string;
  totalCapacity: number;
  allocations: IResourceAllocation[];
  currentUtilization: number;
  isOverAllocated: boolean;
  availableHours: number;
  cost: number;
}

export interface ConflictInfo {
  type: 'over_allocation' | 'schedule_conflict' | 'skill_mismatch';
  severity: 'high' | 'medium' | 'low';
  message: string;
  suggestions: string[];
}

export default function ResourceAllocationGrid({
  projectId,
  showAllProjects = false,
  onAllocationUpdate,
}: ResourceAllocationGridProps) {
  const [sortedMembers, setSortedMembers] = useState<TeamMember[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [memberConflicts, setMemberConflicts] = useState<{ [userId: string]: ConflictInfo[] }>({});
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAllocation, setEditingAllocation] = useState<IResourceAllocation | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'timeline'>('grid');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewingScheduleFor, setViewingScheduleFor] = useState<TeamMember | null>(null);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);

  const queryClient = useQueryClient();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && over) {
      const oldIndex = sortedMembers.findIndex(member => member.id === active.id);
      const newIndex = sortedMembers.findIndex(member => member.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        // Update visual ordering
        const updatedMembers = arrayMove(sortedMembers, oldIndex, newIndex);
        setSortedMembers(updatedMembers);

        // Trigger any necessary updates
        onAllocationUpdate?.();

        toast({
          title: 'Team members reordered',
          description: 'The team member order has been updated.',
        });
      }
    }

    setActiveId(null);
  };

  // Fetch resource allocations
  const { data: allocations = [], isLoading } = useQuery({
    queryKey: ['resource-allocations', projectId, showAllProjects],
    queryFn: async () => {
      if (!projectId && !showAllProjects) return [];
      let url = '';
      if (showAllProjects) {
        url = '/api/resource-allocation';
      } else {
        url = `/api/resource-allocation/project/${projectId}`;
      }
      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to fetch allocations');
      const data = await response.json();
      return data.allocations || [];
    },
  });

  // Process team members data and detect conflicts
  const teamMembers: TeamMember[] = useMemo(() => {
    if (!allocations.length) return [];

    const memberMap = new Map<string, TeamMember>();
    const conflictMap: { [userId: string]: ConflictInfo[] } = {};

    allocations.forEach((allocation: IResourceAllocation) => {
      const userId = allocation.userId.toString();

      if (!memberMap.has(userId)) {
        memberMap.set(userId, {
          id: userId,
          name:
            typeof allocation.userId === 'object' && 'name' in allocation.userId
              ? String(allocation.userId.name)
              : 'Unknown User',
          email:
            typeof allocation.userId === 'object' && 'email' in allocation.userId
              ? String(allocation.userId.email)
              : '',
          avatar:
            typeof allocation.userId === 'object' && 'image' in allocation.userId
              ? String(allocation.userId.image)
              : undefined,
          skills: [],
          role: allocation.role,
          totalCapacity: 0,
          allocations: [],
          currentUtilization: 0,
          isOverAllocated: false,
          availableHours: 40,
          cost: 0,
        });
      }

      const member = memberMap.get(userId)!;
      member.allocations.push(allocation);
      member.totalCapacity += allocation.capacity;
      member.currentUtilization = member.totalCapacity;
      member.isOverAllocated = member.currentUtilization > 100;
      member.skills = [...new Set([...member.skills, ...allocation.skills])];
      member.cost += (allocation.hourlyRate || 0) * (allocation.capacity / 100) * 160; // Assuming 160 hours/month

      // Detect conflicts
      if (member.isOverAllocated && !conflictMap[userId]) {
        conflictMap[userId] = [
          {
            type: 'over_allocation',
            severity: member.currentUtilization > 150 ? 'high' : 'medium',
            message: `${member.name} is over-allocated at ${member.currentUtilization.toFixed(0)}%`,
            suggestions: [
              'Reduce allocation percentage',
              'Reassign some tasks to other team members',
              'Extend project timeline',
            ],
          },
        ];
      }

      // Check for schedule conflicts
      if (allocation.availabilitySchedule && allocation.availabilitySchedule.length > 0) {
        const unavailableDays = allocation.availabilitySchedule.filter(
          schedule => !schedule.isAvailable
        );
        if (unavailableDays.length > 0) {
          if (!conflictMap[userId]) conflictMap[userId] = [];
          conflictMap[userId].push({
            type: 'schedule_conflict',
            severity: 'medium',
            message: 'Resource has schedule conflicts with this project',
            suggestions: [
              'Adjust project schedule',
              'Find alternative resources for conflicting times',
            ],
          });
        }
      }
    });

    // Update conflicts state
    setMemberConflicts(conflictMap);

    return Array.from(memberMap.values());
  }, [allocations]);

  // Delete allocation mutation
  const deleteAllocationMutation = useMutation({
    mutationFn: async (allocationId: string) => {
      const response = await fetch(`/api/resource-allocation/${allocationId}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete allocation');
      return response.json();
    },
    onSuccess: () => {
      toast({ title: 'Allocation deleted successfully!' });
      queryClient.invalidateQueries({ queryKey: ['resource-allocations'] });
      onAllocationUpdate?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete allocation',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleAllocationEdit = useCallback((allocation: IResourceAllocation) => {
    setEditingAllocation(allocation);
    setIsFormOpen(true);
  }, []);

  const handleAllocationDelete = useCallback(
    (allocationId: string) => {
      deleteAllocationMutation.mutate(allocationId);
    },
    [deleteAllocationMutation]
  );

  const handleFormClose = useCallback(() => {
    setIsFormOpen(false);
    setEditingAllocation(null);
  }, []);

  const handleFormSave = useCallback(() => {
    onAllocationUpdate?.();
  }, [onAllocationUpdate]);

  const handleViewSchedule = useCallback((member: TeamMember) => {
    setViewingScheduleFor(member);
  }, []);

  const handleEditMember = useCallback((member: TeamMember) => {
    setEditingMember(member);
  }, []);

  const filteredMembers = useMemo(() => {
    const baseMembers = sortedMembers.length > 0 ? sortedMembers : teamMembers;
    return baseMembers.filter(member => {
      const matchesSearch =
        member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.email.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus =
        filterStatus === 'all' ||
        (filterStatus === 'overallocated' && member.isOverAllocated) ||
        (filterStatus === 'available' && member.currentUtilization < 100);

      return matchesSearch && matchesStatus;
    });
  }, [teamMembers, sortedMembers, searchQuery, filterStatus]);

  // Update sorted members when team members change
  useEffect(() => {
    if (teamMembers.length > 0 && sortedMembers.length === 0) {
      setSortedMembers(teamMembers);
    }
  }, [teamMembers, sortedMembers.length]);

  if (isLoading) {
    return <TeamMemberCardSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search team members..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Members</SelectItem>
            <SelectItem value="overallocated">Over-allocated</SelectItem>
            <SelectItem value="available">Available</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex items-center gap-1 border rounded-lg p-0.5">
          <Button
            variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'timeline' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('timeline')}
          >
            <Calendar className="h-4 w-4" />
          </Button>
        </div>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Allocation
        </Button>
      </div>

      {/* Resource View based on selected mode */}
      {viewMode === 'timeline' ? (
        <ResourceTimelineView members={filteredMembers} />
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={filteredMembers.map(member => member.id)}
            strategy={verticalListSortingStrategy}
          >
            <div
              className={`grid gap-6 ${
                viewMode === 'grid' ? 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'
              }`}
            >
              {filteredMembers.map(member => (
                <SortableTeamMemberCard
                  key={member.id}
                  member={member}
                  onAllocationEdit={handleAllocationEdit}
                  onAllocationDelete={handleAllocationDelete}
                  conflicts={{ [member.id]: memberConflicts[member.id] || [] }}
                  onViewSchedule={() => handleViewSchedule(member)}
                  onEditMember={() => handleEditMember(member)}
                />
              ))}
            </div>
          </SortableContext>

          {/* Drag Overlay for visual feedback during dragging */}
          <DragOverlay>
            {activeId ? (
              <div className="opacity-80">
                <TeamMemberCard
                  member={filteredMembers.find(member => member.id === activeId)!}
                  onAllocationEdit={handleAllocationEdit}
                  onAllocationDelete={handleAllocationDelete}
                  conflicts={{ [activeId]: memberConflicts[activeId] || [] }}
                  onViewSchedule={() => {}}
                  onEditMember={() => {}}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      )}

      {filteredMembers.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No team members found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || filterStatus !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start by creating resource allocations for your team members'}
          </p>
          <Button onClick={() => setIsFormOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Allocation
          </Button>
        </div>
      )}

      {/* Resource Allocation Form */}
      <ResourceAllocationForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        allocation={editingAllocation || undefined}
        projectId={projectId}
        onSave={handleFormSave}
      />

      {/* Member Schedule Dialog */}
      <Dialog open={!!viewingScheduleFor} onOpenChange={() => setViewingScheduleFor(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Schedule for {viewingScheduleFor?.name}</DialogTitle>
          </DialogHeader>
          {viewingScheduleFor && (
            <div className="py-4">
              <MemberScheduleView member={viewingScheduleFor} />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Member Dialog */}
      <Dialog open={!!editingMember} onOpenChange={() => setEditingMember(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit {editingMember?.name}</DialogTitle>
          </DialogHeader>
          {editingMember && (
            <div className="py-4">
              <MemberEditForm
                member={editingMember}
                onSave={() => {
                  queryClient.invalidateQueries({ queryKey: ['resource-allocations'] });
                  setEditingMember(null);
                }}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
