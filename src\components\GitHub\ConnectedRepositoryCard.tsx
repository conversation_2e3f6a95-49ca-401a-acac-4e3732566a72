'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Github,
  ExternalLink,
  Minus,
  RefreshCw,
  CheckCircle,
  Settings,
  Clock,
  Zap,
  ZapOff,
  Activity,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { ConnectedRepository } from '@/types/GitHubTypes';
import { RepositorySettingsModal } from './RepositorySettingsModal';
interface ConnectedRepositoryCardProps {
  repository: ConnectedRepository;
  index: number;
  onDisconnect: () => void;
  isLoading: boolean;
  className?: string;
}

export const ConnectedRepositoryCard: React.FC<ConnectedRepositoryCardProps> = ({
  repository,
  index,
  onDisconnect,
  isLoading,
  className,
}) => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const hasWebhook = !!repository.webhookId;
  const autoCreateEnabled = repository.syncSettings?.autoCreateTasks;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05, duration: 0.3 }}
      className={cn('h-full', className)}
    >
      <Card className="theme-surface-elevated hover-reveal glow-on-hover h-full flex flex-col theme-transition border-success/20">
        <CardHeader className="pb-3 border-b border-border/20 flex-shrink-0">
          <div className="flex justify-between items-start gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
              <div className="bg-success/10 p-1.5 sm:p-2 rounded-lg theme-transition flex-shrink-0">
                <Github className="h-3 w-3 sm:h-4 sm:w-4 text-success theme-transition" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-sm theme-text-primary truncate">
                  {repository.name}
                </CardTitle>
                <p className="text-xs theme-text-secondary truncate">{repository.fullName}</p>
              </div>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="bg-success/10 p-1.5 rounded-full theme-transition flex-shrink-0">
                    <CheckCircle className="h-3 w-3 text-success theme-transition" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Repository connected</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardHeader>

        <CardContent className="pt-3 flex-grow flex flex-col">
          {/* Description */}
          <p className="text-xs theme-text-secondary mb-3 line-clamp-2 min-h-[2.5rem] flex-shrink-0">
            {repository.description || 'No description available'}
          </p>

          {/* Sync Settings */}
          <div className="space-y-2.5 mb-4 flex-shrink-0">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                {autoCreateEnabled ? (
                  <Zap className="h-3 w-3 text-success flex-shrink-0" />
                ) : (
                  <ZapOff className="h-3 w-3 theme-text-secondary flex-shrink-0" />
                )}
                <span className="theme-text-secondary truncate">Auto-create tasks</span>
              </div>
              <Badge
                variant={autoCreateEnabled ? 'default' : 'secondary'}
                className={cn(
                  'text-xs theme-transition flex-shrink-0 px-2 py-0.5',
                  autoCreateEnabled
                    ? 'bg-success/10 text-success border-success/20'
                    : 'bg-secondary text-secondary-foreground'
                )}
              >
                {autoCreateEnabled ? 'On' : 'Off'}
              </Badge>
            </div>

            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <Activity className="h-3 w-3 theme-text-secondary flex-shrink-0" />
                <span className="theme-text-secondary truncate">Webhook</span>
              </div>
              <Badge
                variant={hasWebhook ? 'default' : 'secondary'}
                className={cn(
                  'text-xs theme-transition flex-shrink-0 px-2 py-0.5',
                  hasWebhook
                    ? 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/20'
                    : 'bg-secondary text-secondary-foreground'
                )}
              >
                {hasWebhook ? 'Active' : 'Inactive'}
              </Badge>
            </div>

            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <Clock className="h-3 w-3 theme-text-secondary flex-shrink-0" />
                <span className="theme-text-secondary truncate">Last synced</span>
              </div>
              <span className="theme-text-secondary text-xs flex-shrink-0 max-w-[100px]">
                {repository.lastSyncedAt
                  ? formatDistanceToNow(new Date(repository.lastSyncedAt), { addSuffix: true })
                  : 'Never'}
              </span>
            </div>
          </div>

          {/* Repository Stats */}
          <div className="grid grid-cols-3 gap-2 text-center flex-shrink-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="p-2 theme-surface rounded-lg theme-transition hover:theme-surface-elevated">
                    <div className="text-sm font-semibold theme-text-primary">
                      {repository.openIssuesCount}
                    </div>
                    <div className="text-xs theme-text-secondary">Issues</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{repository.openIssuesCount} open issues</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="p-2 theme-surface rounded-lg theme-transition hover:theme-surface-elevated">
                    <div className="text-sm font-semibold theme-text-primary">
                      {repository.stargazersCount}
                    </div>
                    <div className="text-xs theme-text-secondary">Stars</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{repository.stargazersCount} stars</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="p-2 theme-surface rounded-lg theme-transition hover:theme-surface-elevated">
                    <div className="text-sm font-semibold theme-text-primary">
                      {repository.forksCount}
                    </div>
                    <div className="text-xs theme-text-secondary">Forks</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{repository.forksCount} forks</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardContent>

        {/* Actions */}
        <div className="p-3 border-t border-border/20 flex-shrink-0">
          <div className="flex items-center justify-between gap-3">
            <a
              href={repository.htmlUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-primary hover:text-primary/80 flex items-center theme-transition flex-1 min-w-0"
            >
              <span className="hidden sm:inline truncate">View on GitHub</span>
              <span className="sm:hidden truncate">GitHub</span>
              <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
            </a>
            <div className="flex items-center gap-2 flex-shrink-0">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setIsSettingsOpen(true)}
                      className="h-7 w-7 p-0 theme-button-ghost"
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Repository settings</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Button
                size="sm"
                variant="outline"
                onClick={onDisconnect}
                disabled={isLoading}
                className="text-xs theme-button-secondary h-7 px-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                ) : (
                  <>
                    <Minus className="mr-1 h-3 w-3" />
                    <span className="hidden sm:inline">Disconnect</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {/* Settings Modal */}
      <RepositorySettingsModal
        isOpen={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        repository={repository}
      />
    </motion.div>
  );
};
