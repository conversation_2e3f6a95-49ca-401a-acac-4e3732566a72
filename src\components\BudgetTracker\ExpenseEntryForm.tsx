import React, { useState, useCallback } from 'react';
import {
  Loader2,
  ReceiptCent,
  Upload,
  X,
  Calendar,
  DollarSign,
  Tag,
  FileText,
  Building2,
  Check,
  AlertCircle,
  Clock,
  CreditCard,
  BarChart3,
  ImageIcon,
  Paperclip,
  Info,
} from 'lucide-react';
import { Button } from '../ui/button';
import { IBudgetCategory, IExpense } from '@/models/Budget';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import Modal from '../Global/Modal';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import ReactSelect, { Option } from '../Global/ReactSelect';
import { Textarea } from '../ui/textarea';
import { format } from 'date-fns';
import { BudgetService } from '@/services/Budget.service';
import { Card, CardContent, CardHeader } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Progress } from '../ui/progress';
import Image from 'next/image';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';

const expenseSchema = yup.object({
  amount: yup.number().positive('Amount must be positive').required('Amount is required'),
  category: yup.string().required('Category is required'),
  description: yup.string().max(500, 'Description too long'),
  date: yup.date().required('Date is required'),
  receiptNumber: yup.string().optional(),
  vendor: yup.string().max(200, 'Vendor name too long').optional(),
  attachments: yup.array().optional(),
});

type ExpenseForm = yup.InferType<typeof expenseSchema>;

interface ExpenseEntryFormProps {
  isOpen: boolean;
  onClose: () => void;
  categories: IBudgetCategory[];
  onExpenseAdded: () => void;
  editingExpense?: IExpense;
  projectId: string;
}

export const ExpenseEntryForm: React.FC<ExpenseEntryFormProps> = ({
  isOpen,
  onClose,
  categories,
  onExpenseAdded,
  editingExpense,
  projectId,
}) => {
  const [attachments, setAttachments] = useState<
    Array<{
      file: File;
      preview?: string;
      uploaded?: { name: string; url: string; public_id: string };
    }>
  >([]);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const { toast } = useToast();

  const form = useForm<ExpenseForm>({
    resolver: yupResolver(expenseSchema),
    defaultValues: {
      amount: editingExpense?.amount || 0,
      category: editingExpense?.category || '',
      description: editingExpense?.description || '',
      date: editingExpense?.date || new Date(),
      receiptNumber: editingExpense?.receiptNumber || '',
      vendor: editingExpense?.vendor || '',
      attachments: [],
    },
  });

  const addExpenseMutation = useMutation({
    mutationFn: async (data: ExpenseForm) => {
      const expenseData = {
        ...data,
        date: new Date(data.date).toISOString(),
        attachments: attachments.filter(att => att.uploaded).map(att => att.uploaded!),
      };

      if (editingExpense) {
        return BudgetService.updateExpense(editingExpense._id.toString(), expenseData);
      } else {
        return BudgetService.addExpense(projectId, expenseData);
      }
    },
    onSuccess: () => {
      toast({
        title: `Expense ${editingExpense ? 'updated' : 'added'} successfully!`,
        description: 'Your expense has been saved to the budget tracker.',
      });
      onExpenseAdded();
      onClose();
      form.reset();
      setAttachments([]);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to save expense',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: ExpenseForm) => {
    addExpenseMutation.mutate(data);
  };

  const handleFileUpload = useCallback(
    async (files: FileList | null) => {
      if (!files) return;

      const newFiles = Array.from(files).map(file => ({
        file,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
      }));
      setAttachments(prev => [...prev, ...newFiles]);

      setUploadingFiles(true);
      try {
        const uploadPromises = newFiles.map(async ({ file }) => {
          const uploaded = await BudgetService.uploadExpenseAttachment(file);
          return { file, uploaded };
        });

        const results = await Promise.all(uploadPromises);

        setAttachments(prev =>
          prev.map(att => {
            const result = results.find(r => r.file === att.file);
            return result ? { ...att, uploaded: result.uploaded } : att;
          })
        );
      } catch (error: any) {
        toast({
          title: 'Upload failed',
          description: error.message,
          variant: 'destructive',
        });
      } finally {
        setUploadingFiles(false);
      }
    },
    [toast]
  );

  const removeAttachment = (index: number) => {
    setAttachments(prev => {
      const newAttachments = prev.filter((_, i) => i !== index);
      return newAttachments;
    });
  };

  const selectedCategory = categories.find(cat => cat.name === form.watch('category'));
  const categoryUtilization = selectedCategory
    ? (selectedCategory.spentAmount / selectedCategory.allocatedAmount) * 100
    : 0;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl" className="max-h-[90vh] overflow-hidden">
      <div className="max-w-4xl w-full h-full flex flex-col">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-primary/10 rounded-full theme-shadow-sm">
            <ReceiptCent className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              {editingExpense ? 'Edit Expense' : 'Add New Expense'}
            </h2>
            <p className="text-xs text-muted-foreground">
              {editingExpense ? 'Update expense details' : 'Track a new project expense'}
            </p>
          </div>
        </div>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="flex-1">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {/* Left Column */}
            <div className="space-y-3">
              {/* Amount Field */}
              <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-primary/10 rounded-full">
                      <DollarSign className="h-4 w-4 text-primary" />
                    </div>
                    <h3 className="font-medium text-sm">Amount</h3>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                          <Info className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p>Enter the total amount spent for this expense</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="amount" className="text-xs font-medium">
                    Amount *
                  </Label>
                  <div className="relative">
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      className="text-base font-medium pl-7 theme-focus theme-transition glass h-9"
                      {...form.register('amount', { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                  </div>
                  {form.formState.errors.amount && (
                    <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                      <AlertCircle className="h-3 w-3" />
                      {form.formState.errors.amount.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Category Field */}
              <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-primary/10 rounded-full">
                      <Tag className="h-4 w-4 text-primary" />
                    </div>
                    <h3 className="font-medium text-sm">Category</h3>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                          <Info className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p>Select the budget category this expense belongs to</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="category" className="text-xs font-medium">
                    Select Budget Category *
                  </Label>
                  <ReactSelect
                    options={categories.map(category => ({
                      value: category.name,
                      label: `${category.name} ($${category.allocatedAmount.toLocaleString()})`,
                    }))}
                    value={
                      categories.find(c => c.name === form.watch('category'))
                        ? {
                            value: form.watch('category'),
                            label: `${form.watch('category')} ($${categories.find(c => c.name === form.watch('category'))?.allocatedAmount.toLocaleString() || 0})`,
                          }
                        : null
                    }
                    onChange={option => {
                      if (option) {
                        form.setValue('category', (option as Option).value.toString());
                      }
                    }}
                    placeholder="Select category"
                    isSearchable={true}
                    isClearable={false}
                    error={form.formState.errors.category?.message}
                  />
                  {form.formState.errors.category && (
                    <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                      <AlertCircle className="h-3 w-3" />
                      {form.formState.errors.category.message}
                    </p>
                  )}

                  {selectedCategory && (
                    <Card className="mt-2 theme-shadow-sm border-0 bg-background/50 animate-in">
                      <CardHeader className="p-2 pb-0">
                        <div className="flex items-center justify-between text-xs">
                          <span className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-muted-foreground" />
                            Category Utilization
                          </span>
                          <Badge
                            variant={
                              categoryUtilization > 90
                                ? 'destructive'
                                : categoryUtilization > 70
                                  ? 'outline'
                                  : 'secondary'
                            }
                            className="text-[10px] px-1.5 py-0"
                          >
                            {categoryUtilization.toFixed(1)}%
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="p-2 pt-1">
                        <Progress
                          value={Math.min(categoryUtilization, 100)}
                          className="h-1.5"
                          color={
                            categoryUtilization > 90
                              ? 'destructive'
                              : categoryUtilization > 70
                                ? 'warning'
                                : 'default'
                          }
                        />
                        <div className="flex justify-between text-[10px] text-muted-foreground mt-1">
                          <span>${selectedCategory.spentAmount.toLocaleString()} spent</span>
                          <span>
                            ${selectedCategory.allocatedAmount.toLocaleString()} allocated
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>

              {/* Date Field */}
              <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-primary/10 rounded-full">
                      <Calendar className="h-4 w-4 text-primary" />
                    </div>
                    <h3 className="font-medium text-sm">Date</h3>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                          <Info className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>When was this expense incurred?</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="date" className="text-xs font-medium">
                    Expense Date *
                  </Label>
                  <Input
                    id="date"
                    type="date"
                    {...form.register('date')}
                    value={
                      form.watch('date') ? format(new Date(form.watch('date')), 'yyyy-MM-dd') : ''
                    }
                    onChange={e => form.setValue('date', new Date(e.target.value))}
                    className="theme-focus theme-transition glass h-9"
                  />
                  {form.formState.errors.date && (
                    <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                      <AlertCircle className="h-3 w-3" />
                      {form.formState.errors.date.message}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-3">
              {/* Vendor Field */}
              <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-primary/10 rounded-full">
                      <Building2 className="h-4 w-4 text-primary" />
                    </div>
                    <h3 className="font-medium text-sm">Vendor Details</h3>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                          <Info className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>Who provided the goods or services?</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="space-y-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="vendor" className="text-xs font-medium">
                      Vendor Name
                    </Label>
                    <Input
                      id="vendor"
                      {...form.register('vendor')}
                      placeholder="Vendor or supplier name"
                      className="theme-focus theme-transition glass h-9"
                    />
                    {form.formState.errors.vendor && (
                      <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                        <AlertCircle className="h-3 w-3" />
                        {form.formState.errors.vendor.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="receiptNumber" className="text-xs font-medium">
                      Receipt/Invoice Number
                    </Label>
                    <Input
                      id="receiptNumber"
                      {...form.register('receiptNumber')}
                      placeholder="Receipt or invoice number"
                      className="theme-focus theme-transition glass h-9"
                    />
                    {form.formState.errors.receiptNumber && (
                      <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                        <AlertCircle className="h-3 w-3" />
                        {form.formState.errors.receiptNumber.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Description Field */}
              <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-primary/10 rounded-full">
                      <FileText className="h-4 w-4 text-primary" />
                    </div>
                    <h3 className="font-medium text-sm">Description</h3>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                          <Info className="h-3.5 w-3.5 text-muted-foreground" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>Provide details about this expense</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="description" className="text-xs font-medium">
                    Expense Description
                  </Label>
                  <Textarea
                    id="description"
                    {...form.register('description')}
                    placeholder="Enter expense description"
                    rows={3}
                    className="resize-none theme-focus theme-transition glass text-sm"
                  />
                  {form.formState.errors.description && (
                    <p className="text-xs text-destructive flex items-center gap-1 mt-1 animate-in">
                      <AlertCircle className="h-3 w-3" />
                      {form.formState.errors.description.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-3" />

          {/* Attachments Section */}
          <div className="theme-surface-elevated rounded-lg p-3 theme-shadow-sm hover-reveal">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-primary/10 rounded-full">
                  <Paperclip className="h-4 w-4 text-primary" />
                </div>
                <h3 className="font-medium text-sm">Attachments</h3>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="p-1 rounded-full hover:bg-accent/50 cursor-help">
                      <Info className="h-3.5 w-3.5 text-muted-foreground" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>Upload receipts, invoices or other supporting documents</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center hover:border-primary/30 theme-transition bg-background/50 glass">
              <input
                type="file"
                multiple
                accept="image/*,.pdf,.doc,.docx"
                onChange={e => handleFileUpload(e.target.files)}
                className="hidden"
                id="file-upload"
                disabled={uploadingFiles}
              />
              <label htmlFor="file-upload" className="cursor-pointer block">
                <div className="p-2 bg-primary/5 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Upload className="h-6 w-6 text-primary/70" />
                </div>
                <p className="text-sm font-medium mb-1">
                  {uploadingFiles ? 'Uploading files...' : 'Drop files here or click to upload'}
                </p>
                <p className="text-xs text-muted-foreground">
                  Images, PDFs, Word documents up to 5MB each
                </p>
              </label>
            </div>

            {attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                <h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center gap-1">
                  <ImageIcon className="h-3.5 w-3.5" />
                  Uploaded Files ({attachments.length})
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 theme-surface rounded-lg theme-transition hover:theme-shadow-md animate-in animation-delay-100"
                    >
                      <div className="flex items-center gap-2">
                        {attachment.preview ? (
                          <div className="relative h-10 w-10 rounded-md overflow-hidden">
                            <Image
                              fill
                              src={attachment.preview}
                              alt={attachment.file.name}
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <div className="h-10 w-10 bg-muted rounded-md flex items-center justify-center">
                            <FileText className="h-5 w-5 text-muted-foreground" />
                          </div>
                        )}
                        <div className="overflow-hidden">
                          <p className="text-xs font-medium truncate max-w-[140px]">
                            {attachment.file.name}
                          </p>
                          <p className="text-[10px] text-muted-foreground">
                            {(attachment.file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {attachment.uploaded ? (
                          <Badge
                            variant="secondary"
                            className="text-[10px] px-1.5 py-0 theme-badge-success"
                          >
                            <Check className="h-2.5 w-2.5 mr-0.5" />
                            Uploaded
                          </Badge>
                        ) : uploadingFiles ? (
                          <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                            <Loader2 className="h-2.5 w-2.5 animate-spin mr-0.5" />
                            Uploading
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                            <Clock className="h-2.5 w-2.5 mr-0.5" />
                            Pending
                          </Badge>
                        )}
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeAttachment(index)}
                          disabled={uploadingFiles}
                          className="h-7 w-7 rounded-full hover:bg-destructive/10 hover:text-destructive"
                        >
                          <X className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="theme-transition h-9"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={addExpenseMutation.isPending || uploadingFiles}
              className="min-w-[120px] theme-transition theme-shadow-sm hover:theme-shadow btn-primary h-9"
            >
              {addExpenseMutation.isPending ? (
                <>
                  <Loader2 className="h-3.5 w-3.5 mr-1.5" />
                  {editingExpense ? 'Updating...' : 'Adding...'}
                </>
              ) : uploadingFiles ? (
                <>
                  <Loader2 className="h-3.5 w-3.5 mr-1.5" />
                  Uploading Files...
                </>
              ) : (
                <>
                  <CreditCard className="h-3.5 w-3.5 mr-1.5" />
                  {editingExpense ? 'Update' : 'Add'} Expense
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
