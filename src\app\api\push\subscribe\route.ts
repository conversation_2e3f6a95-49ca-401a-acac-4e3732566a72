import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { User } from '@/models/User';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/push/subscribe');

app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.post('/', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    const subscription = await c.req.json();

    // Validate subscription object
    if (!subscription || !subscription.endpoint || !subscription.keys) {
      return c.json(
        { error: 'Invalid subscription data: missing endpoint or keys' },
        400
      );
    }

    // Validate keys object
    if (!subscription.keys.p256dh || !subscription.keys.auth) {
      return c.json(
        { error: 'Invalid subscription keys: missing p256dh or auth' },
        400
      );
    }

    await connectDB();

    // Store the subscription in the user's document
    const updatedUser = await User.findByIdAndUpdate(
      user.id,
      {
        $set: {
          pushSubscription: {
            endpoint: subscription.endpoint,
            keys: subscription.keys,
            expirationTime: subscription.expirationTime,
            subscribedAt: new Date(),
          }
        }
      },
      { new: true }
    );

    if (!updatedUser) {
      return c.json({ error: 'User not found' }, 404);
    }

    console.log(`Push subscription saved for user: ${user.id}`);

    return c.json({
      success: true,
      message: 'Push subscription saved successfully'
    });
  } catch (error: any) {
    console.error('Push subscription error:', error);
    return c.json(
      { error: 'Failed to save push subscription' },
      500
    );
  }
});

app.delete('/', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();

    // Remove the push subscription from the user's document
    const updatedUser = await User.findByIdAndUpdate(
      user.id,
      { $unset: { pushSubscription: 1 } },
      { new: true }
    );

    if (!updatedUser) {
      return c.json({ error: 'User not found' }, 404);
    }

    console.log(`Push subscription removed for user: ${user.id}`);

    return c.json({
      success: true,
      message: 'Push subscription removed successfully'
    });
  } catch (error: any) {
    console.error('Push unsubscription error:', error);
    return c.json(
      { error: 'Failed to remove push subscription' },
      500
    );
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const DELETE = handle(app);
