import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { ActivityLog } from '@/models/ActivityLog';
import { User } from '@/models/User';
import mongoose from 'mongoose';
import { logger } from 'hono/logger';
import { PipelineStage } from 'mongoose';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    organizationId?: string;
  };
};

interface LogFilters {
  userId?: string;
  actions?: string[];
  resourceTypes?: string[];
  startDate?: Date;
  endDate?: Date;
  resourceId?: string;
  organizationId?: string;
  projectId?: string;
  search?: string;
  ipAddress?: string;
  source?: string;
}

interface CreateLogRequest {
  action:
  | 'create'
  | 'update'
  | 'delete'
  | 'login'
  | 'logout'
  | 'view'
  | 'export'
  | 'import'
  | 'share'
  | 'archive';
  resourceType:
  | 'task'
  | 'project'
  | 'note'
  | 'user'
  | 'organization'
  | 'integration'
  | 'report'
  | 'timeline'
  | 'notification'
  | 'file'
  | 'comment'
  | 'budget';
  resourceId?: string;
  resourceName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

interface AnalyticsQuery {
  userId?: string;
  organizationId?: string;
  startDate?: Date;
  endDate?: Date;
  groupBy?: 'day' | 'week' | 'month';
}

const app = new Hono<{ Variables: Variables }>().basePath('/api/activity-logs');
app.use('*', logger());
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
  await next();
});

// GET routes
app.get('/', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const searchParams = new URLSearchParams(c.req.url.split('?')[1] || '');
    const result = await handleGetLogs(user.id, searchParams);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in GET /:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.get('/analytics', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const searchParams = new URLSearchParams(c.req.url.split('?')[1] || '');
    const result = await handleGetAnalytics(user.id, searchParams);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in GET /analytics:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.get('/export', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const searchParams = new URLSearchParams(c.req.url.split('?')[1] || '');
    const result = await handleExportLogs(user.id, searchParams);
    if (result instanceof Response) {
      return result;
    }
    return c.json(result, 400);
  } catch (error: any) {
    console.error('Error in GET /export:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.get('/users', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const searchParams = new URLSearchParams(c.req.url.split('?')[1] || '');
    const result = await handleGetAvailableUsers(user.id, searchParams);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in GET /users:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.get('/actions', async c => {
  try {
    const result = await handleGetAvailableActions();
    return c.json(result);
  } catch (error: any) {
    console.error('Error in GET /actions:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.get('/resource-types', async c => {
  try {
    const result = await handleGetAvailableResourceTypes();
    return c.json(result);
  } catch (error: any) {
    console.error('Error in GET /resource-types:', error);
    return c.json({ error: error.message }, 500);
  }
});

// POST routes
app.post('/', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const body = await c.req.json();
    const result = await handleCreateLog(body, user.id, c.req.raw);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in POST /:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.post('/bulk', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const body = await c.req.json();
    const result = await handleBulkCreateLogs(body, user.id, c.req.raw);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in POST /bulk:', error);
    return c.json({ error: error.message }, 500);
  }
});

// DELETE routes
app.delete('/:logId', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const logId = c.req.param('logId');
    const result = await handleDeleteLog(logId, user.id);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in DELETE /:logId:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.delete('/', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const searchParams = new URLSearchParams(c.req.url.split('?')[1] || '');
    const result = await handleBulkDeleteLogs(user.id, searchParams);
    return c.json(result);
  } catch (error: any) {
    console.error('Error in DELETE /:', error);
    return c.json({ error: error.message }, 500);
  }
});

// Handler functions
async function handleGetLogs(userId: string, searchParams: URLSearchParams) {
  const page = parseInt(searchParams.get('page') || '1', 10);
  const limit = Math.min(parseInt(searchParams.get('limit') || '25', 10), 100); // Cap at 100
  const skip = (page - 1) * limit;

  const filters = buildFilters(searchParams, userId);
  const query = buildMongoQuery(filters);

  try {
    // Use Promise.all for concurrent operations
    const [logs, totalCount, availableActions, availableResourceTypes, availableUsers] = await Promise.all([
      ActivityLog.find(query)
        .populate('userId', 'name email image')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(), // Use lean() for better performance
      ActivityLog.countDocuments(query),
      getAvailableActions(userId),
      getAvailableResourceTypes(userId),
      getAvailableUsers(userId, filters.organizationId),
    ]);

    // Serialize dates properly
    const serializedLogs = logs.map((log: any) => ({
      ...log,
      id: log._id.toString(),
      userId: log.userId ? (typeof log.userId === 'object' ? log.userId._id.toString() : log.userId.toString()) : null,
      userName: log.userId && typeof log.userId === 'object' ? log.userId.name : null,
      userEmail: log.userId && typeof log.userId === 'object' ? log.userId.email : null,
      userImage: log.userId && typeof log.userId === 'object' ? log.userId.image : null,
      timestamp: log.timestamp instanceof Date ? log.timestamp.toISOString() : new Date(log.timestamp).toISOString(),
      createdAt: log.createdAt instanceof Date ? log.createdAt.toISOString() : new Date(log.createdAt).toISOString(),
      updatedAt: log.updatedAt instanceof Date ? log.updatedAt.toISOString() : new Date(log.updatedAt).toISOString(),
      _id: undefined, // Remove _id from response
      __v: undefined  // Remove __v from response
    }));

    return {
      success: true,
      logs: serializedLogs,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
      availableActions,
      availableResourceTypes,
      availableUsers,
    };
  } catch (error) {
    console.error('Error in handleGetLogs:', error);
    throw error;
  }
}

async function handleCreateLog(logData: CreateLogRequest, userId: string, request: Request) {
  try {
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    const log = await ActivityLog.create({
      userId: new mongoose.Types.ObjectId(userId),
      action: logData.action,
      resourceType: logData.resourceType,
      resourceId: logData.resourceId ? new mongoose.Types.ObjectId(logData.resourceId) : undefined,
      resourceName: logData.resourceName,
      description: logData.description,
      metadata: {
        ...logData.metadata,
        source: 'api',
        sessionId: generateSessionId(request),
        userAgent: userAgent,
      },
      ipAddress: logData.ipAddress || clientIP,
      userAgent: userAgent,
      deviceInfo: parseUserAgent(userAgent),
      timestamp: new Date(),
    });

    return {
      success: true,
      log: {
        id: log._id,
        timestamp: log.timestamp.toISOString(),
        action: log.action,
        resourceType: log.resourceType,
        description: log.description,
      },
      message: 'Activity log created successfully',
    };
  } catch (error) {
    console.error('Error in handleCreateLog:', error);
    throw error;
  }
}

async function handleBulkCreateLogs(
  data: { logs: CreateLogRequest[] },
  userId: string,
  request: Request
) {
  try {
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';
    const sessionId = generateSessionId(request);
    const deviceInfo = parseUserAgent(userAgent);

    const logsToInsert = data.logs.map(logData => ({
      userId: new mongoose.Types.ObjectId(userId),
      action: logData.action,
      resourceType: logData.resourceType,
      resourceId: logData.resourceId ? new mongoose.Types.ObjectId(logData.resourceId) : undefined,
      resourceName: logData.resourceName,
      description: logData.description,
      metadata: {
        ...logData.metadata,
        source: 'api',
        sessionId,
        userAgent,
      },
      ipAddress: logData.ipAddress || clientIP,
      userAgent,
      deviceInfo,
      timestamp: new Date(),
    }));

    const result = await ActivityLog.insertMany(logsToInsert);

    return {
      success: true,
      inserted: result.length,
      message: `${result.length} activity logs created successfully`,
    };
  } catch (error) {
    console.error('Error in handleBulkCreateLogs:', error);
    throw error;
  }
}

async function handleGetAnalytics(userId: string, searchParams: URLSearchParams) {
  try {
    const organizationId = searchParams.get('organizationId');
    const startDateStr = searchParams.get('startDate');
    const endDateStr = searchParams.get('endDate');

    const query: AnalyticsQuery = {};
    if (!organizationId) {
      query.userId = userId;
    } else {
      query.organizationId = organizationId;
    }
    if (startDateStr) query.startDate = new Date(startDateStr);
    if (endDateStr) query.endDate = new Date(endDateStr);

    const analytics = await getAnalyticsData(query);

    return {
      success: true,
      analytics,
    };
  } catch (error) {
    console.error('Error in handleGetAnalytics:', error);
    throw error;
  }
}

async function handleExportLogs(userId: string, searchParams: URLSearchParams) {
  try {
    const format = searchParams.get('format') || 'csv';
    const filters = buildFilters(searchParams, userId);
    const query = buildMongoQuery(filters);

    // Limit export to 10,000 records for performance
    const logs = await ActivityLog.find(query)
      .populate('userId', 'name email')
      .sort({ timestamp: -1 })
      .limit(10000)
      .lean();

    if (format === 'csv') {
      const csv = await generateCSV(logs);
      return new Response(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="activity-logs-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    return { error: 'Unsupported format' };
  } catch (error) {
    console.error('Error in handleExportLogs:', error);
    throw error;
  }
}

async function handleDeleteLog(logId: string, userId: string) {
  try {
    if (!mongoose.Types.ObjectId.isValid(logId)) {
      return { error: 'Invalid log ID' };
    }

    const log = await ActivityLog.findById(logId);
    if (!log) {
      return { error: 'Log not found' };
    }

    // Check if user owns the log or is admin
    if (!log.userId.equals(new mongoose.Types.ObjectId(userId))) {
      const user = await User.findById(userId);
      if (!user || user.role !== 'admin') {
        return { error: 'Forbidden' };
      }
    }

    await ActivityLog.findByIdAndDelete(logId);

    return {
      success: true,
      message: 'Activity log deleted successfully',
    };
  } catch (error) {
    console.error('Error in handleDeleteLog:', error);
    throw error;
  }
}

async function handleBulkDeleteLogs(userId: string, searchParams: URLSearchParams) {
  try {
    const filters = buildFilters(searchParams, userId);
    const query = buildMongoQuery(filters);

    // Only allow users to delete their own logs unless they're admin
    const user = await User.findById(userId);
    if (!user || user.role !== 'admin') {
      query.userId = new mongoose.Types.ObjectId(userId);
    }

    const result = await ActivityLog.deleteMany(query);

    return {
      success: true,
      deleted: result.deletedCount,
      message: `${result.deletedCount} activity logs deleted successfully`,
    };
  } catch (error) {
    console.error('Error in handleBulkDeleteLogs:', error);
    throw error;
  }
}

async function handleGetAvailableUsers(userId: string, searchParams: URLSearchParams) {
  try {
    const organizationId = searchParams.get('organizationId');
    const users = await getAvailableUsers(userId, organizationId || undefined);

    return {
      success: true,
      users,
    };
  } catch (error) {
    console.error('Error in handleGetAvailableUsers:', error);
    throw error;
  }
}

async function handleGetAvailableActions() {
  return {
    success: true,
    actions: [
      'create',
      'update',
      'delete',
      'login',
      'logout',
      'view',
      'export',
      'import',
      'share',
      'archive',
    ],
  };
}

async function handleGetAvailableResourceTypes() {
  return {
    success: true,
    resourceTypes: [
      'task',
      'project',
      'note',
      'user',
      'organization',
      'integration',
      'report',
      'timeline',
      'notification',
      'file',
      'comment',
      'budget',
    ],
  };
}

// Helper functions
function buildFilters(searchParams: URLSearchParams, userId: string): LogFilters {
  const filters: LogFilters = {};

  const filterUserId = searchParams.get('filterUserId');
  if (filterUserId) {
    filters.userId = filterUserId;
  } else {
    filters.userId = userId;
  }

  const actions = searchParams.get('actions');
  if (actions) {
    filters.actions = actions.split(',').map(a => a.trim()).filter(a => a);
  }

  const resourceTypes = searchParams.get('resourceTypes');
  if (resourceTypes) {
    filters.resourceTypes = resourceTypes.split(',').map(rt => rt.trim()).filter(rt => rt);
  }

  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  if (startDate) filters.startDate = new Date(startDate);
  if (endDate) filters.endDate = new Date(endDate);

  const resourceId = searchParams.get('resourceId');
  if (resourceId) filters.resourceId = resourceId;

  const organizationId = searchParams.get('organizationId');
  if (organizationId) filters.organizationId = organizationId;

  const projectId = searchParams.get('projectId');
  if (projectId) filters.projectId = projectId;

  const search = searchParams.get('search');
  if (search) filters.search = search;

  const ipAddress = searchParams.get('ipAddress');
  if (ipAddress) filters.ipAddress = ipAddress;

  const source = searchParams.get('source');
  if (source) filters.source = source;

  return filters;
}

function buildMongoQuery(filters: LogFilters): any {
  const query: any = {};

  if (filters.userId) {
    query.userId = new mongoose.Types.ObjectId(filters.userId);
  }

  if (filters.actions && filters.actions.length > 0) {
    query.action = { $in: filters.actions };
  }

  if (filters.resourceTypes && filters.resourceTypes.length > 0) {
    query.resourceType = { $in: filters.resourceTypes };
  }

  if (filters.startDate || filters.endDate) {
    query.timestamp = {};
    if (filters.startDate) query.timestamp.$gte = filters.startDate;
    if (filters.endDate) query.timestamp.$lte = filters.endDate;
  }

  if (filters.resourceId) {
    query.resourceId = new mongoose.Types.ObjectId(filters.resourceId);
  }

  if (filters.organizationId) {
    query['metadata.organizationId'] = new mongoose.Types.ObjectId(filters.organizationId);
  }

  if (filters.projectId) {
    query['metadata.projectId'] = new mongoose.Types.ObjectId(filters.projectId);
  }

  if (filters.search) {
    query.$or = [
      { description: { $regex: filters.search, $options: 'i' } },
      { resourceName: { $regex: filters.search, $options: 'i' } },
    ];
  }

  if (filters.ipAddress) {
    query.ipAddress = filters.ipAddress;
  }

  if (filters.source) {
    query['metadata.source'] = filters.source;
  }

  return query;
}

async function getAvailableActions(userId: string): Promise<string[]> {
  try {
    const actions = await ActivityLog.distinct('action', {
      userId: new mongoose.Types.ObjectId(userId),
    });
    return actions.sort();
  } catch (error) {
    console.error('Error getting available actions:', error);
    return [
      'create',
      'update',
      'delete',
      'login',
      'logout',
      'view',
      'export',
      'import',
      'share',
      'archive',
    ];
  }
}

async function getAvailableResourceTypes(userId: string): Promise<string[]> {
  try {
    const resourceTypes = await ActivityLog.distinct('resourceType', {
      userId: new mongoose.Types.ObjectId(userId),
    });
    return resourceTypes.sort();
  } catch (error) {
    console.error('Error getting available resource types:', error);
    return [
      'task',
      'project',
      'note',
      'user',
      'organization',
      'integration',
      'report',
      'timeline',
      'notification',
      'file',
      'comment',
      'budget',
    ];
  }
}

async function getAvailableUsers(
  userId: string,
  organizationId?: string
): Promise<Array<{ id: string; name: string; email: string }>> {
  try {
    const userQuery: any = {};
    if (organizationId) {
      userQuery.organizationId = new mongoose.Types.ObjectId(organizationId);
    } else {
      userQuery._id = new mongoose.Types.ObjectId(userId);
    }

    const users = await User.find(userQuery).select('name email').limit(100).sort({ name: 1 }).lean();
    return users.map((user: any) => ({
      id: user._id.toString(),
      name: user.name,
      email: user.email,
    }));
  } catch (error) {
    console.error('Error getting available users:', error);
    return [];
  }
}

async function getAnalyticsData(query: AnalyticsQuery): Promise<any> {
  try {
    const matchStage: any = {};

    if (query.userId) {
      matchStage.userId = new mongoose.Types.ObjectId(query.userId);
    }

    if (query.organizationId) {
      matchStage['metadata.organizationId'] = new mongoose.Types.ObjectId(query.organizationId);
    }

    if (query.startDate || query.endDate) {
      matchStage.timestamp = {};
      if (query.startDate) matchStage.timestamp.$gte = query.startDate;
      if (query.endDate) matchStage.timestamp.$lte = query.endDate;
    }

    const pipeline: PipelineStage[] = [
      { $match: matchStage },
      {
        $facet: {
          totalActions: [{ $count: 'count' }],
          actionBreakdown: [
            { $group: { _id: '$action', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
          ],
          resourceBreakdown: [
            { $group: { _id: '$resourceType', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
          ],
          dailyActivity: [
            {
              $group: {
                _id: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
                count: { $sum: 1 },
              },
            },
            { $sort: { _id: 1 } },
            { $limit: 30 },
            { $project: { date: '$_id', count: 1, _id: 0 } },
          ],
          topUsers: [
            { $group: { _id: '$userId', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 },
            {
              $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: '_id',
                as: 'user',
              },
            },
            {
              $project: {
                userId: { $toString: '$_id' },
                userName: { $ifNull: [{ $arrayElemAt: ['$user.name', 0] }, 'Unknown User'] },
                count: 1,
                _id: 0,
              },
            },
          ],
          recentActivity: [
            { $sort: { timestamp: -1 } },
            { $limit: 10 },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'user',
              },
            },
            {
              $addFields: {
                userName: { $ifNull: [{ $arrayElemAt: ['$user.name', 0] }, 'Unknown User'] },
                userEmail: { $ifNull: [{ $arrayElemAt: ['$user.email', 0] }, ''] },
                userImage: { $ifNull: [{ $arrayElemAt: ['$user.image', 0] }, ''] },
              },
            },
            { $project: { user: 0 } },
          ],
        },
      },
    ];

    const results = await ActivityLog.aggregate(pipeline).exec();
    const data = results[0];

    // Serialize recent activity dates
    const serializedRecentActivity = (data.recentActivity || []).map((activity: any) => ({
      ...activity,
      timestamp: activity.timestamp instanceof Date
        ? activity.timestamp.toISOString()
        : new Date(activity.timestamp).toISOString(),
      createdAt: activity.createdAt instanceof Date
        ? activity.createdAt.toISOString()
        : new Date(activity.createdAt).toISOString(),
      updatedAt: activity.updatedAt instanceof Date
        ? activity.updatedAt.toISOString()
        : new Date(activity.updatedAt).toISOString(),
    }));

    return {
      totalActions: data.totalActions[0]?.count || 0,
      actionBreakdown: data.actionBreakdown.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      resourceBreakdown: data.resourceBreakdown.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      dailyActivity: data.dailyActivity || [],
      topUsers: data.topUsers || [],
      recentActivity: serializedRecentActivity,
    };
  } catch (error) {
    console.error('Error in getAnalyticsData:', error);
    throw error;
  }
}

function getClientIP(request: Request): string {
  const xForwardedFor = request.headers.get('x-forwarded-for');
  const xRealIP = request.headers.get('x-real-ip');

  if (xForwardedFor) {
    return xForwardedFor.split(',')[0].trim();
  }

  return xRealIP || 'unknown';
}

function generateSessionId(request: Request): string {
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const ip = getClientIP(request);
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2);
  return `${timestamp}-${random}-${Buffer.from(userAgent + ip).toString('base64')}`;
}

function parseUserAgent(userAgent: string): any {
  const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent);
  const isTablet = /iPad|Tablet/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;

  let browser = 'Unknown';
  let os = 'Unknown';

  if (userAgent.includes('Chrome')) browser = 'Chrome';
  else if (userAgent.includes('Firefox')) browser = 'Firefox';
  else if (userAgent.includes('Safari')) browser = 'Safari';
  else if (userAgent.includes('Edge')) browser = 'Edge';

  if (userAgent.includes('Windows')) os = 'Windows';
  else if (userAgent.includes('Mac')) os = 'macOS';
  else if (userAgent.includes('Linux')) os = 'Linux';
  else if (userAgent.includes('Android')) os = 'Android';
  else if (userAgent.includes('iOS')) os = 'iOS';

  return {
    browser,
    os,
    isMobile,
    isTablet,
    isDesktop,
  };
}

async function generateCSV(logs: any[]): Promise<string> {
  const headers = [
    'Timestamp',
    'User Name',
    'User Email',
    'Action',
    'Resource Type',
    'Resource Name',
    'Description',
    'IP Address',
    'Browser',
    'OS',
  ];

  const csvRows = [headers.join(',')];

  for (const log of logs) {
    const row = [
      log.timestamp.toISOString(),
      log.userId?.name || 'Unknown',
      log.userId?.email || 'Unknown',
      log.action,
      log.resourceType,
      log.resourceName || '',
      `"${log.description.replace(/"/g, '""')}"`,
      log.ipAddress || '',
      log.deviceInfo?.browser || '',
      log.deviceInfo?.os || '',
    ];
    csvRows.push(row.join(','));
  }

  return csvRows.join('\n');
}

export const GET = handle(app);
export const POST = handle(app);
export const DELETE = handle(app);
export const PUT = handle(app);
export const PATCH = handle(app);