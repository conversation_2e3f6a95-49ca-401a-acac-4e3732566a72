export interface IRole {
  _id?: string;
  id?: string;
  name: string;
  displayName: string;
  description?: string;
  type: 'system' | 'organization' | 'project' | 'custom';
  permissions: Record<string, Record<string, boolean>>;
  restrictions?: {
    maxUsers?: number;
    maxProjects?: number;
    timeRestriction?: {
      startTime?: Date;
      endTime?: Date;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestriction?: string[];
    locationRestriction?: {
      countries?: string[];
      regions?: string[];
    };
    featureRestrictions?: string[];
  };
  metadata?: {
    color?: string;
    icon?: string;
    badge?: string;
    customFields?: Record<string, any>;
  };
  assignedUsers?: Array<{
    id: string;
    name: string;
    email: string;
    assignedAt?: Date;
  }>;
  isSystem?: boolean;
  isDefault?: boolean;
  organizationId?: string;
  projectId?: string;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IRoleFormData {
  name: string;
  displayName: string;
  description: string;
  type: 'system' | 'organization' | 'project' | 'custom';
  permissions: Record<string, Record<string, boolean>>;
  restrictions: {
    maxUsers?: number;
    maxProjects?: number;
    timeRestriction?: {
      startTime?: Date;
      endTime?: Date;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestriction?: string[];
    locationRestriction?: {
      countries?: string[];
      regions?: string[];
    };
    featureRestrictions?: string[];
  };
  metadata: {
    color?: string;
    icon?: string;
    badge?: string;
    customFields?: Record<string, any>;
  };
}

export interface IOrganizationMember {
  userId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  role: string;
  joinedAt: Date;
  _id: string;
}

export interface IOrganization {
  _id: string;
  name: string;
  location?: string;
  logo?: string;
  description?: string;
  ownerId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  members: IOrganizationMember[];
  createdAt: Date;
  updatedAt: Date;
}
