'use client';

import * as React from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { type Value } from '@udecode/plate';
import { Plate } from '@udecode/plate/react';
import { MarkdownPlugin } from '@udecode/plate-markdown';

import { useCreateEditor } from '@/components/ui/EditorUiElement/use-create-editor';
import { Editor, EditorContainer } from '@/components/ui/EditorUiElement/editor';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export type PlateContent = Value;

export interface PlateEditorProps {
  data?: string;
  onChange?: (plateContent: PlateContent) => void;
  placeholder?: string;
  readOnly?: boolean;
  className?: string;
  settings?: {
    fontSize: 'small' | 'medium' | 'large';
    theme: 'light' | 'dark' | 'auto';
  };
}

const DEFAULT_VALUE: PlateContent = [
  {
    type: 'p',
    children: [{ text: '' }],
  },
];

const PlateEditor = React.forwardRef<{ getMarkdown: () => string }, PlateEditorProps>(
  (
    {
      data,
      onChange,
      placeholder = 'Start writing your note...',
      readOnly = false,
      className,
      settings = { fontSize: 'medium', theme: 'auto' },
    },
    ref
  ) => {
    const [initialValue, setInitialValue] = React.useState<PlateContent>();
    const [loading, setLoading] = React.useState(true);

    const editor = useCreateEditor({
      skipInitialization: true,
      readOnly,
      placeholders: readOnly,
    });

    React.useEffect(() => {
      const processData = async () => {
        let processedValue: PlateContent;

        if (!data || data.trim() === '') {
          processedValue = DEFAULT_VALUE;
        } else {
          try {
            processedValue = editor.getApi(MarkdownPlugin).markdown.deserialize(data);
          } catch (error) {
            toast.error('Failed to load note content', {
              description: 'There was an error loading the note content. Please try again.',
            });
            processedValue = DEFAULT_VALUE;
          }
        }

        setInitialValue(processedValue);
        setLoading(false);
      };

      processData();
    }, [data, editor]);

    React.useEffect(() => {
      if (!loading && initialValue && editor) {
        editor.tf.init({
          value: initialValue,
          autoSelect: readOnly ? false : 'end',
        });
      }
    }, [loading, initialValue, editor, readOnly]);

    const handleChange = React.useCallback(
      (newValue: PlateContent) => {
        if (onChange && !readOnly) {
          onChange(newValue);
        }
      },
      [onChange, readOnly]
    );

    // Expose getMarkdown method via ref
    React.useImperativeHandle(
      ref,
      () => ({
        getMarkdown: () => {
          try {
            return editor.getApi(MarkdownPlugin).markdown.serialize();
          } catch (error) {
            toast.error('Failed to convert content to markdown', {
              description: 'There was an error converting the content to markdown format.',
            });
            return '';
          }
        },
      }),
      [editor]
    );

    const fontSizeClasses = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg',
    };

    if (loading) {
      return (
        <div
          className={cn(
            'h-full w-full flex items-center justify-center',
            fontSizeClasses[settings.fontSize],
            className
          )}
        >
          <div className="theme-text-secondary">Loading editor...</div>
        </div>
      );
    }

    return (
      <div
        className={cn('h-full w-full flex flex-col', fontSizeClasses[settings.fontSize], className)}
      >
        <DndProvider
          backend={HTML5Backend}
          options={{
            enableMouseEvents: true,
          }}
        >
          <Plate editor={editor} onChange={({ value }) => handleChange(value)}>
            <EditorContainer className="flex-1 overflow-y-auto theme-scrollbar">
              <Editor
                variant="fullWidth"
                placeholder={placeholder}
                readOnly={readOnly}
                className="min-h-full p-4"
              />
            </EditorContainer>
          </Plate>
        </DndProvider>
      </div>
    );
  }
);

PlateEditor.displayName = 'PlateEditor';

export default PlateEditor;
