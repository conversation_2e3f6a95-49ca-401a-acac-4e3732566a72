'use client';

import React, { memo, useState, useEffect } from 'react';
import { type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Edit3 } from 'lucide-react';
import { useMindMapStore } from '@/stores/mindMapStore';

interface TextNodeData {
  text: string;
  dragging?: boolean;
  isConnecting?: boolean;
  isConnectingFrom?: boolean;
  // Style properties will come from node.style
  fontSize?: number;
  color?: string;
  backgroundColor?: string;
}

export const TextNode = memo(({ id, data, selected }: NodeProps<TextNodeData>) => {
  const { updateNode } = useMindMapStore();
  const [isHovered, setIsHovered] = useState(false);
  const [text, setText] = useState(data.text || 'Double-click to edit');
  const [isEditingThis, setIsEditingThis] = useState(false);

  useEffect(() => {
    setText(data.text || 'Double-click to edit');
  }, [data.text]);

  const handleDoubleClick = () => {
    setIsEditingThis(true);
    setText(data.text || 'Double-click to edit');
  };

  const handleSave = () => {
    updateNode(id, {
      content: {
        ...data,
        text: text,
      },
    });
    setIsEditingThis(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    }
    if (e.key === 'Escape') {
      setText(data.text || 'Double-click to edit');
      setIsEditingThis(false);
    }
  };

  // If this node is being edited, show inline editing
  if (isEditingThis) {
    return (
      <Card
        className="min-w-[150px] transition-all duration-200 border-2 border-blue-500 shadow-xl"
        style={{
          backgroundColor: data.backgroundColor || 'white',
        }}
      >
        <div className="p-3">
          <Input
            value={text}
            onChange={e => setText(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyPress}
            autoFocus
            className="border-none p-0 focus:ring-0 bg-transparent"
            style={{
              fontSize: `${data.fontSize || 14}px`,
              color: data.color || '#000000',
            }}
          />
        </div>
      </Card>
    );
  }

  return (
    <div
      className="relative"
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card
        className={`min-w-[150px] transition-all duration-200 cursor-pointer ${
          selected
            ? 'ring-2 ring-blue-500 shadow-xl scale-105'
            : data.dragging
              ? 'shadow-2xl scale-110 rotate-1'
              : data.isConnectingFrom
                ? 'ring-2 ring-green-500 shadow-xl'
                : data.isConnecting
                  ? 'ring-1 ring-blue-300 hover:ring-2 hover:ring-blue-500'
                  : 'hover:shadow-lg hover:scale-102'
        }`}
        style={{
          backgroundColor: data.backgroundColor || 'white',
          border: '1px solid #e2e8f0',
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-end mb-3">
            {isHovered && (
              <Edit3
                className="h-3 w-3 text-gray-400 hover:text-blue-500 cursor-pointer transition-colors duration-200"
                onClick={() => setIsEditingThis(true)}
              />
            )}
          </div>

          <div
            className="cursor-text min-h-[20px] leading-relaxed"
            style={{
              fontSize: `${data.fontSize || 14}px`,
              color: data.color || '#000000',
            }}
          >
            {text}
          </div>
        </div>
      </Card>
    </div>
  );
});

TextNode.displayName = 'TextNode';
