import React, { useState, useCallback, useEffect, forwardRef } from 'react';
import { X, Upload, File as FileIcon, Eye, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface FileData {
  file: File;
  url: string;
  id: string;
}

interface FileUploaderProps {
  /**
   * Allow multiple file selection
   */
  multiple?: boolean;
  /**
   * Callback when files change
   */
  onValueChange?: (files: File[]) => void;
  /**
   * @deprecated Use onValueChange instead
   */
  onChange?: (files: File[]) => void;
  /**
   * Maximum file size in bytes
   */
  maxSize?: number;
  /**
   * Maximum number of files
   */
  maxFiles?: number;
  /**
   * Accepted file types (MIME types)
   */
  accept?: string[];
  /**
   * Current value (controlled component)
   */
  value?: File[];
  /**
   * Default value (uncontrolled component)
   */
  defaultValue?: File[];
  /**
   * Disable the file uploader
   */
  disabled?: boolean;
  /**
   * Custom placeholder text
   */
  placeholder?: string;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Show error state
   */
  error?: boolean;
  /**
   * Error message to display
   */
  errorMessage?: string;
}

const FileUploader = forwardRef<HTMLDivElement, FileUploaderProps>(
  (
    {
      multiple = false,
      onValueChange,
      onChange,
      maxSize = 5 * 1024 * 1024, // 5MB
      maxFiles = 10,
      accept = ['image/*', 'application/pdf'],
      value,
      defaultValue = [],
      disabled = false,
      placeholder,
      className,
      error = false,
      errorMessage,
    },
    ref
  ) => {
    const [internalFiles, setInternalFiles] = useState<File[]>(defaultValue);
    const [previews, setPreviews] = useState<FileData[]>([]);
    const [selectedPreview, setSelectedPreview] = useState<FileData | null>(null);
    const [isDragActive, setIsDragActive] = useState(false);

    // Determine if component is controlled
    const isControlled = value !== undefined;
    const files = isControlled ? value : internalFiles;

    // Handle both new and legacy callback names
    const handleChange = useCallback(
      (newFiles: File[]) => {
        if (onValueChange) onValueChange(newFiles);
        if (onChange) onChange(newFiles);

        if (!isControlled) {
          setInternalFiles(newFiles);
        }
      },
      [onValueChange, onChange, isControlled]
    );

    // Generate accept object for dropzone
    const generateAcceptObject = useCallback((types: string[]) => {
      const acceptObject: Record<string, string[]> = {};

      types.forEach(type => {
        if (type === 'image/*') {
          acceptObject['image/*'] = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg'];
        } else if (type === 'application/pdf') {
          acceptObject['application/pdf'] = ['.pdf'];
        } else if (type.includes('word')) {
          acceptObject['application/msword'] = ['.doc'];
          acceptObject['application/vnd.openxmlformats-officedocument.wordprocessingml.document'] =
            ['.docx'];
        } else if (type.includes('excel') || type.includes('spreadsheet')) {
          acceptObject['application/vnd.ms-excel'] = ['.xls'];
          acceptObject['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] = [
            '.xlsx',
          ];
        } else if (type === 'text/plain') {
          acceptObject['text/plain'] = ['.txt'];
        } else if (type === 'application/json') {
          acceptObject['application/json'] = ['.json'];
        } else if (type === 'text/csv') {
          acceptObject['text/csv'] = ['.csv'];
        } else {
          acceptObject[type] = [];
        }
      });

      return acceptObject;
    }, []);

    // Generate file type description
    const getFileTypeDescription = useCallback((types: string[]) => {
      const descriptions: string[] = [];

      types.forEach(type => {
        if (type === 'image/*') descriptions.push('Images');
        else if (type === 'application/pdf') descriptions.push('PDF');
        else if (type.includes('word')) descriptions.push('Word');
        else if (type.includes('excel') || type.includes('spreadsheet')) descriptions.push('Excel');
        else if (type === 'text/plain') descriptions.push('Text');
        else if (type === 'application/json') descriptions.push('JSON');
        else if (type === 'text/csv') descriptions.push('CSV');
      });

      return descriptions.length > 0 ? descriptions.join(', ') : 'Files';
    }, []);

    // Update previews when files change
    useEffect(() => {
      const newPreviews = files.map(file => ({
        file,
        url: URL.createObjectURL(file),
        id: `${file.name}-${file.size}-${file.lastModified}`,
      }));

      // Clean up old previews
      previews.forEach(preview => {
        if (!files.some(f => f.name === preview.file.name && f.size === preview.file.size)) {
          URL.revokeObjectURL(preview.url);
        }
      });

      setPreviews(newPreviews);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [files]);

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        previews.forEach(preview => URL.revokeObjectURL(preview.url));
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onDrop = useCallback(
      (acceptedFiles: File[], rejectedFiles: any[]) => {
        if (disabled) return;

        if (rejectedFiles.length > 0) {
          const reason = rejectedFiles[0].errors[0];
          let message = 'Some files were rejected';

          if (reason.code === 'file-too-large') {
            message = `File too large. Maximum size is ${maxSize / (1024 * 1024)}MB`;
          } else if (reason.code === 'file-invalid-type') {
            message = `Invalid file type. Accepted types: ${getFileTypeDescription(accept)}`;
          }

          toast.error(message);
          return;
        }

        let updatedFiles: File[];

        if (multiple) {
          const totalFiles = files.length + acceptedFiles.length;
          if (totalFiles > maxFiles) {
            toast.error(`Maximum ${maxFiles} files allowed`);
            return;
          }
          updatedFiles = [...files, ...acceptedFiles];
        } else {
          updatedFiles = acceptedFiles;
        }

        handleChange(updatedFiles);
      },
      [disabled, files, multiple, maxFiles, maxSize, accept, getFileTypeDescription, handleChange]
    );

    const { getRootProps, getInputProps, open } = useDropzone({
      onDrop,
      accept: generateAcceptObject(accept),
      multiple,
      maxSize,
      disabled,
      noClick: true,
      onDragEnter: () => setIsDragActive(true),
      onDragLeave: () => setIsDragActive(false),
    });

    const removeFile = useCallback(
      (index: number) => {
        const newFiles = files.filter((_, i) => i !== index);
        handleChange(newFiles);
      },
      [files, handleChange]
    );

    const getFileIcon = useCallback(
      (file: File) => {
        if (file.type.startsWith('image/')) {
          const preview = previews.find(p => p.file === file);
          return preview ? (
            <div className="relative w-10 h-10 rounded-md overflow-hidden border">
              <Image src={preview.url} alt={file.name} fill className="object-cover" />
            </div>
          ) : (
            <div className="w-10 h-10 flex items-center justify-center bg-muted rounded-md border">
              <FileIcon className="w-5 h-5 text-muted-foreground" />
            </div>
          );
        }

        return (
          <div className="w-10 h-10 flex items-center justify-center bg-muted rounded-md border">
            <FileIcon className="w-5 h-5 text-muted-foreground" />
          </div>
        );
      },
      [previews]
    );

    const getFileTypeLabel = useCallback((file: File) => {
      if (file.type.startsWith('image/')) return 'Image';
      if (file.type === 'application/pdf') return 'PDF';
      if (file.type.includes('word')) return 'Word Document';
      if (file.type.includes('excel') || file.type.includes('spreadsheet')) return 'Excel';
      if (file.type === 'text/plain') return 'Text File';
      if (file.type === 'application/json') return 'JSON File';
      if (file.type === 'text/csv') return 'CSV File';
      return 'Document';
    }, []);

    const canAddMoreFiles = !disabled && (!multiple || files.length < maxFiles);
    const isMaxFilesReached = multiple && files.length >= maxFiles;

    const defaultPlaceholder = multiple
      ? 'Drop files here or click to browse'
      : 'Drop file here or click to browse';

    return (
      <div ref={ref} className={cn('w-full space-y-2', className)}>
        <div
          {...getRootProps()}
          className={cn(
            'relative border-2 border-dashed rounded-lg p-6 transition-all duration-200',
            'focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
            {
              'border-destructive bg-destructive/5': error,
              'border-muted-foreground/25 bg-muted/50': disabled,
              'border-primary bg-primary/5': isDragActive && !disabled,
              'border-border hover:border-primary/50 hover:bg-muted/50':
                !disabled && !isDragActive && !error,
              'cursor-not-allowed': disabled,
              'cursor-pointer': !disabled,
            }
          )}
        >
          <input {...getInputProps()} />

          <div className="flex flex-col items-center justify-center text-center space-y-4">
            <div
              className={cn(
                'w-12 h-12 rounded-full border-2 border-dashed flex items-center justify-center transition-colors',
                {
                  'border-destructive text-destructive': error,
                  'border-muted-foreground text-muted-foreground': disabled,
                  'border-primary text-primary': isDragActive && !disabled,
                  'border-muted-foreground/50 text-muted-foreground':
                    !disabled && !isDragActive && !error,
                }
              )}
            >
              {error ? <AlertCircle className="w-6 h-6" /> : <Upload className="w-6 h-6" />}
            </div>

            <div className="space-y-2">
              <p
                className={cn('text-sm font-medium', {
                  'text-destructive': error,
                  'text-muted-foreground': disabled,
                  'text-foreground': !disabled && !error,
                })}
              >
                {isMaxFilesReached
                  ? `Maximum ${maxFiles} files reached`
                  : placeholder || defaultPlaceholder}
              </p>

              {!isMaxFilesReached && (
                <p className="text-xs text-muted-foreground">
                  {getFileTypeDescription(accept)} up to {maxSize / (1024 * 1024)}MB
                  {multiple && ` • Maximum ${maxFiles} files`}
                </p>
              )}

              {multiple && files.length > 0 && (
                <p className="text-xs text-muted-foreground">
                  {files.length} of {maxFiles} files selected
                </p>
              )}
            </div>

            {canAddMoreFiles && (
              <button
                type="button"
                onClick={open}
                disabled={disabled}
                className={cn(
                  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
                  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                  'disabled:pointer-events-none disabled:opacity-50',
                  'bg-primary text-primary-foreground hover:bg-primary/90',
                  'h-9 px-4 py-2'
                )}
              >
                Browse Files
              </button>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && errorMessage && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="w-4 h-4" />
            {errorMessage}
          </p>
        )}

        {/* File list */}
        {files.length > 0 && (
          <div className="space-y-2">
            {files.map((file, index) => {
              const preview = previews.find(p => p.file === file);
              return (
                <div
                  key={`${file.name}-${file.size}-${index}`}
                  className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors group"
                >
                  <div className="flex-shrink-0">{getFileIcon(file)}</div>

                  <div className="flex-1 min-w-0 space-y-1">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {getFileTypeLabel(file)} • {(file.size / 1024).toFixed(1)} KB
                    </p>
                  </div>

                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {preview &&
                      (file.type.startsWith('image/') || file.type === 'application/pdf') && (
                        <button
                          type="button"
                          onClick={() => setSelectedPreview(preview)}
                          className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}

                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-destructive hover:text-destructive-foreground h-8 w-8"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Preview modal */}
        {selectedPreview && (
          <div
            className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
            onClick={() => setSelectedPreview(null)}
          >
            <div
              className="relative bg-background rounded-lg max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={e => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedPreview(null)}
                className="absolute top-4 right-4 z-10 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 bg-background/80"
              >
                <X className="w-4 h-4" />
              </button>

              {selectedPreview.file.type.startsWith('image/') ? (
                <Image
                  src={selectedPreview.url}
                  alt={selectedPreview.file.name}
                  width={800}
                  height={600}
                  className="object-contain max-h-[80vh] w-full"
                />
              ) : (
                <iframe
                  src={selectedPreview.url}
                  className="w-full h-[80vh]"
                  title={selectedPreview.file.name}
                />
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

FileUploader.displayName = 'FileUploader';

export default FileUploader;
