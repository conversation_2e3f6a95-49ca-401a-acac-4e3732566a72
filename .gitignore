# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# md
DARK_THEME_GUIDE.md
ANALYTICS_ENHANCEMENT_GUIDE.md
PAYMENT_INTEGRATION_GUIDE.md
RAZORPAY_SETUP_GUIDE.md
STORAGE_SYSTEM.md

# PWA
workbox-*.js
sw.js.map
public/sw.js
public/workbox-*.js
public/precache-manifest.*.js
.next/static/chunks/pages/_app-*.js.map
.next/static/runtime/
.next/pwa/
workbox-*
.workbox/
service-worker.js
precache-manifest.*.js