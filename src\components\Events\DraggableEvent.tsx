import React, { useState, useCallback } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { addMinutes, differenceInMinutes, format } from 'date-fns';
import { MapPin, Repeat, Users, Link, Bell } from 'lucide-react';
const PRIORITY_COLORS = {
  low: '#10b981',
  medium: '#f59e0b',
  high: '#ef4444',
};
interface DraggableEventProps {
  event: any;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  style?: React.CSSProperties;
  onClick: (event: any) => void;
  onResize?: (event: any, newStart: Date, newEnd: Date) => void;
  isResizing?: boolean;
}

const DraggableEvent: React.FC<DraggableEventProps> = ({
  event,
  viewType,
  style,
  onClick,
  onResize,
  isResizing = false,
}) => {
  // Defensive: ensure event._id is string
  const eventId: string = typeof event._id === 'string' ? event._id : `event-${event.title}`;
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: eventId,
    data: {
      type: 'event',
      event,
      originalStart: event.startTime,
      originalEnd: event.endTime,
    },
  });

  const dragStyle = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
  };

  const [isHovered, setIsHovered] = useState(false);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent, direction: 'top' | 'bottom') => {
      if (!onResize) return;
      e.preventDefault();
      e.stopPropagation();
      const startY = e.clientY;
      const startTime = new Date(event.startTime);
      const endTime = new Date(event.endTime);
      const handleMouseMove = (e: MouseEvent) => {
        const deltaY = e.clientY - startY;
        const minutesDelta = Math.round(deltaY / 2);
        if (direction === 'top') {
          const newStart = addMinutes(startTime, minutesDelta);
          if (newStart < endTime) {
            onResize(event, newStart, endTime);
          }
        } else {
          const newEnd = addMinutes(endTime, minutesDelta);
          if (newEnd > startTime) {
            onResize(event, startTime, newEnd);
          }
        }
      };
      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [event, onResize]
  );

  const eventDuration = differenceInMinutes(new Date(event.endTime), new Date(event.startTime));
  const showResizeHandles = viewType !== 'month' && eventDuration >= 30 && isHovered && onResize;

  return (
    <div
      ref={setNodeRef}
      className={`
          relative group px-2 py-1 rounded text-xs font-medium cursor-grab theme-transition
          ${isDragging ? 'opacity-50 scale-95 cursor-grabbing' : 'opacity-100'}
          ${isResizing ? 'z-50' : 'z-10'}
          hover:shadow-md border-l-2 select-none theme-shadow-sm
        `}
      style={{
        backgroundColor: event.colorId || '#3b82f6',
        borderLeftColor: PRIORITY_COLORS[event.priority],
        color: 'white',
        minHeight: viewType === 'month' ? '20px' : '30px',
        ...style,
        ...dragStyle,
      }}
      onClick={e => {
        e.stopPropagation();
        onClick(event);
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={`${event.title}\n${format(new Date(event.startTime), 'HH:mm')} - ${format(new Date(event.endTime), 'HH:mm')}`}
      {...attributes}
      {...listeners}
    >
      <div className="truncate">{event.title}</div>
      {viewType !== 'month' && (
        <div className="text-xs opacity-75">
          {format(new Date(event.startTime), 'HH:mm')} - {format(new Date(event.endTime), 'HH:mm')}
        </div>
      )}
      {event.location && viewType === 'day' && (
        <div className="flex items-center gap-1 mt-1 opacity-75">
          <MapPin className="w-2 h-2" />
          <span className="truncate text-xs">{event.location}</span>
        </div>
      )}
      {/* Event indicators */}
      <div className="absolute top-1 right-1 flex gap-1">
        {event.isRecurring && <Repeat className="w-2 h-2 opacity-75" />}
        {event.attendees && event.attendees.length > 0 && <Users className="w-2 h-2 opacity-75" />}
        {event.taskIds && event.taskIds.length > 0 && <Link className="w-2 h-2 opacity-75" />}
        {event.reminders && event.reminders.length > 0 && <Bell className="w-2 h-2 opacity-75" />}
      </div>
      {/* Resize handles */}
      {showResizeHandles && (
        <>
          <div
            className="absolute top-0 left-0 right-0 h-1 cursor-n-resize bg-white bg-opacity-20 hover:bg-opacity-40"
            onMouseDown={e => handleMouseDown(e, 'top')}
          />
          <div
            className="absolute bottom-0 left-0 right-0 h-1 cursor-s-resize bg-white bg-opacity-20 hover:bg-opacity-40"
            onMouseDown={e => handleMouseDown(e, 'bottom')}
          />
        </>
      )}
    </div>
  );
};

export default DraggableEvent;
