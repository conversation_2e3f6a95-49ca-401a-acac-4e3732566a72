import mongoose, { Schema, Document } from 'mongoose';

export interface IEmailTemplate extends Document {
  name: string;
  description?: string;
  category: 'Tasks' | 'Projects' | 'Reminders' | 'Reports' | 'Team';
  subject: string;
  content: string;
  status: 'active' | 'draft' | 'disabled';
  fromName: string;
  fromEmail: string;
  variables: string[];
  organizationId: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  lastModifiedBy?: mongoose.Types.ObjectId;
  isDefault: boolean;
  templateType: 'system' | 'custom';
  metadata?: {
    usageCount?: number;
    lastUsedAt?: Date;
    tags?: string[];
  };
}

const emailTemplateSchema = new Schema<IEmailTemplate>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    category: {
      type: String,
      required: true,
      enum: ['Tasks', 'Projects', 'Reminders', 'Reports', 'Team'],
    },
    subject: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    content: {
      type: String,
      required: true,
      maxlength: 10000,
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'draft', 'disabled'],
      default: 'draft',
    },
    fromName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
      default: 'TaskFluxio',
    },
    fromEmail: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
      default: '<EMAIL>',
    },
    variables: [
      {
        type: String,
        trim: true,
      },
    ],
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    templateType: {
      type: String,
      enum: ['system', 'custom'],
      default: 'custom',
    },
    metadata: {
      usageCount: {
        type: Number,
        default: 0,
      },
      lastUsedAt: {
        type: Date,
      },
      tags: [
        {
          type: String,
          trim: true,
          lowercase: true,
        },
      ],
    },
  },
  {
    timestamps: true,
  }
);

emailTemplateSchema.index({ organizationId: 1, category: 1 });
emailTemplateSchema.index({ organizationId: 1, status: 1 });
emailTemplateSchema.index({ organizationId: 1, templateType: 1 });
emailTemplateSchema.index({ organizationId: 1, name: 1 }, { unique: true });

emailTemplateSchema.pre('save', function (next) {
  if (this.isModified('content') || this.isModified('subject')) {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables = new Set<string>();

    let match;
    while ((match = variableRegex.exec(this.subject)) !== null) {
      variables.add(match[0]);
    }

    variableRegex.lastIndex = 0; // Reset regex
    while ((match = variableRegex.exec(this.content)) !== null) {
      variables.add(match[0]);
    }

    this.variables = Array.from(variables);
  }
  next();
});

interface IEmailTemplateModel extends mongoose.Model<IEmailTemplate> {
  createDefaultTemplates(organizationId: string, createdBy: string): Promise<void>;
}

emailTemplateSchema.statics.createDefaultTemplates = async function (
  organizationId: string,
  createdBy: string
) {
  const defaultTemplates = [
    {
      name: 'Task Assignment',
      description: 'Sent when a task is assigned to a team member',
      category: 'Tasks',
      status: 'active',
      subject: 'New Task Assigned: {{task.title}}',
      content: `Hi {{user.name}},

You have been assigned a new task: {{task.title}}

Project: {{project.name}}
Due Date: {{due_date}}

Description:
{{task.description}}

You can view and manage this task in your TaskFluxio dashboard.

Best regards,
The TaskFluxio Team`,
      isDefault: true,
      templateType: 'system',
    },
    {
      name: 'Task Completed',
      description: 'Sent when a task is marked as completed',
      category: 'Tasks',
      status: 'active',
      subject: 'Task Completed: {{task.title}}',
      content: `Hi {{user.name}},

Great news! The task "{{task.title}}" has been completed by {{completed_by}}.

Project: {{project.name}}
Completed on: {{completion_date}}

You can view the completed task in your TaskFluxio dashboard.

Best regards,
The TaskFluxio Team`,
      isDefault: true,
      templateType: 'system',
    },
    {
      name: 'Project Invitation',
      description: 'Sent when a user is invited to join a project',
      category: 'Projects',
      status: 'active',
      subject: "You've been invited to {{project.name}}",
      content: `Hi {{user.name}},

{{invited_by}} has invited you to join the project "{{project.name}}" in {{organization.name}}.

Project Description:
{{project.description}}

Click the link below to view the project and start collaborating:
{{project.link}}

Best regards,
The TaskFluxio Team`,
      isDefault: true,
      templateType: 'system',
    },
    {
      name: 'Due Date Reminder',
      description: 'Sent 24 hours before a task is due',
      category: 'Reminders',
      status: 'active',
      subject: 'Reminder: {{task.title}} is due tomorrow',
      content: `Hi {{user.name}},

This is a friendly reminder that your task "{{task.title}}" is due tomorrow ({{due_date}}).

Project: {{project.name}}
Priority: {{task.priority}}

Please make sure to complete this task on time. If you need help or have any questions, don't hesitate to reach out to your team.

Best regards,
The TaskFluxio Team`,
      isDefault: true,
      templateType: 'system',
    },
    {
      name: 'Team Invitation',
      description: 'Sent when a user is invited to join the organization',
      category: 'Team',
      status: 'active',
      subject: 'Welcome to {{organization.name}}',
      content: `Hi {{user.name}},

Welcome to {{organization.name}}! {{invited_by}} has invited you to join the team as a {{role}}.

You now have access to:
- Create and manage projects
- Collaborate with team members
- Track tasks and deadlines
- Access team resources

Get started by logging into your TaskFluxio dashboard:
{{dashboard.link}}

If you have any questions, feel free to reach out to your team or our support team.

Best regards,
The TaskFluxio Team`,
      isDefault: true,
      templateType: 'system',
    },
  ];

  const templates = defaultTemplates.map(template => ({
    ...template,
    organizationId,
    createdBy,
    fromName: 'TaskFluxio',
    fromEmail: '<EMAIL>',
  }));

  try {
    await this.insertMany(templates, { ordered: false });
  } catch (error: any) {
    // Ignore duplicate key errors (templates already exist)
    if (error.code !== 11000) {
      throw error;
    }
  }
};

export const EmailTemplate = (mongoose.models.EmailTemplate ||
  mongoose.model<IEmailTemplate, IEmailTemplateModel>(
    'EmailTemplate',
    emailTemplateSchema
  )) as IEmailTemplateModel;
