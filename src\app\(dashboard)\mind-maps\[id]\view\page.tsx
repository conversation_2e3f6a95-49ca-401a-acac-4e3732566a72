'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Download, Edit, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useMindMap } from '@/hooks/useMindMap';
import { useMindMapStore } from '@/stores/mindMapStore';
import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import MindMapCanvas from '@/components/MindMap/MindMapCanvas';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { EmptyState } from '@/components/Global/EmptyState';
import { ExportMindMapModal } from '@/components/MindMap/ExportMindMapModal';

export default function MindMapViewPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const { data: mindMap, isLoading, error } = useMindMap(id);
  const { setCurrentMindMap } = useMindMapStore();
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  useEffect(() => {
    if (mindMap) {
      setCurrentMindMap({
        ...mindMap,
        nodes: mindMap.nodes || [],
        connections: mindMap.connections || [],
      });
    }

    return () => {
      setCurrentMindMap(null);
    };
  }, [mindMap, setCurrentMindMap]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'theme-badge-success';
      case 'draft':
        return 'theme-badge-warning';
      case 'archived':
        return 'theme-badge-secondary';
      case 'template':
        return 'theme-badge-primary';
      default:
        return 'theme-badge-secondary';
    }
  };

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="theme-surface rounded-lg h-full w-full flex items-center justify-center"
      >
        <LoadingSpinner variant="wave" />
      </motion.div>
    );
  }

  if (error || !mindMap) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="theme-surface rounded-lg h-full w-full flex items-center justify-center"
      >
        <EmptyState
          title="Error"
          description="An error occurred while loading the mind map."
          icon="error"
        />
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface rounded-lg h-full w-full flex flex-col"
    >
      {/* Header */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 theme-surface-elevated theme-border rounded-t-lg">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/mind-maps')}
            className="theme-button-ghost"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Separator orientation="vertical" className="h-6 theme-border" />
          <div>
            <h1 className="text-lg font-semibold theme-text-primary">{mindMap.title}</h1>
            <div className="flex items-center gap-2 text-sm theme-text-secondary">
              <span className={getStatusColor(mindMap.status)}>{mindMap.status}</span>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {mindMap.analytics?.viewCount || 0} views
              </div>
              <span>•</span>
              <span>
                Updated {formatDistanceToNow(new Date(mindMap.updatedAt), { addSuffix: true })}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <span className="theme-badge-primary flex items-center gap-1">
            <Eye className="w-3 h-3 mr-1" />
            View Mode
          </span>

          <Separator orientation="vertical" className="h-6 theme-border" />

          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/mind-maps/${id}/edit`)}
            className="theme-button-ghost flex"
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsExportModalOpen(true);
            }}
            className="theme-button-ghost"
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <MindMapCanvas isEditing={false} />
      </div>
      <ExportMindMapModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        mindMap={mindMap}
      />
    </motion.div>
  );
}
