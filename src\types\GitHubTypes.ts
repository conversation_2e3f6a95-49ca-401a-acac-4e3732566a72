export interface GitHubWebhook {
  id: number;
  name: string;
  active: boolean;
  events: string[];
  config: {
    url: string;
    content_type: string;
    insecure_ssl: string;
  };
}
export interface GitHubPullRequest {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed' | 'merged';
  html_url: string;
  user: {
    login: string;
    avatar_url: string;
  };
  assignees: Array<{
    login: string;
    avatar_url: string;
  }>;
  labels: Array<{
    name: string;
    color: string;
    description?: string;
  }>;
  head: {
    ref: string;
    sha: string;
  };
  base: {
    ref: string;
    sha: string;
  };
  created_at: string;
  updated_at: string;
  closed_at?: string;
  merged_at?: string;
}
export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed';
  html_url: string;
  user: {
    login: string;
    avatar_url: string;
  };
  assignees: Array<{
    login: string;
    avatar_url: string;
  }>;
  labels: Array<{
    name: string;
    color: string;
    description?: string;
  }>;
  created_at: string;
  updated_at: string;
  closed_at?: string;
  comments?: number;
  repository?: {
    id: string;
    name: string;
    fullName: string;
    htmlUrl: string;
    private: boolean;
    language?: string;
  };
}
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description?: string;
  private: boolean;
  html_url: string;
  clone_url: string;
  default_branch: string;
  language?: string;
  stargazers_count: number;
  forks_count: number;
  open_issues_count: number;
  owner: {
    login: string;
    id: number;
    avatar_url: string;
    type: string;
  };
  permissions?: {
    admin: boolean;
    maintain: boolean;
    push: boolean;
    triage: boolean;
    pull: boolean;
  };
}
export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
  bio?: string;
  company?: string;
  location?: string;
  public_repos: number;
  followers: number;
  following: number;
}
export interface ConnectedRepository {
  _id: string;
  repositoryId: string;
  name: string;
  fullName: string;
  description?: string;
  htmlUrl: string;
  private: boolean;
  language?: string;
  stargazersCount: number;
  forksCount: number;
  openIssuesCount: number;
  isConnected: boolean;
  lastSyncedAt?: string;
  syncSettings: {
    autoCreateTasks: boolean;
    syncIssues: boolean;
    syncPullRequests: boolean;
  };
  webhookId?: string;
}
