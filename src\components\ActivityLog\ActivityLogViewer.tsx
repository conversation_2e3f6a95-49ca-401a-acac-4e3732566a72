'use client';
import React, { useState, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RightSidebar } from '@/components/Global/RightSidebar';
import ActivityLogSidebar from './ActivityLogSidebar';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { EmptyState } from '@/components/Global/EmptyState';
import { DataTable } from '@/components/Global/DataTable';
import AdvancedPagination from '@/components/ui/AdvancedPagination';
import { Activity, Eye, RefreshCw, AlertCircle } from 'lucide-react';
import { format as formatDate } from 'date-fns';
import { UserAvatar } from '@/components/ui/UserAvatar';
import { useActivityLogs } from '@/hooks/useActivityLogs';
import { useActivityLogExport } from '@/hooks/useActivityLogExport';
import { ActivityLog } from '@/services/ActivityLogApi.service';

interface ActivityLogViewerProps {
  userId?: string;
  organizationId?: string;
  projectId?: string;
  resourceType?: string;
  resourceId?: string;
  filters?: Record<string, any>;
  showFilters?: boolean;
  showExport?: boolean;
  className?: string;
}

const ACTION_COLORS = {
  create: 'bg-green-100 text-green-800 border-green-200',
  update: 'bg-blue-100 text-blue-800 border-blue-200',
  delete: 'bg-red-100 text-red-800 border-red-200',
  login: 'bg-purple-100 text-purple-800 border-purple-200',
  logout: 'bg-gray-100 text-gray-800 border-gray-200',
  view: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  export: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  import: 'bg-teal-100 text-teal-800 border-teal-200',
  share: 'bg-pink-100 text-pink-800 border-pink-200',
  archive: 'bg-orange-100 text-orange-800 border-orange-200',
};

const RESOURCE_TYPE_COLORS = {
  task: 'bg-blue-50 text-blue-700',
  project: 'bg-green-50 text-green-700',
  note: 'bg-purple-50 text-purple-700',
  user: 'bg-orange-50 text-orange-700',
  organization: 'bg-red-50 text-red-700',
  integration: 'bg-cyan-50 text-cyan-700',
  report: 'bg-indigo-50 text-indigo-700',
  timeline: 'bg-pink-50 text-pink-700',
  notification: 'bg-yellow-50 text-yellow-700',
  file: 'bg-gray-50 text-gray-700',
  comment: 'bg-teal-50 text-teal-700',
  budget: 'bg-violet-50 text-violet-700',
};

export const ActivityLogViewer: React.FC<ActivityLogViewerProps> = ({
  userId,
  organizationId,
  projectId,
  resourceType,
  resourceId,
  filters = {},
  showExport = true,
  className = '',
}) => {
  const [selectedLog, setSelectedLog] = useState<ActivityLog | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Build query parameters
  const queryParams = useMemo(() => ({
    page: currentPage,
    limit: 25,
    userId,
    organizationId,
    projectId,
    resourceType,
    resourceId,
    ...filters,
  }), [currentPage, userId, organizationId, projectId, resourceType, resourceId, filters]);

  // Fetch activity logs using our custom hook
  const {
    data,
    logs,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useActivityLogs({
    filters: queryParams,
    enabled: true,
  });

  // Export functionality
  const { exportLogs, isExporting } = useActivityLogExport();

  const handleExport = async (format: 'csv' | 'json' | 'xlsx') => {
    await exportLogs(format, {
      userId,
      organizationId,
      projectId,
      resourceType,
      resourceId,
      ...filters,
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const columns = useMemo(
    () => [
      {
        key: 'timestamp' as keyof ActivityLog,
        label: 'Time',
        render: (_value: any, log: ActivityLog) => {
          const timestamp = new Date(log.timestamp);
          const isValidDate = !isNaN(timestamp.getTime());

          if (!isValidDate) {
            return <div className="text-sm text-muted-foreground">Invalid date</div>;
          }

          return (
            <div className="text-sm">
              <div>{formatDate(timestamp, 'MMM dd, yyyy')}</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(timestamp, 'HH:mm:ss')}
              </div>
            </div>
          );
        },
        sortable: true,
      },
      {
        key: 'userId' as keyof ActivityLog,
        label: 'User',
        render: (_value: any, log: ActivityLog) => (
          <div className="flex items-center gap-2">
            <UserAvatar
              src={log.userImage}
              alt={log.userName || 'User'}
              size="sm"
              fallbackText={log.userName || log.userEmail || '?'}
            />
            <div className="text-sm">
              <div className="font-medium">{log.userName || 'Unknown User'}</div>
              {log.userEmail && (
                <div className="text-xs text-muted-foreground">{log.userEmail}</div>
              )}
            </div>
          </div>
        ),
      },
      {
        key: 'action' as keyof ActivityLog,
        label: 'Action',
        render: (_value: any, log: ActivityLog) => {
          const colorClass =
            ACTION_COLORS[log.action as keyof typeof ACTION_COLORS] ||
            'bg-gray-100 text-gray-800 border-gray-200';
          return (
            <Badge variant="outline" className={`${colorClass} flex items-center gap-1 w-fit`}>
              {log.action}
            </Badge>
          );
        },
      },
      {
        key: 'resourceType' as keyof ActivityLog,
        label: 'Resource',
        render: (_value: any, log: ActivityLog) => (
          <div className="text-sm">
            <Badge
              variant="secondary"
              className={`${RESOURCE_TYPE_COLORS[log.resourceType as keyof typeof RESOURCE_TYPE_COLORS] || 'bg-gray-50 text-gray-700'} mb-1`}
            >
              {log.resourceType}
            </Badge>
            {log.resourceName && (
              <div className="text-xs text-muted-foreground mt-1">
                {log.resourceName || 'Unnamed Resource'}
              </div>
            )}
          </div>
        ),
      },
      {
        key: 'description' as keyof ActivityLog,
        label: 'Description',
        render: (_value: any, log: ActivityLog) => (
          <div className="text-sm max-w-xs truncate" title={log.description}>
            {log.description}
          </div>
        ),
      },
      {
        key: 'metadata' as keyof ActivityLog,
        label: 'Details',
        render: (_value: any, log: ActivityLog) => (
          <Button
            variant="ghost"
            size="sm"
            onClick={e => {
              e.stopPropagation();
              setSelectedLog(log);
              setShowSidebar(true);
            }}
            className="text-xs"
          >
            <Eye className="w-3 h-3 mr-1" />
            View
          </Button>
        ),
      },
    ],
    []
  );

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      );
    }

    if (isError) {
      return (
        <EmptyState
          type="error"
          icon={<AlertCircle className="w-6 h-6 text-danger" />}
          title="Error Loading Activity Logs"
          description={error?.message || 'Failed to load activity logs'}
          actions={[
            {
              label: 'Retry',
              onClick: () => refetch(),
              variant: 'default',
            },
          ]}
          size="md"
          animated={true}
          showBackground={true}
        />
      );
    }

    if (logs?.length === 0) {
      return (
        <EmptyState
          type="no-data"
          icon={<Activity className="w-6 h-6 text-info" />}
          title="No Activity Logs"
          description="No activity logs found for the selected filters."
          size="md"
          animated={true}
          showBackground={true}
        />
      );
    }

    return (
      <div className="space-y-4 theme-scrollbar">
        <DataTable
          data={logs}
          columns={columns}
          loading={isLoading}
          onRowClick={log => {
            setSelectedLog(log);
            setShowSidebar(true);
          }}
        />
        {data?.pagination && data.pagination.totalPages > 1 && (
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={data.pagination.totalPages}
            totalItems={data.pagination.total}
            itemsPerPage={25}
            onPageChange={handlePageChange}
            showInfo={true}
            itemName="logs"
          />
        )}
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-end">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {showExport && (
            <Select onValueChange={format => handleExport(format as 'csv' | 'json' | 'xlsx')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Export" />
              </SelectTrigger>
              {isExporting && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80">
                  <LoadingSpinner size="sm" />
                </div>
              )}
              <SelectContent>
                <SelectItem value="csv">CSV</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
                <SelectItem value="xlsx">Excel</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <Card>
        <CardContent className="p-6">{renderContent()}</CardContent>
      </Card>

      <RightSidebar
        isOpen={showSidebar}
        onClose={() => setShowSidebar(false)}
        title="Activity Log Details"
      >
        <ActivityLogSidebar log={selectedLog} />
      </RightSidebar>
    </div>
  );
};
