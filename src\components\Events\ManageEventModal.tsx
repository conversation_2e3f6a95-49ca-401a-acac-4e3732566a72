import React from 'react';
import Modal from '../Global/Modal';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { format } from 'date-fns';
import { Checkbox } from '../ui/checkbox';
import ReactSelect, { Option } from '../Global/ReactSelect';
import { MultiValue, SingleValue } from 'react-select';
import { Button } from '../ui/button';
import { Plus, X } from 'lucide-react';
import { EventFormData } from '@/types/EventTypes';
import { IEventAttendee, IEventReminder } from '@/models/Event';

interface Task {
  id: string;
  title: string;
  dueDate?: Date;
}

interface ReminderOption {
  value: number;
  label: string;
}

interface RecurrenceFrequency {
  value: 'daily' | 'weekly' | 'monthly' | 'yearly';
  label: string;
}

interface TimezoneOption {
  value: string;
  label: string;
}

interface ManageEventModalProps {
  showCreateEvent: boolean;
  setShowCreateEvent: (show: boolean) => void;
  formData: EventFormData;
  setFormData: React.Dispatch<React.SetStateAction<EventFormData>>;
  handleCreateEvent: (e: React.FormEvent) => void;
  EVENT_COLORS: string[];
  newAttendee: string;
  setNewAttendee: (email: string) => void;
  addAttendee: () => void;
  removeAttendee: (email: string) => void;
  REMINDER_OPTIONS: ReminderOption[];
  removeReminder: (index: number) => void;
  showRecurringEvents: boolean;
  RECURRENCE_FREQUENCIES: RecurrenceFrequency[];
  addReminder: () => void;
  TIMEZONE_OPTIONS: TimezoneOption[];
  tasks: Task[];
  setSelectedTasks: React.Dispatch<React.SetStateAction<string[]>>;
  showTaskIntegration: boolean;
  selectedTasks: string[];
  resetForm: () => void;
}

const ManageEventModal: React.FC<ManageEventModalProps> = ({
  showCreateEvent,
  setShowCreateEvent,
  formData,
  setFormData,
  handleCreateEvent,
  EVENT_COLORS,
  newAttendee,
  setNewAttendee,
  addAttendee,
  removeAttendee,
  REMINDER_OPTIONS,
  removeReminder,
  showRecurringEvents,
  RECURRENCE_FREQUENCIES,
  addReminder,
  TIMEZONE_OPTIONS,
  tasks,
  setSelectedTasks,
  showTaskIntegration,
  selectedTasks,
  resetForm,
}) => {
  return (
    <Modal isOpen={showCreateEvent} onClose={() => setShowCreateEvent(false)} size="2xl">
      <div className="max-w-2xl max-h-[calc(100vh-12rem)] overflow-y-auto">
        <div>
          <p>Create New Event</p>
        </div>

        <form onSubmit={handleCreateEvent} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="attendees">Attendees</TabsTrigger>
              <TabsTrigger value="recurrence">Recurrence</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div>
                <Label htmlFor="title">Event Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setFormData((prev: EventFormData) => ({ ...prev, title: e.target.value }))
                  }
                  placeholder="Enter event title"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    setFormData((prev: EventFormData) => ({ ...prev, description: e.target.value }))
                  }
                  placeholder="Enter event description"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setFormData((prev: EventFormData) => ({ ...prev, location: e.target.value }))
                  }
                  placeholder="Enter event location"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Start Time</Label>
                  <Input
                    type="datetime-local"
                    value={format(formData.startTime, "yyyy-MM-dd'T'HH:mm")}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setFormData((prev: EventFormData) => ({
                        ...prev,
                        startTime: new Date(e.target.value),
                      }))
                    }
                  />
                </div>
                <div>
                  <Label>End Time</Label>
                  <Input
                    type="datetime-local"
                    value={format(formData.endTime, "yyyy-MM-dd'T'HH:mm")}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setFormData((prev: EventFormData) => ({
                        ...prev,
                        endTime: new Date(e.target.value),
                      }))
                    }
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allDay"
                  checked={formData.isAllDay}
                  onCheckedChange={(checked: boolean) =>
                    setFormData((prev: EventFormData) => ({
                      ...prev,
                      isAllDay: checked as boolean,
                    }))
                  }
                />
                <Label htmlFor="allDay">All day event</Label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Priority</Label>
                  <ReactSelect
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                    ]}
                    value={{
                      value: formData.priority,
                      label: formData.priority.charAt(0).toUpperCase() + formData.priority.slice(1),
                    }}
                    onChange={(newValue: MultiValue<Option> | SingleValue<Option>) => {
                      const option = newValue as SingleValue<Option>;
                      if (option) {
                        setFormData((prev: EventFormData) => ({
                          ...prev,
                          priority: option.value as 'low' | 'medium' | 'high',
                        }));
                      }
                    }}
                    placeholder="Select priority"
                  />
                </div>

                <div>
                  <Label>Color</Label>
                  <div className="flex gap-2 mt-2">
                    {EVENT_COLORS.slice(0, 6).map((color: string) => (
                      <button
                        key={color}
                        type="button"
                        className={`w-6 h-6 rounded border-2 ${
                          formData.colorId === color ? 'border-gray-400' : 'border-gray-200'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() =>
                          setFormData((prev: EventFormData) => ({ ...prev, colorId: color }))
                        }
                      />
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="attendees" className="space-y-4">
              <div>
                <Label>Add Attendees</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newAttendee}
                    onChange={e => setNewAttendee(e.target.value)}
                    placeholder="Enter email address"
                    type="email"
                  />
                  <Button type="button" onClick={addAttendee}>
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {formData.attendees.length > 0 && (
                <div>
                  <Label>Attendees</Label>
                  <div className="space-y-2 mt-2">
                    {formData.attendees.map((attendee: IEventAttendee, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <div>
                          <div className="font-medium">{attendee.name}</div>
                          <div className="text-sm text-gray-500">{attendee.email}</div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAttendee(attendee.email)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <Label>Reminders</Label>
                <div className="space-y-2 mt-2">
                  {formData.reminders.map((reminder: IEventReminder, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <ReactSelect
                        options={REMINDER_OPTIONS.map((option: ReminderOption) => ({
                          value: option.value.toString(),
                          label: option.label,
                        }))}
                        value={
                          REMINDER_OPTIONS.find(
                            (option: ReminderOption) => option.value === reminder.minutes
                          )
                            ? {
                                value: reminder.minutes.toString(),
                                label:
                                  REMINDER_OPTIONS.find(
                                    (option: ReminderOption) => option.value === reminder.minutes
                                  )?.label || '',
                              }
                            : null
                        }
                        onChange={(newValue: MultiValue<Option> | SingleValue<Option>) => {
                          const option = newValue as SingleValue<Option>;
                          if (option) {
                            const newReminders = [...formData.reminders];
                            newReminders[index].minutes = parseInt(option.value.toString());
                            setFormData((prev: EventFormData) => ({
                              ...prev,
                              reminders: newReminders,
                            }));
                          }
                        }}
                        placeholder="Select reminder time"
                        className="w-48"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeReminder(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" size="sm" onClick={addReminder}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Reminder
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="recurrence" className="space-y-4">
              {showRecurringEvents && (
                <>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="recurring"
                      checked={!!formData.recurrence}
                      onCheckedChange={(checked: boolean) => {
                        if (checked) {
                          setFormData((prev: EventFormData) => ({
                            ...prev,
                            recurrence: {
                              frequency: 'weekly',
                              interval: 1,
                              count: 10,
                            },
                          }));
                        } else {
                          setFormData((prev: EventFormData) => ({
                            ...prev,
                            recurrence: undefined,
                          }));
                        }
                      }}
                    />
                    <Label htmlFor="recurring">Make this a recurring event</Label>
                  </div>

                  {formData.recurrence && (
                    <div className="space-y-4 border-l-2 border-primary/20 pl-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Frequency</Label>
                          <ReactSelect
                            options={RECURRENCE_FREQUENCIES.map((freq: RecurrenceFrequency) => ({
                              value: freq.value,
                              label: freq.label,
                            }))}
                            value={
                              RECURRENCE_FREQUENCIES.find(
                                (freq: RecurrenceFrequency) =>
                                  freq.value === formData.recurrence!.frequency
                              )
                                ? {
                                    value: formData.recurrence!.frequency,
                                    label:
                                      RECURRENCE_FREQUENCIES.find(
                                        (freq: RecurrenceFrequency) =>
                                          freq.value === formData.recurrence!.frequency
                                      )?.label || '',
                                  }
                                : null
                            }
                            onChange={(newValue: MultiValue<Option> | SingleValue<Option>) => {
                              const option = newValue as SingleValue<Option>;
                              if (option) {
                                setFormData((prev: EventFormData) => ({
                                  ...prev,
                                  recurrence: {
                                    ...prev.recurrence!,
                                    frequency: option.value as
                                      | 'daily'
                                      | 'weekly'
                                      | 'monthly'
                                      | 'yearly',
                                  },
                                }));
                              }
                            }}
                            placeholder="Select frequency"
                          />
                        </div>

                        <div>
                          <Label>Interval</Label>
                          <Input
                            type="number"
                            min="1"
                            value={formData.recurrence.interval || 1}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                              setFormData((prev: EventFormData) => ({
                                ...prev,
                                recurrence: {
                                  ...prev.recurrence!,
                                  interval: parseInt(e.target.value) || 1,
                                },
                              }))
                            }
                          />
                        </div>
                      </div>

                      <div>
                        <Label>Number of occurrences</Label>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          value={formData.recurrence.count || 10}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setFormData((prev: EventFormData) => ({
                              ...prev,
                              recurrence: {
                                ...prev.recurrence!,
                                count: parseInt(e.target.value) || 10,
                              },
                            }))
                          }
                        />
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div>
                <Label>Timezone</Label>
                <ReactSelect
                  options={TIMEZONE_OPTIONS.map((tz: TimezoneOption) => ({
                    value: tz.value,
                    label: tz.label,
                  }))}
                  value={
                    TIMEZONE_OPTIONS.find((tz: TimezoneOption) => tz.value === formData.timezone)
                      ? {
                          value: formData.timezone,
                          label:
                            TIMEZONE_OPTIONS.find(
                              (tz: TimezoneOption) => tz.value === formData.timezone
                            )?.label || '',
                        }
                      : null
                  }
                  onChange={(newValue: MultiValue<Option> | SingleValue<Option>) => {
                    const option = newValue as SingleValue<Option>;
                    if (option) {
                      setFormData((prev: EventFormData) => ({
                        ...prev,
                        timezone: option.value.toString(),
                      }));
                    }
                  }}
                  placeholder="Select timezone"
                />
              </div>

              <div>
                <Label>Visibility</Label>
                <ReactSelect
                  options={[
                    { value: 'default', label: 'Default' },
                    { value: 'public', label: 'Public' },
                    { value: 'private', label: 'Private' },
                    { value: 'confidential', label: 'Confidential' },
                  ]}
                  value={{
                    value: formData.visibility,
                    label:
                      formData.visibility.charAt(0).toUpperCase() + formData.visibility.slice(1),
                  }}
                  onChange={(newValue: MultiValue<Option> | SingleValue<Option>) => {
                    const option = newValue as SingleValue<Option>;
                    if (option) {
                      setFormData((prev: EventFormData) => ({
                        ...prev,
                        visibility: option.value as
                          | 'default'
                          | 'public'
                          | 'private'
                          | 'confidential',
                      }));
                    }
                  }}
                  placeholder="Select visibility"
                />
              </div>

              {showTaskIntegration && tasks.length > 0 && (
                <div>
                  <Label>Link to Tasks</Label>
                  <div className="space-y-2 mt-2 max-h-32 overflow-y-auto">
                    {tasks.map((task: Task) => (
                      <div key={task.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`task-${task.id}`}
                          checked={selectedTasks.includes(task.id)}
                          onCheckedChange={(checked: boolean) => {
                            if (checked) {
                              setSelectedTasks((prev: string[]) => [...prev, task.id]);
                              setFormData((prev: EventFormData) => ({
                                ...prev,
                                taskIds: [...prev.taskIds, task.id],
                              }));
                            } else {
                              setSelectedTasks((prev: string[]) =>
                                prev.filter((id: string) => id !== task.id)
                              );
                              setFormData((prev: EventFormData) => ({
                                ...prev,
                                taskIds: prev.taskIds.filter((id: string) => id !== task.id),
                              }));
                            }
                          }}
                        />
                        <Label htmlFor={`task-${task.id}`} className="text-sm">
                          {task.title}
                          {task.dueDate && (
                            <span className="text-xs text-gray-500 ml-2">
                              Due: {format(task.dueDate, 'MMM d')}
                            </span>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowCreateEvent(false);
                resetForm();
              }}
            >
              Cancel
            </Button>
            <Button type="submit">Create Event</Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ManageEventModal;
