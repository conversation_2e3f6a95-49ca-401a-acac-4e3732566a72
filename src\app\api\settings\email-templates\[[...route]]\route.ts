import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { User } from '@/models/User';
import { EmailTemplate } from '@/models/EmailTemplate';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/settings/email-templates');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.get('/', async c => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();
    const dbUser = await User.findOne({ email: user.email });
    if (!dbUser || !dbUser.organizationId) {
      return c.json({ error: 'User or organization not found' }, 404);
    }

    // Ensure default templates exist for this organization
    await EmailTemplate.createDefaultTemplates(
      dbUser.organizationId.toString(),
      dbUser._id.toString()
    );

    const category = c.req.query('category');

    // Build query filter
    const filter: any = { organizationId: dbUser.organizationId };
    if (category && category !== 'All') {
      filter.category = category;
    }

    // Fetch templates from database
    const templates = await EmailTemplate.find(filter)
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email')
      .sort({ isDefault: -1, createdAt: -1 })
      .lean();

    // Transform the data to match the expected format
    const transformedTemplates = templates.map((template: any) => ({
      id: template._id.toString(),
      name: template.name,
      description: template.description,
      category: template.category,
      status: template.status,
      subject: template.subject,
      content: template.content,
      variables: template.variables,
      fromName: template.fromName,
      fromEmail: template.fromEmail,
      lastModified: template.updatedAt,
      isDefault: template.isDefault,
      templateType: template.templateType,
      createdBy: template.createdBy,
      lastModifiedBy: template.lastModifiedBy,
      metadata: template.metadata,
    }));

    return c.json({
      success: true,
      templates: transformedTemplates,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.put('/', async c => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();
    const dbUser = await User.findOne({ email: user.email });
    if (!dbUser || !dbUser.organizationId) {
      return c.json({ error: 'User or organization not found' }, 404);
    }

    const {
      templateId,
      name,
      description,
      subject,
      content,
      status,
      category,
      fromName,
      fromEmail,
    } = await c.req.json();

    if (!templateId) {
      return c.json({ error: 'Template ID is required' }, 400);
    }

    // Find the template and verify it belongs to the user's organization
    const existingTemplate = await EmailTemplate.findOne({
      _id: templateId,
      organizationId: dbUser.organizationId,
    });

    if (!existingTemplate) {
      return c.json({ error: 'Template not found' }, 404);
    }

    // Check if it's a system template and user is trying to modify restricted fields
    if (existingTemplate.templateType === 'system' && existingTemplate.isDefault) {
      // Only allow status changes for system templates
      if (status !== undefined && status !== existingTemplate.status) {
        existingTemplate.status = status;
        existingTemplate.lastModifiedBy = dbUser._id;
        await existingTemplate.save();

        return c.json({
          success: true,
          message: 'System template status updated successfully',
          template: {
            id: (existingTemplate._id as any).toString(),
            name: existingTemplate.name,
            description: existingTemplate.description,
            subject: existingTemplate.subject,
            content: existingTemplate.content,
            status: existingTemplate.status,
            category: existingTemplate.category,
            fromName: existingTemplate.fromName,
            fromEmail: existingTemplate.fromEmail,
            lastModified: (existingTemplate as any).updatedAt,
            isDefault: existingTemplate.isDefault,
            templateType: existingTemplate.templateType,
          },
        });
      } else {
        return c.json({ error: 'System templates can only have their status modified' }, 400);
      }
    }

    // Update the template
    const updateData: any = {
      lastModifiedBy: dbUser._id,
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (subject !== undefined) updateData.subject = subject;
    if (content !== undefined) updateData.content = content;
    if (status !== undefined) updateData.status = status;
    if (category !== undefined) updateData.category = category;
    if (fromName !== undefined) updateData.fromName = fromName;
    if (fromEmail !== undefined) updateData.fromEmail = fromEmail;

    const updatedTemplate = await EmailTemplate.findOneAndUpdate(
      { _id: templateId, organizationId: dbUser.organizationId },
      updateData,
      { new: true, runValidators: true }
    )
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    if (!updatedTemplate) {
      return c.json({ error: 'Failed to update template' }, 500);
    }

    return c.json({
      success: true,
      message: 'Email template updated successfully',
      template: {
        id: (updatedTemplate._id as any).toString(),
        name: updatedTemplate.name,
        description: updatedTemplate.description,
        subject: updatedTemplate.subject,
        content: updatedTemplate.content,
        status: updatedTemplate.status,
        category: updatedTemplate.category,
        fromName: updatedTemplate.fromName,
        fromEmail: updatedTemplate.fromEmail,
        variables: updatedTemplate.variables,
        lastModified: (updatedTemplate as any).updatedAt,
        isDefault: updatedTemplate.isDefault,
        templateType: updatedTemplate.templateType,
        createdBy: updatedTemplate.createdBy,
        lastModifiedBy: updatedTemplate.lastModifiedBy,
      },
    });
  } catch (error: any) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return c.json({ error: `Validation error: ${validationErrors.join(', ')}` }, 400);
    }
    if (error.code === 11000) {
      return c.json(
        { error: 'A template with this name already exists in your organization' },
        400
      );
    }
    return c.json({ error: error.message }, 500);
  }
});

app.post('/', async c => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();
    const dbUser = await User.findOne({ email: user.email });
    if (!dbUser || !dbUser.organizationId) {
      return c.json({ error: 'User or organization not found' }, 404);
    }

    const { action, templateId, testEmail } = await c.req.json();

    if (action === 'test-send') {
      if (!templateId || !testEmail) {
        return c.json({ error: 'Template ID and test email are required' }, 400);
      }

      // Find the template to verify it exists and belongs to the organization
      const template = await EmailTemplate.findOne({
        _id: templateId,
        organizationId: dbUser.organizationId,
      });

      if (!template) {
        return c.json({ error: 'Template not found' }, 404);
      }

      // Update usage metadata
      await EmailTemplate.findByIdAndUpdate(templateId, {
        $inc: { 'metadata.usageCount': 1 },
        $set: { 'metadata.lastUsedAt': new Date() },
      });

      // In a real implementation, you would send a test email using the template
      // For now, we'll just return success
      return c.json({
        success: true,
        message: `Test email sent to ${testEmail} using template "${template.name}"`,
      });
    }

    if (action === 'create') {
      const { name, description, category, subject, content, fromName, fromEmail, status } =
        await c.req.json();

      if (!name || !category || !subject || !content) {
        return c.json({ error: 'Name, category, subject, and content are required' }, 400);
      }

      // Create new template in database
      const newTemplate = new EmailTemplate({
        name,
        description: description || '',
        category,
        subject,
        content,
        status: status || 'draft',
        fromName: fromName || 'TaskFluxio',
        fromEmail: fromEmail || '<EMAIL>',
        organizationId: dbUser.organizationId,
        createdBy: dbUser._id,
        templateType: 'custom',
        isDefault: false,
      });

      const savedTemplate = await newTemplate.save();
      await savedTemplate.populate('createdBy', 'name email');

      return c.json({
        success: true,
        message: 'Email template created successfully',
        template: {
          id: (savedTemplate._id as any).toString(),
          name: savedTemplate.name,
          description: savedTemplate.description,
          category: savedTemplate.category,
          subject: savedTemplate.subject,
          content: savedTemplate.content,
          status: savedTemplate.status,
          fromName: savedTemplate.fromName,
          fromEmail: savedTemplate.fromEmail,
          variables: savedTemplate.variables,
          lastModified: (savedTemplate as any).updatedAt,
          isDefault: savedTemplate.isDefault,
          templateType: savedTemplate.templateType,
          createdBy: savedTemplate.createdBy,
        },
      });
    }

    if (action === 'delete') {
      if (!templateId) {
        return c.json({ error: 'Template ID is required' }, 400);
      }

      // Find the template and verify it belongs to the user's organization
      const template = await EmailTemplate.findOne({
        _id: templateId,
        organizationId: dbUser.organizationId,
      });

      if (!template) {
        return c.json({ error: 'Template not found' }, 404);
      }

      // Prevent deletion of system templates
      if (template.templateType === 'system' && template.isDefault) {
        return c.json({ error: 'System templates cannot be deleted' }, 400);
      }

      await EmailTemplate.findByIdAndDelete(templateId);

      return c.json({
        success: true,
        message: 'Email template deleted successfully',
      });
    }

    return c.json({ error: 'Invalid action' }, 400);
  } catch (error: any) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return c.json({ error: `Validation error: ${validationErrors.join(', ')}` }, 400);
    }
    if (error.code === 11000) {
      return c.json(
        { error: 'A template with this name already exists in your organization' },
        400
      );
    }
    return c.json({ error: error.message }, 500);
  }
});

// Add DELETE route for better RESTful API design
app.delete('/:templateId', async c => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();
    const dbUser = await User.findOne({ email: user.email });
    if (!dbUser || !dbUser.organizationId) {
      return c.json({ error: 'User or organization not found' }, 404);
    }

    const templateId = c.req.param('templateId');

    if (!templateId) {
      return c.json({ error: 'Template ID is required' }, 400);
    }

    // Find the template and verify it belongs to the user's organization
    const template = await EmailTemplate.findOne({
      _id: templateId,
      organizationId: dbUser.organizationId,
    });

    if (!template) {
      return c.json({ error: 'Template not found' }, 404);
    }

    // Prevent deletion of system templates
    if (template.templateType === 'system' && template.isDefault) {
      return c.json({ error: 'System templates cannot be deleted' }, 400);
    }

    await EmailTemplate.findByIdAndDelete(templateId);

    return c.json({
      success: true,
      message: 'Email template deleted successfully',
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export const GET = handle(app);
export const PUT = handle(app);
export const POST = handle(app);
export const DELETE = handle(app);
