import { useState, useCallback } from 'react';
// Removed unused toast import
import { useSession } from 'next-auth/react';

declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface SubscriptionPaymentOptions {
  planId: 'free' | 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'yearly';
  amount: number;
  currency: string;
  userInfo: {
    name: string;
    email: string;
    contact?: string;
  };
}

export interface RazorpaySubscriptionResponse {
  razorpay_payment_id: string;
  razorpay_subscription_id: string;
  razorpay_signature: string;
}

export interface CreateSubscriptionOrderResponse {
  subscription: {
    id: string;
    plan_id: string;
    customer_id: string;
    status: string;
    current_start: number;
    current_end: number;
    charge_at: number;
    start_at: number;
    end_at: number;
  };
  user: {
    name: string;
    email: string;
  };
  organization: {
    id: string;
    name: string;
  };
}

export const useSubscriptionPayment = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();

  // Load Razorpay script
  const loadRazorpayScript = useCallback((): Promise<boolean> => {
    return new Promise(resolve => {
      if (window.Razorpay) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }, []);

  // Create subscription order
  const createSubscriptionOrder = useCallback(
    async (options: SubscriptionPaymentOptions): Promise<CreateSubscriptionOrderResponse> => {
      const response = await fetch('/api/payment/subscription/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: options.planId,
          billingCycle: options.billingCycle,
          organizationId: session?.user?.organizationId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create subscription order');
      }

      return response.json();
    },
    [session?.user?.organizationId]
  );

  // Verify subscription payment
  const verifySubscriptionPayment = useCallback(
    async (paymentData: RazorpaySubscriptionResponse): Promise<boolean> => {
      try {
        const response = await fetch('/api/payment/subscription/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...paymentData,
            organizationId: session?.user?.organizationId,
          }),
        });

        if (!response.ok) {
          return false;
        }

        const result = await response.json();
        return result.verified === true;
      } catch (error) {
        console.error('Payment verification error:', error);
        return false;
      }
    },
    [session?.user?.organizationId]
  );

  // Process subscription payment
  const processSubscriptionPayment = useCallback(
    async ({
      planId,
      billingCycle,
      amount,
      currency,
      userInfo,
      onSuccess,
      onFailure,
    }: SubscriptionPaymentOptions & {
      onSuccess: (
        response: RazorpaySubscriptionResponse,
        orderData: CreateSubscriptionOrderResponse
      ) => void;
      onFailure: (error: Error) => void;
    }) => {
      setIsLoading(true);

      try {
        // Step 1: Load Razorpay script
        const isScriptLoaded = await loadRazorpayScript();
        if (!isScriptLoaded) {
          throw new Error('Failed to load payment gateway');
        }

        // Step 2: Create subscription order
        const orderData = await createSubscriptionOrder({
          planId,
          billingCycle,
          amount,
          currency,
          userInfo,
        });

        // Step 3: Get Razorpay key
        const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
        if (!razorpayKey) {
          throw new Error('Payment gateway not configured');
        }

        // Step 4: Prepare Razorpay options for subscription
        const options = {
          key: razorpayKey,
          subscription_id: orderData.subscription.id,
          name: 'TaskMantra',
          description: `${planId.charAt(0).toUpperCase() + planId.slice(1)} Plan - ${billingCycle === 'yearly' ? 'Annual' : 'Monthly'}`,
          handler: async (response: RazorpaySubscriptionResponse) => {
            try {
              // Verify payment
              const isVerified = await verifySubscriptionPayment(response);
              if (!isVerified) {
                throw new Error('Payment verification failed');
              }

              onSuccess(response, orderData);
            } catch (error) {
              onFailure(error as Error);
            }
          },
          prefill: {
            name: userInfo.name,
            email: userInfo.email,
            contact: userInfo.contact,
          },
          theme: {
            color: '#3399cc',
          },
          modal: {
            ondismiss: () => {
              onFailure(new Error('Payment cancelled by user'));
            },
            confirm_close: true,
            animation: true,
          },
          retry: {
            enabled: true,
            max_count: 3,
          },
          // Enable specific payment methods for Indian market
          config: {
            display: {
              blocks: {
                utib: {
                  name: 'Pay using UPI',
                  instruments: [
                    {
                      method: 'upi',
                    },
                  ],
                },
                other: {
                  name: 'Other Payment Methods',
                  instruments: [
                    {
                      method: 'card',
                    },
                    {
                      method: 'netbanking',
                    },
                    {
                      method: 'wallet',
                    },
                  ],
                },
              },
              hide: [
                {
                  method: 'emi',
                },
              ],
              sequence: ['block.utib', 'block.other'],
              preferences: {
                show_default_blocks: false,
              },
            },
          },
        };

        // Step 5: Open Razorpay checkout
        const rzp = new window.Razorpay(options);
        rzp.open();
      } catch (error) {
        onFailure(error as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [loadRazorpayScript, createSubscriptionOrder, verifySubscriptionPayment]
  );

  // Process one-time payment (for plan upgrades with prorated charges)
  const processOneTimePayment = useCallback(
    async ({
      amount,
      description,
      onSuccess,
      onFailure,
    }: {
      amount: number;
      description: string;
      onSuccess: (response: any) => void;
      onFailure: (error: Error) => void;
    }) => {
      setIsLoading(true);

      try {
        const isScriptLoaded = await loadRazorpayScript();
        if (!isScriptLoaded) {
          throw new Error('Failed to load payment gateway');
        }

        // Create one-time order
        const response = await fetch('/api/payment/create-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: amount * 100, // Convert to paise
            currency: 'INR',
            receipt: `upgrade_${Date.now()}`,
            notes: {
              type: 'plan_upgrade',
              organizationId: session?.user?.organizationId,
            },
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to create payment order');
        }

        const orderData = await response.json();
        const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;

        const options = {
          key: razorpayKey,
          amount: orderData.order.amount,
          currency: orderData.order.currency,
          name: 'TaskMantra',
          description,
          order_id: orderData.order.id,
          handler: onSuccess,
          prefill: {
            name: session?.user?.name,
            email: session?.user?.email,
          },
          theme: {
            color: '#3399cc',
          },
          modal: {
            ondismiss: () => {
              onFailure(new Error('Payment cancelled by user'));
            },
          },
        };

        const rzp = new window.Razorpay(options);
        rzp.open();
      } catch (error) {
        onFailure(error as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [loadRazorpayScript, session?.user]
  );

  // Cancel subscription
  const cancelSubscription = useCallback(
    async (subscriptionId: string): Promise<boolean> => {
      try {
        const response = await fetch('/api/payment/subscription/cancel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscriptionId,
            organizationId: session?.user?.organizationId,
          }),
        });

        return response.ok;
      } catch (error) {
        console.error('Subscription cancellation error:', error);
        return false;
      }
    },
    [session?.user?.organizationId]
  );

  // Pause subscription
  const pauseSubscription = useCallback(
    async (subscriptionId: string, pauseReason?: string): Promise<boolean> => {
      try {
        const response = await fetch('/api/payment/subscription/pause', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscriptionId,
            pauseReason,
            organizationId: session?.user?.organizationId,
          }),
        });

        return response.ok;
      } catch (error) {
        console.error('Subscription pause error:', error);
        return false;
      }
    },
    [session?.user?.organizationId]
  );

  return {
    isLoading,
    processSubscriptionPayment,
    processOneTimePayment,
    cancelSubscription,
    pauseSubscription,
    verifySubscriptionPayment,
  };
};
