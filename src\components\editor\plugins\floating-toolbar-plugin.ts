'use client';
import React from 'react';

import { createPlatePlugin } from '@udecode/plate/react';

import { FloatingToolbar } from '@/components/ui/EditorUiElement/floating-toolbar';
import { FloatingToolbarButtons } from '@/components/ui/EditorUiElement/floating-toolbar-buttons';

export const FloatingToolbarPlugin = createPlatePlugin({
  key: 'floating-toolbar',
  render: {
    afterEditable: () =>
      React.createElement(FloatingToolbar, null, React.createElement(FloatingToolbarButtons)),
  },
});
