import type { EmojiCategoryList } from '@udecode/plate-emoji';
import type { UseEmojiPickerType } from '@udecode/plate-emoji/react';

import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

export type EmojiPickerNavigationProps = {
  onClick: (id: EmojiCategoryList) => void;
} & Pick<UseEmojiPickerType, 'emojiLibrary' | 'focusedCategory' | 'i18n' | 'icons'>;

// KEEP: This is for the animated idicator bar under the icon - Opt in if needed
// const getBarProperty = (
//   emojiLibrary: IEmojiFloatingLibrary,
//   focusedCategory?: EmojiCategoryList
// ) => {
//   let width = 0;
//   let position = 0;

//   if (focusedCategory) {
//     width = 100 / emojiLibrary.getGrid().size;
//     position = focusedCategory
//       ? emojiLibrary.indexOf(focusedCategory) * 100
//       : 0;
//   }

//   return { position, width };
// };

export function EmojiPickerNavigation({
  emojiLibrary,
  focusedCategory,
  i18n,
  icons,
  onClick,
}: EmojiPickerNavigationProps) {
  // KEEP: This is for the animated idicator bar under the icon - Opt in if needed
  // const { position, width } = useMemo(
  //   () => getBarProperty(emojiLibrary, focusedCategory),
  //   [emojiLibrary, focusedCategory]
  // );

  return (
    <TooltipProvider delayDuration={500}>
      <nav id="emoji-nav" className="mb-2.5 border-0 border-b theme-border p-2 theme-surface">
        <div className="relative flex items-center justify-evenly gap-1">
          {emojiLibrary
            .getGrid()
            .sections()
            .map(({ id }) => (
              <Tooltip key={id}>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className={cn(
                      'h-fit rounded-full p-2 theme-transition-fast',
                      'text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300',
                      'theme-hover-primary hover-reveal',
                      id === focusedCategory
                        ? 'theme-active-primary bg-primary/15 text-primary shadow-sm scale-105'
                        : 'hover:bg-accent/50'
                    )}
                    onClick={() => {
                      onClick(id);
                    }}
                    aria-label={i18n.categories[id]}
                    type="button"
                  >
                    <span className="inline-flex size-5 items-center justify-center">
                      {icons.categories[id].outline}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="theme-surface-elevated theme-shadow">
                  {i18n.categories[id]}
                </TooltipContent>
              </Tooltip>
            ))}

          {/* This is the animated indicator - Opt In if needed */}
          {/* <div
            className={cn(
              'absolute -bottom-1.5 left-0 h-0.5 w-full rounded-t-lg bg-accent opacity-100 transition-transform duration-200'
            )}
            style={{
              transform: `translateX(${position}%)`,
              visibility: `${focusedCategory ? 'visible' : 'hidden'}`,
              width: `${width}%`,
            }}
          /> */}
        </div>
      </nav>
    </TooltipProvider>
  );
}
