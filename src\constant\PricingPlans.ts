export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number | string;
  tooltip?: string;
}

export interface PlanPrice {
  monthly: number;
  yearly: number;
  yearlyDiscount: number; // Percentage discount for yearly billing
}

export interface PlanLimits {
  projects: number | 'unlimited';
  users: number | 'unlimited';
  integrations: number | 'unlimited';
  automations: number | 'unlimited';
  apiCalls: number | 'unlimited';
  supportLevel: 'community' | 'email' | 'priority' | 'phone' | 'dedicated';
}

export interface PricingPlan {
  id: 'free' | 'basic' | 'professional' | 'enterprise';
  name: string;
  description: string;
  price: PlanPrice;
  limits: PlanLimits;
  features: PlanFeature[];
  popular?: boolean;
  badge?: string;
  ctaText: string;
  targetAudience: string;
}

export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'free',
    name: 'Starter',
    description: 'Perfect for individuals getting started with task management',
    price: {
      monthly: 0,
      yearly: 0,
      yearlyDiscount: 0,
    },
    limits: {
      projects: 3,
      users: 1,
      integrations: 0,
      automations: 0,
      apiCalls: 0,
      supportLevel: 'community',
    },
    features: [
      {
        name: 'Up to 3 Active Projects',
        description: 'Create and manage up to 3 projects simultaneously',
        included: true,
      },
      {
        name: 'Unlimited Personal Tasks',
        description: 'Create unlimited tasks within your projects',
        included: true,
      },
      {
        name: 'Basic Calendar View',
        description: 'Month and week calendar views for task scheduling',
        included: true,
      },
      {
        name: 'Task Priority Levels',
        description: 'High, Medium, Low priority organization',
        included: true,
      },
      {
        name: 'Basic Notifications',
        description: 'Email and in-app notifications for important updates',
        included: true,
      },
      {
        name: 'Mobile App Access',
        description: 'Full access to mobile applications',
        included: true,
      },
      {
        name: 'File Attachments',
        description: 'Attach files to tasks and projects',
        included: true,
      },
      {
        name: 'Team Collaboration',
        description: 'Share projects and collaborate with team members',
        included: false,
      },
      {
        name: 'Advanced Reporting',
        description: 'Detailed analytics and progress reports',
        included: false,
      },
      {
        name: 'Integrations',
        description: 'Connect with third-party tools and services',
        included: false,
      },
    ],
    ctaText: 'Get Started Free',
    targetAudience: 'Individual users, students, freelancers',
  },
  {
    id: 'basic',
    name: 'Personal Pro',
    description: 'Enhanced productivity for professionals and power users',
    price: {
      monthly: 99,
      yearly: 999,
      yearlyDiscount: 17,
    },
    limits: {
      projects: 'unlimited',
      users: 1,
      integrations: 3,
      automations: 3,
      apiCalls: 1000,
      supportLevel: 'email',
    },
    features: [
      {
        name: 'Everything in Starter',
        description: 'All features from the free plan',
        included: true,
      },
      {
        name: 'Unlimited Projects',
        description: 'Create and manage unlimited projects',
        included: true,
      },
      {
        name: 'Advanced Calendar Views',
        description: 'Gantt charts, timeline view, and advanced scheduling',
        included: true,
      },
      {
        name: 'Custom Task Fields',
        description: 'Add custom fields to tasks for better organization',
        included: true,
      },
      {
        name: 'Basic Reporting & Analytics',
        description: 'Track productivity and project progress',
        included: true,
      },
      {
        name: 'Enhanced File Management',
        description: 'Advanced file organization and management features',
        included: true,
      },
      {
        name: 'Priority Email Support',
        description: 'Get help within 24 hours via email',
        included: true,
      },
      {
        name: 'Export to PDF/Excel',
        description: 'Export reports and data in multiple formats',
        included: true,
      },
      {
        name: 'Basic Automation',
        description: 'Set up 3 automated workflows',
        included: true,
        limit: '3 automations',
      },
      {
        name: 'Limited Integrations',
        description: 'Connect with 3 popular tools',
        included: true,
        limit: '3 integrations',
      },
    ],
    ctaText: 'Upgrade to Personal Pro',
    targetAudience: 'Professionals, consultants, small business owners',
  },
  {
    id: 'professional',
    name: 'Team',
    description: 'Complete collaboration solution for growing teams',
    price: {
      monthly: 199,
      yearly: 1999,
      yearlyDiscount: 17,
    },
    limits: {
      projects: 'unlimited',
      users: 10,
      integrations: 10,
      automations: 10,
      apiCalls: 10000,
      supportLevel: 'priority',
    },
    popular: true,
    badge: 'Most Popular',
    features: [
      {
        name: 'Everything in Personal Pro',
        description: 'All features from the Personal Pro plan',
        included: true,
      },
      {
        name: 'Team Collaboration',
        description: 'Invite up to 10 team members',
        included: true,
        limit: 'Up to 10 members',
      },
      {
        name: 'Project Sharing & Permissions',
        description: 'Advanced permission controls for team projects',
        included: true,
      },
      {
        name: 'Advanced Reporting Dashboard',
        description: 'Comprehensive analytics and team performance metrics',
        included: true,
      },
      {
        name: 'Time Tracking',
        description: 'Built-in time tracking for tasks and projects',
        included: true,
      },
      {
        name: 'Team File Sharing',
        description: 'Advanced file sharing and collaboration features',
        included: true,
      },
      {
        name: 'Popular Integrations',
        description: 'Google Workspace, Slack, and 8 more integrations',
        included: true,
        limit: '10 integrations',
      },
      {
        name: 'Advanced Automation',
        description: 'Create complex workflows and automations',
        included: true,
        limit: '10 automations',
      },
      {
        name: 'Video Call Support',
        description: 'Priority support via video calls',
        included: true,
      },
      {
        name: 'Custom Branding',
        description: 'Add your company logo and colors',
        included: true,
      },
    ],
    ctaText: 'Start Team Plan',
    targetAudience: 'Small to medium teams, startups, agencies',
  },
  {
    id: 'enterprise',
    name: 'Business',
    description: 'Enterprise-grade solution with advanced security and support',
    price: {
      monthly: 399,
      yearly: 3999,
      yearlyDiscount: 17,
    },
    limits: {
      projects: 'unlimited',
      users: 'unlimited',
      integrations: 'unlimited',
      automations: 'unlimited',
      apiCalls: 'unlimited',
      supportLevel: 'dedicated',
    },
    features: [
      {
        name: 'Everything in Team',
        description: 'All features from the Team plan',
        included: true,
      },
      {
        name: 'Unlimited Team Members',
        description: 'No limits on team size',
        included: true,
      },
      {
        name: 'Advanced Admin Controls',
        description: 'Comprehensive user and permission management',
        included: true,
      },
      {
        name: 'White-label Options',
        description: 'Complete customization with your branding',
        included: true,
      },
      {
        name: 'API Access',
        description: 'Full REST API access for custom integrations',
        included: true,
      },
      {
        name: 'Enterprise File Management',
        description: 'Advanced file management with enterprise-grade features',
        included: true,
      },
      {
        name: 'Priority Phone Support',
        description: 'Direct phone line with 4-hour response time',
        included: true,
      },
      {
        name: 'Custom Integrations',
        description: 'Build custom integrations with our API',
        included: true,
      },
      {
        name: 'Advanced Security Features',
        description: 'SSO, 2FA, audit logs, and compliance features',
        included: true,
      },
      {
        name: 'Dedicated Account Manager',
        description: 'Personal account manager for onboarding and support',
        included: true,
      },
    ],
    ctaText: 'Contact Sales',
    targetAudience: 'Large teams, enterprises, organizations with compliance needs',
  },
];

// Payment method configurations for Indian market
export const PAYMENT_METHODS = {
  UPI: {
    name: 'UPI',
    description: 'Pay instantly using UPI apps like PhonePe, Paytm, Google Pay',
    icon: 'upi',
    popular: true,
  },
  NETBANKING: {
    name: 'Net Banking',
    description: 'Pay securely using your bank account',
    icon: 'bank',
  },
  CARDS: {
    name: 'Credit/Debit Cards',
    description: 'Visa, Mastercard, RuPay cards accepted',
    icon: 'card',
  },
  WALLETS: {
    name: 'Digital Wallets',
    description: 'Paytm, PhonePe, Amazon Pay, and more',
    icon: 'wallet',
  },
  EMI: {
    name: 'EMI Options',
    description: 'Convert to easy monthly installments',
    icon: 'emi',
    note: 'Available for annual plans',
  },
};

// Billing cycle benefits
export const BILLING_BENEFITS = {
  monthly: {
    name: 'Monthly',
    description: 'Pay month by month',
    flexibility: 'High',
    commitment: 'Low',
  },
  yearly: {
    name: 'Annual',
    description: 'Save 17% with annual billing',
    flexibility: 'Medium',
    commitment: 'Medium',
    benefits: [
      '17% discount (2 months free)',
      'Single invoice for accounting',
      'Priority feature requests',
      'EMI options available',
    ],
  },
};

// Feature categories for better organization
export const FEATURE_CATEGORIES = {
  CORE: 'Core Features',
  COLLABORATION: 'Team Collaboration',
  ANALYTICS: 'Analytics & Reporting',
  INTEGRATIONS: 'Integrations',
  SUPPORT: 'Support & Service',
  SECURITY: 'Security & Compliance',
  CUSTOMIZATION: 'Customization',
};
