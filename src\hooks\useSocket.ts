import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useSession } from 'next-auth/react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Global socket instance to prevent duplicates
let globalSocket: Socket | null = null;
let connectionCount = 0;

export function useSocket() {
  const { data: session } = useSession();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const mountedRef = useRef(true);

  const handleNotification = useCallback((data: any) => {
    queryClient.invalidateQueries({ queryKey: ['notifications'] });
    queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });

    if (data.notification) {
      toast.success(data.notification.title, {
        description: data.notification.description,
        duration: 5000,
      });
    }
  }, [queryClient]);

  const connectSocket = useCallback(() => {
    if (!session?.user?.id || !mountedRef.current) return;

    // Clean up existing timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Use global socket if it exists and is connected
    if (globalSocket && globalSocket.connected && globalSocket.auth?.userId === session.user.id) {
      socketRef.current = globalSocket;
      setIsConnected(true);
      setConnectionError(null);

      // Add notification handler if not already added
      if (!globalSocket.hasListeners('notification')) {
        globalSocket.on('notification', handleNotification);
      }

      connectionCount++;
      return globalSocket;
    }

    // Disconnect existing global socket if user changed
    if (globalSocket && globalSocket.auth?.userId !== session.user.id) {
      globalSocket.disconnect();
      globalSocket = null;
    }

    const socketUrl = process.env.NODE_ENV === 'production'
      ? process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin
      : window.location.origin;

    const socket = io(socketUrl, {
      auth: {
        token: 'authenticated',
        userId: session.user.id
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 10000,
      forceNew: false,
    });

    globalSocket = socket;
    socketRef.current = socket;
    connectionCount++;

    socket.on('connect', () => {
      if (!mountedRef.current) return;
      setIsConnected(true);
      setConnectionError(null);

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    });

    socket.on('disconnect', (reason) => {
      if (!mountedRef.current) return;
      setIsConnected(false);

      // Only attempt reconnection for certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'transport close' || reason === 'transport error') {
        reconnectTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current) {
            connectSocket();
          }
        }, 3000);
      }
    });

    socket.on('connect_error', (error) => {
      if (!mountedRef.current) return;
      setIsConnected(false);
      setConnectionError(error.message);

      reconnectTimeoutRef.current = setTimeout(() => {
        if (mountedRef.current) {
          connectSocket();
        }
      }, 5000);
    });

    socket.on('reconnect', () => {
      if (!mountedRef.current) return;
      setIsConnected(true);
      setConnectionError(null);
    });

    socket.on('reconnect_error', (error) => {
      if (!mountedRef.current) return;
      setConnectionError(error.message);
    });

    socket.on('reconnect_failed', () => {
      if (!mountedRef.current) return;
      setConnectionError('Failed to reconnect after multiple attempts');

      reconnectTimeoutRef.current = setTimeout(() => {
        if (mountedRef.current) {
          connectSocket();
        }
      }, 10000);
    });

    socket.on('notification', handleNotification);
    return socket;
  }, [session?.user?.id, handleNotification]);

  useEffect(() => {
    connectSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      setIsConnected(false);
    };
  }, [connectSocket]);

  const emit = (event: string, data: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
      return true;
    }
    return false;
  };

  return {
    socket: socketRef.current,
    emit,
    isConnected,
    connectionError
  };
}
