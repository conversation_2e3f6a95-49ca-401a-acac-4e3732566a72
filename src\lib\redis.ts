import Redis from 'ioredis';

// Global singleton instances to prevent multiple connections
let redis: Redis | null = null;
let publisher: Redis | null = null;
let subscriber: Redis | null = null;

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: 3,
  lazyConnect: true, // Connect on first use
  enableReadyCheck: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnFailover: 100,
  enableOfflineQueue: false,
  keepAlive: 30000,
};

// Initialize Redis instances with singleton pattern
function getRedisInstance(): Redis {
  if (!redis) {
    redis = new Redis(redisConfig);

    redis.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });

    redis.on('error', (err) => {
      console.error('❌ Redis connection error:', err);
    });

    redis.on('ready', () => {
      console.log('✅ Redis is ready to accept commands');
    });
  }
  return redis;
}

function getPublisherInstance(): Redis {
  if (!publisher) {
    publisher = new Redis(redisConfig);

    publisher.on('connect', () => {
      console.log('✅ Redis publisher connected');
    });

    publisher.on('error', (err) => {
      console.error('❌ Redis publisher error:', err);
    });
  }
  return publisher;
}

function getSubscriberInstance(): Redis {
  if (!subscriber) {
    subscriber = new Redis(redisConfig);

    subscriber.on('connect', () => {
      console.log('✅ Redis subscriber connected');
    });

    subscriber.on('error', (err) => {
      console.error('❌ Redis subscriber error:', err);
    });
  }
  return subscriber;
}

// Export singleton instances
const redisInstance = getRedisInstance();
const publisherInstance = getPublisherInstance();
const subscriberInstance = getSubscriberInstance();

// Helper functions for common operations
export const RedisHelper = {
  // Generate cache key with consistent format
  generateKey: (prefix: string, ...parts: string[]) => {
    return `${prefix}:${parts.join(':')}`;
  },

  // Set with automatic JSON serialization
  setJSON: async (key: string, data: any, ttl?: number) => {
    const serialized = JSON.stringify(data);
    if (ttl) {
      return await redisInstance.setex(key, ttl, serialized);
    }
    return await redisInstance.set(key, serialized);
  },

  // Get with automatic JSON deserialization
  getJSON: async <T = any>(key: string): Promise<T | null> => {
    const data = await redisInstance.get(key);
    if (!data) return null;
    try {
      return JSON.parse(data) as T;
    } catch (error) {
      console.error('Redis JSON parse error:', error);
      return null;
    }
  },

  // Delete multiple keys by pattern
  deleteByPattern: async (pattern: string) => {
    const keys = await redisInstance.keys(pattern);
    if (keys.length > 0) {
      return await redisInstance.del(...keys);
    }
    return 0;
  },

  // Check if Redis is connected
  isConnected: () => {
    return redisInstance.status === 'ready';
  },
};

// Search caching functionality
export class SearchCache {
  private static readonly CACHE_PREFIX = 'search:';
  private static readonly CACHE_TTL = 300; // 5 minutes

  static generateCacheKey(organizationId: string, query: string, filters: any): string {
    const filterString = JSON.stringify(filters);
    return `${this.CACHE_PREFIX}${organizationId}:${query}:${Buffer.from(filterString).toString('base64')}`;
  }

  static async get(cacheKey: string): Promise<any | null> {
    if (!RedisHelper.isConnected()) return null;

    try {
      const cached = await redisInstance.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  static async set(cacheKey: string, data: any): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      await redisInstance.setex(cacheKey, this.CACHE_TTL, JSON.stringify(data));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  static async invalidateUserCache(organizationId: string): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      const pattern = `${this.CACHE_PREFIX}${organizationId}:*`;
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Redis invalidation error:', error);
    }
  }

  static async getPopularSearches(organizationId: string, limit: number = 10): Promise<string[]> {
    if (!RedisHelper.isConnected()) return [];

    try {
      const key = `popular_searches:${organizationId}`;
      const searches = await redis.zrevrange(key, 0, limit - 1);
      return searches;
    } catch (error) {
      console.error('Redis popular searches error:', error);
      return [];
    }
  }

  static async trackSearch(organizationId: string, query: string): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      const key = `popular_searches:${organizationId}`;
      await redis.zincrby(key, 1, query);
      await redis.expire(key, 86400 * 7); // 7 days
    } catch (error) {
      console.error('Redis track search error:', error);
    }
  }
}

export { redis as default, publisher, subscriber };