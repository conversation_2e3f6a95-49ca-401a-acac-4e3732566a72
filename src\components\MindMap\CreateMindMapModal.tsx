import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, Brain, Folder } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

import Modal from '../Global/Modal';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useCreateMindMap } from '@/hooks/useMindMap';
import { useProjectStore } from '@/stores/projectsStore';
import { CreateMindMapRequest } from '@/types/MindMapsTypes';

interface CreateMindMapModalProps {
  showCreateDialog: boolean;
  setShowCreateDialog: (show: boolean) => void;
}

const CreateMindMapModal: React.FC<CreateMindMapModalProps> = ({
  showCreateDialog,
  setShowCreateDialog,
}) => {
  const router = useRouter();
  const { projects } = useProjectStore();
  const createMutation = useCreateMindMap();

  const [form, setForm] = useState<CreateMindMapRequest>({
    title: '',
    description: '',
    projectId: '',
    tags: [],
  });
  const [errors, setErrors] = useState<{ title?: string; projectId?: string }>({});
  const [tagInput, setTagInput] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleProjectChange = (value: string) => {
    setForm(prev => ({ ...prev, projectId: value === 'none' ? undefined : value }));
    if (errors.projectId) {
      setErrors(prev => ({ ...prev, projectId: undefined }));
    }
  };

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      const newTag = tagInput.trim().toLowerCase();
      if (!form.tags?.includes(newTag)) {
        setForm(prev => ({
          ...prev,
          tags: [...(prev.tags || []), newTag],
        }));
      }
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setForm(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || [],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    const newErrors: { title?: string; projectId?: string } = {};
    if (!form.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      const result = await createMutation.mutateAsync({
        ...form,
        title: form.title.trim(),
        description: form.description?.trim() || undefined,
        projectId: form.projectId || undefined,
      });

      // Close modal and reset form
      setShowCreateDialog(false);
      setForm({ title: '', description: '', projectId: '', tags: [] });
      setErrors({});
      setTagInput('');

      // Navigate to the mind map editor
      if (result?.mindMap?._id) {
        router.push(`/mind-maps/${result.mindMap._id}`);
      } else if (result?._id) {
        router.push(`/mind-maps/${result._id}`);
      }
    } catch (error) {
      console.error('Failed to create mind map:', error);
    }
  };

  const handleClose = () => {
    setShowCreateDialog(false);
    setForm({ title: '', description: '', projectId: '', tags: [] });
    setErrors({});
    setTagInput('');
  };

  return (
    <AnimatePresence>
      {showCreateDialog && (
        <Modal isOpen onClose={handleClose}>
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="w-full max-w-lg"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Brain className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold theme-text-primary">Create Mind Map</h2>
                <p className="text-sm theme-text-secondary">Start organizing your ideas visually</p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Title */}
              <div>
                <Label htmlFor="title" className="mb-2 block font-medium theme-text-primary">
                  Title *
                </Label>
                <Input
                  id="title"
                  name="title"
                  value={form.title}
                  onChange={handleChange}
                  placeholder="e.g. Project Planning, Study Notes, Creative Ideas"
                  className={errors.title ? 'border-red-500 focus:border-red-500' : ''}
                  aria-invalid={!!errors.title}
                  aria-errormessage="title-error"
                />
                {errors.title && (
                  <p id="title-error" className="mt-1 text-sm text-red-600">
                    {errors.title}
                  </p>
                )}
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description" className="mb-2 block font-medium theme-text-primary">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  placeholder="Describe the purpose or context of this mind map (optional)"
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Project Selection */}
              <div>
                <Label className="mb-2 block font-medium theme-text-primary">
                  <Folder className="w-4 h-4 inline mr-1" />
                  Project
                </Label>
                <Select value={form.projectId || 'none'} onValueChange={handleProjectChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a project (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Project</SelectItem>
                    {projects.map(project => (
                      <SelectItem key={project._id} value={project._id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Tags */}
              <div>
                <Label htmlFor="tags" className="mb-2 block font-medium theme-text-primary">
                  Tags
                </Label>
                <Input
                  id="tags"
                  value={tagInput}
                  onChange={e => setTagInput(e.target.value)}
                  onKeyDown={handleAddTag}
                  placeholder="Type a tag and press Enter"
                  className="mb-2"
                />
                {form.tags && form.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {form.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 text-xs rounded-md"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="hover:text-blue-600 dark:hover:text-blue-300"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t theme-border">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={handleClose}
                  disabled={createMutation.isPending}
                >
                  Cancel
                </Button>

                <Button
                  type="submit"
                  disabled={createMutation.isPending || !form.title.trim()}
                  className="min-w-[140px]"
                >
                  {createMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Create Mind Map
                    </>
                  )}
                </Button>
              </div>
            </form>
          </motion.div>
        </Modal>
      )}
    </AnimatePresence>
  );
};

export default CreateMindMapModal;
