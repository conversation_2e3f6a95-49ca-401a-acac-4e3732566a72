import React, { useState } from 'react';
import { format } from 'date-fns';
import { CalendarIcon, Filter, Plus, Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { useCalendarStore } from '@/stores/calendarStore';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import CreateTaskModal from '../Tasks/CreateTaskModal';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { TaskService } from '@/services/Task.service';
import { toast } from 'sonner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const CalendarHeader = () => {
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  const { dateRange, setDateRange, setSelectedDate, filters, setFilter, resetFilters, tasks } =
    useCalendarStore();

  // Count tasks by priority
  const priorityCounts = tasks.reduce(
    (counts, task) => {
      if (task.priority === 'High') counts.high++;
      else if (task.priority === 'Medium') counts.medium++;
      else if (task.priority === 'Low') counts.low++;
      return counts;
    },
    { high: 0, medium: 0, low: 0 }
  );

  const handleCreateTask = useMutation({
    mutationFn: (taskData: any) => {
      setIsLoading(true);
      return TaskService.createTask(taskData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-tasks'] });
      setIsLoading(false);
      toast.success('Task created successfully');
    },
  });

  return (
    <header className="flex flex-wrap justify-between items-center p-6 theme-surface glass theme-shadow-sm rounded-b-xl mb-6">
      <div className="flex flex-col mb-4 md:mb-0">
        <h2 className="text-2xl font-semibold tracking-tight theme-text-primary">
          Project Timeline
        </h2>
        <div className="flex gap-3 text-sm mt-2">
          <div className="flex items-center gap-2 priority-indicator-high px-3 py-1.5 rounded-lg">
            <span className="w-2 h-2 rounded-full bg-destructive animate-pulse"></span>
            <span className="font-medium">{priorityCounts.high} High</span>
          </div>
          <div className="flex items-center gap-2 priority-indicator-medium px-3 py-1.5 rounded-lg">
            <span className="w-2 h-2 rounded-full bg-warning animate-pulse"></span>
            <span className="font-medium">{priorityCounts.medium} Medium</span>
          </div>
          <div className="flex items-center gap-2 priority-indicator-low px-3 py-1.5 rounded-lg">
            <span className="w-2 h-2 rounded-full bg-success animate-pulse"></span>
            <span className="font-medium">{priorityCounts.low} Low</span>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-3">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'min-w-[240px] justify-start text-left font-medium theme-button-secondary',
                !dateRange && 'theme-text-secondary',
                dateRange && 'border-primary/30 bg-primary/10 text-primary'
              )}
            >
              <CalendarIcon className="w-4 h-4 mr-2 text-primary" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, 'LLL dd, y')} - {format(dateRange.to, 'LLL dd, y')}
                  </>
                ) : (
                  format(dateRange.from, 'LLL dd, y')
                )
              ) : (
                <span>Select dates</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={dateRange?.from}
              selected={dateRange}
              onSelect={range => range && setDateRange(range)}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>

        <Button
          onClick={() => {
            setSelectedDate(new Date());
            setIsTaskModalOpen(true);
          }}
          className="theme-button-primary theme-shadow hover:theme-shadow-md transition-all duration-200"
        >
          <Plus className="w-4 h-4 mr-2" /> Create Task
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'transition-all duration-200 theme-button-secondary',
                Object.values(filters).some(f => f !== 'all' && f !== '')
                  ? 'border-primary/30 bg-primary/10 text-primary'
                  : 'theme-text-secondary'
              )}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {Object.values(filters).some(f => f !== 'all' && f !== '') && (
                <Badge variant="secondary" className="ml-2">
                  {Object.values(filters).filter(f => f !== 'all' && f !== '').length}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-[320px] p-4 theme-surface-elevated" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-base font-semibold theme-text-primary">Filters</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="h-8 theme-text-secondary hover:theme-text-primary theme-button-ghost"
                >
                  <X className="h-4 w-4 mr-1" /> Reset
                </Button>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium theme-text-primary">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 theme-text-secondary" />
                  <Input
                    placeholder="Search tasks..."
                    className="pl-9 theme-input"
                    value={filters.searchQuery}
                    onChange={e => setFilter('searchQuery', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium theme-text-primary">Status</label>
                <Select value={filters.status} onValueChange={value => setFilter('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="To Do">To Do</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Review">Review</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium theme-text-primary">Priority</label>
                <Select
                  value={filters.priority}
                  onValueChange={value => setFilter('priority', value)}
                >
                  <SelectTrigger className="theme-input">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent className="theme-surface-elevated">
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Active Filters */}
              {Object.values(filters).some(f => f !== 'all' && f !== '') && (
                <div className="pt-3 border-t border-border">
                  <div className="flex flex-wrap gap-2">
                    {filters.status !== 'all' && (
                      <Badge
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => setFilter('status', 'all')}
                      >
                        Status: {filters.status} <X className="h-3 w-3 ml-1 inline" />
                      </Badge>
                    )}
                    {filters.priority !== 'all' && (
                      <Badge
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => setFilter('priority', 'all')}
                      >
                        Priority: {filters.priority} <X className="h-3 w-3 ml-1 inline" />
                      </Badge>
                    )}
                    {filters.searchQuery && (
                      <Badge
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => setFilter('searchQuery', '')}
                      >
                        Search:{' '}
                        {filters.searchQuery.length > 10
                          ? `${filters.searchQuery.substring(0, 10)}...`
                          : filters.searchQuery}
                        <X className="h-3 w-3 ml-1 inline" />
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <CreateTaskModal
        isOpen={isTaskModalOpen}
        onClose={() => setIsTaskModalOpen(false)}
        onCreateTask={handleCreateTask.mutate}
        isLoading={isLoading}
      />
    </header>
  );
};

export default CalendarHeader;
