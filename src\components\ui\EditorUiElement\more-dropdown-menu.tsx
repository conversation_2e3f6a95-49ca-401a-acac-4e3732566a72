'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { SubscriptPlugin, SuperscriptPlugin } from '@udecode/plate-basic-marks/react';
import { KbdPlugin } from '@udecode/plate-kbd/react';
import { useEditorRef } from '@udecode/plate/react';
import { KeyboardIcon, MoreHorizontalIcon, SubscriptIcon, SuperscriptIcon } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

export function MoreDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);

  const items: Record<
    string,
    {
      icon: React.ReactNode;
      label: string;
      description: string;
      color: string;
      action: () => void;
    }
  > = {
    keyboard: {
      icon: <KeyboardIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Keyboard input',
      description: 'Add keyboard input styling',
      color: 'text-purple-600 dark:text-purple-400',
      action: () => {
        editor.tf.toggleMark(KbdPlugin.key);
        editor.tf.collapse({ edge: 'end' });
        editor.tf.focus();
      },
    },
    superscript: {
      icon: <SuperscriptIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Superscript',
      description: 'Format text as superscript',
      color: 'text-blue-600 dark:text-blue-400',
      action: () => {
        editor.tf.toggleMark(SuperscriptPlugin.key, {
          remove: SubscriptPlugin.key,
        });
        editor.tf.focus();
      },
    },
    subscript: {
      icon: <SubscriptIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Subscript',
      description: 'Format text as subscript',
      color: 'text-green-600 dark:text-green-400',
      action: () => {
        editor.tf.toggleMark(SubscriptPlugin.key, {
          remove: SuperscriptPlugin.key,
        });
        editor.tf.focus();
      },
    },
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="More formatting options"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className="theme-transition text-gray-600 dark:text-gray-400">
              <MoreHorizontalIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        {Object.entries(items).map(([key, item]) => (
          <DropdownMenuItem
            key={key}
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg'
            )}
            onSelect={() => {
              item.action();
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', item.color)}>{item.icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">{item.label}</span>
              </div>
              <p className="text-xs theme-text-secondary">{item.description}</p>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
