import { Search } from 'lucide-react';

interface MobileSearchTriggerProps {
  onOpen: () => void;
}

export const MobileSearchTrigger = ({ onOpen }: MobileSearchTriggerProps) => {
  return (
    <button
      onClick={onOpen}
      className="md:hidden p-2 theme-hover-surface rounded-lg theme-transition glow-on-hover"
      aria-label="Search"
    >
      <Search className="w-5 h-5 theme-text-secondary" />
    </button>
  );
};
