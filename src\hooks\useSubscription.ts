import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  SubscriptionService,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  PaymentVerificationRequest,
} from '@/services/Subscription.service';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';

// Query keys for better cache management
export const subscriptionKeys = {
  all: ['subscriptions'] as const,
  subscription: () => [...subscriptionKeys.all, 'subscription'] as const,
  usage: () => [...subscriptionKeys.all, 'usage'] as const,
};

export const useSubscription = () => {
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  // Get current subscription
  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
    refetch: refetchSubscription,
  } = useQuery({
    queryKey: subscriptionKeys.subscription(),
    queryFn: SubscriptionService.getSubscription,
    enabled: !!session?.user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401/403 errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Get usage summary
  const {
    data: usageData,
    isLoading: isLoadingUsage,
    error: usageError,
    refetch: refetchUsage,
  } = useQuery({
    queryKey: subscriptionKeys.usage(),
    queryFn: SubscriptionService.getUsageSummary,
    enabled: !!session?.user,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Create subscription mutation
  const createSubscriptionMutation = useMutation({
    mutationFn: (data: CreateSubscriptionRequest) => SubscriptionService.createSubscription(data),
    onSuccess: () => {
      toast.success('Subscription order created successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create subscription');
    },
  });

  // Verify payment mutation
  const verifyPaymentMutation = useMutation({
    mutationFn: (data: PaymentVerificationRequest) => SubscriptionService.verifyPayment(data),
    onSuccess: () => {
      toast.success('Payment verified successfully');
      // Invalidate and refetch subscription data
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Payment verification failed');
    },
  });

  // Update subscription mutation
  const updateSubscriptionMutation = useMutation({
    mutationFn: (data: UpdateSubscriptionRequest) => SubscriptionService.updateSubscription(data),
    onSuccess: () => {
      toast.success('Subscription updated successfully');
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update subscription');
    },
  });

  // Cancel subscription mutation
  const cancelSubscriptionMutation = useMutation({
    mutationFn: () => SubscriptionService.cancelSubscription(),
    onSuccess: data => {
      toast.success(data.message || 'Subscription cancelled successfully');
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to cancel subscription');
    },
  });

  // Pause subscription mutation
  const pauseSubscriptionMutation = useMutation({
    mutationFn: (reason?: string) => SubscriptionService.pauseSubscription(reason),
    onSuccess: data => {
      toast.success(data.message || 'Subscription paused successfully');
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to pause subscription');
    },
  });

  // Helper functions
  const subscription = subscriptionData?.subscription;
  const usage = usageData?.usage;

  const isLoading = isLoadingSubscription || isLoadingUsage;
  const hasError = subscriptionError || usageError;

  // Plan information
  const currentPlan = subscription?.plan || 'free';
  const planStatus = subscription?.status || 'active';
  const billingCycle = subscription?.billingCycle || 'monthly';

  // Usage helpers
  const getUsagePercentage = (limitType: string): number => {
    if (!usage?.limits[limitType]) return 0;
    const limit = usage.limits[limitType];
    return limit.limit === 'unlimited' ? 0 : limit.percentage;
  };

  const isLimitReached = (limitType: string): boolean => {
    if (!usage?.limits[limitType]) return false;
    const limit = usage.limits[limitType];
    return limit.isAtLimit || limit.isOverLimit;
  };

  const isNearLimit = (limitType: string): boolean => {
    if (!usage?.limits[limitType]) return false;
    return usage.limits[limitType].isNearLimit;
  };

  const formatUsageDisplay = (limitType: string): string => {
    if (!usage?.limits[limitType]) return 'N/A';
    const limit = usage.limits[limitType];
    const unit = limitType === 'storage' ? 'MB' : '';

    if (limit.limit === 'unlimited') {
      return `${limit.current}${unit} / Unlimited`;
    }
    return `${limit.current}${unit} / ${limit.limit}${unit}`;
  };

  // Plan helpers
  const canUpgrade = (): boolean => {
    const planOrder = ['free', 'basic', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);
    return currentIndex < planOrder.length - 1;
  };

  const canDowngrade = (): boolean => {
    const planOrder = ['free', 'basic', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);
    return currentIndex > 0;
  };

  const getRecommendedPlan = (): string | null => {
    return usage?.recommendations.recommendedPlan || null;
  };

  const shouldShowUpgradePrompt = (): boolean => {
    return usage?.recommendations.shouldUpgrade || false;
  };

  // Format price helper
  const formatPrice = (amount: number, currency: string = 'INR'): string => {
    return SubscriptionService.formatPrice(amount, currency);
  };

  // Refresh all data
  const refreshData = async (): Promise<void> => {
    await Promise.all([refetchSubscription(), refetchUsage()]);
  };

  return {
    // Data
    subscription,
    usage,
    currentPlan,
    planStatus,
    billingCycle,

    // Loading states
    isLoading,
    isLoadingSubscription,
    isLoadingUsage,
    hasError,

    // Mutations
    createSubscription: createSubscriptionMutation.mutateAsync,
    verifyPayment: verifyPaymentMutation.mutateAsync,
    updateSubscription: updateSubscriptionMutation.mutateAsync,
    cancelSubscription: cancelSubscriptionMutation.mutateAsync,
    pauseSubscription: pauseSubscriptionMutation.mutateAsync,

    // Mutation states
    isCreatingSubscription: createSubscriptionMutation.isPending,
    isVerifyingPayment: verifyPaymentMutation.isPending,
    isUpdatingSubscription: updateSubscriptionMutation.isPending,
    isCancellingSubscription: cancelSubscriptionMutation.isPending,
    isPausingSubscription: pauseSubscriptionMutation.isPending,

    // Helper functions
    getUsagePercentage,
    isLimitReached,
    isNearLimit,
    formatUsageDisplay,
    canUpgrade,
    canDowngrade,
    getRecommendedPlan,
    shouldShowUpgradePrompt,
    formatPrice,
    refreshData,

    // Refetch functions
    refetchSubscription,
    refetchUsage,
  };
};
