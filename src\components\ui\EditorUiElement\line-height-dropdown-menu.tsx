'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import {
  useLineHeightDropdownMenu,
  useLineHeightDropdownMenuState,
} from '@udecode/plate-line-height/react';
import { WrapText, ChevronDownIcon } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

export function LineHeightDropdownMenu(props: DropdownMenuProps) {
  const [open, setOpen] = React.useState(false);
  const state = useLineHeightDropdownMenuState();
  const { radioGroupProps } = useLineHeightDropdownMenu(state);

  const currentValue = state.value || '1.5';

  const lineHeightItems: Record<
    string,
    {
      icon: React.ReactNode;
      label: string;
      description: string;
      color: string;
    }
  > = {
    '1': {
      icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Single',
      description: 'Compact line spacing',
      color: 'text-red-600 dark:text-red-400',
    },
    '1.15': {
      icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Tight',
      description: 'Slightly compressed spacing',
      color: 'text-orange-600 dark:text-orange-400',
    },
    '1.5': {
      icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Normal',
      description: 'Standard line spacing',
      color: 'text-blue-600 dark:text-blue-400',
    },
    '2': {
      icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Double',
      description: 'Expanded line spacing',
      color: 'text-green-600 dark:text-green-400',
    },
    '2.5': {
      icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Wide',
      description: 'Extra wide spacing',
      color: 'text-purple-600 dark:text-purple-400',
    },
  };

  const currentItem = lineHeightItems[currentValue] || lineHeightItems['1.5'];

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Line height"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', currentItem.color)}>{currentItem.icon}</div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        {state.values.map(value => {
          const item = lineHeightItems[value] || {
            icon: <WrapText className="h-3 w-3 sm:h-4 sm:w-4" />,
            label: value,
            description: `Line height ${value}`,
            color: 'text-gray-600 dark:text-gray-400',
          };

          return (
            <DropdownMenuItem
              key={value}
              className={cn(
                'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
                currentValue === value && 'theme-surface'
              )}
              onSelect={() => {
                radioGroupProps.onValueChange?.(value);
                setOpen(false);
              }}
            >
              <div className={cn('theme-transition', item.color)}>{item.icon}</div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm theme-text-primary">{item.label}</span>
                </div>
                <p className="text-xs theme-text-secondary">{item.description}</p>
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
