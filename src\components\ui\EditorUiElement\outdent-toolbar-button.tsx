'use client';

import * as React from 'react';

import { useOutdentButton } from '@udecode/plate-indent/react';
import { Outdent } from 'lucide-react';
import { ToolbarButton } from './toolbar';
import { cn } from '@/lib/utils';

export function OutdentToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const { props: buttonProps } = useOutdentButton();

  return (
    <ToolbarButton {...props} {...buttonProps} tooltip="Outdent">
      <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
        <Outdent className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
