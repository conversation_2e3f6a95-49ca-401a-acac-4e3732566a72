import { TaskNode } from './task-node';
import { MilestoneNode } from './milestone-node';
import { DecisionNode } from './decision-node';
import { NoteNode } from './note-node';
import { ResourceNode } from './resource-node';
import { TimelineNode } from './timeline-node';
import { TeamNode } from './team-node';
import { ProjectNode } from './project-node';
import { DeadlineNode } from './deadline-node';
import { StatusNode } from './status-node';
import { AnnotationNode } from './annotation-node';
import { ProcessNode } from './process-node';
import { TextNode } from './text-node';

export const nodeTypes = {
  task: TaskNode,
  milestone: MilestoneNode,
  decision: DecisionNode,
  note: NoteNode,
  resource: ResourceNode,
  timeline: TimelineNode,
  team: TeamNode,
  project: ProjectNode,
  deadline: DeadlineNode,
  status: StatusNode,
  annotation: AnnotationNode,
  process: ProcessNode,
  text: TextNode,
};
