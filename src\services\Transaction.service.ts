import axios from 'axios';
import { ITransaction } from '@/models/Transaction';

export interface CreateTransactionRequest {
  planId: 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'yearly';
  previousPlanId?: 'free' | 'basic' | 'professional' | 'enterprise';
  type?: 'subscription_payment' | 'plan_upgrade' | 'plan_downgrade' | 'subscription_renewal';
  metadata?: {
    source?: string;
    campaign?: string;
  };
}

export interface TransactionResponse {
  transaction: ITransaction;
  razorpayOrder?: any;
}

export interface TransactionListResponse {
  transactions: ITransaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RefundRequest {
  transactionId: string;
  amount?: number; // Partial refund amount in paise
  reason: string;
}

export interface RefundResponse {
  success: boolean;
  refund: any;
  transaction: ITransaction;
}

export interface TransactionStats {
  totalRevenue: number;
  monthlyRevenue: number;
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  refundedAmount: number;
  averageTransactionValue: number;
  revenueByPlan: {
    [key: string]: number;
  };
  revenueByMonth: Array<{
    month: string;
    revenue: number;
    transactions: number;
  }>;
}

export class TransactionService {
  private static baseURL = '/api/transactions';

  /**
   * Create a new transaction
   */
  static async createTransaction(data: CreateTransactionRequest): Promise<TransactionResponse> {
    try {
      const response = await axios.post(`${this.baseURL}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create transaction');
    }
  }

  /**
   * Get transaction by ID
   */
  static async getTransaction(transactionId: string): Promise<ITransaction> {
    try {
      const response = await axios.get(`${this.baseURL}/${transactionId}`);
      return response.data.transaction;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch transaction');
    }
  }

  /**
   * Get all transactions for organization
   */
  static async getTransactions(params?: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    planId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<TransactionListResponse> {
    try {
      const response = await axios.get(`${this.baseURL}`, { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch transactions');
    }
  }

  /**
   * Verify payment and update transaction
   */
  static async verifyPayment(data: {
    transactionId: string;
    razorpay_payment_id: string;
    razorpay_order_id: string;
    razorpay_signature: string;
  }): Promise<ITransaction> {
    try {
      const response = await axios.post(`${this.baseURL}/verify`, data);
      return response.data.transaction;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Payment verification failed');
    }
  }

  /**
   * Process refund
   */
  static async processRefund(data: RefundRequest): Promise<RefundResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/refund`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to process refund');
    }
  }

  /**
   * Get transaction statistics
   */
  static async getTransactionStats(params?: {
    startDate?: string;
    endDate?: string;
    planId?: string;
  }): Promise<TransactionStats> {
    try {
      const response = await axios.get(`${this.baseURL}/stats`, { params });
      return response.data.stats;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch transaction stats');
    }
  }

  /**
   * Download transaction receipt
   */
  static async downloadReceipt(transactionId: string): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/${transactionId}/receipt`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to download receipt');
    }
  }

  /**
   * Export transactions to CSV
   */
  static async exportTransactions(params?: {
    startDate?: string;
    endDate?: string;
    status?: string;
    type?: string;
  }): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/export`, {
        params,
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to export transactions');
    }
  }

  /**
   * Get revenue analytics
   */
  static async getRevenueAnalytics(params?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    startDate?: string;
    endDate?: string;
  }): Promise<{
    revenue: Array<{ date: string; amount: number; transactions: number }>;
    growth: {
      percentage: number;
      amount: number;
    };
    topPlans: Array<{ planId: string; revenue: number; percentage: number }>;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/analytics/revenue`, { params });
      return response.data.analytics;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch revenue analytics');
    }
  }

  /**
   * Format transaction amount
   */
  static formatAmount(amountInPaise: number, currency: string = 'INR'): string {
    const amount = amountInPaise / 100;
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Get transaction status badge color
   */
  static getStatusColor(status: string): string {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      completed: 'bg-green-100 text-green-800 border-green-200',
      failed: 'bg-red-100 text-red-800 border-red-200',
      cancelled: 'bg-gray-100 text-gray-800 border-gray-200',
      refunded: 'bg-purple-100 text-purple-800 border-purple-200',
    };
    return colors[status as keyof typeof colors] || colors.pending;
  }

  /**
   * Get transaction type display name
   */
  static getTypeDisplayName(type: string): string {
    const names = {
      subscription_payment: 'Subscription Payment',
      plan_upgrade: 'Plan Upgrade',
      plan_downgrade: 'Plan Downgrade',
      subscription_renewal: 'Subscription Renewal',
      refund: 'Refund',
    };
    return names[type as keyof typeof names] || type;
  }

  /**
   * Get payment method display name
   */
  static getPaymentMethodDisplayName(method: string): string {
    const names = {
      upi: 'UPI',
      netbanking: 'Net Banking',
      card: 'Credit/Debit Card',
      wallet: 'Digital Wallet',
      emi: 'EMI',
    };
    return names[method as keyof typeof names] || method;
  }

  /**
   * Calculate transaction fees (if applicable)
   */
  static calculateTransactionFees(amount: number, paymentMethod: string): number {
    // Razorpay fee structure (approximate)
    const feeRates = {
      upi: 0, // Free for UPI
      netbanking: 0.02, // 2%
      card: 0.025, // 2.5%
      wallet: 0.02, // 2%
      emi: 0.03, // 3%
    };

    const rate = feeRates[paymentMethod as keyof typeof feeRates] || 0.025;
    return Math.round(amount * rate);
  }

  /**
   * Generate transaction summary
   */
  static generateTransactionSummary(transactions: ITransaction[]): {
    totalAmount: number;
    totalTransactions: number;
    successfulTransactions: number;
    failedTransactions: number;
    averageAmount: number;
    paymentMethodBreakdown: { [key: string]: number };
    planBreakdown: { [key: string]: number };
  } {
    const summary = {
      totalAmount: 0,
      totalTransactions: transactions.length,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageAmount: 0,
      paymentMethodBreakdown: {} as { [key: string]: number },
      planBreakdown: {} as { [key: string]: number },
    };

    transactions.forEach(transaction => {
      if (transaction.status === 'completed') {
        summary.totalAmount += transaction.amount;
        summary.successfulTransactions++;
      } else if (transaction.status === 'failed') {
        summary.failedTransactions++;
      }

      // Payment method breakdown
      summary.paymentMethodBreakdown[transaction.paymentMethod] =
        (summary.paymentMethodBreakdown[transaction.paymentMethod] || 0) + 1;

      // Plan breakdown
      summary.planBreakdown[transaction.planId] =
        (summary.planBreakdown[transaction.planId] || 0) + 1;
    });

    summary.averageAmount =
      summary.successfulTransactions > 0 ? summary.totalAmount / summary.successfulTransactions : 0;

    return summary;
  }
}
