import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { User } from '@/models/User';
import Note from '@/models/Note';
import { v2 as cloudinary } from 'cloudinary';

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/notes');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

async function getAuthenticatedUser(c: any) {
  const user = c.get('user');
  if (!user?.email) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  await connectDB();
  const dbUser = await User.findOne({ email: user.email });
  if (!dbUser || !dbUser.organizationId) {
    return c.json({ error: 'User or organization not found' }, 404);
  }

  return { user: dbUser, userData: user };
}

app.get('/', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const category = c.req.query('category') || '';
    const tags = c.req.query('tags')?.split(',').filter(Boolean) || [];
    const filter = c.req.query('filter') || 'all'; // all, pinned, archived, favorites, trash
    const sortBy = c.req.query('sortBy') || 'updatedAt';
    const sortOrder = c.req.query('sortOrder') || 'desc';

    const query: any = {
      userId: user._id,
      organizationId: user.organizationId,
    };

    switch (filter) {
      case 'pinned':
        query.isPinned = true;
        query.deletedAt = null;
        break;
      case 'archived':
        query.isArchived = true;
        query.deletedAt = null;
        break;
      case 'favorites':
        query.isFavorite = true;
        query.deletedAt = null;
        break;
      case 'trash':
        query.deletedAt = { $ne: null };
        break;
      default:
        query.deletedAt = null;
        query.isArchived = false;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    if (category) {
      query.category = category;
    }

    if (tags.length > 0) {
      query.tags = { $in: tags };
    }

    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (page - 1) * limit;
    const notes = await Note.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .populate('collaborators.userId', 'name email avatar')
      .populate('linkedNotes', 'title')
      .populate('linkedTasks', 'name status')
      .populate('linkedProjects', 'name status');

    const total = await Note.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    const categories = await Note.distinct('category', {
      userId: user._id,
      organizationId: user.organizationId,
      deletedAt: null,
    });

    const allTags = await Note.distinct('tags', {
      userId: user._id,
      organizationId: user.organizationId,
      deletedAt: null,
    });

    return c.json({
      success: true,
      notes,
      pagination: {
        currentPage: page,
        totalPages,
        totalNotes: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
      filters: {
        categories,
        tags: allTags,
      },
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const body = await c.req.json();
    const {
      title,
      content,
      contentType,
      tags,
      category,
      color,
      isPinned,
      isFavorite,
      isPublic,
      password,
      settings,
    } = body;

    if (!title?.trim()) {
      return c.json({ error: 'Title is required' }, 400);
    }

    const note = new Note({
      title: title.trim(),
      content: content || '',
      contentType: contentType || 'plate',
      tags: tags || [],
      category: category || 'general',
      color: color || '#ffffff',
      isPinned: isPinned || false,
      isFavorite: isFavorite || false,
      isPublic: isPublic || false,
      password: password || undefined,
      userId: user._id,
      organizationId: user.organizationId,
      settings: {
        allowComments: true,
        allowDownload: true,
        allowPrint: true,
        autoSave: true,
        fontSize: 'medium',
        theme: 'auto',
        ...settings,
      },
    });

    await note.save();

    const populatedNote = await Note.findById(note._id).populate(
      'collaborators.userId',
      'name email avatar'
    );

    return c.json(
      {
        success: true,
        message: 'Note created successfully',
        note: populatedNote,
      },
      201
    );
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/:noteId', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');

    const note = await Note.findOne({
      _id: noteId,
      $or: [{ userId: user._id }, { 'collaborators.userId': user._id }, { isPublic: true }],
      deletedAt: null,
    })
      .populate('collaborators.userId', 'name email avatar')
      .populate('linkedNotes', 'title')
      .populate('linkedTasks', 'name status')
      .populate('linkedProjects', 'name status');

    if (!note) {
      return c.json({ error: 'Note not found' }, 404);
    }

    if (note.userId.toString() === user._id.toString()) {
      note.metadata.viewCount += 1;
      note.metadata.lastViewedAt = new Date();
      await note.save();
    }

    return c.json({ note });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// PUT /api/notes/:noteId - Update specific note
app.put('/:noteId', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');
    const updateData = await c.req.json();

    const note = await Note.findOne({
      _id: noteId,
      $or: [
        { userId: user._id },
        {
          'collaborators.userId': user._id,
          'collaborators.permission': { $in: ['edit', 'admin'] },
        },
      ],
      deletedAt: null,
    });

    if (!note) {
      return c.json({ error: 'Note not found or no edit permission' }, 404);
    }

    // Store previous version if content changed
    if (updateData.content && updateData.content !== note.content) {
      note.versions.push({
        version: note.version,
        content: note.content,
        title: note.title,
        modifiedBy: user._id,
        modifiedAt: new Date(),
        changeDescription: updateData.changeDescription || 'Content updated',
      });

      // Keep only last 10 versions
      if (note.versions.length > 10) {
        note.versions = note.versions.slice(-10);
      }

      note.version += 1;
    }

    // Update note
    Object.assign(note, updateData);
    await note.save();

    const updatedNote = await Note.findById(note._id).populate(
      'collaborators.userId',
      'name email avatar'
    );

    return c.json({
      success: true,
      message: 'Note updated successfully',
      note: updatedNote,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// DELETE /api/notes/:noteId - Delete specific note
app.delete('/:noteId', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');
    const permanent = c.req.query('permanent') === 'true';

    const note = await Note.findOne({
      _id: noteId,
      userId: user._id,
    });

    if (!note) {
      return c.json({ error: 'Note not found' }, 404);
    }

    if (permanent) {
      await Note.findByIdAndDelete(noteId);
    } else {
      await note.softDelete();
    }

    return c.json({
      success: true,
      message: permanent ? 'Note permanently deleted' : 'Note moved to trash',
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// POST /api/notes/:noteId/pin - Toggle pin status
app.post('/:noteId/pin', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');

    const note = await Note.findOne({
      _id: noteId,
      $or: [
        { userId: user._id },
        {
          'collaborators.userId': user._id,
          'collaborators.permission': { $in: ['edit', 'admin'] },
        },
      ],
      deletedAt: null,
    });

    if (!note) {
      return c.json({ error: 'Note not found or no edit permission' }, 404);
    }

    note.isPinned = !note.isPinned;
    note.version += 1;
    await note.save();

    return c.json({
      success: true,
      message: note.isPinned ? 'Note pinned successfully' : 'Note unpinned successfully',
      note: {
        _id: note._id,
        isPinned: note.isPinned,
        version: note.version,
      },
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// POST /api/notes/:noteId/favorite - Toggle favorite status
app.post('/:noteId/favorite', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');

    const note = await Note.findOne({
      _id: noteId,
      $or: [
        { userId: user._id },
        {
          'collaborators.userId': user._id,
          'collaborators.permission': { $in: ['edit', 'admin'] },
        },
      ],
      deletedAt: null,
    });

    if (!note) {
      return c.json({ error: 'Note not found or no edit permission' }, 404);
    }

    note.isFavorite = !note.isFavorite;
    note.version += 1;
    await note.save();

    return c.json({
      success: true,
      message: note.isFavorite ? 'Note added to favorites' : 'Note removed from favorites',
      note: {
        _id: note._id,
        isFavorite: note.isFavorite,
        version: note.version,
      },
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// POST /api/notes/:noteId/archive - Toggle archive status
app.post('/:noteId/archive', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');

    const note = await Note.findOne({
      _id: noteId,
      $or: [
        { userId: user._id },
        {
          'collaborators.userId': user._id,
          'collaborators.permission': { $in: ['edit', 'admin'] },
        },
      ],
      deletedAt: null,
    });

    if (!note) {
      return c.json({ error: 'Note not found or no edit permission' }, 404);
    }

    note.isArchived = !note.isArchived;
    note.version += 1;
    await note.save();

    return c.json({
      success: true,
      message: note.isArchived ? 'Note archived successfully' : 'Note unarchived successfully',
      note: {
        _id: note._id,
        isArchived: note.isArchived,
        version: note.version,
      },
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/:noteId/duplicate', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const noteId = c.req.param('noteId');

    const originalNote = await Note.findOne({
      _id: noteId,
      $or: [{ userId: user._id }, { 'collaborators.userId': user._id }, { isPublic: true }],
      deletedAt: null,
    });

    if (!originalNote) {
      return c.json({ error: 'Note not found' }, 404);
    }

    const duplicateNote = new Note({
      title: `${originalNote.title} (Copy)`,
      content: originalNote.content,
      contentType: originalNote.contentType,
      tags: [...originalNote.tags],
      category: originalNote.category,
      color: originalNote.color,
      isPinned: false,
      isFavorite: false,
      isArchived: false,
      userId: user._id,
      organizationId: user.organizationId,
      settings: { ...originalNote.settings },
      metadata: {
        wordCount: originalNote.metadata?.wordCount || 0,
        readingTime: originalNote.metadata?.readingTime || 0,
        lastViewedAt: new Date(),
        viewCount: 0,
        exportedFormats: [],
      },
    });

    await duplicateNote.save();

    const populatedNote = await Note.findById(duplicateNote._id).populate(
      'collaborators.userId',
      'name email avatar'
    );

    return c.json(
      {
        success: true,
        message: 'Note duplicated successfully',
        note: populatedNote,
      },
      201
    );
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// POST /api/notes/actions - Bulk actions
app.post('/actions', async c => {
  try {
    const authResult = await getAuthenticatedUser(c);
    if ('json' in authResult) return authResult;
    const { user } = authResult;

    const { action, noteIds, data } = await c.req.json();

    if (!action || !noteIds || !Array.isArray(noteIds)) {
      return c.json({ error: 'Invalid request data' }, 400);
    }

    let result;

    switch (action) {
      case 'bulk-delete':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { deletedAt: new Date() }
        );
        break;

      case 'bulk-restore':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: { $ne: null },
          },
          { deletedAt: null }
        );
        break;

      case 'bulk-archive':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isArchived: true }
        );
        break;

      case 'bulk-unarchive':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isArchived: false }
        );
        break;

      case 'bulk-pin':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isPinned: true }
        );
        break;

      case 'bulk-unpin':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isPinned: false }
        );
        break;

      case 'bulk-favorite':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isFavorite: true }
        );
        break;

      case 'bulk-unfavorite':
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { isFavorite: false }
        );
        break;

      case 'bulk-categorize':
        if (!data?.category) {
          return c.json({ error: 'Category is required' }, 400);
        }
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { category: data.category }
        );
        break;

      case 'bulk-tag':
        if (!data?.tags || !Array.isArray(data.tags)) {
          return c.json({ error: 'Tags array is required' }, 400);
        }
        result = await Note.updateMany(
          {
            _id: { $in: noteIds },
            userId: user._id,
            deletedAt: null,
          },
          { $addToSet: { tags: { $each: data.tags } } }
        );
        break;

      case 'duplicate': {
        if (noteIds.length !== 1) {
          return c.json({ error: 'Can only duplicate one note at a time' }, 400);
        }

        const originalNote = await Note.findOne({
          _id: noteIds[0],
          userId: user._id,
          deletedAt: null,
        });

        if (!originalNote) {
          return c.json({ error: 'Note not found' }, 404);
        }

        const duplicatedNote = new Note({
          ...originalNote.toObject(),
          _id: undefined,
          title: `${originalNote.title} (Copy)`,
          isPinned: false,
          version: 1,
          versions: [],
          metadata: {
            wordCount: originalNote.metadata.wordCount,
            readingTime: originalNote.metadata.readingTime,
            lastViewedAt: new Date(),
            viewCount: 0,
            exportedFormats: [],
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        await duplicatedNote.save();
        result = { duplicatedNote };
        break;
      }

      case 'export': {
        const format = data?.format || 'json';
        const notes = await Note.find({
          _id: { $in: noteIds },
          userId: user._id,
          deletedAt: null,
        }).select('-versions -__v');

        let exportData;
        switch (format) {
          case 'json':
            exportData = JSON.stringify(notes, null, 2);
            break;
          case 'csv': {
            const csvHeaders = 'Title,Content,Category,Tags,Created,Updated\n';
            const csvRows = notes
              .map(
                note =>
                  `"${note.title}","${note.content.replace(/"/g, '""')}","${note.category}","${note.tags.join(';')}","${note.createdAt}","${note.updatedAt}"`
              )
              .join('\n');
            exportData = csvHeaders + csvRows;
            break;
          }
          case 'markdown':
            exportData = notes
              .map(note => `# ${note.title}\n\n${note.content}\n\n---\n\n`)
              .join('');
            break;
          default:
            return c.json({ error: 'Unsupported export format' }, 400);
        }

        // Update export history
        await Note.updateMany(
          { _id: { $in: noteIds } },
          { $addToSet: { 'metadata.exportedFormats': format } }
        );

        return c.json({
          success: true,
          exportData,
          format,
          filename: `notes-export-${Date.now()}.${format === 'json' ? 'json' : format === 'csv' ? 'csv' : 'md'}`,
        });
      }

      default:
        return c.json({ error: 'Invalid action' }, 400);
    }

    return c.json({
      success: true,
      message: `${action} completed successfully`,
      modifiedCount: result?.modifiedCount || result?.nModified || 0,
      ...(result?.duplicatedNote && { duplicatedNote: result.duplicatedNote }),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/upload-image', async c => {
  try {
    const user = c.get('user');
    if (!user?.id) {
      console.error('Image upload: No user found in context');
      return c.json({ error: 'Unauthorized' }, 401);
    }

    console.log('Image upload: User authenticated:', user.id);

    const formData = await c.req.formData();
    const file = formData.get('image') as File;

    if (!file) {
      console.error('Image upload: No file provided in form data');
      return c.json({ error: 'No file provided' }, 400);
    }

    console.log('Image upload: File received:', {
      name: file.name,
      type: file.type,
      size: file.size,
    });

    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.error('Image upload: Invalid file type:', file.type);
      return c.json({ error: 'Invalid file type' }, 400);
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('Image upload: File too large:', file.size);
      return c.json({ error: 'File too large' }, 400);
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    console.log('Image upload: Starting Cloudinary upload...');

    const uploadResponse = await Promise.race([
      new Promise((resolve, reject) => {
        cloudinary.uploader
          .upload_stream(
            {
              resource_type: 'image',
              folder: `notes/${user.id}`,
              transformation: [
                { width: 1200, height: 800, crop: 'limit' },
                { quality: 'auto' },
                { format: 'auto' },
              ],
              timeout: 25000, // 25 second timeout
            },
            (error, result) => {
              if (error) {
                console.error('Cloudinary upload error:', error);
                reject(error);
              } else {
                console.log('Cloudinary upload success:', {
                  url: result?.secure_url,
                  public_id: result?.public_id,
                });
                resolve(result);
              }
            }
          )
          .end(buffer);
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Image upload timeout after 25 seconds')), 25000)
      ),
    ]);

    const result = uploadResponse as any;

    return c.json({
      success: 1,
      file: {
        url: result.secure_url,
        width: result.width,
        height: result.height,
        size: file.size,
        name: file.name,
        title: file.name,
      },
    });
  } catch (error: any) {
    console.error('Image upload error:', error);
    return c.json(
      {
        success: 0,
        error: 'Failed to upload image',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      },
      500
    );
  }
});

app.get('/fetch-image', async c => {
  try {
    const user = c.get('user');
    if (!user?.id) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const url = c.req.query('url');

    if (!url) {
      return c.json({ error: 'URL parameter is required' }, 400);
    }

    try {
      new URL(url);
    } catch {
      return c.json({ error: 'Invalid URL' }, 400);
    }

    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    const urlLower = url.toLowerCase();
    const isImageUrl = imageExtensions.some(ext => urlLower.includes(ext));

    if (!isImageUrl) {
      return c.json({ error: 'URL does not point to an image' }, 400);
    }

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TaskFluxio/1.0; +https://TaskFluxio.com)',
        },
      });

      if (!response.ok) {
        return c.json({ error: 'Image not accessible' }, 400);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType?.startsWith('image/')) {
        return c.json({ error: 'URL does not point to a valid image' }, 400);
      }

      const contentLength = response.headers.get('content-length');
      const size = contentLength ? parseInt(contentLength) : 0;

      // Return Editor.js compatible response
      return c.json({
        success: 1,
        file: {
          url: url,
          size: size,
          name: url.split('/').pop() || 'image',
          title: url.split('/').pop() || 'image',
        },
      });
    } catch (error) {
      return c.json({ error: 'Failed to fetch image' }, 400);
    }
  } catch (error) {
    return c.json({ success: 0, error: 'Failed to fetch image' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
export const PATCH = handle(app);
