'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { GitBranch, Plus, RefreshCw, Search, AlertCircle, CheckCircle, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

import { EmptyState } from '@/components/Global/EmptyState';
import { useRouter } from 'next/navigation';
import {
  RepositoryCardSkeleton,
  ConnectedRepositoryCardSkeleton,
  IssueCardSkeleton,
  PullRequestCardSkeleton,
  GitHubHubHeaderSkeleton,
} from '@/components/GitHub/GitHubSkeletons';

import { GitHubStatusCard } from '@/components/GitHub/GitHubStatusCard';
import { GitHubIssueCard } from '@/components/GitHub/GitHubIssueCard';
import { GitHubPullRequestCard } from '@/components/GitHub/GitHubPullRequestCard';
import { RepositoryCard } from '@/components/GitHub/RepositoryCard';
import { ConnectedRepositoryCard } from '@/components/GitHub/ConnectedRepositoryCard';
import GitHubPaginationSkeleton from '@/components/GitHub/GitHubPaginationSkeleton';
import AdvancedPagination from '@/components/ui/AdvancedPagination';
import { useGitHub } from '@/hooks/useGitHub';

export default function GitHubHubPage() {
  const [activeTab, setActiveTab] = useState('repositories');
  const router = useRouter();

  const {
    githubStatus,
    availableRepositories,
    connectedRepositories,
    allIssues,
    allPullRequests,
    filters,
    repositoryPage,
    searchQuery,
    isStatusLoading,
    isAvailableLoading,
    isConnectedLoading,
    isAllIssuesLoading,
    isAllPullsLoading,
    isConnecting,
    isDisconnecting,
    isCreatingTask,
    connectRepository,
    disconnectRepository,
    createTaskFromIssue,
    updateFilters,
    refreshAll,
    setRepositoryPage,
    setSearchQuery,
  } = useGitHub();

  if (isStatusLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="theme-surface px-3 rounded-lg py-2 h-full w-full overflow-y-auto custom-scrollbar"
      >
        <GitHubHubHeaderSkeleton />
        <RepositoryCardSkeleton count={6} />
      </motion.div>
    );
  }

  if (!githubStatus?.connected) {
    return (
      <div className="flex items-center justify-center h-full">
        <EmptyState
          type="error"
          title="GitHub Not Connected"
          description="Please connect your GitHub account to access this page."
          actions={[
            {
              label: 'Go to Integrations',
              onClick: () => router.push('/integrations'),
              icon: <GitBranch className="h-4 w-4" />,
            },
          ]}
          size="lg"
          animated={true}
        />
      </div>
    );
  }

  const filteredAvailableRepos = availableRepositories?.repositories || [];
  const filteredConnectedRepos = connectedRepositories?.repositories || [];

  return (
    <div className="h-full flex flex-col theme-surface rounded-lg overflow-hidden">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex-shrink-0 px-3 sm:px-6 py-3 border-b border-border/20 bg-gradient-to-r from-background/95 via-background to-background/95 backdrop-blur-sm"
      >
        <div className="flex flex-col gap-3">
          <GitHubStatusCard
            status={githubStatus}
            onRefresh={refreshAll}
            isLoading={isAvailableLoading || isConnectedLoading}
            className="compact-status-card"
          />
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 theme-text-secondary h-4 w-4 theme-transition" />
            <Input
              placeholder="Search repositories, issues, and pull requests..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-9 pr-12 py-2 theme-input theme-transition focus:ring-2 focus:ring-primary/20"
            />
            {searchQuery && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isAvailableLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin theme-text-secondary" />
                ) : (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="theme-text-secondary hover:theme-text-primary theme-transition p-1 rounded-md hover:bg-muted/50"
                    title="Clear search"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </motion.div>

      <div className="flex flex-col flex-1 min-h-0">
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="flex-shrink-0 px-2 sm:px-4 lg:px-6 py-3 sm:py-4 border-b border-border/10"
        >
          <div className="flex justify-center">
            <div className="flex items-center gap-0.5 sm:gap-1 p-0.5 sm:p-1 bg-muted/40 dark:bg-muted/30 rounded-lg sm:rounded-xl border border-border/30 shadow-lg backdrop-blur-sm max-w-4xl overflow-x-auto scrollbar-hide">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveTab('repositories')}
                className={`relative text-xs sm:text-sm font-medium theme-transition whitespace-nowrap px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 h-7 sm:h-8 lg:h-9 min-w-0 flex-shrink-0 rounded-md sm:rounded-lg flex-1 sm:flex-initial ${
                  activeTab === 'repositories'
                    ? 'bg-background dark:bg-white text-foreground dark:text-gray-900 shadow-md border border-border/20'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <GitBranch className="h-3 w-3 sm:h-3.5 sm:w-3.5 lg:h-4 lg:w-4 mr-1 sm:mr-1.5 lg:mr-2 text-blue-500 dark:text-blue-400 theme-transition flex-shrink-0" />
                <span className="hidden md:inline">Available</span>
                <span className="md:hidden">Repos</span>
                <span className="ml-0.5 sm:ml-1 text-[9px] sm:text-[10px] lg:text-xs opacity-60 font-normal">
                  ({availableRepositories?.total || 0})
                </span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveTab('connected')}
                className={`relative text-xs sm:text-sm font-medium theme-transition whitespace-nowrap px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 h-7 sm:h-8 lg:h-9 min-w-0 flex-shrink-0 rounded-md sm:rounded-lg flex-1 sm:flex-initial ${
                  activeTab === 'connected'
                    ? 'bg-background dark:bg-white text-foreground dark:text-gray-900 shadow-md border border-border/20'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <CheckCircle className="h-3 w-3 sm:h-3.5 sm:w-3.5 lg:h-4 lg:w-4 mr-1 sm:mr-1.5 lg:mr-2 text-emerald-500 dark:text-emerald-400 theme-transition flex-shrink-0" />
                <span className="hidden md:inline">Connected</span>
                <span className="md:hidden">Conn</span>
                <span className="ml-0.5 sm:ml-1 text-[9px] sm:text-[10px] lg:text-xs opacity-60 font-normal">
                  ({filteredConnectedRepos.length})
                </span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveTab('issues')}
                className={`relative text-xs sm:text-sm font-medium theme-transition whitespace-nowrap px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 h-7 sm:h-8 lg:h-9 min-w-0 flex-shrink-0 rounded-md sm:rounded-lg flex-1 sm:flex-initial ${
                  activeTab === 'issues'
                    ? 'bg-background dark:bg-white text-foreground dark:text-gray-900 shadow-md border border-border/20'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <AlertCircle className="h-3 w-3 sm:h-3.5 sm:w-3.5 lg:h-4 lg:w-4 mr-1 sm:mr-1.5 lg:mr-2 text-amber-500 dark:text-amber-400 theme-transition flex-shrink-0" />
                <span>Issues</span>
                <span className="ml-0.5 sm:ml-1 text-[9px] sm:text-[10px] lg:text-xs opacity-60 font-normal">
                  ({allIssues?.totalIssues})
                </span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveTab('pulls')}
                className={`relative text-xs sm:text-sm font-medium theme-transition whitespace-nowrap px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 h-7 sm:h-8 lg:h-9 min-w-0 flex-shrink-0 rounded-md sm:rounded-lg flex-1 sm:flex-initial ${
                  activeTab === 'pulls'
                    ? 'bg-background dark:bg-white text-foreground dark:text-gray-900 shadow-md border border-border/20'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <GitBranch className="h-3 w-3 sm:h-3.5 sm:w-3.5 lg:h-4 lg:w-4 mr-1 sm:mr-1.5 lg:mr-2 text-violet-500 dark:text-violet-400 theme-transition flex-shrink-0" />
                <span className="hidden md:inline">Pull Requests</span>
                <span className="md:hidden">PRs</span>
              </Button>
            </div>
          </div>
        </motion.div>

        <div className="flex-1 min-h-0 overflow-hidden">
          {/* Repositories Tab */}
          {activeTab === 'repositories' && (
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {isAvailableLoading ? (
                  <div className="px-3 sm:px-6 py-4">
                    <RepositoryCardSkeleton count={6} />
                  </div>
                ) : filteredAvailableRepos.length === 0 ? (
                  <div className="flex items-center justify-center h-full px-3 sm:px-6">
                    <EmptyState
                      type="search"
                      title="No repositories found"
                      description={
                        searchQuery
                          ? `No repositories match your search for "${searchQuery}"`
                          : 'No repositories available to connect'
                      }
                      actions={
                        searchQuery
                          ? [
                              {
                                label: 'Clear search',
                                onClick: () => setSearchQuery(''),
                                variant: 'outline',
                              },
                            ]
                          : []
                      }
                      size="md"
                      animated={true}
                    />
                  </div>
                ) : (
                  <div className="px-3 sm:px-6 py-4">
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.4, delay: 0.1 }}
                      className="grid gap-3 sm:gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4"
                    >
                      {filteredAvailableRepos.map((repo: any, index: number) => (
                        <motion.div
                          key={repo?.id || index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          className="h-full"
                        >
                          <RepositoryCard
                            repository={repo}
                            index={index}
                            isConnected={repo?.isConnected || false}
                            onConnect={() => repo?.id && connectRepository(repo.id.toString())}
                            onDisconnect={() =>
                              repo?.id && disconnectRepository(repo.id.toString())
                            }
                            isLoading={isConnecting || isDisconnecting}
                            className="h-full"
                          />
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>
                )}
              </div>

              {!isAvailableLoading && filteredAvailableRepos.length > 0 && (
                <div className="flex-shrink-0 border-t border-border/20 bg-background/95 backdrop-blur-sm px-3 sm:px-6 py-4">
                  {isAvailableLoading ? (
                    <GitHubPaginationSkeleton />
                  ) : (
                    availableRepositories &&
                    (availableRepositories.totalPages > 1 || availableRepositories.hasMore) && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.3 }}
                      >
                        <AdvancedPagination
                          currentPage={repositoryPage}
                          totalPages={availableRepositories.totalPages || 1}
                          hasMore={availableRepositories.hasMore || false}
                          totalItems={availableRepositories.total}
                          itemsPerPage={availableRepositories.perPage || 30}
                          onPageChange={setRepositoryPage}
                          isLoading={isAvailableLoading}
                          itemName="repositories"
                          showInfo={true}
                          showFirstLast={true}
                        />
                      </motion.div>
                    )
                  )}
                </div>
              )}
            </div>
          )}

          {/* Connected Tab */}
          {activeTab === 'connected' && (
            <div className="h-full overflow-y-auto custom-scrollbar px-3 sm:px-6 py-4">
              {isConnectedLoading ? (
                <ConnectedRepositoryCardSkeleton count={4} />
              ) : filteredConnectedRepos.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <EmptyState
                    type="custom"
                    icon={<GitBranch className="w-full h-full" />}
                    title="No connected repositories"
                    description={
                      searchQuery
                        ? `No connected repositories match your search for "${searchQuery}"`
                        : 'Connect repositories to start syncing issues and pull requests'
                    }
                    actions={
                      searchQuery
                        ? [
                            {
                              label: 'Clear search',
                              onClick: () => setSearchQuery(''),
                              variant: 'outline',
                            },
                          ]
                        : [
                            {
                              label: 'Browse Available Repositories',
                              onClick: () => setActiveTab('repositories'),
                              icon: <Plus className="h-4 w-4" />,
                            },
                          ]
                    }
                    size="md"
                    animated={true}
                  />
                </div>
              ) : (
                <div className="grid gap-3 sm:gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {filteredConnectedRepos.map((repo: any, index: number) => (
                    <div key={repo?._id || index} className="h-full">
                      <ConnectedRepositoryCard
                        repository={repo}
                        index={index}
                        onDisconnect={() =>
                          repo?.repositoryId && disconnectRepository(repo.repositoryId)
                        }
                        isLoading={isDisconnecting}
                        className="h-full"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Issues Tab */}
          {activeTab === 'issues' && (
            <div className="h-full overflow-y-auto custom-scrollbar px-3 sm:px-6 py-4">
              {isAllIssuesLoading ? (
                <IssueCardSkeleton count={8} />
              ) : !allIssues || allIssues?.issues === undefined ? (
                <div className="flex items-center justify-center h-full">
                  <EmptyState
                    type="custom"
                    icon={<AlertCircle className="w-full h-full" />}
                    title="No Issues Found"
                    description="Failed to load issues. Please try again."
                    actions={[
                      {
                        label: 'Retry',
                        onClick: () => window.location.reload(),
                        variant: 'outline' as const,
                      },
                    ]}
                    size="md"
                    animated={true}
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <div className="flex flex-wrap items-center gap-2">
                      <Badge variant="outline" className="theme-transition text-xs">
                        {allIssues?.totalIssues || 0} issues
                      </Badge>
                      <Badge variant="secondary" className="theme-transition text-xs">
                        {allIssues?.totalRepositories || 0} repositories
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 overflow-x-auto">
                      <Button
                        variant={filters.state === 'open' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'open', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        Open
                      </Button>
                      <Button
                        variant={filters.state === 'closed' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'closed', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        Closed
                      </Button>
                      <Button
                        variant={filters.state === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'all', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        All
                      </Button>
                    </div>
                  </div>

                  {/* Issues Content */}
                  {allIssues?.issues?.length === 0 ? (
                    <div className="flex items-center justify-center h-full min-h-[400px]">
                      <EmptyState
                        type="custom"
                        icon={<AlertCircle className="w-full h-full" />}
                        title={`No ${filters.state === 'all' ? '' : filters.state.charAt(0).toUpperCase() + filters.state.slice(1)} Issues Found`}
                        description={
                          filteredConnectedRepos.length === 0
                            ? 'Connect repositories to start viewing issues'
                            : filters.state === 'all'
                              ? 'No issues found in your connected repositories'
                              : `No ${filters.state} issues found in your connected repositories`
                        }
                        actions={
                          filteredConnectedRepos.length === 0
                            ? [
                                {
                                  label: 'Browse Available Repositories',
                                  onClick: () => setActiveTab('repositories'),
                                  icon: <Plus className="h-4 w-4" />,
                                },
                              ]
                            : []
                        }
                        size="md"
                        animated={true}
                      />
                    </div>
                  ) : (
                    <>
                      {/* Issues List */}
                      <div className="space-y-3">
                        {allIssues.issues.map((issue: any, index: number) => (
                          <GitHubIssueCard
                            key={issue?.id || index}
                            issue={issue}
                            index={index}
                            onCreateTask={issueNumber =>
                              issue.repository?.id &&
                              createTaskFromIssue(issue.repository.id, issueNumber)
                            }
                            isCreatingTask={isCreatingTask}
                          />
                        ))}
                      </div>

                      {/* Pagination */}
                      {(allIssues.totalPages > 1 || allIssues.hasMore) && (
                        <div className="pt-4">
                          <AdvancedPagination
                            currentPage={filters.currentPage}
                            totalPages={allIssues.totalPages || 1}
                            hasMore={allIssues.hasMore || false}
                            totalItems={allIssues.totalIssues}
                            itemsPerPage={allIssues.perPage || 30}
                            onPageChange={page => updateFilters({ currentPage: page })}
                            isLoading={isAllIssuesLoading}
                            itemName="issues"
                            showInfo={true}
                            showFirstLast={true}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Pull Requests Tab */}
          {activeTab === 'pulls' && (
            <div className="h-full overflow-y-auto custom-scrollbar px-3 sm:px-6 py-4">
              {isAllPullsLoading ? (
                <PullRequestCardSkeleton count={6} />
              ) : !allPullRequests || allPullRequests?.pullRequests === undefined ? (
                <div className="flex items-center justify-center h-full">
                  <EmptyState
                    type="custom"
                    icon={<GitBranch className="w-full h-full" />}
                    title="No Pull Requests Found"
                    description="Failed to load pull requests. Please try again."
                    actions={[
                      {
                        label: 'Retry',
                        onClick: () => window.location.reload(),
                        variant: 'outline' as const,
                      },
                    ]}
                    size="md"
                    animated={true}
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Pull Requests Summary and Filters - Always visible */}
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <div className="flex flex-wrap items-center gap-2">
                      <Badge variant="outline" className="theme-transition text-xs">
                        {allPullRequests?.totalPullRequests || 0} pull requests
                      </Badge>
                      <Badge variant="secondary" className="theme-transition text-xs">
                        {allPullRequests?.totalRepositories || 0} repositories
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 overflow-x-auto">
                      <Button
                        variant={filters.state === 'open' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'open', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        Open
                      </Button>
                      <Button
                        variant={filters.state === 'closed' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'closed', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        Closed
                      </Button>
                      <Button
                        variant={filters.state === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateFilters({ state: 'all', currentPage: 1 })}
                        className="text-xs theme-transition whitespace-nowrap"
                      >
                        All
                      </Button>
                    </div>
                  </div>

                  {/* Pull Requests Content */}
                  {allPullRequests?.pullRequests?.length === 0 ? (
                    <div className="flex items-center justify-center h-full min-h-[400px]">
                      <EmptyState
                        type="custom"
                        icon={<GitBranch className="w-full h-full" />}
                        title={`No ${filters.state === 'all' ? '' : filters.state.charAt(0).toUpperCase() + filters.state.slice(1)} Pull Requests Found`}
                        description={
                          filteredConnectedRepos.length === 0
                            ? 'Connect repositories to start viewing pull requests'
                            : filters.state === 'all'
                              ? 'No pull requests found in your connected repositories'
                              : `No ${filters.state} pull requests found in your connected repositories`
                        }
                        actions={
                          filteredConnectedRepos.length === 0
                            ? [
                                {
                                  label: 'Browse Available Repositories',
                                  onClick: () => setActiveTab('repositories'),
                                  icon: <Plus className="h-4 w-4" />,
                                },
                              ]
                            : []
                        }
                        size="md"
                        animated={true}
                      />
                    </div>
                  ) : (
                    <>
                      {/* Pull Requests List */}
                      <div className="space-y-3">
                        {allPullRequests.pullRequests.map((pr: any, index: number) => (
                          <GitHubPullRequestCard
                            key={pr?.id || index}
                            pullRequest={pr}
                            index={index}
                          />
                        ))}
                      </div>

                      {/* Pagination */}
                      {(allPullRequests.totalPages > 1 || allPullRequests.hasMore) && (
                        <div className="pt-4">
                          <AdvancedPagination
                            currentPage={filters.currentPage}
                            totalPages={allPullRequests.totalPages || 1}
                            hasMore={allPullRequests.hasMore || false}
                            totalItems={allPullRequests.totalPullRequests}
                            itemsPerPage={allPullRequests.perPage || 30}
                            onPageChange={page => updateFilters({ currentPage: page })}
                            isLoading={isAllPullsLoading}
                            itemName="pull requests"
                            showInfo={true}
                            showFirstLast={true}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
