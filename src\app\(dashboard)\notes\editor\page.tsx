'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Save,
  Palette,
  Hash,
  Pin,
  Star,
  Archive,
  Trash2,
  MoreHorizontal,
  PanelRightOpen,
  PanelRightClose,
} from 'lucide-react';
import { NoteService } from '@/services/Note.service';
import { noteSchema } from '@/Schemas/Note';
import { CreateNoteData, Note } from '@/types/Note';
import { noteCategories } from '@/hooks/useNote';
import ReactSelect from '@/components/Global/ReactSelect';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import NoteColorPicker from '@/components/Notes/NoteColorPicker';
import PlateEditor, { PlateContent } from '@/components/Notes/PlateEditor';
import { motion } from 'framer-motion';

export default function NoteEditorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const noteId = searchParams.get('id');
  const isEditing = !!noteId;

  const [showColorPicker, setShowColorPicker] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [plateContent, setPlateContent] = useState<PlateContent | null>(null);
  const [showSidebar, setShowSidebar] = useState(true);

  const editorRef = useRef<{ getMarkdown: () => string }>(null);

  const formik = useFormik<CreateNoteData>({
    initialValues: {
      title: '',
      content: '',
      tags: [],
      category: 'general',
      color: '#ffffff',
      isPinned: false,
      isFavorite: false,
      settings: {
        allowComments: true,
        allowDownload: true,
        allowPrint: true,
        autoSave: true,
        fontSize: 'medium',
        theme: 'auto',
      },
    },
    validationSchema: noteSchema,
    onSubmit: values => {
      if (isEditing) {
        updateNoteMutation.mutate(values);
      } else {
        createNoteMutation.mutate(values);
      }
    },
  });

  const { data: note, isLoading: isLoadingNote } = useQuery({
    queryKey: ['note', noteId],
    queryFn: async () => {
      const data = await NoteService.getNoteById(noteId!);
      return data.note;
    },
    enabled: isEditing,
  });

  const createNoteMutation = useMutation({
    mutationFn: (data: CreateNoteData) => NoteService.createNote(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note created successfully');
      // Redirect to notes home page after successful creation
      setTimeout(() => {
        router.push('/notes');
      }, 100);
    },
    onError: (error: Error) => {
      toast.error('Failed to create note', { description: error.message });
    },
  });

  const updateNoteMutation = useMutation({
    mutationFn: (data: Partial<Note>) => NoteService.updateNote(noteId!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['note', noteId] });
    },
    onError: (error: Error) => {
      toast.error('Failed to update note', { description: error.message });
    },
  });

  const deleteNoteMutation = useMutation({
    mutationFn: () => NoteService.deleteNote(noteId!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Note deleted successfully');
      router.push('/notes');
    },
    onError: (error: Error) => {
      toast.error('Failed to delete note', { description: error.message });
    },
  });
  useEffect(() => {
    if (note && isEditing) {
      formik.setValues({
        title: note.title,
        content: note.content,
        tags: note.tags,
        category: note.category,
        color: note.color,
        isPinned: note.isPinned,
        isFavorite: note.isFavorite,
        settings: note.settings,
      });
      setAutoSaveEnabled(note?.settings?.autoSave);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [note, isEditing]);

  useEffect(() => {
    if (!autoSaveEnabled || !isEditing || !plateContent) return;

    const timeoutId = setTimeout(() => {
      try {
        if (editorRef.current) {
          const markdownContent = editorRef.current.getMarkdown();
          const updatedValues = { ...formik.values, content: markdownContent };
          updateNoteMutation.mutate(updatedValues);
        }
      } catch (error) {
        // Silently fail auto-save to avoid disrupting user experience
      }
    }, 2000);

    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plateContent, autoSaveEnabled, isEditing]);

  const handleSave = () => {
    try {
      if (editorRef.current) {
        const markdownContent = editorRef.current.getMarkdown();
        formik.setFieldValue('content', markdownContent);

        setTimeout(() => {
          formik.handleSubmit();
        }, 0);
      } else {
        formik.handleSubmit();
      }
    } catch (error) {
      toast.error('Failed to save note', {
        description: 'There was an error saving the note. Please try again.',
      });
    }
  };

  const handleQuickAction = (field: keyof Note) => {
    if (!isEditing) return;
    const currentValue = formik.values[field as keyof CreateNoteData];
    formik.setFieldValue(field, !currentValue);
    updateNoteMutation.mutate({ [field]: !currentValue });
  };

  const categoryOptions = noteCategories.map(cat => ({
    value: cat,
    label: cat.charAt(0).toUpperCase() + cat.slice(1),
  }));

  const tagOptions = note?.tags?.map((tag: string) => ({ value: tag, label: tag })) || [];

  if (isLoadingNote) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-4 rounded-md py-2 h-full w-full flex flex-col"
    >
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push('/notes')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Notes
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center gap-2">
              <Input
                placeholder="Note title..."
                value={formik.values.title}
                onChange={e => formik.setFieldValue('title', e.target.value)}
                className="text-lg font-semibold border-none shadow-none focus-visible:ring-0 px-0"
              />
              {formik.values.isPinned && <Pin className="h-4 w-4 text-primary" />}
              {formik.values.isFavorite && <Star className="h-4 w-4 text-yellow-500" />}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Sidebar Toggle - Always visible on mobile */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSidebar(!showSidebar)}
              className="lg:hidden"
            >
              {showSidebar ? (
                <PanelRightClose className="h-4 w-4" />
              ) : (
                <PanelRightOpen className="h-4 w-4" />
              )}
            </Button>

            {/* Edit-only features */}
            {isEditing && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowColorPicker(!showColorPicker)}
                  className="hidden sm:flex"
                >
                  <Palette className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleQuickAction('isPinned')}>
                      <Pin className="h-4 w-4 mr-2" />
                      {formik.values.isPinned ? 'Unpin' : 'Pin'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleQuickAction('isFavorite')}>
                      <Star className="h-4 w-4 mr-2" />
                      {formik.values.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleQuickAction('isArchived')}>
                      <Archive className="h-4 w-4 mr-2" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => setShowColorPicker(!showColorPicker)}
                      className="sm:hidden"
                    >
                      <Palette className="h-4 w-4 mr-2" />
                      Note Color
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => deleteNoteMutation.mutate()}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}

            <Button
              onClick={handleSave}
              disabled={createNoteMutation.isPending || updateNoteMutation.isPending}
              className="theme-button-primary"
            >
              <Save className="h-4 w-4 mr-2" />
              {isEditing ? 'Save' : 'Create'}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden relative">
        <div className="flex-1 flex flex-col min-w-0 h-full">
          <div className="flex-1 overflow-hidden">
            <PlateEditor
              ref={editorRef}
              data={formik.values.content}
              onChange={setPlateContent}
              placeholder="Start writing your note..."
              settings={formik.values.settings}
              className="h-full"
            />
          </div>
        </div>

        {/* Mobile Overlay */}
        {showSidebar && (
          <div
            className="lg:hidden fixed inset-0 bg-black/20 z-30"
            onClick={() => setShowSidebar(false)}
          />
        )}

        {/* Responsive Sidebar */}
        <div
          className={`
          ${showSidebar ? 'translate-x-0' : 'translate-x-full'}
          lg:translate-x-0 lg:relative lg:w-80
          fixed right-0 top-0 bottom-0 w-80 z-40
          border-l bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60
          p-4 space-y-6 overflow-y-auto theme-scrollbar
          transition-transform duration-300 ease-in-out
          lg:bg-muted/30 lg:backdrop-blur-none
        `}
        >
          {isEditing && showColorPicker && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Note Color</CardTitle>
              </CardHeader>
              <CardContent>
                <NoteColorPicker
                  selectedColor={formik.values.color}
                  onColorChange={color => formik.setFieldValue('color', color)}
                />
              </CardContent>
            </Card>
          )}

          {/* Category & Tags - Always visible */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Category & Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-xs font-medium text-muted-foreground">Category</label>
                <ReactSelect
                  options={categoryOptions}
                  value={categoryOptions.find(opt => opt.value === formik.values.category) || null}
                  onChange={option => {
                    const singleOption = option as { value: string; label: string } | null;
                    formik.setFieldValue('category', singleOption?.value || 'general');
                  }}
                  placeholder="Select category"
                />
              </div>
              <div>
                <label className="text-xs font-medium text-muted-foreground">Tags</label>
                <ReactSelect
                  isMulti
                  options={tagOptions}
                  value={formik.values?.tags?.map((tag: string) => ({ value: tag, label: tag }))}
                  onChange={options => {
                    const multiOptions = options as { value: string; label: string }[] | null;
                    formik.setFieldValue(
                      'tags',
                      multiOptions?.map((opt: { value: string; label: string }) => opt.value) || []
                    );
                  }}
                  placeholder="Add tags"
                />
              </div>
            </CardContent>
          </Card>

          {/* Note Information - Only show in edit mode */}
          {isEditing && note && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Note Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs text-muted-foreground">
                <div>Created: {new Date(note.createdAt).toLocaleDateString()}</div>
                <div>Updated: {new Date(note.updatedAt).toLocaleDateString()}</div>
                <div>Version: {note.version}</div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </motion.div>
  );
}
