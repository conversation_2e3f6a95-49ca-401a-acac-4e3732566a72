'use client';

import type React from 'react';
import { createContext, useContext, useState, useCallback } from 'react';

interface NodeEditContextType {
  editingNodeId: string | null;
  editingNodeData: any;
  editingNodeType: string | null;
  startEditing: (nodeId: string, nodeData: any) => void;
  stopEditing: () => void;
  isEditing: (nodeId: string) => boolean;
}

const NodeEditContext = createContext<NodeEditContextType | undefined>(undefined);

export function NodeEditProvider({ children }: { children: React.ReactNode }) {
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [editingNodeData, setEditingNodeData] = useState<any>(null);
  const [editingNodeType, setEditingNodeType] = useState<string | null>(null);

  const startEditing = useCallback((nodeId: string, nodeData: any) => {
    setEditingNodeId(nodeId);
    setEditingNodeData(nodeData);
    setEditingNodeType(nodeData.nodeType || null);
  }, []);

  const stopEditing = useCallback(() => {
    setEditingNodeId(null);
    setEditingNodeData(null);
    setEditingNodeType(null);
  }, []);

  const isEditing = useCallback(
    (nodeId: string) => {
      return editingNodeId === nodeId;
    },
    [editingNodeId]
  );

  return (
    <NodeEditContext.Provider
      value={{
        editingNodeId,
        editingNodeData,
        editingNodeType,
        startEditing,
        stopEditing,
        isEditing,
      }}
    >
      {children}
    </NodeEditContext.Provider>
  );
}

export function useNodeEdit() {
  const context = useContext(NodeEditContext);
  if (context === undefined) {
    throw new Error('useNodeEdit must be used within a NodeEditProvider');
  }
  return context;
}
