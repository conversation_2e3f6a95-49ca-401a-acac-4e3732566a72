'use client';

import React from 'react';
import Modal from '../Global/Modal';
interface StorageTierUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string;
  currentTierId: string;
  targetTierId: string;
}
export const StorageTierUpgradeModal: React.FC<StorageTierUpgradeModalProps> = ({
  isOpen,
  onClose,
  organizationId,
  currentTierId,
  targetTierId,
}) => {
  console.log('organizationId', organizationId);
  console.log('currentTierId', currentTierId);
  console.log('targetTierId', targetTierId);
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div></div>
    </Modal>
  );
};
