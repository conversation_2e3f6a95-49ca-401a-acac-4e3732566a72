import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { Subscription } from '@/models/Subscription';
import { SubscriptionService } from '@/services/Subscription.service';
import { SubscriptionCreationService } from '@/services/SubscriptionCreation.service';
import Razorpay from 'razorpay';
import crypto from 'crypto';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/subscriptions');

app.use('*', logger());

// Authentication middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    await connectDB();

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Auth middleware error:', error);
    return c.json({ error: 'Authentication failed' }, 401);
  }
  await next();
});

// Validation schemas
const createSubscriptionSchema = z.object({
  planId: z.enum(['free', 'basic', 'professional', 'enterprise']),
  billingCycle: z.enum(['monthly', 'yearly']),
});

const updateSubscriptionSchema = z.object({
  planId: z.enum(['free', 'basic', 'professional', 'enterprise']).optional(),
  billingCycle: z.enum(['monthly', 'yearly']).optional(),
  status: z.enum(['active', 'suspended', 'cancelled', 'past_due', 'trialing', 'paused']).optional(),
});

const paymentVerificationSchema = z.object({
  razorpay_payment_id: z.string(),
  razorpay_subscription_id: z.string(),
  razorpay_signature: z.string(),
});

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

// GET /api/subscriptions - Get current subscription
app.get('/', async c => {
  const user = c.get('user');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const subscription = await Subscription.findOne({
      organizationId: user.organizationId,
    }).populate('organizationId', 'name');

    if (!subscription) {
      // Create default free subscription using the service
      const newSubscription = await SubscriptionCreationService.createDefaultSubscription(
        user.organizationId
      );
      return c.json({ subscription: newSubscription });
    }

    return c.json({ subscription });
  } catch (error: any) {
    console.error('Get subscription error:', error);
    return c.json({ error: 'Failed to fetch subscription' }, 500);
  }
});

// GET /api/subscriptions/usage - Get usage summary
app.get('/usage', async c => {
  const user = c.get('user');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Mock usage data based on your requirements
    const mockUsage = {
      organizationId: user.organizationId,
      planId: 'free',
      metrics: {
        projects: 2, // You mentioned you created 2 projects
        users: 1,
        integrations: 0,
        automations: 0,
        apiCalls: 150,
      },
      limits: {
        projects: {
          current: 2,
          limit: 5,
          percentage: 40,
          isNearLimit: false,
          isAtLimit: false,
          isOverLimit: false,
        },
        users: {
          current: 1,
          limit: 3,
          percentage: 33,
          isNearLimit: false,
          isAtLimit: false,
          isOverLimit: false,
        },
        integrations: {
          current: 0,
          limit: 2,
          percentage: 0,
          isNearLimit: false,
          isAtLimit: false,
          isOverLimit: false,
        },
        automations: {
          current: 0,
          limit: 5,
          percentage: 0,
          isNearLimit: false,
          isAtLimit: false,
          isOverLimit: false,
        },
        apiCalls: {
          current: 150,
          limit: 1000,
          percentage: 15,
          isNearLimit: false,
          isAtLimit: false,
          isOverLimit: false,
        },
      },
      recommendations: {
        shouldUpgrade: false,
        recommendedPlan: null,
        reasons: [],
      },
      lastUpdated: new Date(),
    };

    return c.json({ usage: mockUsage });
  } catch (error: any) {
    console.error('Get usage error:', error);
    return c.json({ error: 'Failed to fetch usage data' }, 500);
  }
});

// POST /api/subscriptions/create - Create subscription order
app.post('/create', zValidator('json', createSubscriptionSchema), async c => {
  const user = c.get('user');
  const { planId, billingCycle } = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Validate plan (pricing calculated in transaction service)

    if (planId === 'free') {
      return c.json({ error: 'Cannot create payment for free plan' }, 400);
    }

    // Create Razorpay plan if it doesn't exist
    const planConfig = SubscriptionService.getPlanConfig(planId);
    if (!planConfig) {
      return c.json({ error: 'Invalid plan' }, 400);
    }

    // Create subscription in Razorpay
    const subscriptionData = {
      plan_id: `${planId}_${billingCycle}`,
      customer_notify: 1 as 0 | 1,
      quantity: 1,
      total_count: billingCycle === 'yearly' ? 1 : 12, // 1 year for yearly, 12 months for monthly
      notes: {
        organizationId: user.organizationId,
        planId,
        billingCycle,
      },
    };

    const razorpaySubscription = await razorpay.subscriptions.create(subscriptionData);

    return c.json({
      subscription: razorpaySubscription,
      user: {
        name: user.name,
        email: user.email,
      },
      organization: {
        id: user.organizationId,
      },
    });
  } catch (error: any) {
    console.error('Create subscription error:', error);
    return c.json({ error: error.message || 'Failed to create subscription' }, 500);
  }
});

// POST /api/subscriptions/verify - Verify payment
app.post('/verify', zValidator('json', paymentVerificationSchema), async c => {
  const user = c.get('user');
  const { razorpay_payment_id, razorpay_subscription_id, razorpay_signature } = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Verify signature
    const body = razorpay_payment_id + '|' + razorpay_subscription_id;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(body.toString())
      .digest('hex');

    if (expectedSignature !== razorpay_signature) {
      return c.json({ error: 'Invalid payment signature' }, 400);
    }

    // Get subscription details from Razorpay
    const razorpaySubscription = await razorpay.subscriptions.fetch(razorpay_subscription_id);
    const planId = razorpaySubscription.notes?.planId as string;
    const billingCycle = razorpaySubscription.notes?.billingCycle as 'monthly' | 'yearly';

    if (!planId || !billingCycle) {
      return c.json({ error: 'Invalid subscription data' }, 400);
    }

    // Update or create subscription in database
    const pricing = SubscriptionService.calculatePlanPrice(planId, billingCycle);
    const features = SubscriptionService.getPlanFeatures(planId);
    const limits = SubscriptionService.getPlanLimits(planId);

    const subscriptionUpdate = {
      plan: planId,
      status: 'active',
      subscriptionId: razorpay_subscription_id,
      billingCycle,
      amount: pricing.finalPrice,
      originalAmount: pricing.originalPrice,
      discountPercentage: pricing.discountPercentage,
      currentPeriodStart: new Date(),
      currentPeriodEnd: SubscriptionService.calculateNextBillingDate(new Date(), billingCycle),
      features,
      ...limits,
      lastPaymentDate: new Date(),
      nextPaymentDate: SubscriptionService.calculateNextBillingDate(new Date(), billingCycle),
      paymentMethod: 'razorpay',
    };

    const subscription = await Subscription.findOneAndUpdate(
      { organizationId: user.organizationId },
      subscriptionUpdate,
      { upsert: true, new: true }
    );

    return c.json({
      verified: true,
      subscription,
      message: 'Payment verified and subscription activated',
    });
  } catch (error: any) {
    console.error('Verify payment error:', error);
    return c.json({ error: error.message || 'Payment verification failed' }, 500);
  }
});

// PUT /api/subscriptions - Update subscription
app.put('/', zValidator('json', updateSubscriptionSchema), async c => {
  const user = c.get('user');
  const updateData = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const subscription = await Subscription.findOne({
      organizationId: user.organizationId,
    });

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404);
    }

    // Validate plan change if planId is provided
    if (updateData.planId && updateData.planId !== subscription.plan) {
      // Mock current usage for validation
      const currentUsage = {
        projects: 2,
        users: 1,
        integrations: 0,
        automations: 0,
        apiCalls: 150,
      };

      const validation = SubscriptionService.validatePlanChange(
        subscription.plan,
        updateData.planId,
        currentUsage
      );

      if (!validation.isValid) {
        return c.json(
          {
            error: 'Plan change not allowed',
            reasons: validation.errors,
          },
          400
        );
      }
    }

    // Update subscription
    Object.assign(subscription, updateData);

    if (updateData.planId) {
      const features = SubscriptionService.getPlanFeatures(updateData.planId);
      const limits = SubscriptionService.getPlanLimits(updateData.planId);
      Object.assign(subscription, { features, ...limits });
    }

    await subscription.save();

    return c.json({ subscription });
  } catch (error: any) {
    console.error('Update subscription error:', error);
    return c.json({ error: 'Failed to update subscription' }, 500);
  }
});

// POST /api/subscriptions/cancel - Cancel subscription
app.post('/cancel', async c => {
  const user = c.get('user');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const subscription = await Subscription.findOne({
      organizationId: user.organizationId,
    });

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404);
    }

    // Cancel in Razorpay if subscription ID exists
    if (subscription.subscriptionId) {
      await razorpay.subscriptions.cancel(subscription.subscriptionId, true);
    }

    // Update subscription status
    subscription.status = 'cancelled';
    subscription.plan = 'free';
    subscription.features = SubscriptionService.getPlanFeatures('free');
    Object.assign(subscription, SubscriptionService.getPlanLimits('free'));

    await subscription.save();

    return c.json({
      success: true,
      message: 'Subscription cancelled successfully',
    });
  } catch (error: any) {
    console.error('Cancel subscription error:', error);
    return c.json({ error: 'Failed to cancel subscription' }, 500);
  }
});

// POST /api/subscriptions/pause - Pause subscription
app.post('/pause', async c => {
  const user = c.get('user');
  const { reason } = await c.req.json();

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const subscription = await Subscription.findOne({
      organizationId: user.organizationId,
    });

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404);
    }

    // Pause in Razorpay if subscription ID exists
    if (subscription.subscriptionId) {
      await razorpay.subscriptions.pause(subscription.subscriptionId);
    }

    // Update subscription status
    subscription.status = 'paused';
    subscription.pausedAt = new Date();
    subscription.pauseReason = reason;

    await subscription.save();

    return c.json({
      success: true,
      message: 'Subscription paused successfully',
    });
  } catch (error: any) {
    console.error('Pause subscription error:', error);
    return c.json({ error: 'Failed to pause subscription' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
