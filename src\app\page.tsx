'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import Footer from '@/components/Home/footer';
import Hero from '@/components/Home/hero';
import Navbar from '@/components/Home/navbar';
import StatsSection from '@/components/Home/stats';
interface FallbackComponentProps {
  error?: Error;
  resetErrorBoundary?: () => void;
}

interface AnimatedContentProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
}

const FallbackComponent: React.FC<FallbackComponentProps> = ({ error }) => {
  return (
    <div className="overflow-hidden bg-background relative">
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]" />
      <Navbar />
      <div className="relative z-10">
        <Hero />
      </div>
      <div className="relative z-10">
        <StatsSection />
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500/10 border border-red-500/20 rounded-lg p-4 text-sm text-red-600 dark:text-red-400">
          Animations unavailable - displaying static version
        </div>
      )}
    </div>
  );
};

// Loading component
const LoadingContent: React.FC = () => {
  return (
    <div className="overflow-hidden bg-background relative">
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05] animate-pulse" />
      <div className="animate-pulse">
        <Navbar />
      </div>
      <div className="relative z-10 animate-pulse">
        <Hero />
      </div>
      <div className="relative z-10 animate-pulse">
        <StatsSection />
      </div>
      <div className="relative z-10 animate-pulse">
        <Footer />
      </div>
    </div>
  );
};

// Client-side wrapper component for framer-motion content
const AnimatedContent: React.FC<AnimatedContentProps> = ({ containerRef }) => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setIsReady(true);
  }, []);

  if (!isReady) {
    return <LoadingContent />;
  }

  return <AnimatedContentInner containerRef={containerRef} />;
};

// Separate component to handle framer-motion hooks
const AnimatedContentInner: React.FC<AnimatedContentProps> = ({ containerRef }) => {
  const [MotionDiv, setMotionDiv] = useState<any>(null);

  useEffect(() => {
    import('framer-motion')
      .then(({ motion }) => setMotionDiv(motion.div))
      .catch(() => setMotionDiv('div'));
  }, []);

  if (!MotionDiv) {
    return <LoadingContent />;
  }

  return (
    <MotionDiv
      ref={containerRef}
      className="overflow-hidden bg-background relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]" />
      <Navbar />
      <div className="relative z-10">
        <Hero />
      </div>
      <div className="relative z-10">
        <StatsSection />
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
    </MotionDiv>
  );
};

// Static content component
const StaticContent: React.FC = () => {
  return (
    <div className="overflow-hidden bg-background relative">
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]" />
      <Navbar />
      <div className="relative z-10">
        <Hero />
      </div>
      <div className="relative z-10">
        <StatsSection />
      </div>
      <div className="relative z-10">
        <Footer />
      </div>
    </div>
  );
};

// Error boundary fallback
const ErrorFallback: React.FC<FallbackComponentProps> = ({ error, resetErrorBoundary }) => {
  return <FallbackComponent error={error} resetErrorBoundary={resetErrorBoundary} />;
};

// Main Home component
const Home: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isClient, setIsClient] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasFramerMotion, setHasFramerMotion] = useState<boolean>(false);

  const checkFramerMotion = useCallback(async () => {
    try {
      await import('framer-motion');
      setHasFramerMotion(true);
    } catch (error) {
      setHasFramerMotion(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    setIsClient(true);
    checkFramerMotion();
  }, [checkFramerMotion]);

  // Return static content during SSR, loading, or if framer-motion is unavailable
  if (!isClient || isLoading) {
    return <LoadingContent />;
  }

  if (!hasFramerMotion) {
    return <StaticContent />;
  }

  // Render animated content only on client-side with framer-motion available
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <AnimatedContent containerRef={containerRef} />
    </ErrorBoundary>
  );
};

export default Home;
