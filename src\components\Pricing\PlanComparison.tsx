'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
// Removed unused Tabs import
import {
  Check,
  X,
  Info,
  Crown,
  Zap,
  Shield,
  HardDrive,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { PRICING_PLANS, FEATURE_CATEGORIES } from '@/constant/PricingPlans';
import { SubscriptionService } from '@/services/Subscription.service';

interface PlanComparisonProps {
  onPlanSelect?: (planId: string, billingCycle: 'monthly' | 'yearly') => void;
  currentPlan?: string;
}

export const PlanComparison: React.FC<PlanComparisonProps> = ({
  onPlanSelect,
  currentPlan = 'free',
}) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['CORE']));

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <HardDrive className="h-5 w-5 text-gray-500" />;
      case 'basic':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-5 w-5 text-orange-500" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  // Organize features by category
  const organizeFeaturesByCategory = () => {
    const categorizedFeatures: Record<string, any[]> = {
      CORE: [],
      COLLABORATION: [],
      ANALYTICS: [],
      INTEGRATIONS: [],
      STORAGE: [],
      SUPPORT: [],
      SECURITY: [],
      CUSTOMIZATION: [],
    };

    // Add plan limits as features
    if (!categorizedFeatures.CORE.find(f => f.name === 'Projects')) {
      categorizedFeatures.CORE.push({
        name: 'Projects',
        type: 'limit',
        values: PRICING_PLANS.map(p => p.limits.projects),
      });
    }
    if (!categorizedFeatures.CORE.find(f => f.name === 'Team Members')) {
      categorizedFeatures.CORE.push({
        name: 'Team Members',
        type: 'limit',
        values: PRICING_PLANS.map(p => p.limits.users),
      });
    }
    if (!categorizedFeatures.INTEGRATIONS.find(f => f.name === 'Integrations')) {
      categorizedFeatures.INTEGRATIONS.push({
        name: 'Integrations',
        type: 'limit',
        values: PRICING_PLANS.map(p => p.limits.integrations),
      });
    }

    // Categorize features from plans
    const allFeatures = new Set<string>();
    PRICING_PLANS.forEach(plan => {
      plan.features.forEach(feature => allFeatures.add(feature.name));
    });

    Array.from(allFeatures).forEach(featureName => {
      let category = 'CORE';

      if (
        featureName.toLowerCase().includes('team') ||
        featureName.toLowerCase().includes('collaboration') ||
        featureName.toLowerCase().includes('sharing')
      ) {
        category = 'COLLABORATION';
      } else if (
        featureName.toLowerCase().includes('report') ||
        featureName.toLowerCase().includes('analytic') ||
        featureName.toLowerCase().includes('export')
      ) {
        category = 'ANALYTICS';
      } else if (
        featureName.toLowerCase().includes('integration') ||
        featureName.toLowerCase().includes('api') ||
        featureName.toLowerCase().includes('automation')
      ) {
        category = 'INTEGRATIONS';
      } else if (
        featureName.toLowerCase().includes('storage') ||
        featureName.toLowerCase().includes('file')
      ) {
        category = 'STORAGE';
      } else if (
        featureName.toLowerCase().includes('support') ||
        featureName.toLowerCase().includes('phone') ||
        featureName.toLowerCase().includes('email')
      ) {
        category = 'SUPPORT';
      } else if (
        featureName.toLowerCase().includes('security') ||
        featureName.toLowerCase().includes('sso') ||
        featureName.toLowerCase().includes('audit')
      ) {
        category = 'SECURITY';
      } else if (
        featureName.toLowerCase().includes('brand') ||
        featureName.toLowerCase().includes('custom') ||
        featureName.toLowerCase().includes('white')
      ) {
        category = 'CUSTOMIZATION';
      }

      categorizedFeatures[category].push({
        name: featureName,
        type: 'feature',
        values: PRICING_PLANS.map(plan => {
          const feature = plan.features.find(f => f.name === featureName);
          return feature ? feature.included : false;
        }),
      });
    });

    return categorizedFeatures;
  };

  const categorizedFeatures = organizeFeaturesByCategory();

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Compare All Plans</h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
          Find the perfect plan for your needs with detailed feature comparison
        </p>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center gap-4">
          <span
            className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
          >
            Monthly
          </span>
          <Switch
            checked={billingCycle === 'yearly'}
            onCheckedChange={checked => setBillingCycle(checked ? 'yearly' : 'monthly')}
          />
          <span
            className={`text-sm font-medium ${billingCycle === 'yearly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
          >
            Annual
          </span>
          {billingCycle === 'yearly' && (
            <Badge variant="secondary" className="ml-2">
              Save 17%
            </Badge>
          )}
        </div>
      </div>

      {/* Comparison Table */}
      <Card className="overflow-hidden">
        <CardHeader className="bg-gray-50 dark:bg-gray-800">
          <div className="grid grid-cols-5 gap-4">
            <div className="col-span-1">
              <CardTitle className="text-lg">Features</CardTitle>
            </div>
            {PRICING_PLANS.map(plan => {
              const pricing = SubscriptionService.calculatePlanPrice(plan.id, billingCycle);
              const isCurrentPlan = currentPlan === plan.id;

              return (
                <div key={plan.id} className="col-span-1 text-center">
                  <div className="flex flex-col items-center gap-2">
                    {getPlanIcon(plan.id)}
                    <div className="font-semibold">{plan.name}</div>
                    {plan.popular && (
                      <Badge className="bg-purple-500 text-white text-xs">Most Popular</Badge>
                    )}
                    {isCurrentPlan && (
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200 text-xs"
                      >
                        Current
                      </Badge>
                    )}
                    <div className="text-lg font-bold">
                      {plan.price.monthly === 0 ? (
                        'Free'
                      ) : (
                        <>
                          {SubscriptionService.formatPrice(pricing.finalPrice)}
                          <span className="text-xs font-normal text-gray-500">
                            /{billingCycle === 'yearly' ? 'year' : 'month'}
                          </span>
                        </>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant={isCurrentPlan ? 'outline' : 'default'}
                      disabled={isCurrentPlan}
                      onClick={() => onPlanSelect?.(plan.id, billingCycle)}
                      className="w-full"
                    >
                      {isCurrentPlan ? 'Current Plan' : plan.ctaText}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {Object.entries(categorizedFeatures).map(([categoryKey, features]) => {
            if (features.length === 0) return null;

            const categoryName = FEATURE_CATEGORIES[categoryKey as keyof typeof FEATURE_CATEGORIES];
            const isExpanded = expandedCategories.has(categoryKey);

            return (
              <div key={categoryKey} className="border-b border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => toggleCategory(categoryKey)}
                  className="w-full px-6 py-4 flex items-center justify-between bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <h3 className="font-semibold text-left">{categoryName}</h3>
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </button>

                {isExpanded && (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {features.map((feature, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-5 gap-4 px-6 py-3 hover:bg-gray-50 dark:hover:bg-gray-800"
                      >
                        <div className="col-span-1 flex items-center gap-2">
                          <span className="font-medium text-sm">{feature.name}</span>
                          {feature.tooltip && <Info className="h-3 w-3 text-gray-400" />}
                        </div>
                        {feature.values.map((value: any, planIndex: number) => (
                          <div
                            key={planIndex}
                            className="col-span-1 text-center flex items-center justify-center"
                          >
                            {feature.type === 'limit' ? (
                              <span className="text-sm font-medium">
                                {typeof value === 'string'
                                  ? value
                                  : value === 'unlimited'
                                    ? '∞'
                                    : value}
                              </span>
                            ) : (
                              <div className={`${value ? 'text-green-500' : 'text-gray-300'}`}>
                                {value ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Bottom CTA */}
      <div className="text-center mt-8">
        <p className="text-sm text-gray-500 mb-4">
          Still have questions? Our team is here to help you choose the right plan.
        </p>
        <Button variant="outline">Contact Sales</Button>
      </div>
    </div>
  );
};
