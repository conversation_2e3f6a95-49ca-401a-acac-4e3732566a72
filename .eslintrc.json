{"extends": ["next/core-web-vitals", "next/typescript", "eslint:recommended"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "warn", "no-unused-vars": "off", "react/react-in-jsx-scope": "off", "react/no-unescaped-entities": "off", "no-case-declarations": "off", "@typescript-eslint/no-unused-vars": ["warn", {"varsIgnorePattern": "^_|Props$|Interface$", "argsIgnorePattern": "^_", "destructuredArrayIgnorePattern": "^_", "args": "none", "caughtErrors": "none", "ignoreRestSiblings": true}]}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "env": {"browser": true, "node": true, "es2021": true}, "settings": {"react": {"version": "detect"}}}