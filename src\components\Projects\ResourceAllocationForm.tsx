import { useToast } from '@/hooks/use-toast';
import { IResourceAllocation } from '@/models/ResourceAllocation';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import * as yup from 'yup';
import Modal from '../Global/Modal';
import {
  Loader2,
  Plus,
  Trash2,
  Users,
  Calendar,
  Clock,
  DollarSign,
  User,
  Briefcase,
  Target,
  FileText,
  Settings,
} from 'lucide-react';
import { ScrollArea } from '../ui/scroll-area';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import ReactSelect, { Option } from '../Global/ReactSelect';
import { Input } from '../ui/input';
import { CustomSkillsSelect } from './CustomSkillsSelect';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { useProjectStore } from '@/stores/projectsStore';
import { OrganizationService } from '@/services/Organization.service';

const availabilitySchema = yup.object({
  dayOfWeek: yup.number().min(0).max(6).required('Day of week is required'),
  startTime: yup
    .string()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format')
    .required('Start time is required'),
  endTime: yup
    .string()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format')
    .required('End time is required'),
  isAvailable: yup.boolean().default(true),
});

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' },
];

const resourceAllocationSchema = yup.object({
  userId: yup.string().required('User is required'),
  projectId: yup.string().required('Project is required'),
  role: yup.string().required('Role is required').max(100, 'Role name too long'),
  capacity: yup
    .number()
    .min(0, 'Capacity cannot be negative')
    .max(100, 'Capacity cannot exceed 100%')
    .required('Capacity is required'),
  startDate: yup.date().required('Start date is required'),
  endDate: yup
    .date()
    .required('End date is required')
    .min(yup.ref('startDate'), 'End date must be after start date'),
  skills: yup.array().of(yup.string()).max(20, 'Too many skills'),
  hourlyRate: yup.number().min(0, 'Hourly rate cannot be negative').default(0),
  availabilitySchedule: yup
    .array()
    .of(availabilitySchema)
    .max(7, 'Cannot have more than 7 availability entries'),
  status: yup
    .string()
    .oneOf(['active', 'inactive', 'completed', 'cancelled'], 'Invalid status')
    .default('active'),
  notes: yup.string().max(500, 'Notes too long'),
});

type ResourceAllocationFormType = yup.InferType<typeof resourceAllocationSchema>;

export const ResourceAllocationForm: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  allocation?: IResourceAllocation;
  userId?: string;
  projectId?: string;
  onSave: () => void;
}> = ({ isOpen, onClose, allocation, userId, projectId, onSave }) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const form = useForm<ResourceAllocationFormType>({
    resolver: yupResolver(resourceAllocationSchema),
    defaultValues: {
      userId: allocation?.userId.toString() || userId || '',
      projectId: allocation?.projectId.toString() || projectId || '',
      role: allocation?.role || '',
      capacity: allocation?.capacity || 50,
      startDate: allocation?.startDate ? new Date(allocation.startDate) : new Date(),
      endDate: allocation?.endDate ? new Date(allocation.endDate) : new Date(),
      skills: allocation?.skills || [],
      hourlyRate: allocation?.hourlyRate || 0,
      availabilitySchedule: allocation?.availabilitySchedule || [],
      status: allocation?.status || 'active',
      notes: allocation?.notes || '',
    },
  });

  const {
    fields: schedule,
    append: addSchedule,
    remove: removeSchedule,
  } = useFieldArray({
    control: form.control,
    name: 'availabilitySchedule',
  });

  const { data: organizations } = useQuery({
    queryKey: ['organizations'],
    queryFn: async () => {
      try {
        return await OrganizationService.getOrganizations();
      } catch (error: any) {
        toast({
          title: 'Failed to fetch organization data',
          description: error.message,
          variant: 'destructive',
        });
        throw error;
      }
    },
  });

  const users = useMemo(
    () => organizations?.members?.map((member: any) => member.userId) || [],
    [organizations]
  );
  const { projects } = useProjectStore();

  const saveAllocationMutation = useMutation({
    mutationFn: async (data: ResourceAllocationFormType) => {
      const endpoint = allocation
        ? `/api/resource-allocation/${allocation.allocationId}`
        : '/api/resource-allocation';

      const response = await fetch(endpoint, {
        method: allocation ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to save allocation');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({ title: `Allocation ${allocation ? 'updated' : 'created'} successfully!` });
      queryClient.invalidateQueries({ queryKey: ['resource-allocations'] });
      onSave();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to save allocation',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: ResourceAllocationFormType) => {
    saveAllocationMutation.mutate(data);
  };

  const addScheduleEntry = () => {
    addSchedule({
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="h-full flex flex-col min-h-[80vh]">
        {/* Header */}
        <div className="flex items-center justify-between pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold theme-text-primary ">
                {allocation ? 'Edit Resource Allocation' : 'Create Resource Allocation'}
              </h2>
              <p className="text-sm theme-text-secondary">
                {allocation
                  ? 'Update resource assignment details'
                  : 'Assign team member to project'}
              </p>
            </div>
          </div>
          {allocation && (
            <Badge className={getStatusColor(allocation.status)}>{allocation.status}</Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
                  <Tabs defaultValue="basic" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic" className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Basic Info
                      </TabsTrigger>
                      <TabsTrigger value="schedule" className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Schedule
                      </TabsTrigger>
                      <TabsTrigger value="details" className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Details
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-6 mt-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Briefcase className="h-5 w-5" />
                            Assignment Details
                          </CardTitle>
                          <CardDescription>
                            Define who will work on which project and their role
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="userId"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="flex items-center gap-2">
                                    <User className="h-4 w-4" />
                                    Team Member *
                                  </FormLabel>
                                  <FormControl>
                                    <ReactSelect
                                      options={
                                        users?.map((user: any) => ({
                                          value: user._id,
                                          label: user.name,
                                        })) || []
                                      }
                                      value={
                                        users?.find((user: any) => user._id === field.value)
                                          ? {
                                              value: field.value,
                                              label: users.find(
                                                (user: any) => user._id === field.value
                                              ).name,
                                            }
                                          : null
                                      }
                                      onChange={option => {
                                        if (option) {
                                          field.onChange((option as Option).value);
                                        }
                                      }}
                                      placeholder="Select team member"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="projectId"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="flex items-center gap-2">
                                    <Briefcase className="h-4 w-4" />
                                    Project *
                                  </FormLabel>
                                  <FormControl>
                                    <ReactSelect
                                      options={
                                        projects?.map((project: any) => ({
                                          value: project._id,
                                          label: project.name,
                                        })) || []
                                      }
                                      value={
                                        projects?.find(
                                          (project: any) => project._id === field.value
                                        )
                                          ? {
                                              value: field.value,
                                              label: projects.find(
                                                (project: any) => project._id === field.value
                                              ).name,
                                            }
                                          : null
                                      }
                                      onChange={option => {
                                        if (option) {
                                          field.onChange((option as Option).value);
                                        }
                                      }}
                                      placeholder="Select project"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="role"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Role *</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder="e.g., Frontend Developer, Project Manager"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="capacity"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="flex items-center gap-2">
                                    <Target className="h-4 w-4" />
                                    Capacity (%) *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      {...field}
                                      onChange={e => field.onChange(parseInt(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Percentage of their time allocated to this project
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="startDate"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4" />
                                    Start Date *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      type="date"
                                      value={field.value?.toISOString().split('T')[0]}
                                      onChange={e => field.onChange(new Date(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    When the resource starts working on this project
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="endDate"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4" />
                                    End Date *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      type="date"
                                      value={field.value?.toISOString().split('T')[0]}
                                      onChange={e => field.onChange(new Date(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription>When the allocation ends</FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="schedule" className="space-y-6 mt-6">
                      <Card>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Availability Schedule
                              </CardTitle>
                              <CardDescription>
                                Define when the resource is available to work
                              </CardDescription>
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={addScheduleEntry}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Schedule
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {schedule.length === 0 ? (
                              <div className="text-center py-8 text-gray-500">
                                <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                                <p>No schedule entries yet</p>
                                <p className="text-sm">
                                  Click "Add Schedule" to create availability slots
                                </p>
                              </div>
                            ) : (
                              schedule.map((item, index) => (
                                <Card key={item.id} className="border-l-4 border-l-blue-500">
                                  <CardContent className="p-4">
                                    <div className="flex items-center gap-4 flex-wrap">
                                      <FormField
                                        control={form.control}
                                        name={`availabilitySchedule.${index}.dayOfWeek`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>Day</FormLabel>
                                            <FormControl>
                                              <ReactSelect
                                                options={DAYS_OF_WEEK.map(day => ({
                                                  value: day.value.toString(),
                                                  label: day.label,
                                                }))}
                                                value={
                                                  DAYS_OF_WEEK.find(
                                                    day => day.value === field.value
                                                  )
                                                    ? {
                                                        value: field.value.toString(),
                                                        label:
                                                          DAYS_OF_WEEK.find(
                                                            day => day.value === field.value
                                                          )?.label || '',
                                                      }
                                                    : null
                                                }
                                                onChange={option => {
                                                  if (option) {
                                                    field.onChange(
                                                      parseInt((option as Option).value.toString())
                                                    );
                                                  }
                                                }}
                                                placeholder="Select day"
                                                className="w-36"
                                              />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />
                                      <FormField
                                        control={form.control}
                                        name={`availabilitySchedule.${index}.startTime`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>Start Time</FormLabel>
                                            <FormControl>
                                              <Input type="time" {...field} className="w-32" />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />
                                      <FormField
                                        control={form.control}
                                        name={`availabilitySchedule.${index}.endTime`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>End Time</FormLabel>
                                            <FormControl>
                                              <Input type="time" {...field} className="w-32" />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />
                                      <FormField
                                        control={form.control}
                                        name={`availabilitySchedule.${index}.isAvailable`}
                                        render={({ field }) => (
                                          <FormItem className="flex items-center space-x-2 space-y-0">
                                            <FormControl>
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                            </FormControl>
                                            <FormLabel>Available</FormLabel>
                                          </FormItem>
                                        )}
                                      />
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => removeSchedule(index)}
                                        className="text-red-600 hover:text-red-700"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="details" className="space-y-6 mt-6">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <DollarSign className="h-5 w-5" />
                              Financial Details
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <FormField
                              control={form.control}
                              name="hourlyRate"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Hourly Rate</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min="0"
                                      step="0.01"
                                      {...field}
                                      onChange={e => field.onChange(parseFloat(e.target.value))}
                                      placeholder="0.00"
                                    />
                                  </FormControl>
                                  <FormDescription>Cost per hour for this resource</FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <User className="h-5 w-5" />
                              Skills & Expertise
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <FormField
                              control={form.control}
                              name="skills"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Skills</FormLabel>
                                  <FormControl>
                                    <CustomSkillsSelect
                                      selectedSkills={
                                        (field.value || []).filter(Boolean) as string[]
                                      }
                                      onChange={field.onChange}
                                      placeholder="Select or create skills"
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Relevant skills for this allocation
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </CardContent>
                        </Card>
                      </div>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            Additional Information
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <FormField
                            control={form.control}
                            name="notes"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Notes</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="Additional notes or comments" />
                                </FormControl>
                                <FormDescription>
                                  Any additional information about this allocation
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </form>
              </Form>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 mt-1">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={saveAllocationMutation.isPending}
            className="min-w-[120px]"
          >
            {saveAllocationMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {allocation ? 'Update' : 'Create'} Allocation
          </Button>
        </div>
      </div>
    </Modal>
  );
};
