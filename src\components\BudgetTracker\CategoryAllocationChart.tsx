import React from 'react';
import { CURRENCY_OPTIONS } from '@/constant/Currency';
import { IBudgetCategory } from '@/models/Budget';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

const CHART_COLORS = [
  '#0088FE',
  '#00C49F',
  '#FFBB28',
  '#FF8042',
  '#8884D8',
  '#82CA9D',
  '#FFC658',
  '#FF7C7C',
  '#8DD1E1',
  '#D084D0',
];

export const CategoryAllocationChart: React.FC<{
  categories: IBudgetCategory[];
  currency: string;
}> = ({ categories, currency }) => {
  const currencySymbol = CURRENCY_OPTIONS.find(c => c.value === currency)?.symbol || '$';

  const chartData = categories.map((category, index) => ({
    name: category.name,
    allocated: category.allocatedAmount,
    spent: category.spentAmount,
    utilization:
      category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0,
    color: CHART_COLORS[index % CHART_COLORS.length],
  }));

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Budget Allocation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsPieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="allocated"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value: number) => [
                  `${currencySymbol}${value.toLocaleString()}`,
                  'Allocated',
                ]}
              />
            </RechartsPieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Category Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value: number) => `${currencySymbol}${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="allocated" fill="#8884d8" name="Allocated" />
              <Bar dataKey="spent" fill="#82ca9d" name="Spent" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};
