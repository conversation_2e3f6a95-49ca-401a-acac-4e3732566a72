'use client';

import { memo, useState } from 'react';
import { Handle, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { ListOrdered, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';

interface ProcessNodeData {
  title: string;
  steps: Array<{ number: number; description: string }>;
  currentStep: number;
}

export const ProcessNode = memo(({ id, data, selected }: NodeProps<ProcessNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const handleDoubleClick = () => {
    startEditing(id, {
      ...data,
      nodeType: 'process',
    });
  };

  return (
    <Card
      className={`min-w-[250px] max-w-[350px] bg-gradient-to-br from-cyan-50 to-blue-50 border-cyan-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-cyan-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-cyan-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <ListOrdered className="h-5 w-5 text-cyan-600" />
            <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          </div>
          {isHovered && (
            <Edit3
              className="h-3 w-3 text-gray-400 cursor-pointer hover:text-gray-600"
              onClick={handleDoubleClick}
            />
          )}
        </div>

        <div className="bg-white/70 rounded-md p-3 border border-cyan-100">
          <div className="space-y-2">
            {data.steps.map(step => {
              const isCurrentStep = step.number === data.currentStep;
              const isPastStep = step.number < data.currentStep;

              return (
                <div
                  key={step.number}
                  className={`flex items-start p-2 rounded ${
                    isCurrentStep ? 'bg-cyan-100' : isPastStep ? 'bg-gray-100' : ''
                  }`}
                >
                  <div
                    className={`flex items-center justify-center w-6 h-6 rounded-full mr-2 flex-shrink-0 ${
                      isCurrentStep
                        ? 'bg-cyan-500 text-white'
                        : isPastStep
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 text-gray-700'
                    }`}
                  >
                    <span className="text-xs font-medium">{step.number}</span>
                  </div>
                  <span
                    className={`text-xs ${
                      isCurrentStep
                        ? 'text-cyan-800 font-medium'
                        : isPastStep
                          ? 'text-gray-500'
                          : 'text-gray-700'
                    }`}
                  >
                    {step.description}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-cyan-500 border-2 border-white"
      />
    </Card>
  );
});

ProcessNode.displayName = 'ProcessNode';
