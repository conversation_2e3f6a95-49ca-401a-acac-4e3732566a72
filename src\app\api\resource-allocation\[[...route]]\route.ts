import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { zValidator } from '@hono/zod-validator';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { ResourceAllocation } from '@/models/ResourceAllocation';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import { NotificationService } from '@/services/Notification.service';
import { z } from 'zod';

type Variables = {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/resource-allocation');

app.use('*', logger());

// Auth middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const userData = {
      id: session.user.id || '',
      name: session.user.name || '',
      email: session.user.email || '',
      image: session.user.image || '',
      organizationId: session.user.organizationId || '',
    };
    c.set('user', userData);
    await next();
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Validation schemas
const availabilityScheduleSchema = z.object({
  dayOfWeek: z.number().min(0).max(6),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  isAvailable: z.boolean().default(true),
});

const workloadTrackingSchema = z.object({
  currentUtilization: z.number().min(0).max(100).default(0),
  allocatedHours: z.number().min(0).default(0),
  actualHours: z.number().min(0).default(0),
  lastUpdated: z.string().datetime().optional(),
});

const resourceAllocationSchema = z.object({
  userId: z.string(),
  projectId: z.string(),
  role: z.string().min(1).max(100),
  capacity: z.number().min(0).max(100),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  skills: z.array(z.string()).max(20).optional(),
  hourlyRate: z.number().min(0).default(0),
  availabilitySchedule: z.array(availabilityScheduleSchema).optional(),
  workloadTracking: workloadTrackingSchema.optional(),
  status: z.enum(['active', 'inactive', 'completed', 'cancelled']).default('active'),
  notes: z.string().max(500).optional(),
});

const updateResourceAllocationSchema = resourceAllocationSchema.partial();

// Create resource allocation
app.post('/', zValidator('json', resourceAllocationSchema), async c => {
  const user = c.get('user');
  const allocationData = c.req.valid('json');

  try {
    await connectDB();

    // Validate start date is before end date
    if (new Date(allocationData.startDate) >= new Date(allocationData.endDate)) {
      return c.json({ error: 'End date must be after start date' }, 400);
    }

    // Check if user exists
    const targetUser = await User.findOne({
      _id: allocationData.userId,
      organizationId: user.organizationId,
    });

    if (!targetUser) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Check if project exists
    const project = await Project.findOne({
      _id: allocationData.projectId,
      organizationId: user.organizationId,
    });

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    // Check for allocation conflicts
    const conflicts = await checkAllocationConflicts(
      allocationData.userId,
      allocationData.startDate,
      allocationData.endDate,
      allocationData.capacity,
      null,
      user.organizationId
    );

    if (conflicts.length > 0) {
      return c.json(
        {
          error: 'Allocation conflicts detected',
          conflicts,
        },
        409
      );
    }

    // Create allocation
    const allocation = new ResourceAllocation({
      ...allocationData,
      organizationId: user.organizationId,
      createdBy: user.id,
      workloadTracking: {
        currentUtilization: 0,
        allocatedHours: 0,
        actualHours: 0,
        lastUpdated: new Date(),
      },
    });

    await allocation.save();

    // Send notification to assigned user
    try {
      await NotificationService.createNotification({
        type: 'success',
        title: 'New Resource Allocation',
        description: `You have been allocated to project ${project.name} as ${allocationData.role}`,
        userId: allocationData.userId,
        metadata: {
          projectId: allocationData.projectId,
          allocationId: allocation.allocationId,
          role: allocationData.role,
          capacity: allocationData.capacity,
        },
      });
    } catch (notificationError) {
      console.error('Failed to send allocation notification:', notificationError);
    }

    return c.json({ success: true, allocation });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get allocations for a specific project
app.get('/project/:projectId', async c => {
  const user = c.get('user');
  const projectId = c.req.param('projectId');

  try {
    await connectDB();

    // Verify project exists and user has access
    const project = await Project.findOne({
      _id: projectId,
      organizationId: user.organizationId,
    });

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    const allocations = await ResourceAllocation.find({
      projectId,
      organizationId: user.organizationId,
    })
      .populate('userId', 'name email image skills role')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    return c.json({ allocations });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update resource allocation
app.put('/:id', zValidator('json', updateResourceAllocationSchema), async c => {
  const user = c.get('user');
  const allocationId = c.req.param('id');
  const updateData = c.req.valid('json');

  try {
    await connectDB();

    const allocation = await ResourceAllocation.findOne({
      allocationId,
      organizationId: user.organizationId,
    });

    if (!allocation) {
      return c.json({ error: 'Allocation not found' }, 404);
    }

    // Validate date range if being updated
    if (updateData.startDate && updateData.endDate) {
      if (new Date(updateData.startDate) >= new Date(updateData.endDate)) {
        return c.json({ error: 'End date must be after start date' }, 400);
      }
    }

    // Check for conflicts if capacity, dates, or user are being updated
    if (
      updateData.capacity !== undefined ||
      updateData.startDate ||
      updateData.endDate ||
      updateData.userId
    ) {
      const conflicts = await checkAllocationConflicts(
        updateData.userId || allocation.userId.toString(),
        updateData.startDate || allocation.startDate.toISOString(),
        updateData.endDate || allocation.endDate.toISOString(),
        updateData.capacity !== undefined ? updateData.capacity : allocation.capacity,
        allocationId,
        user.organizationId
      );

      if (conflicts.length > 0) {
        return c.json(
          {
            error: 'Allocation conflicts detected',
            conflicts,
          },
          409
        );
      }
    }

    // Update allocation
    Object.assign(allocation, updateData);
    await allocation.save();

    return c.json({ success: true, allocation });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get team capacity analysis
app.get('/capacity', async c => {
  const user = c.get('user');
  const { startDate, endDate, userId } = c.req.query();

  try {
    await connectDB();

    // Build query for allocations
    const query: any = {
      organizationId: user.organizationId,
      status: 'active',
    };

    if (startDate && endDate) {
      query.$or = [
        {
          startDate: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) },
        },
        {
          endDate: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) },
        },
        {
          $and: [
            { startDate: { $lte: new Date(startDate as string) } },
            { endDate: { $gte: new Date(endDate as string) } },
          ],
        },
      ];
    }

    if (userId) {
      query.userId = userId;
    }

    const allocations = await ResourceAllocation.find(query)
      .populate('userId', 'name email image skills role')
      .populate('projectId', 'name');

    // Group allocations by user
    const userCapacityMap = new Map();

    allocations.forEach(allocation => {
      const userId = allocation.userId._id.toString();

      if (!userCapacityMap.has(userId)) {
        userCapacityMap.set(userId, {
          user: allocation.userId,
          totalCapacity: 0,
          allocations: [],
          isOverAllocated: false,
          conflicts: [],
        });
      }

      const userCapacity = userCapacityMap.get(userId);
      userCapacity.totalCapacity += allocation.capacity;
      userCapacity.allocations.push({
        allocationId: allocation.allocationId,
        projectName: allocation.projectId.name,
        role: allocation.role,
        capacity: allocation.capacity,
        startDate: allocation.startDate,
        endDate: allocation.endDate,
        workloadTracking: allocation.workloadTracking,
      });

      if (userCapacity.totalCapacity > 100) {
        userCapacity.isOverAllocated = true;
      }
    });

    const capacityAnalysis = Array.from(userCapacityMap.values());

    // Calculate organization-wide metrics
    const totalUsers = capacityAnalysis.length;
    const overAllocatedUsers = capacityAnalysis.filter(u => u.isOverAllocated).length;
    const averageCapacity =
      totalUsers > 0
        ? capacityAnalysis.reduce((sum, u) => sum + u.totalCapacity, 0) / totalUsers
        : 0;

    return c.json({
      summary: {
        totalUsers,
        overAllocatedUsers,
        averageCapacity: Math.round(averageCapacity * 100) / 100,
        utilizationRate: Math.min(100, averageCapacity),
      },
      userCapacities: capacityAnalysis,
      conflicts: await getOrganizationConflicts(user.organizationId),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get allocation by ID
app.get('/:id', async c => {
  const user = c.get('user');
  const allocationId = c.req.param('id');

  try {
    await connectDB();

    const allocation = await ResourceAllocation.findOne({
      allocationId,
      organizationId: user.organizationId,
    })
      .populate('userId', 'name email image skills role')
      .populate('projectId', 'name')
      .populate('createdBy', 'name email');

    if (!allocation) {
      return c.json({ error: 'Allocation not found' }, 404);
    }

    return c.json({ allocation });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete allocation
app.delete('/:id', async c => {
  const user = c.get('user');
  const allocationId = c.req.param('id');

  try {
    await connectDB();

    const allocation = await ResourceAllocation.findOne({
      allocationId,
      organizationId: user.organizationId,
    });

    if (!allocation) {
      return c.json({ error: 'Allocation not found' }, 404);
    }

    await ResourceAllocation.findByIdAndDelete(allocation._id);

    return c.json({ success: true });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get resource utilization reports
app.get('/reports/utilization', async c => {
  const user = c.get('user');
  const { period = 'month', startDate, endDate } = c.req.query();

  try {
    await connectDB();

    const query: any = {
      organizationId: user.organizationId,
      status: 'active',
    };

    // Set date range based on period or custom dates
    let dateRange = {};
    if (startDate && endDate) {
      dateRange = {
        $or: [
          { startDate: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) } },
          { endDate: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) } },
          {
            $and: [
              { startDate: { $lte: new Date(startDate as string) } },
              { endDate: { $gte: new Date(endDate as string) } },
            ],
          },
        ],
      };
    } else {
      const now = new Date();
      const periodStart = new Date();

      switch (period) {
        case 'week':
          periodStart.setDate(now.getDate() - 7);
          break;
        case 'month':
          periodStart.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          periodStart.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          periodStart.setFullYear(now.getFullYear() - 1);
          break;
      }

      dateRange = {
        $or: [
          { startDate: { $gte: periodStart, $lte: now } },
          { endDate: { $gte: periodStart, $lte: now } },
          {
            $and: [{ startDate: { $lte: periodStart } }, { endDate: { $gte: now } }],
          },
        ],
      };
    }

    Object.assign(query, dateRange);

    const allocations = await ResourceAllocation.find(query)
      .populate('userId', 'name email skills role')
      .populate('projectId', 'name');

    // Generate utilization report
    const utilizationReport = generateUtilizationReport(allocations);

    return c.json({ report: utilizationReport });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get availability calendar
app.get('/availability/:userId', async c => {
  const user = c.get('user');
  const userId = c.req.param('userId');
  const { startDate, endDate } = c.req.query();

  try {
    await connectDB();

    // Get user's allocations
    const allocations = await ResourceAllocation.find({
      userId,
      organizationId: user.organizationId,
      status: 'active',
      startDate: { $lte: new Date(endDate as string) },
      endDate: { $gte: new Date(startDate as string) },
    }).populate('projectId', 'name');

    // Build availability calendar
    const calendar = buildAvailabilityCalendar(
      allocations,
      new Date(startDate as string),
      new Date(endDate as string)
    );

    return c.json({ calendar });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Helper function to check allocation conflicts
async function checkAllocationConflicts(
  userId: string,
  startDate: string,
  endDate: string,
  capacity: number,
  excludeAllocationId: string | null,
  organizationId: string
): Promise<any[]> {
  const query: any = {
    userId,
    organizationId,
    status: 'active',
    $or: [
      {
        startDate: { $gte: new Date(startDate), $lte: new Date(endDate) },
      },
      {
        endDate: { $gte: new Date(startDate), $lte: new Date(endDate) },
      },
      {
        $and: [
          { startDate: { $lte: new Date(startDate) } },
          { endDate: { $gte: new Date(endDate) } },
        ],
      },
    ],
  };

  if (excludeAllocationId) {
    query.allocationId = { $ne: excludeAllocationId };
  }

  const overlappingAllocations = await ResourceAllocation.find(query).populate('projectId', 'name');

  const conflicts: any[] = [];
  let totalCapacity = capacity;

  overlappingAllocations.forEach(allocation => {
    totalCapacity += allocation.capacity;
  });

  if (totalCapacity > 100) {
    conflicts.push({
      type: 'over_allocation',
      severity: 'high',
      message: `Total capacity allocation (${totalCapacity}%) exceeds 100%`,
      conflictingAllocations: overlappingAllocations.map(a => ({
        allocationId: a.allocationId,
        projectName: a.projectId.name,
        capacity: a.capacity,
        startDate: a.startDate,
        endDate: a.endDate,
      })),
    });
  }

  return conflicts;
}

// Helper function to get organization-wide conflicts
async function getOrganizationConflicts(organizationId: string): Promise<any[]> {
  const allocations = await ResourceAllocation.find({
    organizationId,
    status: 'active',
  })
    .populate('userId', 'name email')
    .populate('projectId', 'name');

  const userAllocations = new Map();

  // Group allocations by user
  allocations.forEach(allocation => {
    const userId = allocation.userId._id.toString();
    if (!userAllocations.has(userId)) {
      userAllocations.set(userId, []);
    }
    userAllocations.get(userId).push(allocation);
  });

  const conflicts: any[] = [];

  // Check each user for conflicts
  userAllocations.forEach((userAllocations, userId) => {
    const overlappingPeriods = findOverlappingPeriods(userAllocations);

    overlappingPeriods.forEach(period => {
      const totalCapacity = period.allocations.reduce((sum, a) => sum + a.capacity, 0);

      if (totalCapacity > 100) {
        conflicts.push({
          type: 'over_allocation',
          severity: 'high',
          userId,
          userName: period.allocations[0].userId.name,
          message: `User over-allocated by ${totalCapacity - 100}% during ${period.startDate.toDateString()} - ${period.endDate.toDateString()}`,
          period: {
            startDate: period.startDate,
            endDate: period.endDate,
          },
          allocations: period.allocations.map(a => ({
            allocationId: a.allocationId,
            projectName: a.projectId.name,
            capacity: a.capacity,
          })),
        });
      }
    });
  });

  return conflicts;
}

// Helper function to find overlapping periods
function findOverlappingPeriods(allocations: any[]): any[] {
  const periods: any[] = [];

  for (let i = 0; i < allocations.length; i++) {
    for (let j = i + 1; j < allocations.length; j++) {
      const a1 = allocations[i];
      const a2 = allocations[j];

      const start1 = new Date(a1.startDate);
      const end1 = new Date(a1.endDate);
      const start2 = new Date(a2.startDate);
      const end2 = new Date(a2.endDate);

      // Check if periods overlap
      if (start1 <= end2 && start2 <= end1) {
        const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
        const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));

        periods.push({
          startDate: overlapStart,
          endDate: overlapEnd,
          allocations: [a1, a2],
        });
      }
    }
  }

  return periods;
}

// Helper function to generate utilization report
function generateUtilizationReport(allocations: any[]): any {
  const userUtilization = new Map();
  const projectUtilization = new Map();

  allocations.forEach(allocation => {
    const userId = allocation.userId._id.toString();
    const projectId = allocation.projectId._id.toString();

    // User utilization
    if (!userUtilization.has(userId)) {
      userUtilization.set(userId, {
        user: allocation.userId,
        totalCapacity: 0,
        activeAllocations: 0,
        projects: new Set(),
      });
    }

    const userUtil = userUtilization.get(userId);
    userUtil.totalCapacity += allocation.capacity;
    userUtil.activeAllocations += 1;
    userUtil.projects.add(allocation.projectId.name);

    // Project utilization
    if (!projectUtilization.has(projectId)) {
      projectUtilization.set(projectId, {
        project: allocation.projectId,
        totalCapacity: 0,
        resourceCount: 0,
        roles: new Set(),
      });
    }

    const projUtil = projectUtilization.get(projectId);
    projUtil.totalCapacity += allocation.capacity;
    projUtil.resourceCount += 1;
    projUtil.roles.add(allocation.role);
  });

  // Convert Sets to arrays for JSON serialization
  const userReport = Array.from(userUtilization.values()).map(u => ({
    ...u,
    projects: Array.from(u.projects),
    utilizationLevel:
      u.totalCapacity <= 80 ? 'optimal' : u.totalCapacity <= 100 ? 'high' : 'over_allocated',
  }));

  const projectReport = Array.from(projectUtilization.values()).map(p => ({
    ...p,
    roles: Array.from(p.roles),
    averageCapacity: p.totalCapacity / p.resourceCount,
  }));

  return {
    summary: {
      totalAllocations: allocations.length,
      uniqueUsers: userUtilization.size,
      uniqueProjects: projectUtilization.size,
      overAllocatedUsers: userReport.filter(u => u.utilizationLevel === 'over_allocated').length,
      averageUtilization:
        userReport.reduce((sum, u) => sum + u.totalCapacity, 0) / Math.max(1, userReport.length),
    },
    userUtilization: userReport,
    projectUtilization: projectReport,
  };
}

// Helper function to build availability calendar
function buildAvailabilityCalendar(allocations: any[], startDate: Date, endDate: Date): any {
  const calendar: any[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const dayAllocations = allocations.filter(allocation => {
      const allocStart = new Date(allocation.startDate);
      const allocEnd = new Date(allocation.endDate);
      return currentDate >= allocStart && currentDate <= allocEnd;
    });

    const totalCapacity = dayAllocations.reduce((sum, a) => sum + a.capacity, 0);

    calendar.push({
      date: new Date(currentDate),
      totalCapacity,
      availableCapacity: Math.max(0, 100 - totalCapacity),
      allocations: dayAllocations.map(a => ({
        allocationId: a.allocationId,
        projectName: a.projectId.name,
        role: a.role,
        capacity: a.capacity,
      })),
      status:
        totalCapacity === 0
          ? 'available'
          : totalCapacity <= 80
            ? 'optimal'
            : totalCapacity <= 100
              ? 'high'
              : 'over_allocated',
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return calendar;
}

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
