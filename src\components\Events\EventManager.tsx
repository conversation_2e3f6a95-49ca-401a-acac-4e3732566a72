'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { ConfirmDialog } from '@/components/Global/ConfirmDialog';
import { Plus, RefreshCw } from 'lucide-react';
import { addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addMinutes } from 'date-fns';
import { IEvent, IEventAttendee, IEventReminder } from '@/models/Event';
import { EventFormData, EventManagerProps } from '@/types/EventTypes';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ManageEventModal from './ManageEventModal';

import EventDetailsModal from './EventDetailsModal';
import DragDropCalendar from './DragDropCalendar';

import { TIMEZONE_OPTIONS } from '@/constant/TimeZone';
import EventService from '@/services/Event.service';
import { useTaskStore } from '@/stores/taskStore';

const EVENT_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
  '#64748b',
];

const REMINDER_OPTIONS = [
  { value: 0, label: 'At event time' },
  { value: 5, label: '5 minutes before' },
  { value: 15, label: '15 minutes before' },
  { value: 30, label: '30 minutes before' },
  { value: 60, label: '1 hour before' },
  { value: 120, label: '2 hours before' },
  { value: 1440, label: '1 day before' },
];

const RECURRENCE_FREQUENCIES = [
  { value: 'daily' as const, label: 'Daily' },
  { value: 'weekly' as const, label: 'Weekly' },
  { value: 'monthly' as const, label: 'Monthly' },
  { value: 'yearly' as const, label: 'Yearly' },
];

const EventManager: React.FC<EventManagerProps> = ({
  userId,
  organizationId,
  projectId,
  initialView = 'month',
  showTaskIntegration = true,
  showRecurringEvents = true,
  allowEventCreation = true,
  allowEventEditing = true,
  allowEventDeletion = true,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { tasks } = useTaskStore();
  const [currentDate] = useState(new Date());
  const [viewType] = useState<'month' | 'week' | 'day' | 'agenda'>(initialView);
  const [selectedEvent, setSelectedEvent] = useState<IEvent | null>(null);
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [showCreateEvent, setShowCreateEvent] = useState(false);

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Form state
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    location: '',
    startTime: new Date(),
    endTime: addMinutes(new Date(), 60),
    isAllDay: false,
    timezone: 'UTC',
    attendees: [],
    reminders: [{ method: 'popup', minutes: 15, isActive: true }],
    taskIds: [],
    colorId: EVENT_COLORS[0],
    category: '',
    tags: [],
    priority: 'medium',
    visibility: 'default',
    attachments: [],
  });

  const [newAttendee, setNewAttendee] = useState('');
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);

  const eventFilters = useMemo(() => {
    const startDate =
      viewType === 'month'
        ? startOfMonth(currentDate)
        : viewType === 'week'
          ? startOfWeek(currentDate)
          : currentDate;
    const endDate =
      viewType === 'month'
        ? endOfMonth(currentDate)
        : viewType === 'week'
          ? endOfWeek(currentDate)
          : addDays(currentDate, 1);

    return {
      userId,
      organizationId,
      projectId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  }, [userId, organizationId, projectId, currentDate, viewType]);

  // Queries
  const { data: eventsData, isLoading: loading } = useQuery({
    queryKey: ['events', eventFilters],
    queryFn: () => EventService.getEvents(eventFilters),
  });

  const events = useMemo(() => eventsData?.events || [], [eventsData]);

  // Event handlers
  const handleEventClick = useCallback((event: IEvent) => {
    setSelectedEvent(event);
    setShowEventDialog(true);
  }, []);

  // Mutations
  const createEventMutation = useMutation({
    mutationFn: EventService.createEvent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      setShowCreateEvent(false);
      resetForm();
      toast({
        title: 'Event Created',
        description: 'Event has been created successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create event',
        variant: 'destructive',
      });
    },
  });

  const updateEventMutation = useMutation({
    mutationFn: ({ eventId, updates }: { eventId: string; updates: any }) =>
      EventService.updateEvent(eventId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      toast({
        title: 'Event Updated',
        description: 'Event has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update event',
        variant: 'destructive',
      });
    },
  });

  const deleteEventMutation = useMutation({
    mutationFn: EventService.deleteEvent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      setShowDeleteConfirm(false);
      setShowEventDialog(false);
      setSelectedEvent(null);
      toast({
        title: 'Event Deleted',
        description: 'Event has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete event',
        variant: 'destructive',
      });
    },
  });

  const handleEventDrop = useCallback(
    (eventId: string, newStart: Date, newEnd: Date) => {
      updateEventMutation.mutate({
        eventId,
        updates: {
          startTime: newStart,
          endTime: newEnd,
        },
      });
    },
    [updateEventMutation]
  );
  // Recurring event helpers
  const generateRecurringEvents = useCallback((baseEvent: EventFormData) => {
    if (!baseEvent.recurrence) return [baseEvent];

    const events: EventFormData[] = [baseEvent];
    const { frequency, interval = 1, count, until } = baseEvent.recurrence;

    let currentDate = new Date(baseEvent.startTime);
    const duration =
      new Date(baseEvent.endTime).getTime() - new Date(baseEvent.startTime).getTime();

    for (let i = 1; i < (count || 10); i++) {
      switch (frequency) {
        case 'daily':
          currentDate = addDays(currentDate, interval);
          break;
        case 'weekly':
          currentDate = addDays(currentDate, interval * 7);
          break;
        case 'monthly':
          currentDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + interval,
            currentDate.getDate()
          );
          break;
        case 'yearly':
          currentDate = new Date(
            currentDate.getFullYear() + interval,
            currentDate.getMonth(),
            currentDate.getDate()
          );
          break;
      }

      if (until && currentDate > until) break;

      const recurringEvent: EventFormData = {
        ...baseEvent,
        startTime: new Date(currentDate),
        endTime: new Date(currentDate.getTime() + duration),
      };

      events.push(recurringEvent);
    }

    return events;
  }, []);
  const handleCreateEvent = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      const basePayload = {
        ...formData,
        userId,
        organizationId,
        projectId,
        attendees: formData.attendees.map(attendee => ({
          ...attendee,
          addedAt: new Date(),
        })),
      };

      if (formData.recurrence) {
        const recurringEvents = generateRecurringEvents(basePayload);
        recurringEvents.forEach(eventData => {
          createEventMutation.mutate(eventData);
        });
      } else {
        createEventMutation.mutate(basePayload);
      }
    },
    [formData, userId, organizationId, projectId, generateRecurringEvents, createEventMutation]
  );

  const handleUpdateEvent = useCallback(
    (eventId: string, updates: Partial<IEvent>) => {
      updateEventMutation.mutate({ eventId, updates });
    },
    [updateEventMutation]
  );

  const handleDeleteEvent = useCallback(() => {
    if (!selectedEvent) return;
    deleteEventMutation.mutate(selectedEvent._id as string);
  }, [selectedEvent, deleteEventMutation]);

  // Form helpers
  const resetForm = useCallback(() => {
    setFormData({
      title: '',
      description: '',
      location: '',
      startTime: new Date(),
      endTime: addMinutes(new Date(), 60),
      isAllDay: false,
      timezone: 'UTC',
      attendees: [],
      reminders: [{ method: 'popup', minutes: 15, isActive: true }],
      taskIds: [],
      colorId: EVENT_COLORS[0],
      category: '',
      tags: [],
      priority: 'medium',
      visibility: 'default',
      attachments: [],
    });
    setSelectedTasks([]);
    setNewAttendee('');
  }, []);

  const addAttendee = useCallback(() => {
    if (!newAttendee.trim()) return;

    const attendee: IEventAttendee = {
      email: newAttendee.trim(),
      name: newAttendee.split('@')[0],
      responseStatus: 'needsAction',
      isOptional: false,
      addedAt: new Date(),
    };

    setFormData(prev => ({
      ...prev,
      attendees: [...prev.attendees, attendee],
    }));
    setNewAttendee('');
  }, [newAttendee]);

  const removeAttendee = useCallback((email: string) => {
    setFormData(prev => ({
      ...prev,
      attendees: prev.attendees.filter(a => a.email !== email),
    }));
  }, []);

  const addReminder = useCallback(() => {
    const newReminder: IEventReminder = {
      method: 'popup',
      minutes: 15,
      isActive: true,
    };

    setFormData(prev => ({
      ...prev,
      reminders: [...prev.reminders, newReminder],
    }));
  }, []);

  const removeReminder = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      reminders: prev.reminders.filter((_, i) => i !== index),
    }));
  }, []);

  // Conflict detection
  const detectEventConflicts = useCallback(
    (newEvent: Partial<EventFormData>, excludeEventId?: string) => {
      const conflicts: IEvent[] = [];
      const newStart = new Date(newEvent.startTime!);
      const newEnd = new Date(newEvent.endTime!);

      events.forEach(event => {
        if (excludeEventId && event._id === excludeEventId) return;

        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);

        // Check for time overlap
        if (newStart < eventEnd && newEnd > eventStart) {
          // Check if same attendees are involved
          const hasCommonAttendees = newEvent.attendees?.some(newAttendee =>
            event.attendees.some(eventAttendee => eventAttendee.email === newAttendee.email)
          );

          if (hasCommonAttendees) {
            conflicts.push(event);
          }
        }
      });

      return conflicts;
    },
    [events]
  );

  // Removed unused navigation handlers and view title functions

  // Enhanced drag and drop handlers
  const handleEventDropComplete = useCallback(
    (eventId: string, dropDate: Date) => {
      const event = events.find(e => e._id === eventId);
      if (!event) return;

      const eventDuration = new Date(event.endTime).getTime() - new Date(event.startTime).getTime();
      const newEndTime = new Date(dropDate.getTime() + eventDuration);

      // Check for conflicts
      const conflicts = detectEventConflicts(
        {
          startTime: dropDate,
          endTime: newEndTime,
          attendees: event.attendees,
        },
        eventId
      );

      if (conflicts.length > 0) {
        const conflictTitles = conflicts.map(c => c.title).join(', ');
        const proceed = window.confirm(
          `This event conflicts with: ${conflictTitles}. Do you want to proceed anyway?`
        );

        if (!proceed) return;
      }

      handleEventDrop(eventId, dropDate, newEndTime);
    },
    [events, detectEventConflicts, handleEventDrop]
  );

  return (
    <div className="space-y-6 theme-transition">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold theme-text-primary">Events & Calendar</h2>
          <p className="theme-text-secondary mt-1 text-sm">
            Manage events and integrate with tasks
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => queryClient.invalidateQueries({ queryKey: ['events'] })}
            disabled={loading}
            className="theme-button-secondary"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          {allowEventCreation && (
            <Button onClick={() => setShowCreateEvent(true)} className="theme-button-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Event
            </Button>
          )}
        </div>
      </div>

      {/* Calendar View */}
      <DragDropCalendar
        initialView={initialView}
        allowEventCreation={allowEventCreation}
        events={events}
        onEventUpdate={(eventId, updates) => {
          updateEventMutation.mutate({ eventId, updates });
        }}
        onEventClick={handleEventClick}
        onEventDrop={handleEventDropComplete}
        className="min-h-[600px] theme-surface-elevated theme-shadow-sm"
      />
      {/* Modals and dialogs remain unchanged */}
      <ManageEventModal
        showCreateEvent={showCreateEvent}
        setShowCreateEvent={setShowCreateEvent}
        formData={formData}
        setFormData={setFormData}
        handleCreateEvent={handleCreateEvent}
        EVENT_COLORS={EVENT_COLORS}
        newAttendee={newAttendee}
        setNewAttendee={setNewAttendee}
        addAttendee={addAttendee}
        removeAttendee={removeAttendee}
        REMINDER_OPTIONS={REMINDER_OPTIONS}
        removeReminder={removeReminder}
        showRecurringEvents={showRecurringEvents}
        RECURRENCE_FREQUENCIES={RECURRENCE_FREQUENCIES}
        addReminder={addReminder}
        TIMEZONE_OPTIONS={TIMEZONE_OPTIONS}
        tasks={tasks}
        setSelectedTasks={setSelectedTasks}
        showTaskIntegration={showTaskIntegration}
        selectedTasks={selectedTasks}
        resetForm={resetForm}
      />
      <EventDetailsModal
        showEventDialog={showEventDialog}
        setShowEventDialog={setShowEventDialog}
        selectedEvent={selectedEvent}
        allowEventEditing={allowEventEditing}
        handleUpdateEvent={handleUpdateEvent}
        allowEventDeletion={allowEventDeletion}
        setShowDeleteConfirm={setShowDeleteConfirm}
      />

      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="Delete Event"
        description={`Are you sure you want to delete "${selectedEvent?.title}"? This action cannot be undone.`}
        confirmText="Delete Event"
        cancelText="Cancel"
        onConfirm={handleDeleteEvent}
        variant="destructive"
      />
    </div>
  );
};

export default EventManager;
