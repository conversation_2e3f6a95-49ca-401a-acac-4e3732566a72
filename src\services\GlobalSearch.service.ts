import axios from 'axios';
import { toast } from 'sonner';

export interface SearchQuery {
  query: string;
  type?: string;
  status?: string;
  priority?: string;
  tags?: string;
  project?: string;
  page?: number;
  limit?: number;
}

export interface SearchResult {
  id: string;
  type: 'task' | 'project' | 'note' | 'user';
  title: string;
  description?: string;
  url: string;
  relevanceScore: number;
  metadata: Record<string, any>;
  highlights?: Record<string, string[]>;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  totalPages: number;
  searchTime: number;
  cached?: boolean;
}

export class GlobalSearchService {
  static async search(params: SearchQuery): Promise<SearchResponse> {
    try {
      const response = await axios.get('/api/search', {
        params,
        timeout: 10000,
      });

      return {
        results: response.data.results || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        totalPages: response.data.totalPages || 0,
        searchTime: response.data.searchTime || 0,
        cached: response.data.cached || false,
      };
    } catch (error: any) {
      toast.error('Error during search', error.message);
      return {
        results: [],
        total: 0,
        page: 1,
        totalPages: 0,
        searchTime: 0,
        cached: false,
      };
    }
  }
}
