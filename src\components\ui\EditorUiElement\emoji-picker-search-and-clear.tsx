'use client';

import type { UseEmojiPickerType } from '@udecode/plate-emoji/react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

import { emojiSearchIcons } from './emoji-icons';

export type EmojiPickerSearchAndClearProps = Pick<
  UseEmojiPickerType,
  'clearSearch' | 'i18n' | 'searchValue'
>;

export function EmojiPickerSearchAndClear({
  clearSearch,
  i18n,
  searchValue,
}: EmojiPickerSearchAndClearProps) {
  return (
    <div className="flex items-center theme-text-primary">
      <div
        className={cn(
          'absolute top-1/2 left-3 z-10 flex size-5 -translate-y-1/2 items-center justify-center',
          'text-blue-600 dark:text-blue-400 theme-transition'
        )}
      >
        {emojiSearchIcons.loupe}
      </div>
      {searchValue && (
        <Button
          size="icon"
          variant="ghost"
          className={cn(
            'absolute top-1/2 right-1 flex size-7 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full',
            'theme-button-ghost theme-transition-fast hover-reveal',
            'text-muted-foreground hover:text-destructive hover:bg-destructive/10',
            'border-none bg-transparent'
          )}
          onClick={clearSearch}
          title={i18n.clear}
          aria-label="Clear"
          type="button"
        >
          {emojiSearchIcons.delete}
        </Button>
      )}
    </div>
  );
}
