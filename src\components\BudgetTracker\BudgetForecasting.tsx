'use client';

import React, { useMemo } from 'react';
import { IBudget, IExpense } from '@/models/Budget';
import { addMonths, format, subMonths } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Area,
  AreaChart,
} from 'recharts';
import { Alert, AlertDescription } from '../ui/alert';
import { AlertTriangle, TrendingUp } from 'lucide-react';
import { CURRENCY_OPTIONS } from '@/constant/Currency';

interface BudgetForecastingProps {
  budget: IBudget;
  expenses: IExpense[];
}

interface ForecastDataPoint {
  month: string;
  spent: number;
  projected: number;
}

export const BudgetForecasting: React.FC<BudgetForecastingProps> = ({ budget, expenses }) => {
  // Get currency symbol for formatting
  const currencySymbol = useMemo(() => {
    return CURRENCY_OPTIONS.find(c => c.value === budget.currency)?.symbol || '$';
  }, [budget.currency]);

  const forecastData = useMemo<ForecastDataPoint[]>(() => {
    // Calculate monthly spending trend
    const monthlySpending = new Map<string, number>();

    // Ensure expense.date is properly handled as it could be a string or Date
    expenses.forEach(expense => {
      const expenseDate = expense.date instanceof Date ? expense.date : new Date(expense.date);
      const monthKey = format(expenseDate, 'yyyy-MM');
      monthlySpending.set(monthKey, (monthlySpending.get(monthKey) || 0) + expense.amount);
    });

    // Get last 6 months of data
    const forecastMonths: ForecastDataPoint[] = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const targetMonth = subMonths(currentDate, i);
      const monthKey = format(targetMonth, 'yyyy-MM');
      const spent = monthlySpending.get(monthKey) || 0;

      forecastMonths.push({
        month: format(targetMonth, 'MMM yyyy'),
        spent,
        projected: 0, // Will be calculated
      });
    }

    // Calculate projection for next 6 months
    const avgMonthlySpend =
      forecastMonths.reduce((sum, month) => sum + month.spent, 0) / forecastMonths.length;

    for (let i = 1; i <= 6; i++) {
      const futureMonth = addMonths(currentDate, i);
      forecastMonths.push({
        month: format(futureMonth, 'MMM yyyy'),
        spent: 0,
        projected: avgMonthlySpend,
      });
    }

    return forecastMonths;
  }, [expenses]);

  const projectedTotal = useMemo(() => {
    const currentDate = new Date();
    const remainingMonths = 12 - currentDate.getMonth();

    // Calculate average monthly spend based on expenses from the current year only
    const currentYear = currentDate.getFullYear();
    const currentYearExpenses = expenses.filter(exp => {
      const expDate = exp.date instanceof Date ? exp.date : new Date(exp.date);
      return expDate.getFullYear() === currentYear;
    });

    const avgMonthlySpend =
      currentYearExpenses.length > 0
        ? currentYearExpenses.reduce((sum, exp) => sum + exp.amount, 0) /
          Math.max(1, currentDate.getMonth() + 1)
        : 0;

    return budget.spentAmount + avgMonthlySpend * remainingMonths;
  }, [budget, expenses]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Budget Forecasting
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {currencySymbol}
              {projectedTotal.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            </div>
            <p className="text-sm text-muted-foreground">Projected Year-End Total</p>
          </div>
          <div className="text-center">
            <div
              className={`text-2xl font-bold ${projectedTotal > budget.totalBudget ? 'text-red-500' : 'text-green-500'}`}
            >
              {projectedTotal > budget.totalBudget ? '+' : ''}
              {currencySymbol}
              {Math.abs(projectedTotal - budget.totalBudget).toLocaleString(undefined, {
                maximumFractionDigits: 2,
              })}
            </div>
            <p className="text-sm text-muted-foreground">
              {projectedTotal > budget.totalBudget ? 'Over Budget' : 'Under Budget'}
            </p>
          </div>
          <div className="text-center">
            <div
              className={`text-2xl font-bold ${projectedTotal > budget.totalBudget ? 'text-red-500' : projectedTotal / budget.totalBudget > 0.8 ? 'text-yellow-500' : 'text-green-500'}`}
            >
              {((projectedTotal / budget.totalBudget) * 100).toFixed(1)}%
            </div>
            <p className="text-sm text-muted-foreground">Projected Utilization</p>
          </div>
        </div>

        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={forecastData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              formatter={(value: number) =>
                `${currencySymbol}${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}`
              }
              labelFormatter={label => `Period: ${label}`}
            />
            <Legend />
            <Area
              type="monotone"
              dataKey="spent"
              stackId="1"
              stroke="#8884d8"
              fill="#8884d8"
              name="Actual Spending"
            />
            <Area
              type="monotone"
              dataKey="projected"
              stackId="2"
              stroke="#82ca9d"
              fill="#82ca9d"
              name="Projected Spending"
            />
          </AreaChart>
        </ResponsiveContainer>

        {projectedTotal > budget.totalBudget && (
          <Alert className="mt-4" variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Warning: Current spending trend indicates you may exceed your total budget by{' '}
              {currencySymbol}
              {(projectedTotal - budget.totalBudget).toLocaleString(undefined, {
                maximumFractionDigits: 2,
              })}{' '}
              by year-end.
            </AlertDescription>
          </Alert>
        )}

        {projectedTotal <= budget.totalBudget && projectedTotal / budget.totalBudget > 0.8 && (
          <Alert className="mt-4" variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Caution: You are projected to use{' '}
              {((projectedTotal / budget.totalBudget) * 100).toFixed(1)}% of your budget by
              year-end.
            </AlertDescription>
          </Alert>
        )}

        {projectedTotal <= budget.totalBudget && projectedTotal / budget.totalBudget <= 0.8 && (
          <Alert className="mt-4">
            <AlertDescription>
              You are on track with your budget. Current projection shows you will use{' '}
              {((projectedTotal / budget.totalBudget) * 100).toFixed(1)}% of your total budget by
              year-end.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

// Export for testing purposes
export type { BudgetForecastingProps };
