import axios from 'axios';

export class NoteService {
  static async getNotes(params: {
    page: number;
    search: string;
    category: string;
    tags: string[];
    filter: string;
    sortBy: string;
    sortOrder: string;
  }): Promise<any> {
    try {
      const response = await axios.get('/api/notes', { params });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch notes');
    }
  }

  static async getNoteById(noteId: string): Promise<any> {
    try {
      const response = await axios.get(`/api/notes/${noteId}`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch note');
    }
  }

  static async createNote(data: any): Promise<any> {
    try {
      const response = await axios.post('/api/notes', data);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create note');
    }
  }

  static async updateNote(noteId: string, data: any): Promise<any> {
    try {
      const response = await axios.put(`/api/notes/${noteId}`, data);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to update note');
    }
  }

  static async deleteNote(noteId: string, permanent = false): Promise<any> {
    try {
      const response = await axios.delete(`/api/notes/${noteId}?permanent=${permanent}`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to delete note');
    }
  }

  static async togglePin(noteId: string): Promise<any> {
    try {
      const response = await axios.post(`/api/notes/${noteId}/pin`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to toggle pin status');
    }
  }

  static async toggleFavorite(noteId: string): Promise<any> {
    try {
      const response = await axios.post(`/api/notes/${noteId}/favorite`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to toggle favorite status');
    }
  }

  static async toggleArchive(noteId: string): Promise<any> {
    try {
      const response = await axios.post(`/api/notes/${noteId}/archive`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to toggle archive status');
    }
  }

  static async duplicateNote(noteId: string): Promise<any> {
    try {
      const response = await axios.post(`/api/notes/${noteId}/duplicate`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to duplicate note');
    }
  }

  static async bulkAction(action: string, noteIds: string[], data?: any): Promise<any> {
    try {
      const response = await axios.post('/api/notes/actions', { action, noteIds, data });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to perform bulk action');
    }
  }

  static async getAllTags(): Promise<any> {
    try {
      const response = await axios.get('/api/notes/tags');
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch tags');
    }
  }
}
