import React from 'react';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { AlertTriangle, Edit, Trash2, Grip } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { format } from 'date-fns';
import { IResourceAllocation } from '@/models/ResourceAllocation';
import { ConflictInfo } from './ResourceAllocationGrid';

interface DraggableAllocationCardProps {
  allocation: IResourceAllocation;
  conflicts: ConflictInfo[];
  onEdit: (allocation: IResourceAllocation) => void;
  onDelete: (allocationId: string) => void;
  index: number;
}

export const DraggableAllocationCard: React.FC<DraggableAllocationCardProps> = ({
  allocation,
  conflicts,
  onEdit,
  onDelete,
  index,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    transform,
    isDragging,
  } = useDraggable({
    id: allocation.allocationId,
    data: {
      type: 'allocation',
      allocation,
      index,
    },
  });

  const { isOver, setNodeRef: setDropRef } = useDroppable({
    id: `allocation-drop-${allocation.allocationId}`,
    data: {
      type: 'allocation-drop',
      allocation,
      index,
    },
  });

  const dragStyle = {
    transform: CSS.Translate.toString(transform),
  };

  // Handle case where workloadTracking might not exist
  const currentUtilization =
    allocation.workloadTracking?.currentUtilization || allocation.capacity || 0;

  const utilizationColor =
    currentUtilization > 100
      ? 'text-red-500'
      : currentUtilization > 80
        ? 'text-yellow-500'
        : 'text-green-500';

  const hasConflicts = conflicts && conflicts.length > 0;
  const highPriorityConflicts = conflicts?.filter(c => c.severity === 'high').length || 0;

  return (
    <div
      ref={node => {
        setDragRef(node);
        setDropRef(node);
      }}
      className={`transition-all duration-200 ${isDragging ? 'opacity-50' : ''} ${isOver ? 'ring-2 ring-primary' : ''}`}
      style={dragStyle}
    >
      <div
        className={`p-3 border rounded-lg transition-all ${
          hasConflicts ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-white'
        } hover:shadow-md`}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Grip
              className="h-4 w-4 text-muted-foreground cursor-grab"
              {...attributes}
              {...listeners}
            />
            <span className="font-medium">{allocation.role}</span>
            <Badge variant="outline">{allocation.capacity}%</Badge>
            {hasConflicts && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {highPriorityConflicts} conflicts
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" onClick={() => onEdit(allocation)}>
                    <Edit className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Edit allocation</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(allocation.allocationId)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete allocation</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="space-y-1 text-sm text-muted-foreground">
          <div className="flex justify-between">
            <span>Utilization:</span>
            <span className={utilizationColor}>{currentUtilization}%</span>
          </div>
          <div className="flex justify-between">
            <span>Period:</span>
            <span>
              {format(new Date(allocation.startDate), 'MMM dd')} -{' '}
              {format(new Date(allocation.endDate), 'MMM dd')}
            </span>
          </div>
          {allocation.skills && allocation.skills.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {allocation.skills.slice(0, 3).map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {allocation.skills.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{allocation.skills.length - 3} more
                </Badge>
              )}
            </div>
          )}
        </div>

        {hasConflicts && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="font-medium text-red-700 mb-1">Conflicts detected:</div>
            {conflicts.slice(0, 2).map((conflict, index) => (
              <div key={index} className="text-red-600">
                • {conflict.message}
              </div>
            ))}
            {conflicts.length > 2 && (
              <div className="text-red-600">• +{conflicts.length - 2} more conflicts</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
