import { SearchQuery } from '@/services/GlobalSearch.service';
import { CheckSquare, FolderOpen, FileText, User } from 'lucide-react';

interface SearchFiltersProps {
  filters: Partial<SearchQuery>;
  onFiltersChange: (filters: Partial<SearchQuery>) => void;
}

export const SearchFilters = ({ filters, onFiltersChange }: SearchFiltersProps) => {
  const contentTypes = [
    { value: 'task', label: 'Tasks', icon: CheckSquare, color: 'text-blue-500' },
    { value: 'project', label: 'Projects', icon: FolderOpen, color: 'text-green-500' },
    { value: 'note', label: 'Notes', icon: FileText, color: 'text-purple-500' },
    { value: 'user', label: 'Users', icon: User, color: 'text-orange-500' },
  ];

  const statusOptions = ['todo', 'in-progress', 'completed', 'cancelled'];
  const priorityOptions = ['low', 'medium', 'high', 'urgent'];

  const handleTypeToggle = (type: string) => {
    const currentTypes = filters.type?.split(',') || [];
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type];

    onFiltersChange({
      ...filters,
      type: newTypes.length ? newTypes.join(',') : undefined,
    });
  };

  const handleFilterChange = (key: keyof SearchQuery, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined,
    });
  };

  const selectedTypes = filters.type?.split(',') || [];

  return (
    <div className="p-4 theme-surface theme-border border-b">
      <div className="space-y-4">
        {/* Content Types */}
        <div>
          <label className="form-label mb-2 block">Content Type</label>
          <div className="flex flex-wrap gap-2">
            {contentTypes.map(({ value, label, icon: Icon, color }) => (
              <button
                key={value}
                onClick={() => handleTypeToggle(value)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm theme-transition hover-reveal ${
                  selectedTypes.includes(value)
                    ? 'theme-active-primary theme-shadow-sm'
                    : 'theme-surface-elevated interactive-hover'
                }`}
              >
                <Icon
                  className={`w-4 h-4 ${selectedTypes.includes(value) ? 'text-primary' : color}`}
                />
                {label}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Status Filter */}
          <div>
            <label className="form-label mb-2 block">Status</label>
            <select
              value={filters.status || ''}
              onChange={e => handleFilterChange('status', e.target.value)}
              className="form-input w-full theme-transition"
            >
              <option value="">All Status</option>
              {statusOptions.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
                </option>
              ))}
            </select>
          </div>

          {/* Priority Filter */}
          <div>
            <label className="form-label mb-2 block">Priority</label>
            <select
              value={filters.priority || ''}
              onChange={e => handleFilterChange('priority', e.target.value)}
              className="form-input w-full theme-transition"
            >
              <option value="">All Priorities</option>
              {priorityOptions.map(priority => (
                <option key={priority} value={priority}>
                  {priority.charAt(0).toUpperCase() + priority.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Clear Filters */}
        {(filters.type || filters.status || filters.priority) && (
          <button
            onClick={() => onFiltersChange({})}
            className="text-sm text-primary hover:text-primary/80 theme-transition font-medium"
          >
            Clear all filters
          </button>
        )}
      </div>
    </div>
  );
};
