'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface NoteViewSkeletonProps {
  className?: string;
}

export const NoteViewSkeleton: React.FC<NoteViewSkeletonProps> = ({ className }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className={`theme-surface h-full w-full flex flex-col ${className || ''}`}
    >
      {/* Header Skeleton */}
      <div className="theme-divider theme-surface-elevated backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-6">
            <Skeleton className="h-8 w-32 loading-skeleton" />
            <Separator orientation="vertical" className="h-6 theme-border" />
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-64 loading-skeleton" />
              <div className="flex items-center gap-1.5">
                <Skeleton className="h-6 w-6 rounded-full loading-skeleton" />
                <Skeleton className="h-6 w-6 rounded-full loading-skeleton" />
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="h-8 w-20 loading-skeleton" />
            <Skeleton className="h-8 w-8 loading-skeleton" />
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden h-full">
        {/* Content Area Skeleton */}
        <div className="flex-1 flex flex-col p-6 space-y-4">
          <Skeleton className="h-8 w-3/4 loading-skeleton" />
          <Skeleton className="h-4 w-full loading-skeleton" />
          <Skeleton className="h-4 w-5/6 loading-skeleton" />
          <Skeleton className="h-4 w-4/5 loading-skeleton" />
          <div className="space-y-3 mt-6">
            <Skeleton className="h-6 w-2/3 loading-skeleton" />
            <Skeleton className="h-4 w-full loading-skeleton" />
            <Skeleton className="h-4 w-full loading-skeleton" />
            <Skeleton className="h-4 w-3/4 loading-skeleton" />
          </div>
          <div className="space-y-3 mt-6">
            <Skeleton className="h-4 w-full loading-skeleton" />
            <Skeleton className="h-4 w-5/6 loading-skeleton" />
            <Skeleton className="h-4 w-4/5 loading-skeleton" />
            <Skeleton className="h-4 w-full loading-skeleton" />
          </div>
          <div className="space-y-3 mt-6">
            <Skeleton className="h-4 w-4/5 loading-skeleton" />
            <Skeleton className="h-4 w-full loading-skeleton" />
            <Skeleton className="h-4 w-3/4 loading-skeleton" />
          </div>
        </div>

        {/* Sidebar Skeleton */}
        <div className="w-80 h-full overflow-y-auto theme-scrollbar theme-divider theme-surface-elevated">
          {/* Category & Tags Card Skeleton */}
          <Card className="m-4 dashboard-card">
            <CardHeader className="pb-3">
              <CardTitle className="dashboard-card-title text-sm flex items-center gap-2">
                <Skeleton className="h-4 w-4 loading-skeleton" />
                <Skeleton className="h-4 w-32 loading-skeleton" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Skeleton className="h-3 w-16 loading-skeleton mb-2" />
                <Skeleton className="h-10 w-full loading-skeleton" />
              </div>
              <div>
                <Skeleton className="h-3 w-12 loading-skeleton mb-2" />
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-6 w-16 rounded-full loading-skeleton" />
                  <Skeleton className="h-6 w-20 rounded-full loading-skeleton" />
                  <Skeleton className="h-6 w-14 rounded-full loading-skeleton" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Note Information Card Skeleton */}
          <Card className="m-4 dashboard-card">
            <CardHeader className="pb-3">
              <CardTitle className="dashboard-card-title text-sm flex items-center gap-2">
                <Skeleton className="h-4 w-4 loading-skeleton" />
                <Skeleton className="h-4 w-36 loading-skeleton" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Skeleton className="h-3.5 w-3.5 loading-skeleton" />
                  <Skeleton className="h-3 w-16 loading-skeleton" />
                  <Skeleton className="h-3 w-20 loading-skeleton" />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Additional Card Skeleton */}
          <Card className="m-4 dashboard-card">
            <CardHeader className="pb-3">
              <CardTitle className="dashboard-card-title text-sm flex items-center gap-2">
                <Skeleton className="h-4 w-4 loading-skeleton" />
                <Skeleton className="h-4 w-28 loading-skeleton" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Skeleton className="h-4 w-full loading-skeleton" />
                <Skeleton className="h-4 w-3/4 loading-skeleton" />
                <Skeleton className="h-4 w-5/6 loading-skeleton" />
              </div>
              <div className="flex gap-2 mt-4">
                <Skeleton className="h-8 w-20 loading-skeleton" />
                <Skeleton className="h-8 w-24 loading-skeleton" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
};

export default NoteViewSkeleton;
