'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
// Button is used in EmptyState actions
import { Skeleton } from '@/components/ui/skeleton';
import { EmptyState } from '@/components/Global/EmptyState';
import NotePagination from './NotePagination';
import { FileText, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Note } from '@/types/Note';
import NoteCard from './NoteCard';

interface NotesListProps {
  notes: Note[];
  isLoading: boolean;
  viewMode: 'grid' | 'list';
  isSelectionMode: boolean;
  selectedNotes: string[];
  onToggleSelection: (noteId: string) => void;
  onQuickAction: (noteId: string, field: keyof Note) => void;
  onDeleteNote: (data: { noteId: string; permanent: boolean }) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalNotes: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  onPageChange?: (page: number) => void;
  className?: string;
}

const NotesList: React.FC<NotesListProps> = ({
  notes,
  isLoading,
  viewMode,
  isSelectionMode,
  selectedNotes,
  onToggleSelection,
  onQuickAction,
  onDeleteNote,
  pagination,
  onPageChange,
  className,
}) => {
  const router = useRouter();

  const handleNoteView = (noteId: string) => {
    router.push(`/notes/view/${noteId}`);
  };

  const handleNoteEdit = (noteId: string) => {
    router.push(`/notes/editor?id=${noteId}`);
  };

  const handleNoteDelete = (noteId: string) => {
    onDeleteNote({ noteId, permanent: false });
  };

  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div
          className={cn(
            'grid gap-4',
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          )}
        >
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                  <div className="flex gap-2">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!notes.length) {
    return (
      <div className={className}>
        <EmptyState
          icon={<FileText className="h-12 w-12" />}
          title="No notes found"
          description="Create your first note to get started with organizing your thoughts and ideas."
          actions={[
            {
              label: 'Create Note',
              onClick: () => router.push('/notes/editor'),
              variant: 'default',
              icon: <Plus className="h-4 w-4" />,
            },
          ]}
        />
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div
        className={cn(
          'grid gap-4',
          viewMode === 'grid'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1'
        )}
      >
        {notes.map(note => (
          <NoteCard
            key={note._id}
            note={note}
            viewMode={viewMode}
            isSelectionMode={isSelectionMode}
            isSelected={selectedNotes.includes(note._id)}
            onToggleSelection={() => onToggleSelection(note._id)}
            onView={() => handleNoteView(note._id)}
            onEdit={() => handleNoteEdit(note._id)}
            onDelete={() => handleNoteDelete(note._id)}
            onQuickAction={onQuickAction}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {(pagination.currentPage - 1) * 20 + 1} to{' '}
            {Math.min(pagination.currentPage * 20, pagination.totalNotes)} of{' '}
            {pagination.totalNotes} notes
          </div>

          <NotePagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            onPageChange={onPageChange || (() => {})}
            showFirstLast
            showPrevNext
          />
        </div>
      )}
    </div>
  );
};

export default NotesList;
