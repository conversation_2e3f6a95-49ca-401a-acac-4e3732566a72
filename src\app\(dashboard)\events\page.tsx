'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import EventManager from '@/components/Events/EventManager';
import { motion } from 'framer-motion';

const EventsPage: React.FC = () => {
  const { data: session } = useSession();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-3 rounded-lg py-2 h-full w-full overflow-y-auto custom-scrollbar"
    >
      <EventManager
        userId={session?.user?.id}
        organizationId={session?.user?.organizationId || ''}
        showTaskIntegration={true}
        allowEventCreation={true}
        allowEventEditing={true}
        allowEventDeletion={true}
      />
    </motion.div>
  );
};

export default EventsPage;
