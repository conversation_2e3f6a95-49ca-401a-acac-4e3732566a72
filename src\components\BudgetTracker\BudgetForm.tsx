import React, { useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import Modal from '../Global/Modal';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import ReactSelect, { Option } from '../Global/ReactSelect';
import {
  Trash2,
  Plus,
  AlertTriangle,
  DollarSign,
  Bell,
  Calendar,
  BarChart3,
  Check,
  Loader2,
  Euro,
  PoundSterling,
  BadgeIndianRupee,
  CircleDollarSign,
  BellRing,
  BellOff,
  Mail,
  MessageSquare,
  Percent,
  CircleCheck,
  CirclePause,
  CircleAlert,
  JapaneseYen,
} from 'lucide-react';
import { CURRENCY_OPTIONS } from '@/constant/Currency';
import { Switch } from '../ui/switch';
import { Alert, AlertDescription } from '../ui/alert';
import {
  BudgetService,
  IBudgetCreateData,
  IBudgetUpdateData,
  IBudgetCategory,
} from '@/services/Budget.service';
import { cn } from '@/lib/utils';
import { IBudget } from '@/models/Budget';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const budgetSchema = yup.object({
  projectId: yup.string().required('Project ID is required'),
  totalBudget: yup
    .number()
    .positive('Total budget must be positive')
    .required('Total budget is required'),
  currency: yup.string().required('Currency is required'),
  categories: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required('Category name is required'),
        allocatedAmount: yup
          .number()
          .positive('Allocated amount must be positive')
          .required('Allocated amount is required'),
        description: yup.string().optional(),
      })
    )
    .min(1, 'At least one category is required'),
  alerts: yup.object({
    thresholdPercentage: yup.number().min(1).max(100).required('Threshold percentage is required'),
    enabled: yup.boolean(),
    emailNotifications: yup.boolean(),
    pushNotifications: yup.boolean(),
  }),
  status: yup.string().required('Status is required'),
  fiscalYear: yup.number().optional(),
});

type BudgetFormData = yup.InferType<typeof budgetSchema>;

interface BudgetFormProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  onBudgetSaved: () => void;
  budget?: IBudget;
  mode: 'create' | 'edit';
}

export const BudgetForm: React.FC<BudgetFormProps> = ({
  isOpen,
  onClose,
  projectId,
  onBudgetSaved,
  budget,
  mode,
}) => {
  const [categoryError, setCategoryError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const currentYear = new Date().getFullYear();

  // For debugging
  React.useEffect(() => {
    if (mode === 'edit' && budget) {
      console.log('Editing budget:', budget);
    }
  }, [budget, mode]);

  const isEditMode = mode === 'edit' && budget;

  // Initialize form with default values based on mode
  const defaultValues = React.useMemo(() => {
    if (isEditMode && budget) {
      return {
        projectId: budget.projectId.toString(), // Convert ObjectId to string
        totalBudget: budget.totalBudget,
        currency: budget.currency,
        categories: budget.categories.map(cat => ({
          name: cat.name,
          allocatedAmount: cat.allocatedAmount,
          description: cat.description || '',
        })),
        alerts: {
          thresholdPercentage: budget.alerts?.thresholdPercentage || 80,
          enabled: budget.alerts?.enabled || true,
          emailNotifications: budget.alerts?.emailNotifications || true,
          pushNotifications: budget.alerts?.pushNotifications || true,
        },
        status: budget.status,
        fiscalYear: budget.fiscalYear,
      };
    } else {
      return {
        projectId,
        totalBudget: 0,
        currency: 'INR',
        categories: [{ name: 'General', allocatedAmount: 0, description: '' }],
        alerts: {
          thresholdPercentage: 80,
          enabled: true,
          emailNotifications: true,
          pushNotifications: true,
        },
        status: 'Active',
        fiscalYear: currentYear,
      };
    }
  }, [isEditMode, budget, projectId, currentYear]);

  const form = useForm<BudgetFormData>({
    resolver: yupResolver(budgetSchema),
    defaultValues,
  });

  // Reset form when budget changes or mode changes
  React.useEffect(() => {
    if (isOpen) {
      form.reset(defaultValues);
    }
  }, [form, defaultValues, isOpen]);

  const {
    fields: categoryFields,
    append: appendCategory,
    remove: removeCategory,
    replace: replaceCategories,
  } = useFieldArray({
    control: form.control,
    name: 'categories',
  });

  // Ensure categories are properly loaded when editing
  React.useEffect(() => {
    if (isEditMode && budget && budget.categories) {
      const formattedCategories = budget.categories.map(cat => ({
        name: cat.name,
        allocatedAmount: cat.allocatedAmount,
        description: cat.description || '',
      }));

      replaceCategories(formattedCategories);
    }
  }, [isEditMode, budget, replaceCategories]);

  const createBudgetMutation = useMutation({
    mutationFn: async (data: BudgetFormData) => {
      const createData: IBudgetCreateData = {
        projectId: data.projectId,
        totalBudget: data.totalBudget,
        currency: (data.currency as IBudgetCreateData['currency']) ?? 'USD',
        categories: (data.categories ?? []) as IBudgetCategory[],
        alerts: {
          thresholdPercentage: data.alerts?.thresholdPercentage ?? 80,
          enabled: data.alerts?.enabled ?? true,
          emailNotifications: data.alerts?.emailNotifications ?? true,
          pushNotifications: data.alerts?.pushNotifications ?? true,
        },
        status: (data.status as IBudgetCreateData['status']) ?? 'Active',
        fiscalYear: data.fiscalYear,
      };
      return BudgetService.createBudget(createData);
    },
    onSuccess: () => {
      toast({ title: 'Budget created successfully!' });
      queryClient.invalidateQueries({ queryKey: ['budget', projectId] });
      onBudgetSaved();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create budget',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const updateBudgetMutation = useMutation({
    mutationFn: async (data: BudgetFormData) => {
      if (!budget) throw new Error('Budget not found');

      console.log('Updating budget with data:', data);

      const updateData: IBudgetUpdateData = {
        totalBudget: data.totalBudget,
        currency: (data.currency as IBudgetUpdateData['currency']) ?? 'USD',
        categories: (data.categories ?? []) as IBudgetCategory[],
        alerts: {
          thresholdPercentage: data.alerts?.thresholdPercentage ?? 80,
          enabled: data.alerts?.enabled ?? true,
          emailNotifications: data.alerts?.emailNotifications ?? true,
          pushNotifications: data.alerts?.pushNotifications ?? true,
        },
        status: (data.status as IBudgetUpdateData['status']) ?? 'Active',
        fiscalYear: data.fiscalYear,
      };
      return BudgetService.updateBudget(budget.budgetId, updateData);
    },
    onSuccess: () => {
      toast({ title: 'Budget updated successfully!' });
      queryClient.invalidateQueries({ queryKey: ['budget', projectId] });
      onBudgetSaved();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to update budget',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: BudgetFormData) => {
    console.log('Form submission data:', data);

    const totalAllocated = (data.categories ?? []).reduce(
      (sum, cat) => sum + (cat?.allocatedAmount || 0),
      0
    );

    if (totalAllocated > data.totalBudget) {
      setCategoryError('Total allocated amount cannot exceed total budget');
      return;
    }

    setCategoryError(null);

    if (isEditMode) {
      updateBudgetMutation.mutate(data);
    } else {
      createBudgetMutation.mutate(data);
    }
  };

  const addCategory = () => {
    appendCategory({ name: '', allocatedAmount: 0, description: '' });
  };

  const removeCategoryField = (index: number) => {
    removeCategory(index);
  };

  const watchedCategories = form.watch('categories') || [];
  const totalAllocated = watchedCategories.reduce(
    (sum: number, cat: { allocatedAmount: number }) => sum + (cat?.allocatedAmount || 0),
    0
  );
  const totalBudget = form.watch('totalBudget') ?? 0;
  const remainingBudget = totalBudget - totalAllocated;
  const selectedCurrency = form.watch('currency');

  // Get currency symbol based on selected currency
  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'INR':
        return <BadgeIndianRupee className="h-4 w-4" />;
      case 'USD':
        return <DollarSign className="h-4 w-4" />;
      case 'EUR':
        return <Euro className="h-4 w-4" />;
      case 'GBP':
        return <PoundSterling className="h-4 w-4" />;
      case 'JPY':
        return <JapaneseYen className="h-4 w-4" />;
      default:
        return <CircleDollarSign className="h-4 w-4" />;
    }
  };

  // Get currency symbol as string
  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case 'INR':
        return '₹';
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      default:
        return '$';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <CircleCheck className="h-4 w-4 text-success" />;
      case 'Completed':
        return <Check className="h-4 w-4 text-primary" />;
      case 'Exceeded':
        return <CircleAlert className="h-4 w-4 text-destructive" />;
      case 'Frozen':
        return <CirclePause className="h-4 w-4 text-muted-foreground" />;
      default:
        return <CircleCheck className="h-4 w-4 text-success" />;
    }
  };

  const isPending = isEditMode ? updateBudgetMutation.isPending : createBudgetMutation.isPending;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <div className="w-full theme-scrollbar">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-3 bg-primary/10 rounded-full theme-shadow-sm">
            <BarChart3 className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold">
              {isEditMode ? 'Edit Budget' : 'Create New Budget'}
            </h2>
            <p className="text-sm text-muted-foreground">
              {isEditMode
                ? 'Update budget settings and allocations'
                : 'Set up budget tracking for your project'}
            </p>
          </div>
        </div>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                General
              </TabsTrigger>
              <TabsTrigger value="categories" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Categories
              </TabsTrigger>
              <TabsTrigger value="alerts" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Alerts
              </TabsTrigger>
            </TabsList>

            {/* General Tab */}
            <TabsContent value="general" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-5">
                  {/* Total Budget Field */}
                  <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm">
                    <div className="space-y-3">
                      <Label
                        htmlFor="totalBudget"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        {getCurrencyIcon(selectedCurrency)}
                        <span>Total Budget *</span>
                      </Label>
                      <div className="relative">
                        <Input
                          id="totalBudget"
                          type="number"
                          step="0.01"
                          {...form.register('totalBudget', { valueAsNumber: true })}
                          placeholder="0.00"
                          className="pl-8 theme-focus theme-transition text-lg font-medium"
                        />
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                          {getCurrencySymbol(selectedCurrency)}
                        </span>
                      </div>
                      {form.formState.errors.totalBudget && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.totalBudget.message}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Currency Field */}
                  <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm">
                    <div className="space-y-3">
                      <Label
                        htmlFor="currency"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        <CircleDollarSign className="h-4 w-4 text-primary" />
                        <span>Currency *</span>
                      </Label>
                      <ReactSelect
                        options={CURRENCY_OPTIONS.map(currency => ({
                          value: currency.value,
                          label: currency.label,
                        }))}
                        value={
                          CURRENCY_OPTIONS.find(c => c.value === form.watch('currency')) || null
                        }
                        onChange={option => {
                          if (option) {
                            form.setValue('currency', (option as Option).value.toString());
                          }
                        }}
                        placeholder="Select currency"
                        isSearchable={true}
                        isClearable={false}
                      />
                      {form.formState.errors.currency && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.currency.message}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Status Field */}
                  <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm">
                    <div className="space-y-3">
                      <Label
                        htmlFor="status"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        {getStatusIcon(form.watch('status'))}
                        <span>Status *</span>
                      </Label>
                      <ReactSelect
                        options={[
                          { value: 'Active', label: 'Active' },
                          { value: 'Completed', label: 'Completed' },
                          { value: 'Exceeded', label: 'Exceeded' },
                          { value: 'Frozen', label: 'Frozen' },
                        ]}
                        value={{ value: form.watch('status'), label: form.watch('status') }}
                        onChange={option => {
                          if (option) {
                            form.setValue('status', (option as Option).value.toString());
                          }
                        }}
                        placeholder="Select status"
                        isSearchable={true}
                        isClearable={false}
                      />
                      {form.formState.errors.status && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.status.message}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Fiscal Year Field */}
                  <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm">
                    <div className="space-y-3">
                      <Label
                        htmlFor="fiscalYear"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        <Calendar className="h-4 w-4 text-primary" />
                        <span>Fiscal Year</span>
                      </Label>
                      <Input
                        id="fiscalYear"
                        type="number"
                        {...form.register('fiscalYear', { valueAsNumber: true })}
                        placeholder={currentYear.toString()}
                        className="theme-focus theme-transition"
                      />
                      {form.formState.errors.fiscalYear && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.fiscalYear.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="theme-surface-elevated rounded-xl p-6 theme-shadow-md">
                    <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      Budget Summary
                    </h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-4 bg-background/50 rounded-lg border border-border/50">
                        <div className="flex items-center gap-2">
                          {getCurrencyIcon(selectedCurrency)}
                          <span className="text-sm font-medium">Total Budget:</span>
                        </div>
                        <span className="font-medium text-lg">
                          {getCurrencySymbol(selectedCurrency)}
                          {totalBudget.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-background/50 rounded-lg border border-border/50">
                        <div className="flex items-center gap-2">
                          <BarChart3 className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium">Allocated:</span>
                        </div>
                        <span className="font-medium text-lg">
                          {getCurrencySymbol(selectedCurrency)}
                          {totalAllocated.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-background/50 rounded-lg border border-border/50">
                        <div className="flex items-center gap-2">
                          {remainingBudget < 0 ? (
                            <CircleAlert className="h-4 w-4 text-destructive" />
                          ) : (
                            <CircleCheck className="h-4 w-4 text-success" />
                          )}
                          <span className="text-sm font-medium">Remaining:</span>
                        </div>
                        <span
                          className={cn(
                            'font-medium text-lg',
                            remainingBudget < 0 ? 'text-destructive' : 'text-success'
                          )}
                        >
                          {getCurrencySymbol(selectedCurrency)}
                          {remainingBudget.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    {remainingBudget < 0 && (
                      <Alert className="mt-4 border-destructive/50 bg-destructive/10">
                        <AlertTriangle className="h-4 w-4 text-destructive" />
                        <AlertDescription className="text-destructive">
                          Total allocated amount exceeds total budget
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Categories Tab */}
            <TabsContent value="categories" className="space-y-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  Budget Categories
                </h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCategory}
                  className="theme-hover-primary theme-transition"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Category
                </Button>
              </div>

              {categoryError && (
                <Alert className="mb-6 border-destructive/50 bg-destructive/10">
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                  <AlertDescription className="text-destructive">{categoryError}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                {categoryFields.map((field, index) => (
                  <div
                    key={field.id}
                    className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm theme-transition hover:shadow-md"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                      <div className="space-y-1.5">
                        <Label className="text-sm font-medium">Category Name *</Label>
                        <Input
                          {...form.register(`categories.${index}.name`)}
                          placeholder="Category name"
                          className="theme-focus theme-transition"
                        />
                        {form.formState.errors.categories?.[index]?.name && (
                          <p className="text-sm text-destructive">
                            {form.formState.errors.categories[index].name?.message}
                          </p>
                        )}
                      </div>
                      <div className="space-y-1.5">
                        <Label className="text-sm font-medium">Allocated Amount *</Label>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            {...form.register(`categories.${index}.allocatedAmount`, {
                              valueAsNumber: true,
                            })}
                            placeholder="0.00"
                            className="pl-8 theme-focus theme-transition"
                          />
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                            {getCurrencySymbol(selectedCurrency)}
                          </span>
                        </div>
                        {form.formState.errors.categories?.[index]?.allocatedAmount && (
                          <p className="text-sm text-destructive">
                            {form.formState.errors.categories[index].allocatedAmount?.message}
                          </p>
                        )}
                      </div>
                      <div className="flex items-end gap-3">
                        <div className="flex-1 space-y-1.5">
                          <Label className="text-sm font-medium">Description</Label>
                          <Input
                            {...form.register(`categories.${index}.description`)}
                            placeholder="Optional description"
                            className="theme-focus theme-transition"
                          />
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeCategoryField(index)}
                          disabled={categoryFields.length === 1}
                          className="h-10 w-10 rounded-full hover:bg-destructive/10 hover:text-destructive theme-transition"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Alerts Tab */}
            <TabsContent value="alerts" className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    {form.watch('alerts.enabled') ? (
                      <BellRing className="h-5 w-5 text-primary" />
                    ) : (
                      <BellOff className="h-5 w-5 text-muted-foreground" />
                    )}
                    Alert Settings
                  </h3>
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor="alerts-main-toggle"
                      className="text-sm font-medium cursor-pointer"
                    >
                      {form.watch('alerts.enabled') ? 'Alerts Enabled' : 'Alerts Disabled'}
                    </Label>
                    <Switch
                      id="alerts-main-toggle"
                      checked={form.watch('alerts.enabled')}
                      onCheckedChange={checked => form.setValue('alerts.enabled', checked)}
                      className="theme-focus data-[state=checked]:bg-primary"
                    />
                  </div>
                </div>

                <div
                  className={cn(
                    'transition-opacity duration-200',
                    !form.watch('alerts.enabled') && 'opacity-60'
                  )}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left Column */}
                    <div className="space-y-6">
                      {/* Threshold Setting */}
                      <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm border border-border/50">
                        <div className="flex items-center gap-2 mb-4">
                          <Percent className="h-5 w-5 text-primary" />
                          <h4 className="font-medium">Threshold Settings</h4>
                        </div>

                        <div className="space-y-4">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="thresholdPercentage" className="text-sm font-medium">
                                Budget Usage Threshold
                              </Label>
                              <span className="text-sm font-medium text-primary">
                                {form.watch('alerts.thresholdPercentage')}%
                              </span>
                            </div>

                            <div className="relative">
                              <Input
                                id="thresholdPercentage"
                                type="range"
                                min="1"
                                max="100"
                                step="1"
                                {...form.register('alerts.thresholdPercentage', {
                                  valueAsNumber: true,
                                })}
                                className="theme-focus theme-transition cursor-pointer"
                              />
                              <div className="absolute -bottom-5 left-0 right-0 flex justify-between text-xs text-muted-foreground">
                                <span>1%</span>
                                <span>50%</span>
                                <span>100%</span>
                              </div>
                            </div>
                          </div>

                          <div className="pt-4">
                            <p className="text-sm text-muted-foreground">
                              You will be alerted when budget usage reaches{' '}
                              {form.watch('alerts.thresholdPercentage')}% of the total budget.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-6">
                      {/* Notification Channels */}
                      <div className="theme-surface-elevated rounded-xl p-5 theme-shadow-sm border border-border/50">
                        <div className="flex items-center gap-2 mb-4">
                          <Bell className="h-5 w-5 text-primary" />
                          <h4 className="font-medium">Notification Channels</h4>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-center justify-between p-3 rounded-lg bg-background/80 border border-border/50">
                            <div className="flex items-center gap-3">
                              <Mail className="h-5 w-5 text-primary" />
                              <div>
                                <p className="text-sm font-medium">Email Notifications</p>
                                <p className="text-xs text-muted-foreground">
                                  Receive alerts via email
                                </p>
                              </div>
                            </div>
                            <Switch
                              id="email-notifications"
                              checked={form.watch('alerts.emailNotifications')}
                              onCheckedChange={checked =>
                                form.setValue('alerts.emailNotifications', checked)
                              }
                              className="theme-focus data-[state=checked]:bg-primary"
                              disabled={!form.watch('alerts.enabled')}
                            />
                          </div>

                          <div className="flex items-center justify-between p-3 rounded-lg bg-background/80 border border-border/50">
                            <div className="flex items-center gap-3">
                              <MessageSquare className="h-5 w-5 text-primary" />
                              <div>
                                <p className="text-sm font-medium">Push Notifications</p>
                                <p className="text-xs text-muted-foreground">
                                  Receive alerts in-app
                                </p>
                              </div>
                            </div>
                            <Switch
                              id="push-notifications"
                              checked={form.watch('alerts.pushNotifications')}
                              onCheckedChange={checked =>
                                form.setValue('alerts.pushNotifications', checked)
                              }
                              className="theme-focus data-[state=checked]:bg-primary"
                              disabled={!form.watch('alerts.enabled')}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 pt-6">
            <Button type="button" variant="outline" onClick={onClose} className="theme-transition">
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="theme-transition theme-shadow-sm hover:theme-shadow"
            >
              {isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  {isEditMode ? 'Update Budget' : 'Create Budget'}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
