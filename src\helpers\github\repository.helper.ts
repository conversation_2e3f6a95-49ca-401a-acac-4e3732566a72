import { GitHubRepository } from '@/models/GitHubRepository';
import { Integration } from '@/models/Integration';
import { GitHubService } from '@/services/GitHub.service';
import { NotificationService } from '@/services/Notification.service';
import { GitHubWebhook } from '@/models/GitHubWebhook';
import jwt from 'jsonwebtoken';
import axios from 'axios';
import crypto from 'crypto';

export class GitHubRepositoryHelper {
  static async refreshGitHubToken(integration: any) {
    const appId = process.env.GITHUB_APP_ID;
    const privateKey = process.env.GITHUB_APP_PRIVATE_KEY;

    if (!appId || !privateKey) {
      throw new Error('GitHub App credentials not configured');
    }

    const installationId = integration.metadata?.installationId;
    if (!installationId) {
      throw new Error('Installation ID not found');
    }

    try {
      const now = Math.floor(Date.now() / 1000);
      const payload = {
        iat: now - 60,
        exp: now + 600,
        iss: appId,
      };

      const decodedPrivateKey = privateKey.includes('-----BEGIN')
        ? privateKey
        : Buffer.from(privateKey, 'base64').toString('utf8');

      const jwtToken = jwt.sign(payload, decodedPrivateKey, { algorithm: 'RS256' });

      const response = await axios.post(
        `https://api.github.com/app/installations/${installationId}/access_tokens`,
        {},
        {
          headers: {
            Authorization: `Bearer ${jwtToken}`,
            Accept: 'application/vnd.github.v3+json',
          },
        }
      );

      integration.accessToken = response.data.token;
      integration.expiresAt = new Date(response.data.expires_at);
      integration.lastSyncedAt = new Date();

      if (response.data.permissions) {
        integration.metadata.permissions = response.data.permissions;
      }
      if (response.data.repository_selection) {
        integration.metadata.repositorySelection = response.data.repository_selection;
      }

      await integration.save();

      return integration;
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('GitHub App installation not found. Please reconnect the app.');
      }
      throw new Error(`Failed to refresh GitHub token: ${error.message}`);
    }
  }

  static async ensureValidToken(integration: any) {
    const bufferTime = 5 * 60 * 1000;
    const expiryWithBuffer = new Date(integration.expiresAt.getTime() - bufferTime);

    if (integration.expiresAt && new Date() > expiryWithBuffer) {
      return await this.refreshGitHubToken(integration);
    }

    return integration;
  }

  static async getAvailableRepositoriesWithStatus(
    userId: string,
    page: number = 1,
    perPage: number = 30,
    searchQuery: string = ''
  ) {
    let integration = await Integration.findOne({
      userId,
      provider: 'github',
    });

    if (!integration) {
      throw new Error('GitHub not connected');
    }

    try {
      integration = await this.ensureValidToken(integration);

      const { repositories, hasMore, totalCount } = await GitHubService.getInstallationRepositories(
        integration.accessToken,
        page,
        perPage,
        searchQuery
      );

      const filteredRepositories = repositories;
      const filteredTotal = totalCount;

      const connectedRepos = await GitHubRepository.find({
        userId,
        isConnected: true,
      }).select('repositoryId');

      const connectedRepoIds = new Set(connectedRepos.map(repo => repo.repositoryId));

      const repositoriesWithStatus = filteredRepositories.map(repo => ({
        id: repo.id,
        name: repo.name,
        full_name: repo.full_name,
        private: repo.private,
        html_url: repo.html_url,
        description: repo.description,
        language: repo.language,
        stargazers_count: repo.stargazers_count,
        forks_count: repo.forks_count,
        updated_at: repo.updated_at,
        default_branch: repo.default_branch,
        isConnected: connectedRepoIds.has(repo.id.toString()),
        permissions: repo.permissions,
        open_issues_count: repo.open_issues_count,
      }));

      const totalPages = Math.ceil(filteredTotal / perPage);

      return {
        repositories: repositoriesWithStatus,
        hasMore: searchQuery.trim() ? filteredRepositories.length === perPage : hasMore,
        page,
        perPage,
        total: filteredTotal,
        totalPages,
      };
    } catch (error: any) {
      if (error.message.includes('installation not found')) {
        throw new Error('GitHub App access has been revoked. Please reconnect.');
      }

      throw error;
    }
  }

  static async connectRepository(userId: string, organizationId: string, repositoryId: string) {
    let integration = await Integration.findOne({
      userId,
      provider: 'github',
    });

    if (!integration) {
      throw new Error('GitHub not connected');
    }

    try {
      integration = await this.ensureValidToken(integration);
      const existingRepo = await GitHubRepository.findOne({
        userId,
        repositoryId: repositoryId.toString(),
      });

      if (existingRepo) {
        if (existingRepo.isConnected) {
          throw new Error('Repository already connected');
        } else {
          existingRepo.isConnected = true;
          existingRepo.lastSyncedAt = new Date();
          await existingRepo.save();
          return existingRepo;
        }
      }

      const { repositories } = await GitHubService.getInstallationRepositories(
        integration.accessToken,
        1,
        100
      );

      const repoData = repositories.find(repo => repo.id.toString() === repositoryId.toString());

      if (!repoData) {
        throw new Error('Repository not found or not accessible');
      }

      const repository = await GitHubRepository.create({
        integrationId: integration._id,
        userId,
        organizationId,
        repositoryId: repositoryId.toString(),
        name: repoData.name,
        fullName: repoData.full_name,
        description: repoData.description,
        private: repoData.private,
        htmlUrl: repoData.html_url,
        cloneUrl: repoData.clone_url,
        defaultBranch: repoData.default_branch,
        language: repoData.language,
        stargazersCount: repoData.stargazers_count,
        forksCount: repoData.forks_count,
        openIssuesCount: repoData.open_issues_count,
        owner: {
          login: repoData.owner.login,
          id: repoData.owner.id.toString(),
          avatarUrl: repoData.owner.avatar_url,
          type: repoData.owner.type,
        },
        permissions: repoData.permissions || {
          admin: false,
          maintain: false,
          push: false,
          triage: false,
          pull: true,
        },
        isConnected: true,
        lastSyncedAt: new Date(),
        syncSettings: {
          autoCreateTasks: true,
          syncIssues: true,
          syncPullRequests: false,
          labelMapping: new Map(),
        },
      });

      if (repository.syncSettings.autoCreateTasks) {
        try {
          await this.createWebhookForRepository(repository, integration, userId, organizationId);
        } catch (webhookError: any) {
          // do nothing
        }
      }

      await NotificationService.createNotification({
        userId,
        title: 'Repository Connected',
        description: `Successfully connected repository: ${repoData.full_name}`,
        type: 'integration',
        link: '/github-hub',
      });

      return repository;
    } catch (error: any) {
      if (error.message.includes('installation not found')) {
        throw new Error('GitHub App access has been revoked. Please reconnect.');
      }

      throw error;
    }
  }

  static async disconnectRepository(userId: string, repositoryId: string) {
    const repository = await GitHubRepository.findOne({
      userId,
      repositoryId: repositoryId.toString(),
    });

    if (!repository) {
      throw new Error('Repository not found');
    }

    repository.isConnected = false;
    await repository.save();

    if (repository.webhookId) {
      let integration = await Integration.findOne({
        userId,
        provider: 'github',
      });

      if (integration) {
        try {
          integration = await this.ensureValidToken(integration);

          const [owner, repo] = repository.fullName.split('/');
          await GitHubService.deleteWebhook(
            integration.accessToken,
            owner,
            repo,
            repository.webhookId
          );
        } catch (error: any) {
          throw new Error(`Failed to delete webhook: ${error.message}`);
        }
      }

      // Remove webhook record
      await GitHubWebhook.deleteOne({
        repositoryId: repository._id,
      });
    }

    // Create notification
    await NotificationService.createNotification({
      userId,
      title: 'Repository Disconnected',
      description: `Disconnected repository: ${repository.fullName}`,
      type: 'integration',
    });

    return { success: true };
  }

  static async getConnectedRepositories(userId: string) {
    const repositories = await GitHubRepository.find({
      userId,
      isConnected: true,
    }).sort({ updatedAt: -1 });

    return { repositories };
  }

  static async validateRepositoryAccess(userId: string, repositoryId: string) {
    const repository = await GitHubRepository.findOne({
      userId,
      repositoryId,
      isConnected: true,
    });

    if (!repository) {
      throw new Error('Repository not found or not connected');
    }

    return repository;
  }

  static async getRepositoryWithIntegration(userId: string, repositoryId: string) {
    const repository = await this.validateRepositoryAccess(userId, repositoryId);

    let integration = await Integration.findOne({
      userId,
      provider: 'github',
    });

    if (!integration) {
      throw new Error('GitHub not connected');
    }

    integration = await this.ensureValidToken(integration);

    return { repository, integration };
  }

  static formatRepositoryResponse(repository: any) {
    return {
      id: repository.repositoryId,
      name: repository.name,
      fullName: repository.fullName,
      htmlUrl: repository.htmlUrl,
    };
  }

  static async syncRepositoryData(userId: string, repositoryId: string) {
    try {
      const { repository, integration } = await this.getRepositoryWithIntegration(
        userId,
        repositoryId
      );

      const { repositories } = await GitHubService.getInstallationRepositories(
        integration.accessToken,
        1,
        100
      );

      const repoData = repositories.find(repo => repo.id.toString() === repositoryId);

      if (!repoData) {
        throw new Error('Repository not found in GitHub');
      }

      repository.name = repoData.name;
      repository.description = repoData.description;
      repository.stargazersCount = repoData.stargazers_count;
      repository.forksCount = repoData.forks_count;
      repository.openIssuesCount = repoData.open_issues_count;
      repository.lastSyncedAt = new Date();

      await repository.save();

      return repository;
    } catch (error: any) {
      if (error.message.includes('installation not found')) {
        throw new Error('GitHub App access has been revoked. Please reconnect.');
      }

      throw error;
    }
  }

  static async createWebhookForRepository(
    repository: any,
    integration: any,
    userId: string,
    organizationId?: string
  ) {
    // Check if webhook already exists
    const existingWebhook = await GitHubWebhook.findOne({
      repositoryId: repository._id,
      active: true,
    });

    if (existingWebhook) {
      return existingWebhook;
    }

    // Generate webhook secret
    const webhookSecret = crypto.randomBytes(32).toString('hex');
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/webhooks/github`;
    const events = ['issues', 'pull_request'];

    // Create webhook on GitHub
    const [owner, repo] = repository.fullName.split('/');
    const githubWebhook = await GitHubService.createWebhook(
      integration.accessToken,
      owner,
      repo,
      webhookUrl,
      webhookSecret,
      events
    );

    // Save webhook to database
    const webhook = new GitHubWebhook({
      repositoryId: repository._id,
      userId,
      organizationId,
      githubWebhookId: githubWebhook.id.toString(),
      webhookUrl,
      secret: webhookSecret,
      events,
      active: true,
      config: {
        url: webhookUrl,
        contentType: 'application/json',
        insecureSsl: false,
      },
    });

    await webhook.save();

    // Update repository with webhook info
    repository.webhookId = githubWebhook.id.toString();
    repository.webhookSecret = webhookSecret;
    await repository.save();

    return webhook;
  }
}
