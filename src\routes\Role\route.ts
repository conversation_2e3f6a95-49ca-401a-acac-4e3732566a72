import { Role } from '@/models/Role';
import { Organization } from '@/models/organization';
// import { User } from '@/models/User';
import { ActivityLog } from '@/models/ActivityLog';
import mongoose from 'mongoose';
import { connectDB } from '@/Utility/db';

// Helper function to check if user has owner permissions
async function checkOwnerPermissions(userId: string, organizationId: string): Promise<boolean> {
  const organization = await Organization.findById(organizationId);
  if (!organization) return false;

  // Check if user is the original owner OR has Owner role in members
  const isOriginalOwner = organization.ownerId?.toString() === userId;
  const hasOwnerRole = organization.members.some(
    member => member.userId.toString() === userId && member.role === 'Owner'
  );

  return isOriginalOwner || hasOwnerRole;
}

// Helper function to get default roles
async function getDefaultRoles(organizationId: string) {
  const organization = await Organization.findById(organizationId)
    .populate('ownerId', 'name email image')
    .populate('members.userId', 'name email image');

  if (!organization) return [];

  // Get all users with Owner role (including original owner and members with Owner role)
  const ownerUsers: Array<{ id: any; name: any; email: any; assignedAt: any }> = [];

  // Add original owner if exists
  if (organization.ownerId) {
    ownerUsers.push({
      id: organization.ownerId._id,
      name: organization.ownerId.name,
      email: organization.ownerId.email,
      assignedAt: organization.createdAt,
    });
  }

  // Add members with Owner role (but avoid duplicating the original owner)
  const ownerMembers = organization.members
    .filter(
      (member: any) =>
        member.role === 'Owner' &&
        member.userId._id.toString() !== organization.ownerId?._id.toString()
    )
    .map((member: any) => ({
      id: member.userId._id,
      name: member.userId.name,
      email: member.userId.email,
      assignedAt: member.joinedAt,
    }));

  ownerUsers.push(...ownerMembers);

  // Get members with Member role only
  const memberUsers = organization.members
    .filter((member: any) => member.role === 'Member')
    .map((member: any) => ({
      id: member.userId._id,
      name: member.userId.name,
      email: member.userId.email,
      assignedAt: member.joinedAt,
    }));

  // Get members with Guest role only
  const guestUsers = organization.members
    .filter((member: any) => member.role === 'Guest')
    .map((member: any) => ({
      id: member.userId._id,
      name: member.userId.name,
      email: member.userId.email,
      assignedAt: member.joinedAt,
    }));

  return [
    {
      _id: 'owner',
      id: 'owner',
      name: 'owner',
      displayName: 'Owner',
      description: 'Full access to all features and settings',
      type: 'system',
      permissions: {
        tasks: { read: true, write: true, delete: true, admin: true, assign: true, manage: true },
        projects: {
          read: true,
          write: true,
          delete: true,
          admin: true,
          create: true,
          manage: true,
          archive: true,
        },
        users: { read: true, write: true, delete: true, admin: true, invite: true, manage: true },
        organization: {
          read: true,
          write: true,
          delete: true,
          admin: true,
          manage: true,
          billing: true,
        },
        integrations: { read: true, write: true, delete: true, manage: true, configure: true },
      },
      assignedUsers: ownerUsers,
      isSystem: true,
      isDefault: false,
      metadata: {
        color: '#ef4444',
        icon: '👑',
      },
    },
    {
      _id: 'member',
      id: 'member',
      name: 'member',
      displayName: 'Member',
      description: 'Can create and manage tasks and projects',
      type: 'system',
      permissions: {
        tasks: {
          read: true,
          write: true,
          delete: false,
          admin: false,
          assign: true,
          manage: false,
        },
        projects: {
          read: true,
          write: true,
          delete: false,
          admin: false,
          create: true,
          manage: false,
          archive: false,
        },
        users: {
          read: true,
          write: false,
          delete: false,
          admin: false,
          invite: false,
          manage: false,
        },
        organization: {
          read: true,
          write: false,
          delete: false,
          admin: false,
          manage: false,
          billing: false,
        },
        integrations: { read: true, write: false, delete: false, manage: false, configure: false },
      },
      assignedUsers: memberUsers,
      isSystem: true,
      isDefault: true,
      metadata: {
        color: '#22c55e',
        icon: '👥',
      },
    },
    {
      _id: 'guest',
      id: 'guest',
      name: 'guest',
      displayName: 'Guest',
      description: 'View-only access to projects and tasks',
      type: 'system',
      permissions: {
        tasks: {
          read: true,
          write: false,
          delete: false,
          admin: false,
          assign: false,
          manage: false,
        },
        projects: {
          read: true,
          write: false,
          delete: false,
          admin: false,
          create: false,
          manage: false,
          archive: false,
        },
        users: {
          read: true,
          write: false,
          delete: false,
          admin: false,
          invite: false,
          manage: false,
        },
        organization: {
          read: false,
          write: false,
          delete: false,
          admin: false,
          manage: false,
          billing: false,
        },
        integrations: { read: false, write: false, delete: false, manage: false, configure: false },
      },
      assignedUsers: guestUsers,
      isSystem: true,
      isDefault: false,
      metadata: {
        color: '#3b82f6',
        icon: '👁️',
      },
    },
  ];
}

// Get organization roles
export async function getOrganizationRoles(organizationId: string, userId: string) {
  try {
    await connectDB();

    // Check if user has access to organization
    const organization = await Organization.findById(organizationId);
    if (!organization) {
      throw new Error('Organization not found');
    }

    const isOwner = organization.ownerId?.toString() === userId;
    const isMember = organization.members.some(member => member.userId.toString() === userId);

    if (!isOwner && !isMember) {
      throw new Error('Access denied');
    }

    // Get default roles for the organization
    const defaultRoles = await getDefaultRoles(organizationId);

    // Get custom roles
    const customRoles = await Role.find({ organizationId })
      .populate('assignedUsers.userId', 'name email image')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    const allRoles = [...defaultRoles, ...customRoles];

    return { success: true, roles: allRoles };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch roles');
  }
}

// Create role
export async function createRole(roleData: any, userId: string) {
  try {
    await connectDB();

    const {
      name,
      displayName,
      description,
      type,
      permissions,
      restrictions,
      metadata,
      organizationId,
    } = roleData;

    // Validate required fields
    if (!name || !displayName) {
      throw new Error('Name and display name are required');
    }

    // Check if user has permission to create roles
    if (organizationId) {
      const hasPermission = await checkOwnerPermissions(userId, organizationId);
      if (!hasPermission) {
        throw new Error('Only organization owners can create custom roles');
      }
    }

    // Check if role name already exists in the organization
    const existingRole = await Role.findOne({
      name: name.toLowerCase(),
      organizationId: organizationId ? new mongoose.Types.ObjectId(organizationId) : undefined,
    });

    if (existingRole) {
      throw new Error('Role name already exists');
    }

    const role = await Role.createCustomRole(
      {
        name: name.toLowerCase(),
        displayName,
        description,
        type: type || 'custom',
        permissions: permissions || {},
        restrictions: restrictions || {},
        metadata: metadata || {},
        organizationId: organizationId ? new mongoose.Types.ObjectId(organizationId) : undefined,
      },
      userId
    );

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'create',
      resourceType: 'role',
      resourceId: role._id,
      description: `Created custom role: ${displayName}`,
      metadata: {
        roleName: name,
        roleType: type,
        organizationId,
      },
    });

    return { success: true, role, message: 'Role created successfully' };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to create role');
  }
}

// Update role
export async function updateRole(roleId: string, updates: any, userId: string) {
  try {
    await connectDB();

    const role = await Role.findById(roleId);

    if (!role) {
      throw new Error('Role not found');
    }

    // Check if role is system role
    if (role.isSystem) {
      throw new Error('Cannot modify system roles');
    }

    // Check if user has permission to update role
    if (role.organizationId) {
      const hasPermission = await checkOwnerPermissions(userId, role.organizationId.toString());
      if (!hasPermission) {
        throw new Error('Only organization owners can update custom roles');
      }
    }

    // Update role
    Object.assign(role, updates);
    await role.save();

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'update',
      resourceType: 'role',
      resourceId: role._id,
      description: `Updated custom role: ${role.displayName}`,
      metadata: {
        roleName: role.name,
        changes: updates,
      },
    });

    return { success: true, role, message: 'Role updated successfully' };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to update role');
  }
}

// Delete role
export async function deleteRole(roleId: string, userId: string) {
  try {
    await connectDB();

    const role = await Role.findById(roleId);

    if (!role) {
      throw new Error('Role not found');
    }

    // Check if role is system role
    if (role.isSystem) {
      throw new Error('Cannot delete system roles');
    }

    // Check if user has permission to delete role
    if (role.organizationId) {
      const hasPermission = await checkOwnerPermissions(userId, role.organizationId.toString());
      if (!hasPermission) {
        throw new Error('Only organization owners can delete custom roles');
      }
    }

    // Check if role has assigned users
    if (role.assignedUsers && role.assignedUsers.length > 0) {
      throw new Error('Cannot delete role with assigned users. Please reassign users first.');
    }

    await Role.findByIdAndDelete(roleId);

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'delete',
      resourceType: 'role',
      resourceId: role._id,
      description: `Deleted custom role: ${role.displayName}`,
      metadata: {
        roleName: role.name,
      },
    });

    return { success: true, message: 'Role deleted successfully' };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to delete role');
  }
}

// Assign users to role
export async function assignUsersToRole(roleId: string, userIds: string[], userId: string) {
  try {
    await connectDB();

    const role = await Role.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }

    // Check if user has permission
    if (role.organizationId) {
      const hasPermission = await checkOwnerPermissions(userId, role.organizationId.toString());
      if (!hasPermission) {
        throw new Error('Only organization owners can assign users to roles');
      }
    }

    // Assign users to role
    for (const assignUserId of userIds) {
      role.assignUser(assignUserId, userId);
    }

    await role.save();

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'update',
      resourceType: 'role',
      resourceId: role._id,
      description: `Assigned ${userIds.length} user(s) to role: ${role.displayName}`,
      metadata: {
        roleName: role.name,
        assignedUsers: userIds,
      },
    });

    return { success: true, message: `${userIds.length} user(s) assigned to role successfully` };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to assign users to role');
  }
}

// Get organization users
export async function getOrganizationUsers(
  organizationId: string,
  userId: string,
  search?: string
) {
  try {
    await connectDB();

    // Check if user has access to organization
    const organization = await Organization.findById(organizationId)
      .populate('ownerId', 'name email image')
      .populate('members.userId', 'name email image');

    if (!organization) {
      throw new Error('Organization not found');
    }

    const isOwner = organization.ownerId?._id.toString() === userId;
    const isMember = organization.members.some(
      (member: any) => member.userId._id.toString() === userId
    );

    if (!isOwner && !isMember) {
      throw new Error('Access denied');
    }

    // Get all users in the organization
    const users: Array<{
      id: any;
      name: any;
      email: any;
      image: any;
      roles: string[];
      joinedAt: any;
    }> = [];

    // Add owner
    if (organization.ownerId) {
      users.push({
        id: organization.ownerId._id,
        name: organization.ownerId.name,
        email: organization.ownerId.email,
        image: organization.ownerId.image,
        roles: ['Owner'],
        joinedAt: organization.createdAt,
      });
    }

    // Add members
    organization.members.forEach((member: any) => {
      // Avoid duplicating the owner if they're also in members
      if (member.userId._id.toString() !== organization.ownerId?._id.toString()) {
        users.push({
          id: member.userId._id,
          name: member.userId.name,
          email: member.userId.email,
          image: member.userId.image,
          roles: [member.role],
          joinedAt: member.joinedAt,
        });
      }
    });

    // Filter by search term if provided
    let filteredUsers = users;
    if (search) {
      filteredUsers = users.filter(
        user =>
          user.name.toLowerCase().includes(search.toLowerCase()) ||
          user.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    return { success: true, users: filteredUsers };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch users');
  }
}
