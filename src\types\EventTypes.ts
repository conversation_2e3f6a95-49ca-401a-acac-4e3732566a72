import { IEvent, IEvent<PERSON>ttendee, IEventR<PERSON>inder, IRecurrenceRule } from '@/models/Event';
import React from 'react';

export interface CalendarViewProps {
  events: IEvent[];
  currentDate: Date;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  onEventClick: (event: IEvent) => void;
  onEventDrop: (eventId: string, dropDate: Date) => void;
  onTimeSlotClick: (date: Date) => void;
  onEventResize: (eventId: string, newStart: Date, newEnd: Date) => void;
  onDragStart: (eventId: string) => void;
  onDragEnd: () => void;
  draggedEventId: string | null;
}
export interface EventManagerProps {
  userId?: string;
  organizationId?: string;
  projectId?: string;
  initialView?: 'month' | 'week' | 'day' | 'agenda';
  showTaskIntegration?: boolean;
  showRecurringEvents?: boolean;
  allowEventCreation?: boolean;
  allowEventEditing?: boolean;
  allowEventDeletion?: boolean;
  className?: string;
}

export interface EventFormData {
  title: string;
  description: string;
  location: string;
  startTime: Date;
  endTime: Date;
  isAllDay: boolean;
  timezone: string;
  attendees: IEventAttendee[];
  reminders: IEventReminder[];
  recurrence?: IRecurrenceRule;
  taskIds: string[];
  colorId: string;
  category: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  visibility: 'default' | 'public' | 'private' | 'confidential';
  attachments: File[];
}

export interface DraggableEventProps {
  event: IEvent;
  onClick: (event: IEvent) => void;
  style?: React.CSSProperties;
  onDragStart?: (eventId: string) => void;
  onDragEnd?: () => void;
}

export interface DroppableTimeSlotProps {
  date: Date;
  hour?: number;
  onDrop: (droppedItem: { id: string; event: IEvent }, dropDate: Date) => void;
  onClick: (date: Date) => void;
  children: React.ReactNode;
}
