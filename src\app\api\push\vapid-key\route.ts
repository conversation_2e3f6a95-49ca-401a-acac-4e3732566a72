import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/push/vapid-key');

app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.get('/', async (c) => {
  try {
    // Check if VAPID public key is configured
    const vapidPublicKey = process.env.VAPID_PUBLIC_KEY;
    if (!vapidPublicKey) {
      console.error('VAPID_PUBLIC_KEY not configured in environment variables');
      return c.json(
        {
          error: 'VAPID public key not configured',
          message: 'Push notifications are not available. Please configure VAPID keys.',
          configured: false,
        },
        500
      );
    }

    // Clean the key (remove any whitespace)
    const cleanPublicKey = vapidPublicKey.trim();

    console.log('Serving VAPID public key (length):', cleanPublicKey.length);

    return c.json({
      publicKey: cleanPublicKey,
      configured: true,
    });
  } catch (error: any) {
    console.error('VAPID key endpoint error:', error);
    return c.json(
      { error: 'Failed to get VAPID public key' },
      500
    );
  }
});

export const GET = handle(app);
