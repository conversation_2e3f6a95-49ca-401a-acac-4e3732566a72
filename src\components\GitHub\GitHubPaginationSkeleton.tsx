'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface GitHubPaginationSkeletonProps {
  className?: string;
}

const GitHubPaginationSkeleton: React.FC<GitHubPaginationSkeletonProps> = ({ className }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className={cn(
        'flex flex-col lg:flex-row items-center justify-between gap-6 py-6 px-4 rounded-xl theme-surface-elevated border border-border/50 backdrop-blur-sm',
        className
      )}
    >
      <div className="flex items-center gap-3">
        <div className="w-2 h-2 rounded-full bg-primary/30 animate-pulse"></div>
        <div className="h-4 w-48 bg-muted/50 rounded-md animate-pulse"></div>
      </div>

      <div className="flex items-center gap-2">
        <div className="h-10 w-10 bg-muted/50 rounded-lg animate-pulse"></div>
        <div className="h-10 w-10 bg-muted/50 rounded-lg animate-pulse"></div>

        <div className="flex items-center gap-1 mx-2">
          {[1, 2, 3, 4, 5].map(i => (
            <div
              key={i}
              className="h-10 w-10 bg-muted/50 rounded-lg animate-pulse"
              style={{
                animationDelay: `${i * 0.1}s`,
              }}
            ></div>
          ))}
        </div>

        <div className="h-10 w-10 bg-muted/50 rounded-lg animate-pulse"></div>
        <div className="h-10 w-10 bg-muted/50 rounded-lg animate-pulse"></div>
      </div>
    </motion.div>
  );
};

export default GitHubPaginationSkeleton;
