import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import {
  activityLogApiService,
  AnalyticsParams,
  AnalyticsData,
} from '@/services/ActivityLogApi.service';
import { ACTIVITY_LOG_QUERY_KEYS } from './useActivityLogs';

export interface UseActivityAnalyticsOptions {
  params?: AnalyticsParams;
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

export interface UseActivityAnalyticsReturn {
  data: AnalyticsData | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  isFetching: boolean;
  isRefetching: boolean;
  // Computed analytics
  totalActions: number;
  mostActiveAction: string | null;
  mostActiveResource: string | null;
  activeUsersCount: number;
  hasData: boolean;
}

export const useActivityAnalytics = (
  options: UseActivityAnalyticsOptions = {}
): UseActivityAnalyticsReturn => {
  const { params = {}, enabled = true, staleTime = 2 * 60 * 1000 } = options;

  const queryKey = useMemo(() => ACTIVITY_LOG_QUERY_KEYS.analytics(params), [params]);

  const {
    data: response,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    isRefetching,
  } = useQuery({
    queryKey,
    queryFn: () => activityLogApiService.getAnalytics(params),
    enabled,
    staleTime,
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx)
      if (error?.response?.status && error.response.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const data = response?.analytics;

  // Computed values
  const computedValues = useMemo(() => {
    if (!data) {
      return {
        totalActions: 0,
        mostActiveAction: null,
        mostActiveResource: null,
        activeUsersCount: 0,
        hasData: false,
      };
    }

    // Find most active action
    const mostActiveAction =
      Object.entries(data.actionBreakdown).sort(([, a], [, b]) => b - a)[0]?.[0] || null;

    // Find most active resource
    const mostActiveResource =
      Object.entries(data.resourceBreakdown).sort(([, a], [, b]) => b - a)[0]?.[0] || null;

    return {
      totalActions: data.totalActions,
      mostActiveAction,
      mostActiveResource,
      activeUsersCount: data.topUsers.length,
      hasData: data.totalActions > 0,
    };
  }, [data]);

  return {
    data,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
    isFetching,
    isRefetching,
    ...computedValues,
  };
};

// Hook for analytics with date range filtering
export interface UseActivityAnalyticsWithDateRangeOptions {
  userId?: string;
  organizationId?: string;
  dateRange?: { start: Date; end: Date };
  enabled?: boolean;
}

export const useActivityAnalyticsWithDateRange = (
  options: UseActivityAnalyticsWithDateRangeOptions = {}
) => {
  const { userId, organizationId, dateRange, enabled = true } = options;

  const params: AnalyticsParams = useMemo(
    () => ({
      userId,
      organizationId,
      startDate: dateRange?.start,
      endDate: dateRange?.end,
      groupBy: 'day',
    }),
    [userId, organizationId, dateRange]
  );

  return useActivityAnalytics({
    params,
    enabled: enabled && (!!userId || !!organizationId),
  });
};

// Hook for real-time analytics (shorter cache time)
export const useRealTimeActivityAnalytics = (options: UseActivityAnalyticsOptions = {}) => {
  return useActivityAnalytics({
    ...options,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 60 * 1000, // 1 minute
  });
};

// Hook for analytics chart data formatting
export interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
}

export interface UseAnalyticsChartsReturn {
  actionChartData: ChartDataPoint[];
  resourceChartData: ChartDataPoint[];
  dailyActivityChartData: ChartDataPoint[];
  topUsersChartData: ChartDataPoint[];
  isLoading: boolean;
  error: Error | null;
}

export const useAnalyticsCharts = (
  options: UseActivityAnalyticsOptions = {}
): UseAnalyticsChartsReturn => {
  const { data, isLoading, error } = useActivityAnalytics(options);

  const chartData = useMemo(() => {
    if (!data) {
      return {
        actionChartData: [],
        resourceChartData: [],
        dailyActivityChartData: [],
        topUsersChartData: [],
      };
    }

    // Format action breakdown for charts
    const actionChartData: ChartDataPoint[] = Object.entries(data.actionBreakdown)
      .map(([action, count]) => ({
        name: action,
        value: count,
      }))
      .sort((a, b) => b.value - a.value);

    // Format resource breakdown for charts
    const resourceChartData: ChartDataPoint[] = Object.entries(data.resourceBreakdown)
      .map(([resource, count]) => ({
        name: resource,
        value: count,
      }))
      .sort((a, b) => b.value - a.value);

    // Format daily activity for charts
    const dailyActivityChartData: ChartDataPoint[] = data.dailyActivity
      .map(item => ({
        name: new Date(item.date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        }),
        value: item.count,
        date: item.date,
      }))
      .sort((a, b) => new Date(a.date!).getTime() - new Date(b.date!).getTime());

    // Format top users for charts
    const topUsersChartData: ChartDataPoint[] = data.topUsers
      .slice(0, 10) // Limit to top 10
      .map(user => ({
        name: user.userName || 'Unknown User',
        value: user.count,
      }));

    return {
      actionChartData,
      resourceChartData,
      dailyActivityChartData,
      topUsersChartData,
    };
  }, [data]);

  return {
    ...chartData,
    isLoading,
    error: error as Error | null,
  };
};
