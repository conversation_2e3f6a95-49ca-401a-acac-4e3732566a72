'use client';

import React, { useMemo } from 'react';
import {
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  Radar,
} from 'recharts';
import { format, isBefore, addDays } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  BarChart2,
  Users,
  Calendar,
  PieChart as PieChartIcon,
  Percent,
  TrendingUp,
  Activity,
  Award,
  Target,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChartCard } from '@/components/Global/ChartCard';

interface User {
  _id: string;
  name: string;
  email: string;
  image: string;
}

interface Task {
  _id: string;
  name: string;
  description: string;
  status: string;
  priority: string;
  dueDate?: string;
  startDate?: string;
  estimatedTime?: number;
  loggedTime?: number;
  assignedTo: User[];
  createdAt: string;
  updatedAt: string;
}

interface Project {
  _id: string;
  name: string;
  description: string;
  status: string;
  priority: string;
  tasks: Task[];
  createdAt: string;
  updatedAt: string;
}

const STATUS_COLORS = {
  'To Do': '#f97316',
  'In Progress': '#3b82f6',
  Review: '#8b5cf6',
  Hold: '#f59e0b',
  Discussion: '#64748b',
  Completed: '#22c55e',
};

const PRIORITY_COLORS = {
  High: '#ef4444',
  Medium: '#f59e0b',
  Low: '#22c55e',
};

const ProjectAnalytics = ({ project }: { project: Project }) => {
  const {
    taskStatusData,
    taskPriorityData,
    completionRate,
    timeTrackingData,
    assigneeWorkloadData,
    dueDateAnalysis,
    totalTasks,
    tasksByMonth,
    totalEstimatedTime,
    totalLoggedTime,
    assigneeWorkload,
  } = useMemo(() => {
    const statusCounts: Record<string, number> = {
      'To Do': 0,
      'In Progress': 0,
      Review: 0,
      Hold: 0,
      Discussion: 0,
      Completed: 0,
    };

    const priorityCounts: Record<string, number> = {
      High: 0,
      Medium: 0,
      Low: 0,
    };

    let totalEstimatedTime = 0;
    let totalLoggedTime = 0;

    const assigneeWorkload: Record<
      string,
      {
        name: string;
        image: string;
        taskCount: number;
        estimatedHours: number;
        loggedHours: number;
        completedTasks: number;
        totalTasks: number;
      }
    > = {};

    const today = new Date();
    let overdueTasks = 0;
    let upcomingTasks = 0;
    let completedOnTime = 0;
    let noDeadline = 0;

    const monthlyTaskCounts: Record<string, number> = {};

    project.tasks.forEach(task => {
      statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;

      priorityCounts[task.priority] = (priorityCounts[task.priority] || 0) + 1;

      if (task.estimatedTime) totalEstimatedTime += task.estimatedTime;
      if (task.loggedTime) totalLoggedTime += task.loggedTime;

      task.assignedTo.forEach(user => {
        if (!assigneeWorkload[user._id]) {
          assigneeWorkload[user._id] = {
            name: user.name,
            image: user.image,
            taskCount: 0,
            estimatedHours: 0,
            loggedHours: 0,
            completedTasks: 0,
            totalTasks: 0,
          };
        }

        assigneeWorkload[user._id].taskCount += 1;
        assigneeWorkload[user._id].totalTasks += 1;

        if (task.estimatedTime) {
          assigneeWorkload[user._id].estimatedHours += task.estimatedTime;
        }

        if (task.loggedTime) {
          assigneeWorkload[user._id].loggedHours += task.loggedTime;
        }

        if (task.status === 'Completed') {
          assigneeWorkload[user._id].completedTasks += 1;
        }
      });

      if (task.dueDate) {
        const dueDate = new Date(task.dueDate);

        if (task.status === 'Completed') {
          completedOnTime += 1;
        } else if (isBefore(dueDate, today)) {
          overdueTasks += 1;
        } else if (isBefore(dueDate, addDays(today, 7))) {
          upcomingTasks += 1;
        }
      } else {
        noDeadline += 1;
      }

      const creationMonth = format(new Date(task.createdAt), 'MMM yyyy');
      monthlyTaskCounts[creationMonth] = (monthlyTaskCounts[creationMonth] || 0) + 1;
    });

    const taskStatusData = Object.entries(statusCounts).map(([name, value]) => ({
      name,
      value,
      color: STATUS_COLORS[name as keyof typeof STATUS_COLORS] || '#94a3b8',
    }));

    const taskPriorityData = Object.entries(priorityCounts).map(([name, value]) => ({
      name,
      value,
      color: PRIORITY_COLORS[name as keyof typeof PRIORITY_COLORS] || '#94a3b8',
    }));

    const totalTaskCount = project.tasks.length;
    const completedTaskCount = statusCounts['Completed'] || 0;
    const completionRate =
      totalTaskCount > 0 ? Math.round((completedTaskCount / totalTaskCount) * 100) : 0;

    const timeTrackingData = [
      { name: 'Estimated', hours: totalEstimatedTime },
      { name: 'Logged', hours: totalLoggedTime },
    ];

    // Format assignee workload data
    const assigneeWorkloadData = Object.values(assigneeWorkload)
      .sort((a, b) => b.taskCount - a.taskCount)
      .map(assignee => ({
        name: assignee.name,
        image: assignee.image,
        tasks: assignee.taskCount,
        estimatedHours: assignee.estimatedHours,
        loggedHours: assignee.loggedHours,
        completion:
          assignee.totalTasks > 0
            ? Math.round((assignee.completedTasks / assignee.totalTasks) * 100)
            : 0,
      }));

    // Format due date analysis
    const dueDateAnalysis = [
      { name: 'Overdue', value: overdueTasks, color: '#ef4444' },
      { name: 'Due Soon', value: upcomingTasks, color: '#f59e0b' },
      { name: 'Completed', value: completedOnTime, color: '#22c55e' },
      { name: 'No Deadline', value: noDeadline, color: '#94a3b8' },
    ];

    // Format monthly task data
    const tasksByMonth = Object.entries(monthlyTaskCounts)
      .map(([month, count]) => ({
        month,
        tasks: count,
      }))
      .sort((a, b) => {
        const dateA = new Date(a.month);
        const dateB = new Date(b.month);
        return dateA.getTime() - dateB.getTime();
      });

    return {
      taskStatusData,
      taskPriorityData,
      completionRate,
      timeTrackingData,
      assigneeWorkloadData,
      dueDateAnalysis,
      totalTasks: totalTaskCount,
      tasksByMonth,
      totalEstimatedTime,
      totalLoggedTime,
      assigneeWorkload,
    };
  }, [project]);

  const statCards = [
    {
      title: 'Completion Rate',
      value: `${completionRate}%`,
      icon: Percent,
      variant: 'primary' as const,
      delay: 0.05,
      trend: completionRate > 70 ? { value: 5, isPositive: true } : undefined,
    },
    {
      title: 'Total Tasks',
      value: totalTasks,
      icon: BarChart2,
      variant: 'primary' as const,
      delay: 0,
    },
    {
      title: 'In Progress',
      value: taskStatusData.find(item => item.name === 'In Progress')?.value || 0,
      icon: Clock,
      variant: 'primary' as const,
      delay: 0.1,
    },
    {
      title: 'Completed',
      value: taskStatusData.find(item => item.name === 'Completed')?.value || 0,
      icon: CheckCircle,
      variant: 'success' as const,
      delay: 0.2,
    },
    {
      title: 'Overdue',
      value: dueDateAnalysis.find(item => item.name === 'Overdue')?.value || 0,
      icon: AlertTriangle,
      variant: 'destructive' as const,
      delay: 0.3,
    },
  ];

  // Create performance metrics for radar chart
  const performanceMetrics = useMemo(() => {
    // const completedTasks = taskStatusData.find(item => item.name === 'Completed')?.value || 0;
    const totalTasksCount = totalTasks || 1;

    return [
      {
        subject: 'Completion',
        value: completionRate / 100,
        fullMark: 1,
      },
      {
        subject: 'On Time',
        value:
          (dueDateAnalysis.find(item => item.name === 'Completed')?.value || 0) / totalTasksCount,
        fullMark: 1,
      },
      {
        subject: 'Efficiency',
        value:
          totalEstimatedTime > 0
            ? Math.min(totalEstimatedTime / Math.max(totalLoggedTime, 1), 1)
            : 0.5,
        fullMark: 1,
      },
      {
        subject: 'Distribution',
        value:
          1 - (dueDateAnalysis.find(item => item.name === 'Overdue')?.value || 0) / totalTasksCount,
        fullMark: 1,
      },
      {
        subject: 'Engagement',
        value:
          Object.keys(assigneeWorkload).length > 0
            ? Math.min(1, Object.keys(assigneeWorkload).length / 5)
            : 0.5,
        fullMark: 1,
      },
    ];
  }, [
    totalTasks,
    completionRate,
    dueDateAnalysis,
    totalEstimatedTime,
    totalLoggedTime,
    assigneeWorkload,
  ]);

  // Enhanced tooltip with dark mode support
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const color = payload[0].color || payload[0].fill || 'hsl(var(--primary))';

      return (
        <div className="chart-tooltip p-4 rounded-lg">
          <p className="font-semibold theme-text-primary mb-1">{label || payload[0].name}</p>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }}></div>
            <p className="text-sm theme-text-secondary">
              {payload[0].name || 'Value'}:{' '}
              <span className="font-medium theme-text-primary">{payload[0].value}</span>
              {payload[0].payload && typeof payload[0].payload.percent === 'number' && (
                <span className="ml-1 theme-text-secondary">
                  ({(payload[0].payload.percent * 100).toFixed(0)}%)
                </span>
              )}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ScrollArea className="h-[calc(100vh-12rem)]">
      <div className="space-y-6 theme-surface bg-grid-pattern p-4 sm:p-6 rounded-xl theme-shadow-sm">
        {/* Project Score Card */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="theme-surface-elevated theme-shadow-md rounded-xl border border-border/20 backdrop-blur-md overflow-hidden"
        >
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 p-5">
            {/* Left section with completion circle */}
            <div className="md:col-span-3 flex justify-center">
              <div className="relative flex flex-col items-center">
                {/* Semi-circle progress indicator */}
                <div className="relative w-48 h-24 overflow-hidden">
                  <div className="absolute w-48 h-48 top-0 left-0">
                    <svg viewBox="0 0 200 200" className="w-full h-full">
                      {/* Background semi-circle */}
                      <path
                        d="M20,100 A80,80 0 1,1 180,100"
                        fill="none"
                        stroke="hsl(var(--muted))"
                        strokeWidth="12"
                        strokeLinecap="round"
                      />

                      {/* Progress arc */}
                      <path
                        d="M20,100 A80,80 0 1,1 180,100"
                        fill="none"
                        stroke="hsl(var(--primary))"
                        strokeWidth="12"
                        strokeLinecap="round"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - completionRate / 100)}
                        className="drop-shadow-[0_0_2px_rgba(var(--primary),0.5)]"
                      />

                      {/* Indicator dot */}
                      <circle
                        cx={100 - 80 * Math.cos((Math.PI * completionRate) / 100)}
                        cy={100 - 80 * Math.sin((Math.PI * completionRate) / 100)}
                        r="8"
                        fill="hsl(var(--primary))"
                        className="drop-shadow-md"
                      />
                    </svg>
                  </div>
                </div>

                {/* Percentage display */}
                <div className="mt-2 flex flex-col items-center">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold theme-text-primary">{completionRate}</span>
                    <span className="text-xl font-medium theme-text-secondary ml-1">%</span>
                  </div>
                  <span className="text-sm theme-text-secondary font-medium mt-1">
                    Project Completion
                  </span>
                </div>

                {/* Status indicator */}
                <div className="mt-3 px-3 py-1 rounded-full bg-primary/10 border border-primary/20">
                  <span className="text-xs font-medium text-primary">
                    {completionRate < 25
                      ? 'Just Started'
                      : completionRate < 50
                        ? 'In Progress'
                        : completionRate < 75
                          ? 'Well Underway'
                          : completionRate < 100
                            ? 'Almost Done'
                            : 'Completed'}
                  </span>
                </div>
              </div>
            </div>

            {/* Stats grid */}
            <div className="md:col-span-5">
              <div className="flex items-center gap-2 mb-2">
                <Award className="h-4 w-4 text-primary" />
                <h3 className="text-sm font-medium text-foreground">Project Stats</h3>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {[
                  {
                    label: 'Tasks',
                    value: totalTasks,
                    icon: BarChart2,
                    color: 'bg-blue-500/10 text-blue-500 dark:bg-blue-500/20',
                  },
                  {
                    label: 'In Progress',
                    value: taskStatusData.find(item => item.name === 'In Progress')?.value || 0,
                    icon: Clock,
                    color: 'bg-amber-500/10 text-amber-500 dark:bg-amber-500/20',
                  },
                  {
                    label: 'Completed',
                    value: taskStatusData.find(item => item.name === 'Completed')?.value || 0,
                    icon: CheckCircle,
                    color: 'bg-green-500/10 text-green-500 dark:bg-green-500/20',
                  },
                  {
                    label: 'Overdue',
                    value: dueDateAnalysis.find(item => item.name === 'Overdue')?.value || 0,
                    icon: AlertTriangle,
                    color: 'bg-red-500/10 text-red-500 dark:bg-red-500/20',
                  },
                  {
                    label: 'Team',
                    value: Object.keys(assigneeWorkload).length,
                    icon: Users,
                    color: 'bg-violet-500/10 text-violet-500 dark:bg-violet-500/20',
                  },
                  {
                    label: 'Hours',
                    value: totalLoggedTime,
                    icon: Clock,
                    color: 'bg-cyan-500/10 text-cyan-500 dark:bg-cyan-500/20',
                  },
                ].map(stat => (
                  <div
                    key={stat.label}
                    className="flex items-center gap-2 p-2 rounded-lg theme-surface border border-border/10 theme-shadow-sm"
                  >
                    <div className={`p-1.5 rounded-full ${stat.color}`}>
                      <stat.icon className="h-3.5 w-3.5" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">{stat.label}</p>
                      <p className="text-base font-semibold text-foreground">{stat.value}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right section with radar chart */}
            <div className="md:col-span-4">
              <div className="flex items-center gap-2 mb-1">
                <Target className="h-4 w-4 text-primary" />
                <h3 className="text-sm font-medium text-foreground">Performance</h3>
              </div>
              <div className="h-[160px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart outerRadius={60} data={performanceMetrics}>
                    <PolarGrid className="text-border/30" />
                    <PolarAngleAxis dataKey="subject" className="text-xs text-muted-foreground" />
                    <Radar
                      name="Performance"
                      dataKey="value"
                      stroke="hsl(var(--primary))"
                      fill="hsl(var(--primary))"
                      fillOpacity={0.3}
                    />
                    <Tooltip content={<CustomTooltip />} />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main Analytics Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid grid-cols-3 mb-4 theme-surface backdrop-blur-sm p-1 border border-border/30">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-md data-[state=active]:font-medium data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              <BarChart2 className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger
              value="team"
              className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-md data-[state=active]:font-medium data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              <Users className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Team</span>
            </TabsTrigger>
            <TabsTrigger
              value="timeline"
              className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-md data-[state=active]:font-medium data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              <Calendar className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Timeline</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab Content */}
          <TabsContent value="overview" className="mt-0 space-y-6">
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Top Stats Cards - Simplified */}
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
                  {statCards.map((card, index) => (
                    <motion.div
                      key={card.title}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className={`theme-surface-elevated theme-shadow-sm hover-reveal theme-transition rounded-lg p-4 ${
                        card.variant === 'success'
                          ? 'border-l-4 border-l-green-500'
                          : card.variant === 'destructive'
                            ? 'border-l-4 border-l-red-500'
                            : 'border-l-4 border-l-primary'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`p-2 rounded-full ${
                              card.variant === 'success'
                                ? 'bg-green-500/10 text-green-500'
                                : card.variant === 'destructive'
                                  ? 'bg-red-500/10 text-red-500'
                                  : 'bg-primary/10 text-primary'
                            }`}
                          >
                            <card.icon className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="text-sm theme-text-secondary font-medium">{card.title}</p>
                            <p className="text-xl font-bold theme-text-primary">{card.value}</p>
                          </div>
                        </div>
                        {card.trend && (
                          <div
                            className={`text-sm font-medium ${card.trend.isPositive ? 'text-green-500' : 'text-red-500'}`}
                          >
                            {card.trend.isPositive ? '+' : '-'}
                            {card.trend.value}%
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Charts Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  {/* Status Distribution */}
                  <ChartCard
                    title="Task Status Distribution"
                    description="Breakdown of tasks by current status"
                    icon={PieChartIcon}
                    delay={0.4}
                    className="theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={taskStatusData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={2}
                          dataKey="value"
                          label={false}
                          labelLine={false}
                        >
                          {taskStatusData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                              stroke="hsl(var(--background))"
                              strokeWidth={2}
                            />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                        <Legend
                          layout="horizontal"
                          verticalAlign="bottom"
                          align="center"
                          iconType="circle"
                          iconSize={10}
                          wrapperStyle={{ paddingTop: 20 }}
                          formatter={value => (
                            <span className="text-sm text-foreground">{value}</span>
                          )}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartCard>

                  {/* Priority Distribution */}
                  <ChartCard
                    title="Task Priority Distribution"
                    description="Breakdown of tasks by priority level"
                    icon={Target}
                    delay={0.5}
                    className="theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={taskPriorityData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={2}
                          dataKey="value"
                          label={false}
                          labelLine={false}
                        >
                          {taskPriorityData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                              stroke="hsl(var(--background))"
                              strokeWidth={2}
                            />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                        <Legend
                          layout="horizontal"
                          verticalAlign="bottom"
                          align="center"
                          iconType="circle"
                          iconSize={10}
                          wrapperStyle={{ paddingTop: 20 }}
                          formatter={value => (
                            <span className="text-sm text-foreground">{value}</span>
                          )}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartCard>
                </div>

                {/* Time Tracking */}
                <ChartCard
                  title="Time Tracking"
                  description="Comparison of estimated vs. logged hours"
                  icon={Clock}
                  height="h-[250px]"
                  delay={0.6}
                  className="mt-4 theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={timeTrackingData} layout="vertical">
                      <CartesianGrid
                        strokeDasharray="3 3"
                        horizontal={true}
                        vertical={false}
                        className="stroke-border/40 dark:stroke-border/20"
                      />
                      <XAxis
                        type="number"
                        tickLine={false}
                        axisLine={false}
                        className="text-muted-foreground text-xs"
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        width={100}
                        tickLine={false}
                        axisLine={false}
                        className="text-muted-foreground text-xs"
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend
                        wrapperStyle={{ paddingTop: 15 }}
                        iconType="circle"
                        formatter={value => (
                          <span className="text-sm text-foreground">{value}</span>
                        )}
                      />
                      <Bar
                        dataKey="hours"
                        name="Hours"
                        fill="hsl(var(--primary))"
                        radius={[0, 6, 6, 0]}
                        barSize={30}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartCard>
              </motion.div>
            </AnimatePresence>
          </TabsContent>

          {/* Team Tab Content */}
          <TabsContent value="team" className="mt-0 space-y-6">
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Team Workload Chart */}
                <ChartCard
                  title="Team Workload Distribution"
                  description="Number of tasks assigned to each team member"
                  icon={Activity}
                  delay={0.3}
                  className="theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={assigneeWorkloadData} layout="vertical">
                      <CartesianGrid
                        strokeDasharray="3 3"
                        horizontal={true}
                        vertical={false}
                        className="stroke-border/40 dark:stroke-border/20"
                      />
                      <XAxis
                        type="number"
                        tickLine={false}
                        axisLine={false}
                        className="text-muted-foreground text-xs"
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        width={100}
                        tickLine={false}
                        axisLine={false}
                        tick={props => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <text
                                x={-25}
                                y={0}
                                dy={4}
                                textAnchor="end"
                                className="theme-text-secondary"
                                fontSize={13}
                                fontWeight={500}
                              >
                                {payload.value}
                              </text>
                            </g>
                          );
                        }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend
                        wrapperStyle={{ paddingTop: 15 }}
                        iconType="circle"
                        formatter={value => (
                          <span className="text-sm text-foreground">{value}</span>
                        )}
                      />
                      <Bar
                        dataKey="tasks"
                        name="Tasks"
                        fill="hsl(var(--primary))"
                        radius={[0, 6, 6, 0]}
                        barSize={30}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartCard>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                  {assigneeWorkloadData.map((member, index) => (
                    <motion.div
                      key={member.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Card className="theme-surface-elevated theme-shadow-md overflow-hidden hover-reveal theme-transition">
                        <CardContent className="pt-6 relative">
                          <div className="absolute top-2 right-2">
                            <Badge
                              variant={
                                member.completion > 70
                                  ? 'success'
                                  : member.completion > 40
                                    ? 'warning'
                                    : 'destructive'
                              }
                              className="text-xs"
                            >
                              {member.completion}% Complete
                            </Badge>
                          </div>

                          <div className="flex items-center gap-4 mb-4">
                            <Avatar className="h-14 w-14 border-2 border-border shadow-sm">
                              <AvatarImage src={member.image} alt={member.name} />
                              <AvatarFallback className="bg-primary text-primary-foreground">
                                {member.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h3 className="font-semibold theme-text-primary">{member.name}</h3>
                              <p className="text-sm theme-text-secondary">
                                {member.tasks} tasks assigned
                              </p>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="theme-text-secondary">Completion Rate</span>
                                <span className="font-medium text-primary">
                                  {member.completion}%
                                </span>
                              </div>
                              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-primary rounded-full transition-all duration-500 ease-out"
                                  style={{ width: `${member.completion}%` }}
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-3 gap-2 theme-surface p-3 rounded-lg">
                              <div className="text-center">
                                <span className="text-xs theme-text-secondary block">
                                  Estimated
                                </span>
                                <span className="font-medium theme-text-primary">
                                  {member.estimatedHours}h
                                </span>
                              </div>
                              <div className="text-center border-x border-border/30">
                                <span className="text-xs theme-text-secondary block">Logged</span>
                                <span className="font-medium theme-text-primary">
                                  {member.loggedHours}h
                                </span>
                              </div>
                              <div className="text-center">
                                <span className="text-xs theme-text-secondary block">
                                  Efficiency
                                </span>
                                <span className="font-medium theme-text-primary">
                                  {member.estimatedHours > 0
                                    ? Math.round((member.loggedHours / member.estimatedHours) * 100)
                                    : 0}
                                  %
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </AnimatePresence>
          </TabsContent>

          {/* Timeline Tab Content */}
          <TabsContent value="timeline" className="mt-0 space-y-6">
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Due Date Analysis */}
                <ChartCard
                  title="Due Date Analysis"
                  description="Status of tasks based on deadlines"
                  icon={PieChartIcon}
                  height="h-[250px]"
                  delay={0.3}
                  className="theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={dueDateAnalysis}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={90}
                        paddingAngle={2}
                        dataKey="value"
                        label={false}
                        labelLine={false}
                      >
                        {dueDateAnalysis.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.color}
                            stroke="hsl(var(--background))"
                            strokeWidth={2}
                          />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                      <Legend
                        layout="horizontal"
                        verticalAlign="bottom"
                        align="center"
                        iconType="circle"
                        iconSize={10}
                        wrapperStyle={{ paddingTop: 20 }}
                        formatter={value => (
                          <span className="text-sm text-foreground">{value}</span>
                        )}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartCard>

                {/* Tasks by Month */}
                <ChartCard
                  title="Tasks Created by Month"
                  description="Task creation trend over time"
                  icon={TrendingUp}
                  delay={0.4}
                  className="mt-6 theme-surface-elevated theme-shadow-sm hover-reveal theme-transition"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={tasksByMonth}>
                      <defs>
                        <linearGradient id="colorTasks" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.8} />
                          <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0.1} />
                        </linearGradient>
                      </defs>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        vertical={false}
                        className="stroke-border/40 dark:stroke-border/20"
                      />
                      <XAxis
                        dataKey="month"
                        tickLine={false}
                        axisLine={false}
                        className="text-muted-foreground text-xs"
                      />
                      <YAxis
                        tickLine={false}
                        axisLine={false}
                        className="text-muted-foreground text-xs"
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend
                        layout="horizontal"
                        verticalAlign="top"
                        align="right"
                        iconType="circle"
                        wrapperStyle={{ paddingBottom: 10 }}
                        formatter={value => (
                          <span className="text-sm text-foreground">{value}</span>
                        )}
                      />
                      <Area
                        type="monotone"
                        dataKey="tasks"
                        name="Tasks Created"
                        stroke="hsl(var(--primary))"
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorTasks)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartCard>
              </motion.div>
            </AnimatePresence>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};

export default ProjectAnalytics;
