import { UserPlus, PanelRightOpen, PanelLeftOpen } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthProvider';
import { Playfair_Display } from 'next/font/google';
import { Button } from '../ui/button';
import { TeamLoggerPopover } from './TeamLogger';
import InviteModal from './InviteModal';
import { NotificationsPopover } from './NotificationsPopover';
import { ThemeToggle } from '../ui/theme-toggle';
import { useSidebarStore } from '@/stores/sidebarStore';
import { GlobalSearch, SearchTrigger, MobileSearchTrigger } from '../GlobalSearch';

const playwrite = Playfair_Display({
  subsets: ['latin'],
  weight: '400',
});

const Header = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const { session } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { isExpanded, setIsExpanded } = useSidebarStore();

  const onOpen = () => {
    setIsOpen(true);
  };

  const onOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return date.toLocaleDateString('en-US', options);
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <header className="theme-surface-elevated theme-shadow-lg rounded-lg">
      <div className="flex justify-between items-center px-3 sm:px-4 py-2">
        <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
          <button
            className={`hidden md:flex items-center justify-center w-6 h-6 rounded-lg logo-toggle-btn hover:bg-primary/10 transition-all duration-300 flex-shrink-0`}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <PanelLeftOpen className="h-4 w-4 logo-toggle-icon" />
            ) : (
              <PanelRightOpen className="h-4 w-4 logo-toggle-icon" />
            )}
          </button>

          <div className="min-w-0 flex-1">
            <h1
              className={`${playwrite.className} text-base sm:text-lg md:text-xl font-bold mb-0.5 sm:mb-1 theme-text-primary truncate`}
            >
              {getGreeting()}, {session?.user?.name}
            </h1>
            <p className="text-xs sm:text-sm theme-text-secondary hidden xs:block">
              {formatDate(currentTime)}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-1 sm:gap-2 md:gap-4 flex-shrink-0">
          <div className="hidden md:block">
            <SearchTrigger onOpen={() => setIsSearchOpen(true)} />
          </div>
          <div className="md:hidden">
            <MobileSearchTrigger onOpen={() => setIsSearchOpen(true)} />
          </div>

          <NotificationsPopover />
          <ThemeToggle />

          <div className="hidden sm:block">
            <TeamLoggerPopover />
          </div>

          <Button
            variant="ghost"
            size="sm"
            className="theme-button-secondary theme-shadow-sm text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 h-8 sm:h-9"
            onClick={onOpen}
          >
            <UserPlus className="w-4 h-4 sm:w-5 sm:h-5 cursor-pointer theme-transition" />
            <span className="hidden sm:inline ml-1 sm:ml-2">Invite</span>
          </Button>
        </div>
      </div>

      <div className="xs:hidden px-3 pb-2">
        <p className="text-xs theme-text-secondary">{formatDate(currentTime)}</p>
      </div>

      <InviteModal isOpen={isOpen} onOpenChange={onOpenChange} />
      <GlobalSearch isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </header>
  );
};

export default Header;
