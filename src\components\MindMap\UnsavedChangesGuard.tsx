'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle } from 'lucide-react';

interface UnsavedChangesGuardProps {
  hasUnsavedChanges: boolean;
  onConfirmNavigation?: () => void;
  onCancelNavigation?: () => void;
}

export function UnsavedChangesGuard({
  hasUnsavedChanges,
  onConfirmNavigation,
  onCancelNavigation,
}: UnsavedChangesGuardProps) {
  const router = useRouter();
  const [showDialog, setShowDialog] = React.useState(false);
  const [pendingNavigation, setPendingNavigation] = React.useState<string | null>(null);
  const [isInitialized, setIsInitialized] = React.useState(false);

  // Add a delay before the guard becomes active to prevent false positives during initial load
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 2000); // 2 second delay

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only activate the guard if initialized and there are actual unsaved changes
    if (!isInitialized || !hasUnsavedChanges) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Only prevent unload if there are actual unsaved changes and guard is initialized
      if (isInitialized && hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    const handlePopState = (e: PopStateEvent) => {
      if (isInitialized && hasUnsavedChanges) {
        e.preventDefault();
        setShowDialog(true);
        // Push the current state back to prevent navigation
        window.history.pushState(null, '', window.location.href);
      }
    };

    // Only add the beforeunload listener if we actually have unsaved changes and are initialized
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [hasUnsavedChanges, isInitialized]);

  const handleConfirmNavigation = () => {
    setShowDialog(false);
    onConfirmNavigation?.();

    if (pendingNavigation) {
      router.push(pendingNavigation);
      setPendingNavigation(null);
    }
  };

  const handleCancelNavigation = () => {
    setShowDialog(false);
    setPendingNavigation(null);
    onCancelNavigation?.();
  };
  return (
    <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
      <AlertDialogContent className="theme-surface-elevated">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 rounded-full bg-warning/20 flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-warning" />
            </div>
            <div>
              <AlertDialogTitle className="theme-text-primary">Unsaved Changes</AlertDialogTitle>
              <AlertDialogDescription className="theme-text-secondary">
                You have unsaved changes that will be lost if you leave this page. Are you sure you
                want to continue?
              </AlertDialogDescription>
            </div>
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancelNavigation} className="theme-button-ghost">
            Stay on Page
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirmNavigation}
            className="theme-button-primary bg-warning hover:bg-warning/90"
          >
            Leave Without Saving
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
