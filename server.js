import { createServer } from 'http';
import { parse } from 'url';
import next from 'next';
import cluster from 'cluster';
import os from 'os';
import { initializeSocketIO, initializeNotificationSystem } from './src/lib/socketio.ts';

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = parseInt(process.env.PORT || '4000');

if (cluster.isPrimary && !dev && process.env.CLUSTER_MODE === 'true') {
    const numCPUs = Math.min(os.cpus().length, 4);

    console.log(`Master ${process.pid} is running. Starting ${numCPUs} workers...`);

    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }

    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died. Starting new worker...`);
        cluster.fork();
    });
} else {
    const app = next({ dev, hostname, port });
    const handle = app.getRequestHandler();

    process.on('uncaughtException', (error) => {
        console.error('Uncaught Exception:', error);
        process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
        console.error('Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
    });

    app.prepare().then(() => {
        const server = createServer(async (req, res) => {
            try {
                if (req.url?.startsWith('/socket.io/')) {
                    return;
                }

                res.setHeader('X-Content-Type-Options', 'nosniff');
                res.setHeader('X-Frame-Options', 'DENY');
                res.setHeader('X-XSS-Protection', '1; mode=block');

                if (!dev) {
                    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
                }

                const parsedUrl = parse(req.url, true);
                await handle(req, res, parsedUrl);
            } catch (err) {
                console.error('Request handling error:', err);
                res.statusCode = 500;
                res.end('Internal server error');
            }
        });

        server.keepAliveTimeout = 65000;
        server.headersTimeout = 66000;
        server.timeout = 120000;

        const io = initializeSocketIO(server);

        initializeNotificationSystem();

        const gracefulShutdown = () => {
            console.log('Starting graceful shutdown...');

            server.close(() => {
                console.log('HTTP server closed.');

                io.close(() => {
                    console.log('Socket.IO server closed.');
                    process.exit(0);
                });
            });

            setTimeout(() => {
                console.log('Force shutdown after timeout');
                process.exit(1);
            }, 10000);
        };

        process.on('SIGTERM', gracefulShutdown);
        process.on('SIGINT', gracefulShutdown);

        server.listen(port, hostname, () => {
            console.log(`Server ready on http://${hostname}:${port} (PID: ${process.pid})`);
        });
    }).catch((err) => {
        console.error('Server startup error:', err);
        process.exit(1);
    });
}