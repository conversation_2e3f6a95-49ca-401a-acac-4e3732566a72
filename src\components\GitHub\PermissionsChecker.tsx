'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import axios from 'axios';

interface PermissionsCheckerProps {
  repositoryId: string;
  repositoryName: string;
}

interface PermissionResult {
  repository: string;
  permissions: {
    admin: boolean;
    maintain: boolean;
    push: boolean;
    triage: boolean;
    pull: boolean;
  };
  canCreateWebhooks: boolean;
}

export const PermissionsChecker: React.FC<PermissionsCheckerProps> = ({
  repositoryId,
  repositoryName,
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [result, setResult] = useState<PermissionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkPermissions = async () => {
    setIsChecking(true);
    setError(null);
    setResult(null);

    try {
      const response = await axios.get(`/api/github/repositories/${repositoryId}/permissions`);
      setResult(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to check permissions');
    } finally {
      setIsChecking(false);
    }
  };

  const getPermissionIcon = (hasPermission: boolean) => {
    return hasPermission ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getPermissionBadge = (hasPermission: boolean, label: string) => {
    return (
      <Badge variant={hasPermission ? 'default' : 'destructive'} className="text-xs">
        {label}
      </Badge>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          GitHub App Permissions
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Check if your GitHub App has the required permissions for {repositoryName}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={checkPermissions} disabled={isChecking} className="w-full">
          {isChecking ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Checking Permissions...
            </>
          ) : (
            'Check Permissions'
          )}
        </Button>

        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="space-y-4">
            <Alert variant={result.canCreateWebhooks ? 'default' : 'destructive'}>
              {result.canCreateWebhooks ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {result.canCreateWebhooks
                  ? 'Your GitHub App has the required permissions to create webhooks!'
                  : 'Your GitHub App lacks the required permissions to create webhooks.'}
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Repository Permissions</h4>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Admin</span>
                    <div className="flex items-center gap-1">
                      {getPermissionIcon(result.permissions.admin)}
                      {getPermissionBadge(
                        result.permissions.admin,
                        result.permissions.admin ? 'Yes' : 'No'
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Maintain</span>
                    <div className="flex items-center gap-1">
                      {getPermissionIcon(result.permissions.maintain)}
                      {getPermissionBadge(
                        result.permissions.maintain,
                        result.permissions.maintain ? 'Yes' : 'No'
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Push</span>
                    <div className="flex items-center gap-1">
                      {getPermissionIcon(result.permissions.push)}
                      {getPermissionBadge(
                        result.permissions.push,
                        result.permissions.push ? 'Yes' : 'No'
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Webhook Capability</h4>
                <div className="flex items-center gap-2">
                  {getPermissionIcon(result.canCreateWebhooks)}
                  <span className="text-xs">
                    {result.canCreateWebhooks ? 'Can create webhooks' : 'Cannot create webhooks'}
                  </span>
                </div>
              </div>
            </div>

            {!result.canCreateWebhooks && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">To fix this issue:</p>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>Go to your GitHub App settings</li>
                      <li>Update Repository Permissions: Set "Administration" to "Read & Write"</li>
                      <li>Save the changes</li>
                      <li>Reinstall the app on your repository</li>
                      <li>Try creating the webhook again</li>
                    </ol>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
