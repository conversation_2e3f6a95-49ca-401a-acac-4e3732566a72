'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Crown,
  Zap,
  Shield,
  ArrowRight,
  Check,
  AlertTriangle,
  TrendingUp,
  Users,
  HardDrive,
  Calendar,
  Puzzle,
} from 'lucide-react';
import { PRICING_PLANS } from '@/constant/PricingPlans';
import { SubscriptionService } from '@/services/Subscription.service';
import { UsageSummary } from '@/services/UsageTracking.service';

interface UpgradePromptProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: (planId: string) => void;
  currentPlan: string;
  usageSummary?: UsageSummary;
  triggerReason?: 'limit_reached' | 'near_limit' | 'feature_request' | 'recommendation';
  limitType?: 'projects' | 'users' | 'storage' | 'integrations' | 'automations';
}

export const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  isOpen,
  onClose,
  onUpgrade,
  currentPlan,
  usageSummary,
  triggerReason = 'recommendation',
  limitType,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const currentPlanConfig = PRICING_PLANS.find(p => p.id === currentPlan);
  const recommendedPlan = usageSummary?.recommendations.recommendedPlan || 'basic';
  const availablePlans = PRICING_PLANS.filter(p => p.id !== currentPlan);

  const handleUpgrade = async () => {
    if (!selectedPlan) return;

    setIsLoading(true);
    try {
      await onUpgrade(selectedPlan);
      onClose();
    } catch (error) {
      console.error('Upgrade failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'basic':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-5 w-5 text-orange-500" />;
      default:
        return <Zap className="h-5 w-5" />;
    }
  };

  const getLimitIcon = (type?: string) => {
    switch (type) {
      case 'projects':
        return <Calendar className="h-4 w-4" />;
      case 'users':
        return <Users className="h-4 w-4" />;
      case 'storage':
        return <HardDrive className="h-4 w-4" />;
      case 'integrations':
        return <Puzzle className="h-4 w-4" />;
      default:
        return <TrendingUp className="h-4 w-4" />;
    }
  };

  const getPromptTitle = () => {
    switch (triggerReason) {
      case 'limit_reached':
        return `${limitType?.charAt(0).toUpperCase()}${limitType?.slice(1)} Limit Reached`;
      case 'near_limit':
        return `Approaching ${limitType?.charAt(0).toUpperCase()}${limitType?.slice(1)} Limit`;
      case 'feature_request':
        return 'Upgrade Required';
      default:
        return 'Unlock More Potential';
    }
  };

  const getPromptDescription = () => {
    switch (triggerReason) {
      case 'limit_reached':
        return `You've reached your ${limitType} limit on the ${currentPlanConfig?.name} plan. Upgrade to continue growing.`;
      case 'near_limit':
        return `You're using ${usageSummary?.limits[limitType as keyof typeof usageSummary.limits]?.percentage.toFixed(0)}% of your ${limitType} limit. Consider upgrading to avoid interruptions.`;
      case 'feature_request':
        return 'This feature is available on higher plans. Upgrade to unlock advanced capabilities.';
      default:
        return 'Based on your usage patterns, upgrading could significantly improve your productivity.';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2">
            {getLimitIcon(limitType)}
            <DialogTitle className="text-xl">{getPromptTitle()}</DialogTitle>
          </div>
          <DialogDescription className="text-base">{getPromptDescription()}</DialogDescription>
        </DialogHeader>

        {/* Current Usage Display */}
        {usageSummary && limitType && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Current Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(usageSummary.limits).map(([key, limit]) => {
                  if (limit.limit === 'unlimited') return null;

                  const isHighlighted = key === limitType;

                  return (
                    <div
                      key={key}
                      className={`${isHighlighted ? 'bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800' : ''}`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span
                          className={`text-sm font-medium capitalize ${isHighlighted ? 'text-yellow-800 dark:text-yellow-200' : ''}`}
                        >
                          {key}
                        </span>
                        <span
                          className={`text-sm ${isHighlighted ? 'text-yellow-800 dark:text-yellow-200' : 'text-gray-600 dark:text-gray-400'}`}
                        >
                          {limit.current} / {limit.limit}
                        </span>
                      </div>
                      <Progress
                        value={limit.percentage}
                        className={`h-2 ${isHighlighted ? 'bg-yellow-100 dark:bg-yellow-900' : ''}`}
                      />
                      {isHighlighted && limit.isNearLimit && (
                        <div className="flex items-center gap-1 mt-2">
                          <AlertTriangle className="h-3 w-3 text-yellow-600" />
                          <span className="text-xs text-yellow-700 dark:text-yellow-300">
                            {limit.percentage >= 100 ? 'Limit reached' : 'Approaching limit'}
                          </span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Plan Options */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Choose Your Upgrade</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availablePlans.map(plan => {
              const pricing = SubscriptionService.calculatePlanPrice(plan.id, 'monthly');
              const isRecommended = plan.id === recommendedPlan;
              const isSelected = selectedPlan === plan.id;

              return (
                <Card
                  key={plan.id}
                  className={`cursor-pointer transition-all ${
                    isSelected
                      ? 'border-blue-500 shadow-md'
                      : isRecommended
                        ? 'border-purple-500 shadow-sm'
                        : 'hover:shadow-sm'
                  }`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getPlanIcon(plan.id)}
                        <CardTitle className="text-base">{plan.name}</CardTitle>
                      </div>
                      {isRecommended && (
                        <Badge className="bg-purple-500 text-white text-xs">Recommended</Badge>
                      )}
                    </div>
                    <div className="text-lg font-bold">
                      {SubscriptionService.formatPrice(pricing.finalPrice)}
                      <span className="text-sm font-normal text-gray-500">/month</span>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {/* Key benefits for this upgrade */}
                      {limitType && (
                        <div className="flex items-center gap-2 text-sm">
                          <Check className="h-3 w-3 text-green-500" />
                          <span>
                            {plan.limits[limitType as keyof typeof plan.limits] === 'unlimited'
                              ? `Unlimited ${limitType}`
                              : `${plan.limits[limitType as keyof typeof plan.limits]} ${limitType}`}
                          </span>
                        </div>
                      )}

                      {/* Show top 3 features */}
                      {plan.features.slice(0, 3).map(
                        (feature, index) =>
                          feature.included && (
                            <div key={index} className="flex items-center gap-2 text-sm">
                              <Check className="h-3 w-3 text-green-500" />
                              <span>{feature.name}</span>
                            </div>
                          )
                      )}

                      {plan.features.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{plan.features.length - 3} more features
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Benefits Summary */}
        {selectedPlan && (
          <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  What you'll get with {PRICING_PLANS.find(p => p.id === selectedPlan)?.name}
                </span>
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                Immediate access to all features, increased limits, and priority support. You can
                downgrade anytime if needed.
              </div>
            </CardContent>
          </Card>
        )}

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Maybe Later
          </Button>
          <Button
            onClick={handleUpgrade}
            disabled={!selectedPlan || isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              'Processing...'
            ) : (
              <>
                Upgrade Now
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
