import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { Project } from '@/models/Project';
import { SubscriptionValidator } from '@/middleware/subscriptionValidator';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/projects');

app.use('*', logger());

// Authentication middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    await connectDB();

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Auth middleware error:', error);
    return c.json({ error: 'Authentication failed' }, 401);
  }
  await next();
});

// Validation schemas
const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Invalid color format')
    .optional(),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
});

const updateProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Invalid color format')
    .optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
  status: z.enum(['active', 'archived', 'completed']).optional(),
});

// GET /api/projects - Get all projects for organization
app.get('/', async c => {
  const user = c.get('user');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const projects = await Project.find({
      organizationId: user.organizationId,
    })
      .populate('createdBy', 'name email')
      .populate('members', 'name email')
      .sort({ createdAt: -1 });

    return c.json({ projects });
  } catch (error: any) {
    console.error('Get projects error:', error);
    return c.json({ error: 'Failed to fetch projects' }, 500);
  }
});

// GET /api/projects/:id - Get specific project
app.get('/:id', async c => {
  const user = c.get('user');
  const projectId = c.req.param('id');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const project = await Project.findOne({
      _id: projectId,
      organizationId: user.organizationId,
    })
      .populate('createdBy', 'name email')
      .populate('members', 'name email');

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    return c.json({ project });
  } catch (error: any) {
    console.error('Get project error:', error);
    return c.json({ error: 'Failed to fetch project' }, 500);
  }
});

// POST /api/projects - Create new project with subscription validation
app.post('/', zValidator('json', createProjectSchema), async c => {
  const user = c.get('user');
  const projectData = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    // Validate subscription limits
    const validation = await SubscriptionValidator.validateAction(
      user.organizationId,
      'create_project'
    );

    if (!validation.allowed) {
      return c.json(
        {
          error: validation.reason,
          upgradeRequired: validation.upgradeRequired,
          recommendedPlan: validation.recommendedPlan,
          currentUsage: validation.currentUsage,
          limit: validation.limit,
        },
        403
      );
    }

    // Create project
    const project = new Project({
      ...projectData,
      organizationId: user.organizationId,
      createdBy: user.id,
      members: [user.id],
      status: 'active',
    });

    await project.save();

    // Populate the created project
    await project.populate('createdBy', 'name email');
    await project.populate('members', 'name email');

    return c.json(
      {
        project,
        message: 'Project created successfully',
      },
      201
    );
  } catch (error: any) {
    console.error('Create project error:', error);
    return c.json({ error: 'Failed to create project' }, 500);
  }
});

// PUT /api/projects/:id - Update project
app.put('/:id', zValidator('json', updateProjectSchema), async c => {
  const user = c.get('user');
  const projectId = c.req.param('id');
  const updateData = c.req.valid('json');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const project = await Project.findOne({
      _id: projectId,
      organizationId: user.organizationId,
    });

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    // Check if user has permission to update
    const isCreator = project.createdBy.toString() === user.id;
    const isMember = project.members.some((member: any) => member.toString() === user.id);

    if (!isCreator && !isMember) {
      return c.json({ error: 'Permission denied' }, 403);
    }

    // Update project
    Object.assign(project, updateData);
    project.updatedAt = new Date();
    await project.save();

    // Populate the updated project
    await project.populate('createdBy', 'name email');
    await project.populate('members', 'name email');

    return c.json({
      project,
      message: 'Project updated successfully',
    });
  } catch (error: any) {
    console.error('Update project error:', error);
    return c.json({ error: 'Failed to update project' }, 500);
  }
});

// DELETE /api/projects/:id - Delete project
app.delete('/:id', async c => {
  const user = c.get('user');
  const projectId = c.req.param('id');

  if (!user || !user.organizationId) {
    return c.json({ error: 'User not authenticated or no organization' }, 401);
  }

  try {
    const project = await Project.findOne({
      _id: projectId,
      organizationId: user.organizationId,
    });

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    // Check if user is the creator
    const isCreator = project.createdBy.toString() === user.id;
    if (!isCreator) {
      return c.json({ error: 'Only project creator can delete the project' }, 403);
    }

    await Project.findByIdAndDelete(projectId);

    return c.json({
      success: true,
      message: 'Project deleted successfully',
    });
  } catch (error: any) {
    console.error('Delete project error:', error);
    return c.json({ error: 'Failed to delete project' }, 500);
  }
});

// POST /api/projects/:id/members - Add member to project (with user limit validation)
app.post(
  '/:id/members',
  zValidator(
    'json',
    z.object({
      userId: z.string().min(1, 'User ID is required'),
    })
  ),
  async c => {
    const user = c.get('user');
    const projectId = c.req.param('id');
    const { userId } = c.req.valid('json');

    if (!user || !user.organizationId) {
      return c.json({ error: 'User not authenticated or no organization' }, 401);
    }

    try {
      // Validate subscription limits for user invitation
      const validation = await SubscriptionValidator.validateAction(
        user.organizationId,
        'invite_user'
      );

      if (!validation.allowed) {
        return c.json(
          {
            error: validation.reason,
            upgradeRequired: validation.upgradeRequired,
            recommendedPlan: validation.recommendedPlan,
          },
          403
        );
      }

      const project = await Project.findOne({
        _id: projectId,
        organizationId: user.organizationId,
      });

      if (!project) {
        return c.json({ error: 'Project not found' }, 404);
      }

      // Check if user is already a member
      if (project.members.includes(userId)) {
        return c.json({ error: 'User is already a member' }, 400);
      }

      // Add member
      project.members.push(userId);
      await project.save();

      await project.populate('members', 'name email');

      return c.json({
        project,
        message: 'Member added successfully',
      });
    } catch (error: any) {
      console.error('Add member error:', error);
      return c.json({ error: 'Failed to add member' }, 500);
    }
  }
);

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
