import { GitH<PERSON>U<PERSON>, GitHubPullRequest, GitHubIssue, GitHubRepository } from '@/types/GitHubTypes';
import axios from 'axios';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';

export class GitHubService {
  private static readonly BASE_URL = 'https://api.github.com';
  private static readonly TIMEOUT = 15000;

  static getOAuthUrl(clientId: string, redirectUri: string, state: string): string {
    const scopes = ['repo', 'user:email', 'admin:repo_hook'].join(' ');
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scopes,
      state,
      response_type: 'code',
    });
    return `https://github.com/login/oauth/authorize?${params.toString()}`;
  }

  static async getUser(accessToken: string): Promise<GitHubUser> {
    try {
      const response = await axios.get(`${this.BASE_URL}/user`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        timeout: this.TIMEOUT,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to fetch user: ${error.message}`);
    }
  }

  static async getUserRepositories(
    accessToken: string,
    page: number = 1,
    perPage: number = 30,
    searchQuery: string = '',
    installationId?: string
  ): Promise<{ repositories: any[]; hasMore: boolean }> {
    try {
      let url;
      let params: any = {
        sort: 'updated_at',
        direction: 'desc',
        per_page: perPage,
        page,
      };

      if (installationId) {
        url = `${this.BASE_URL}/installation/repositories`;
        params = {
          per_page: perPage,
          page,
        };
      } else {
        url = `${this.BASE_URL}/user/repos`;
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        params,
        timeout: this.TIMEOUT,
      });

      let repositories = response.data.repositories || response.data;

      if (searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase();
        repositories = repositories.filter(
          repo =>
            repo.name.toLowerCase().includes(searchLower) ||
            repo.full_name.toLowerCase().includes(searchLower) ||
            (repo.description && repo.description.toLowerCase().includes(searchLower))
        );
      }

      const hasMore = repositories.length === perPage;

      return { repositories, hasMore };
    } catch (error: any) {
      throw new Error(`Failed to fetch repositories: ${error.message}`);
    }
  }

  static async getInstallationRepositories(
    accessToken: string,
    page: number = 1,
    perPage: number = 30,
    searchQuery: string = ''
  ): Promise<{ repositories: any[]; hasMore: boolean; totalCount: number }> {
    try {
      const fetchPerPage = searchQuery.trim() ? 100 : perPage;
      const fetchPage = searchQuery.trim() ? 1 : page;

      const response = await axios.get(`${this.BASE_URL}/installation/repositories`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        params: {
          per_page: fetchPerPage,
          page: fetchPage,
          sort: 'updated',
          direction: 'desc',
        },
        timeout: this.TIMEOUT,
      });

      let repositories = response.data.repositories || [];
      let totalCount = response.data.total_count || repositories.length;

      if (searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase();
        const filteredRepos = repositories.filter(
          (repo: any) =>
            repo.name?.toLowerCase().includes(searchLower) ||
            repo.full_name?.toLowerCase().includes(searchLower) ||
            repo.description?.toLowerCase().includes(searchLower) ||
            repo.language?.toLowerCase().includes(searchLower)
        );

        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        repositories = filteredRepos.slice(startIndex, endIndex);
        totalCount = filteredRepos.length;
      }

      const hasMore = searchQuery.trim()
        ? page * perPage < totalCount
        : page * perPage < (response.data.total_count || repositories.length);

      return { repositories, hasMore, totalCount };
    } catch (error: any) {
      throw new Error(`Failed to fetch installation repositories: ${error.message}`);
    }
  }

  static async getRepository(
    accessToken: string,
    owner: string,
    repo: string
  ): Promise<GitHubRepository> {
    try {
      const response = await axios.get(`${this.BASE_URL}/repos/${owner}/${repo}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        timeout: this.TIMEOUT,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to fetch repository: ${error.message}`);
    }
  }

  static async getRepositoryIssues(
    accessToken: string,
    owner: string,
    repo: string,
    page: number = 1,
    perPage: number = 30,
    state: 'open' | 'closed' | 'all' = 'open',
    searchQuery: string = ''
  ): Promise<{ issues: GitHubIssue[]; hasMore: boolean }> {
    try {
      let url = `${this.BASE_URL}/repos/${owner}/${repo}/issues`;
      let params: any = {
        state,
        sort: 'updated',
        direction: 'desc',
        per_page: perPage,
        page,
      };

      // If search query is provided, use GitHub search API instead
      if (searchQuery.trim()) {
        url = `${this.BASE_URL}/search/issues`;
        params = {
          q: `${searchQuery} repo:${owner}/${repo} is:issue`,
          sort: 'updated',
          order: 'desc',
          per_page: perPage,
          page,
        };

        // Add state filter to search query if not 'all'
        if (state !== 'all') {
          params.q += ` state:${state}`;
        }
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        params,
        timeout: this.TIMEOUT,
      });

      const issues = searchQuery.trim()
        ? response.data.items.filter((issue: any) => !issue.pull_request)
        : response.data.filter((issue: any) => !issue.pull_request);
      const hasMore = issues.length === perPage;

      return { issues, hasMore };
    } catch (error: any) {
      throw new Error(`Failed to fetch issues: ${error.message}`);
    }
  }

  // Pull Requests operations
  static async getRepositoryPullRequests(
    accessToken: string,
    owner: string,
    repo: string,
    page: number = 1,
    perPage: number = 30,
    state: 'open' | 'closed' | 'all' = 'open'
  ): Promise<{ pullRequests: GitHubPullRequest[]; hasMore: boolean }> {
    try {
      const response = await axios.get(`${this.BASE_URL}/repos/${owner}/${repo}/pulls`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        params: {
          state,
          sort: 'updated',
          direction: 'desc',
          per_page: perPage,
          page,
        },
        timeout: this.TIMEOUT,
      });

      const pullRequests = response.data;
      const hasMore = pullRequests.length === perPage;

      return { pullRequests, hasMore };
    } catch (error: any) {
      throw new Error(`Failed to fetch pull requests: ${error.message}`);
    }
  }

  // Webhook operations
  static async createWebhook(
    accessToken: string,
    owner: string,
    repo: string,
    webhookUrl: string,
    secret: string,
    events: string[] = ['issues', 'pull_request']
  ): Promise<any> {
    try {
      const response = await axios.post(
        `${this.BASE_URL}/repos/${owner}/${repo}/hooks`,
        {
          name: 'web',
          active: true,
          events,
          config: {
            url: webhookUrl,
            content_type: 'json',
            secret,
            insecure_ssl: '0',
          },
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/vnd.github.v3+json',
          },
          timeout: this.TIMEOUT,
        }
      );
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 422) {
        throw new Error('Webhook already exists');
      }
      throw error;
    }
  }

  static async deleteWebhook(
    accessToken: string,
    owner: string,
    repo: string,
    webhookId: string
  ): Promise<void> {
    try {
      await axios.delete(`${this.BASE_URL}/repos/${owner}/${repo}/hooks/${webhookId}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        timeout: this.TIMEOUT,
      });
    } catch (error: any) {
      throw new Error(`Failed to delete webhook: ${error.message}`);
    }
  }

  static async getWebhooks(accessToken: string, owner: string, repo: string): Promise<any[]> {
    try {
      const response = await axios.get(`${this.BASE_URL}/repos/${owner}/${repo}/hooks`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
        timeout: this.TIMEOUT,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to fetch webhooks: ${error.message}`);
    }
  }

  // GitHub App authentication methods
  static generateAppJWT(appId: string, privateKey: string): string {
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iat: now - 60,
      exp: now + 600,
      iss: appId,
    };

    let cleanPrivateKey = privateKey;

    cleanPrivateKey = cleanPrivateKey.replace(/\\n/g, '\n');

    if (!cleanPrivateKey.includes('\n')) {
      cleanPrivateKey = cleanPrivateKey
        .replace(/-----BEGIN RSA PRIVATE KEY-----/g, '-----BEGIN RSA PRIVATE KEY-----\n')
        .replace(/-----END RSA PRIVATE KEY-----/g, '\n-----END RSA PRIVATE KEY-----')
        .replace(/-----BEGIN PRIVATE KEY-----/g, '-----BEGIN PRIVATE KEY-----\n')
        .replace(/-----END PRIVATE KEY-----/g, '\n-----END PRIVATE KEY-----');
    }

    cleanPrivateKey = cleanPrivateKey
      .replace(/-----BEGIN RSA PRIVATE KEY-----\s*/g, '-----BEGIN RSA PRIVATE KEY-----\n')
      .replace(/\s*-----END RSA PRIVATE KEY-----/g, '\n-----END RSA PRIVATE KEY-----')
      .replace(/-----BEGIN PRIVATE KEY-----\s*/g, '-----BEGIN PRIVATE KEY-----\n')
      .replace(/\s*-----END PRIVATE KEY-----/g, '\n-----END PRIVATE KEY-----');

    cleanPrivateKey = cleanPrivateKey.trim();

    if (!cleanPrivateKey.includes('-----BEGIN') || !cleanPrivateKey.includes('-----END')) {
      throw new Error('Invalid private key format. Must be a valid PEM formatted private key.');
    }

    try {
      return jwt.sign(payload, cleanPrivateKey, { algorithm: 'RS256' });
    } catch (error: any) {
      throw new Error(`Failed to generate JWT: ${error.message}`);
    }
  }

  static async getInstallation(appJWT: string, installationId: string): Promise<any> {
    try {
      const response = await axios.get(`${this.BASE_URL}/app/installations/${installationId}`, {
        headers: {
          Authorization: `Bearer ${appJWT}`,
          Accept: 'application/vnd.github.v3+json',
        },
        timeout: this.TIMEOUT,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to get installation: ${error.message}`);
    }
  }

  static async getInstallationToken(appJWT: string, installationId: string): Promise<any> {
    try {
      const response = await axios.post(
        `${this.BASE_URL}/app/installations/${installationId}/access_tokens`,
        {},
        {
          headers: {
            Authorization: `Bearer ${appJWT}`,
            Accept: 'application/vnd.github.v3+json',
          },
          timeout: this.TIMEOUT,
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to get installation token: ${error.message}`);
    }
  }

  // Webhook verification
  static verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload, 'utf8')
        .digest('hex');

      const actualSignature = signature.replace('sha256=', '');

      return crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(actualSignature, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  // Client-side API methods (accessed from frontend)
  static async getOAuthUrlClient(): Promise<{ url: string; state: string; popup: boolean }> {
    try {
      const response = await axios.get('/api/github/oauth/authorize?popup=true');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to get OAuth URL');
    }
  }

  static async getConnectionStatus(): Promise<any> {
    try {
      const response = await axios.get('/api/github/status');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to get connection status');
    }
  }

  static async disconnect(): Promise<any> {
    try {
      const response = await axios.delete('/api/github/disconnect');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to disconnect GitHub');
    }
  }

  static async getAvailableRepositories(
    page: number = 1,
    perPage: number = 30,
    search: string = ''
  ): Promise<any> {
    try {
      const response = await axios.get('/api/github/repositories/available', {
        params: { page, per_page: perPage, search },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch available repositories');
    }
  }

  static async getConnectedRepositories(): Promise<any> {
    try {
      const response = await axios.get('/api/github/repositories');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch connected repositories');
    }
  }

  static async connectRepository(repositoryId: string): Promise<any> {
    try {
      const response = await axios.post('/api/github/repositories/connect', {
        repositoryId,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to connect repository');
    }
  }

  static async disconnectRepository(repositoryId: string): Promise<any> {
    try {
      const response = await axios.post('/api/github/repositories/disconnect', {
        repositoryId,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to disconnect repository');
    }
  }

  static async getRepositoryPullRequestsClient(
    repositoryId: string,
    page: number = 1,
    state: string = 'open'
  ): Promise<any> {
    try {
      const response = await axios.get(`/api/github/repositories/${repositoryId}/pulls`, {
        params: { page, state },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch pull requests');
    }
  }

  static async getRepositoryIssuesClient(
    repositoryId: string,
    page: number = 1,
    state: string = 'open',
    search: string = ''
  ): Promise<any> {
    try {
      const response = await axios.get(`/api/github/repositories/${repositoryId}/issues`, {
        params: { page, state, search },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch issues');
    }
  }

  static async createTaskFromIssue(repositoryId: string, issueNumber: number): Promise<any> {
    try {
      const response = await axios.post('/api/github/create-task', {
        repositoryId,
        issueNumber,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create task from issue');
    }
  }

  static async getAllIssues(
    page: number = 1,
    state: string = 'open',
    search: string = ''
  ): Promise<any> {
    try {
      const response = await axios.get('/api/github/issues/all', {
        params: { page, state, search },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch all issues');
    }
  }

  static async getAllPullRequests(page: number = 1, state: string = 'open'): Promise<any> {
    try {
      const response = await axios.get('/api/github/pulls/all', {
        params: { page, state },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch all pull requests');
    }
  }

  static async updateRepositorySettings(
    repositoryId: string,
    settings: {
      autoCreateTasks?: boolean;
      syncIssues?: boolean;
      syncPullRequests?: boolean;
      labelMapping?: Record<string, string>;
    }
  ): Promise<any> {
    try {
      const response = await axios.patch(`/api/github/repositories/${repositoryId}/settings`, {
        settings,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to update repository settings');
    }
  }
}
