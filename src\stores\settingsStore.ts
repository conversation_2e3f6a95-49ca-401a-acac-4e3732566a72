import { create } from 'zustand';
import { SettingService } from '@/services/Setting.service';

interface SettingsStore {
  theme: string;
  isLoading: boolean;
  isInitialized: boolean;
  setTheme: (theme: string) => void;
  initializeTheme: () => Promise<string>;
  updateThemeInDB: (theme: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
}

export const useSettingsStore = create<SettingsStore>(set => ({
  theme: 'dark',
  isLoading: false,
  isInitialized: false,
  setTheme: (theme: string) => set({ theme }),
  setLoading: (loading: boolean) => set({ isLoading: loading }),

  initializeTheme: async () => {
    try {
      set({ isLoading: true });
      const response = await SettingService.getGeneralSettings();
      const theme = response?.general?.appearance?.theme || 'dark';
      set({ theme, isLoading: false, isInitialized: true });
      return theme;
    } catch (error) {
      set({ theme: 'dark', isLoading: false, isInitialized: true });
      return 'dark';
    }
  },

  updateThemeInDB: async (theme: string) => {
    try {
      set({ isLoading: true });
      await SettingService.updateTheme(theme);
      set({ theme, isLoading: false });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },
}));
