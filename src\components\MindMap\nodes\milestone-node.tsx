'use client';

import { memo, useState } from 'react';
import { Handle, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Flag, Calendar, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';

interface MilestoneNodeData {
  title: string;
  date: string;
  status: 'pending' | 'completed' | 'overdue';
}

export const MilestoneNode = memo(({ id, data, selected }: NodeProps<MilestoneNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    completed: 'bg-green-100 text-green-800 border-green-200',
    overdue: 'bg-red-100 text-red-800 border-red-200',
  };

  const handleDoubleClick = () => {
    startEditing(id, { ...data, nodeType: 'milestone' });
  };

  return (
    <div
      className="relative"
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />

      <Card
        className={`min-w-[200px] bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg transition-all duration-200 cursor-pointer ${
          selected ? 'ring-2 ring-green-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
        }`}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Flag className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-gray-900 text-sm">
                {data?.title || 'Untitled Milestone'}
              </h3>
            </div>
            {isHovered && (
              <Edit3
                className="h-3 w-3 text-gray-400 hover:text-green-500 cursor-pointer transition-colors duration-200"
                onClick={() => startEditing(id, { ...data, nodeType: 'milestone' })}
              />
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-xs text-gray-600">
              <Calendar className="h-3 w-3 mr-1" />
              {data?.date ? new Date(data.date).toLocaleDateString() : 'No date set'}
            </div>

            <Badge
              variant="outline"
              className={`text-xs ${statusColors[data?.status || 'pending']}`}
            >
              {data?.status || 'pending'}
            </Badge>
          </div>
        </div>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
    </div>
  );
});

MilestoneNode.displayName = 'MilestoneNode';
