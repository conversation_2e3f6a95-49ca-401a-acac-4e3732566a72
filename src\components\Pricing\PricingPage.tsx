'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Check, X, Crown, Zap, Shield, HardDrive, Star } from 'lucide-react';
import { PRICING_PLANS, BILLING_BENEFITS, PAYMENT_METHODS } from '@/constant/PricingPlans';
import { SubscriptionService } from '@/services/Subscription.service';
// Removed unused imports
import { toast } from 'sonner';

interface PricingPageProps {
  currentPlan?: string;
  onPlanSelect?: (planId: string, billingCycle: 'monthly' | 'yearly') => void;
  showCurrentPlan?: boolean;
}

export const PricingPage: React.FC<PricingPageProps> = ({
  currentPlan = 'free',
  onPlanSelect,
  showCurrentPlan = true,
}) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isLoading, setIsLoading] = useState<string | null>(null);
  // Removed unused session and isLoadingSubscription variables

  const handlePlanSelect = async (planId: string) => {
    if (isLoading) return;

    setIsLoading(planId);

    try {
      if (onPlanSelect) {
        await onPlanSelect(planId, billingCycle);
      } else {
        // Default behavior - show upgrade modal or redirect
        toast.info('Please sign in to upgrade your plan');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to process plan selection');
    } finally {
      setIsLoading(null);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <HardDrive className="h-6 w-6 text-gray-500" />;
      case 'basic':
        return <Zap className="h-6 w-6 text-blue-500" />;
      case 'professional':
        return <Crown className="h-6 w-6 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-6 w-6 text-orange-500" />;
      default:
        return <HardDrive className="h-6 w-6" />;
    }
  };

  // Removed unused getFeatureIcon function

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Choose Your Perfect Plan
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
          Designed specifically for the Indian market with affordable pricing
        </p>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span
            className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
          >
            Monthly
          </span>
          <Switch
            checked={billingCycle === 'yearly'}
            onCheckedChange={checked => setBillingCycle(checked ? 'yearly' : 'monthly')}
          />
          <span
            className={`text-sm font-medium ${billingCycle === 'yearly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
          >
            Annual
          </span>
          {billingCycle === 'yearly' && (
            <Badge variant="secondary" className="ml-2">
              Save 17%
            </Badge>
          )}
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {PRICING_PLANS.map(plan => {
          const pricing = SubscriptionService.calculatePlanPrice(plan.id, billingCycle);
          const isCurrentPlan = showCurrentPlan && currentPlan === plan.id;
          const isPopular = plan.popular;

          return (
            <Card
              key={plan.id}
              className={`relative ${isPopular ? 'border-purple-500 shadow-lg scale-105' : ''} ${isCurrentPlan ? 'border-green-500' : ''}`}
            >
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-purple-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    {plan.badge}
                  </Badge>
                </div>
              )}

              {isCurrentPlan && (
                <div className="absolute -top-3 right-4">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Current Plan
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-3">{getPlanIcon(plan.id)}</div>
                <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
                <CardDescription className="text-sm">{plan.description}</CardDescription>

                <div className="mt-4">
                  {plan.price.monthly === 0 ? (
                    <div className="text-3xl font-bold">Free</div>
                  ) : (
                    <div>
                      <div className="text-3xl font-bold">
                        {SubscriptionService.formatPrice(pricing.finalPrice)}
                        <span className="text-sm font-normal text-gray-500">
                          /{billingCycle === 'yearly' ? 'year' : 'month'}
                        </span>
                      </div>
                      {billingCycle === 'yearly' && pricing.discount > 0 && (
                        <div className="text-sm text-gray-500 line-through">
                          {SubscriptionService.formatPrice(pricing.originalPrice)}/year
                        </div>
                      )}
                      {billingCycle === 'yearly' && (
                        <div className="text-sm text-green-600 font-medium">
                          Save {SubscriptionService.formatPrice(pricing.discount)} annually
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <Button
                  className="w-full mb-6"
                  variant={isCurrentPlan ? 'outline' : isPopular ? 'default' : 'outline'}
                  disabled={isCurrentPlan || isLoading === plan.id}
                  onClick={() => handlePlanSelect(plan.id)}
                >
                  {isLoading === plan.id
                    ? 'Processing...'
                    : isCurrentPlan
                      ? 'Current Plan'
                      : plan.ctaText}
                </Button>

                <div className="space-y-3">
                  {plan.features.slice(0, 6).map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div
                        className={`mt-0.5 ${feature.included ? 'text-green-500' : 'text-gray-300'}`}
                      >
                        {feature.included ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div
                          className={`text-sm font-medium ${feature.included ? 'text-gray-900 dark:text-white' : 'text-gray-400'}`}
                        >
                          {feature.name}
                        </div>
                        {feature.limit && (
                          <div className="text-xs text-gray-500">{feature.limit}</div>
                        )}
                      </div>
                    </div>
                  ))}

                  {plan.features.length > 6 && (
                    <div className="text-xs text-gray-500 text-center pt-2">
                      +{plan.features.length - 6} more features
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                <div className="text-xs text-gray-500 text-center">{plan.targetAudience}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Payment Methods */}
      <div className="text-center mb-12">
        <h3 className="text-lg font-semibold mb-4">Accepted Payment Methods</h3>
        <div className="flex flex-wrap justify-center gap-4">
          {Object.values(PAYMENT_METHODS).map(method => (
            <div
              key={method.name}
              className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <span className="text-sm font-medium">{method.name}</span>
              {(method as any).popular && (
                <Badge variant="secondary" className="text-xs">
                  Popular
                </Badge>
              )}
            </div>
          ))}
        </div>
        <p className="text-sm text-gray-500 mt-2">
          All payments are processed securely through Razorpay
        </p>
      </div>

      {/* Billing Benefits */}
      {billingCycle === 'yearly' && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold mb-4 text-center">Annual Billing Benefits</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {BILLING_BENEFITS.yearly.benefits?.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span className="text-sm">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* FAQ or Additional Info */}
      <div className="text-center">
        <p className="text-sm text-gray-500 mb-2">
          Need help choosing? Contact our sales team for personalized recommendations.
        </p>
        <p className="text-xs text-gray-400">
          All plans include a 14-day free trial. No credit card required for the free plan.
        </p>
      </div>
    </div>
  );
};
