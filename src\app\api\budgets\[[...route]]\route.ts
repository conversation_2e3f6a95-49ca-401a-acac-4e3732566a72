import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { zValidator } from '@hono/zod-validator';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Budget } from '@/models/Budget';
import { Project } from '@/models/Project';
import { NotificationService } from '@/services/Notification.service';
import { z } from 'zod';
import { toast } from 'sonner';

type Variables = {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/budgets');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const userData = {
      id: session.user.id || '',
      name: session.user.name || '',
      email: session.user.email || '',
      image: session.user.image || '',
      organizationId: session.user.organizationId || '',
    };
    c.set('user', userData);
    await next();
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

const budgetCategorySchema = z.object({
  name: z.string().min(1).max(100),
  allocatedAmount: z.number().min(0),
  spentAmount: z.number().min(0).default(0),
  description: z.string().optional(),
});

const expenseSchema = z.object({
  amount: z.number().min(0.01),
  category: z.string().min(1),
  description: z.string().optional(),
  date: z.string().datetime(),
  receiptNumber: z.string().optional(),
  vendor: z.string().optional(),
  attachments: z
    .array(
      z.object({
        name: z.string(),
        url: z.string(),
        public_id: z.string(),
      })
    )
    .optional(),
});

const budgetSchema = z.object({
  projectId: z.string(),
  totalBudget: z.number().min(0),
  categories: z.array(budgetCategorySchema).min(1),
  currency: z.enum(['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR']).default('USD'),
  alerts: z
    .object({
      thresholdPercentage: z.number().min(1).max(100).default(80),
      notifyUsers: z.array(z.string()).optional(),
      enabled: z.boolean().default(true),
      emailNotifications: z.boolean().default(true),
      pushNotifications: z.boolean().default(true),
    })
    .optional(),
  status: z.enum(['Active', 'Completed', 'Exceeded', 'Frozen']).default('Active'),
  fiscalYear: z.number().min(2000).max(3000).optional(),
});

const budgetUpdateSchema = z.object({
  totalBudget: z.number().min(0).optional(),
  categories: z.array(budgetCategorySchema).optional(),
  currency: z.enum(['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR']).optional(),
  alerts: z
    .object({
      thresholdPercentage: z.number().min(1).max(100),
      notifyUsers: z.array(z.string()).optional(),
      enabled: z.boolean(),
      emailNotifications: z.boolean(),
      pushNotifications: z.boolean(),
    })
    .optional(),
  status: z.enum(['Active', 'Completed', 'Exceeded', 'Frozen']).optional(),
  fiscalYear: z.number().min(2000).max(3000).optional(),
});

// Create a new budget
app.post('/', zValidator('json', budgetSchema), async c => {
  const user = c.get('user');
  const budgetData = c.req.valid('json');

  try {
    await connectDB();

    const project = await Project.findOne({
      _id: budgetData.projectId,
      organizationId: user.organizationId,
    });

    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    const existingBudget = await Budget.findOne({
      projectId: budgetData.projectId,
      organizationId: user.organizationId,
    });

    if (existingBudget) {
      return c.json({ error: 'Budget already exists for this project' }, 409);
    }

    const totalAllocated = budgetData.categories.reduce((sum, cat) => sum + cat.allocatedAmount, 0);
    if (totalAllocated > budgetData.totalBudget) {
      return c.json({ error: 'Total allocated amount cannot exceed total budget' }, 400);
    }

    const budget = new Budget({
      ...budgetData,
      createdBy: user.id,
      organizationId: user.organizationId,
      spentAmount: 0,
      expenses: [],
    });

    await budget.save();

    try {
      const notifications = (project.members || []).map(memberId => ({
        type: 'success' as const,
        title: 'Budget Created',
        description: `Budget has been created for project ${project.name}`,
        userId: memberId,
        metadata: {
          projectId: project._id,
          budgetId: budget.budgetId,
          amount: budgetData.totalBudget,
          currency: budgetData.currency,
        },
      }));

      await Promise.all(
        notifications.map(notification => NotificationService.createNotification(notification))
      );
    } catch (notificationError) {
      toast.error('Failed to send budget creation notification');
    }

    return c.json({ success: true, budget });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get all budgets for the organization
app.get('/', async c => {
  const user = c.get('user');
  const { limit = '50', skip = '0', status, fiscalYear } = c.req.query();

  try {
    await connectDB();

    const query: any = { organizationId: user.organizationId };

    if (status) {
      query.status = status;
    }

    if (fiscalYear) {
      query.fiscalYear = parseInt(fiscalYear);
    }

    const budgets = await Budget.find(query)
      .populate('projectId', 'name')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(skip));

    const total = await Budget.countDocuments(query);

    return c.json({
      budgets,
      total,
      page: Math.floor(parseInt(skip) / parseInt(limit)) + 1,
      totalPages: Math.ceil(total / parseInt(limit)),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update expense
app.put('/expenses/:expenseId', zValidator('json', expenseSchema), async c => {
  const user = c.get('user');
  const expenseId = c.req.param('expenseId');
  const updateData = c.req.valid('json');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      'expenses._id': expenseId,
      organizationId: user.organizationId,
    });

    if (!budget) {
      return c.json({ error: 'Expense not found' }, 404);
    }

    const expenseIndex = budget.expenses.findIndex(expense => expense._id.toString() === expenseId);

    if (expenseIndex === -1) {
      return c.json({ error: 'Expense not found' }, 404);
    }

    const category = budget.categories.find(cat => cat.name === updateData.category);
    if (!category) {
      return c.json({ error: 'Category not found in budget' }, 400);
    }

    budget.expenses[expenseIndex] = {
      ...budget.expenses[expenseIndex],
      ...updateData,
      date: new Date(updateData.date),
      attachments: updateData.attachments || [],
    };

    await budget.save();

    await checkBudgetThresholds(budget);

    return c.json({ success: true, expense: budget.expenses[expenseIndex], budget });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete expense
app.delete('/expenses/:expenseId', async c => {
  const user = c.get('user');
  const expenseId = c.req.param('expenseId');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      'expenses._id': expenseId,
      organizationId: user.organizationId,
    });

    if (!budget) {
      return c.json({ error: 'Expense not found' }, 404);
    }

    const expenseIndex = budget.expenses.findIndex(expense => expense._id.toString() === expenseId);

    if (expenseIndex === -1) {
      return c.json({ error: 'Expense not found' }, 404);
    }

    budget.expenses.splice(expenseIndex, 1);
    await budget.save();

    return c.json({ success: true });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get budget analytics and reports
app.get('/reports/:budgetId', async c => {
  const user = c.get('user');
  const budgetId = c.req.param('budgetId');
  const { period = 'monthly' } = c.req.query();

  try {
    await connectDB();

    const budget = await Budget.findOne({
      budgetId,
      organizationId: user.organizationId,
    }).populate('projectId', 'name');

    if (!budget) {
      return c.json({ error: 'Budget not found' }, 404);
    }

    const utilizationPercentage =
      budget.totalBudget > 0 ? (budget.spentAmount / budget.totalBudget) * 100 : 0;
    const remainingAmount = budget.totalBudget - budget.spentAmount;

    const categoryAnalysis = budget.categories.map(category => {
      const categoryExpenses = budget.expenses.filter(
        expense => expense.category === category.name
      );
      const categorySpent = categoryExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      const categoryUtilization =
        category.allocatedAmount > 0 ? (categorySpent / category.allocatedAmount) * 100 : 0;

      return {
        name: category.name,
        allocated: category.allocatedAmount,
        spent: categorySpent,
        remaining: category.allocatedAmount - categorySpent,
        utilization: categoryUtilization,
        expenseCount: categoryExpenses.length,
      };
    });

    const spendingTrends = generateSpendingTrends(budget.expenses, period);
    const breakdown = generateSpendingBreakdown(budget.expenses, period);

    const varianceAnalysis = categoryAnalysis.map(category => ({
      category: category.name,
      budgetVariance: category.allocated - category.spent,
      variancePercentage:
        category.allocated > 0
          ? ((category.allocated - category.spent) / category.allocated) * 100
          : 0,
      status:
        category.spent > category.allocated
          ? 'over_budget'
          : category.utilization > 80
            ? 'near_limit'
            : 'on_track',
    }));

    const forecast = generateBudgetForecast(budget.expenses, budget.totalBudget);

    const analytics = {
      summary: {
        totalBudget: budget.totalBudget,
        spentAmount: budget.spentAmount,
        remainingAmount,
        utilizationPercentage,
        currency: budget.currency,
        status: budget.status,
      },
      categoryAnalysis,
      spendingTrends,
      breakdown,
      varianceAnalysis,
      forecast,
      alerts: {
        overBudgetCategories: categoryAnalysis.filter(cat => cat.utilization > 100),
        nearLimitCategories: categoryAnalysis.filter(
          cat => cat.utilization > 80 && cat.utilization <= 100
        ),
        underutilizedCategories: categoryAnalysis.filter(cat => cat.utilization < 20),
      },
    };

    return c.json({ analytics });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update budget
app.put('/:budgetId', zValidator('json', budgetUpdateSchema), async c => {
  const user = c.get('user');
  const budgetId = c.req.param('budgetId');
  const updateData = c.req.valid('json');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      budgetId,
      organizationId: user.organizationId,
    });

    if (!budget) {
      return c.json({ error: 'Budget not found' }, 404);
    }

    if (updateData.categories) {
      const totalBudget = updateData.totalBudget || budget.totalBudget;
      const totalAllocated = updateData.categories.reduce(
        (sum, cat) => sum + cat.allocatedAmount,
        0
      );

      if (totalAllocated > totalBudget) {
        return c.json({ error: 'Total allocated amount cannot exceed total budget' }, 400);
      }
    }

    Object.assign(budget, updateData);
    await budget.save();

    await checkBudgetThresholds(budget);

    return c.json({ success: true, budget });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete budget
app.delete('/:budgetId', async c => {
  const user = c.get('user');
  const budgetId = c.req.param('budgetId');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      budgetId,
      organizationId: user.organizationId,
    });

    if (!budget) {
      return c.json({ error: 'Budget not found' }, 404);
    }

    await Budget.findByIdAndDelete(budget._id);

    return c.json({ success: true });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

async function checkBudgetThresholds(budget: any): Promise<void> {
  if (!budget.alerts?.enabled) return;

  const utilizationPercentage =
    budget.totalBudget > 0 ? (budget.spentAmount / budget.totalBudget) * 100 : 0;

  if (utilizationPercentage >= budget.alerts.thresholdPercentage) {
    try {
      const project = await Project.findById(budget.projectId);
      const recipients =
        budget.alerts.notifyUsers?.length > 0 ? budget.alerts.notifyUsers : project?.members || [];

      const notifications = recipients.map(userId => ({
        userId,
        type: 'error',
        title: 'Budget Threshold Alert',
        description: `Budget utilization has reached ${utilizationPercentage.toFixed(1)}% for project ${project?.name}`,
        metadata: {
          projectId: budget.projectId,
          budgetId: budget.budgetId,
          utilizationPercentage,
          threshold: budget.alerts.thresholdPercentage,
          remainingAmount: budget.totalBudget - budget.spentAmount,
        },
      }));

      await Promise.all(
        notifications.map(notification => NotificationService.createNotification(notification))
      );
    } catch (error) {
      toast.error('Failed to send budget threshold notification');
    }
  }

  for (const category of budget.categories) {
    const categoryUtilization =
      category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0;

    if (categoryUtilization >= budget.alerts.thresholdPercentage) {
      try {
        const project = await Project.findById(budget.projectId);
        const recipients =
          budget.alerts.notifyUsers?.length > 0
            ? budget.alerts.notifyUsers
            : project?.members || [];

        const notifications = recipients.map(userId => ({
          userId,
          type: 'error',
          title: 'Category Budget Alert',
          description: `${category.name} category has reached ${categoryUtilization.toFixed(1)}% utilization in project ${project?.name}`,
          metadata: {
            projectId: budget.projectId,
            budgetId: budget.budgetId,
            category: category.name,
            utilizationPercentage: categoryUtilization,
            threshold: budget.alerts.thresholdPercentage,
          },
        }));

        await Promise.all(
          notifications.map(notification => NotificationService.createNotification(notification))
        );
      } catch (error) {
        toast.error('Failed to send category threshold notification');
      }
    }
  }
}

function generateSpendingTrends(expenses: any[], period: string): any[] {
  const trends = new Map();

  expenses.forEach(expense => {
    const date = new Date(expense.date);
    let key: string;
    let weekStart;
    let quarter;

    switch (period) {
      case 'daily':
        key = date.toISOString().split('T')[0];
        break;
      case 'weekly':
        weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'monthly':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'quarterly':
        quarter = Math.floor(date.getMonth() / 3) + 1;
        key = `${date.getFullYear()}-Q${quarter}`;
        break;
      default:
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }

    if (!trends.has(key)) {
      trends.set(key, { period: key, amount: 0, count: 0 });
    }

    const trend = trends.get(key);
    trend.amount += expense.amount;
    trend.count += 1;
  });

  return Array.from(trends.values()).sort((a, b) => a.period.localeCompare(b.period));
}

function generateSpendingBreakdown(expenses: any[], _period: string): any[] {
  const breakdown = new Map();

  expenses.forEach(expense => {
    const category = expense.category;

    if (!breakdown.has(category)) {
      breakdown.set(category, { category, amount: 0, count: 0, percentage: 0 });
    }

    const item = breakdown.get(category);
    item.amount += expense.amount;
    item.count += 1;
  });

  const total = Array.from(breakdown.values()).reduce((sum, item) => sum + item.amount, 0);

  breakdown.forEach(item => {
    item.percentage = total > 0 ? (item.amount / total) * 100 : 0;
  });

  return Array.from(breakdown.values()).sort((a, b) => b.amount - a.amount);
}

function generateBudgetForecast(expenses: any[], totalBudget: number): any {
  if (expenses.length === 0) {
    return {
      projectedSpend: 0,
      projectedCompletion: null,
      burnRate: 0,
      daysRemaining: null,
    };
  }

  const sortedExpenses = expenses.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  const firstExpense = new Date(sortedExpenses[0].date);
  const lastExpense = new Date(sortedExpenses[sortedExpenses.length - 1].date);
  const daysDiff = Math.max(
    1,
    Math.ceil((lastExpense.getTime() - firstExpense.getTime()) / (1000 * 60 * 60 * 24))
  );

  const totalSpent = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const dailyBurnRate = totalSpent / daysDiff;

  const remainingBudget = totalBudget - totalSpent;
  const daysUntilExhaustion = remainingBudget > 0 ? Math.ceil(remainingBudget / dailyBurnRate) : 0;

  const projectedCompletion = new Date();
  projectedCompletion.setDate(projectedCompletion.getDate() + daysUntilExhaustion);

  return {
    projectedSpend: totalSpent + dailyBurnRate * 30,
    projectedCompletion: daysUntilExhaustion > 0 ? projectedCompletion : null,
    burnRate: dailyBurnRate,
    daysRemaining: daysUntilExhaustion,
  };
}

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
