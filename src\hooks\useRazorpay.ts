import { useState, useCallback } from 'react';

import { SubscriptionService } from '@/services/Subscription.service';
import { TransactionService } from '@/services/Transaction.service';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';

declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface RazorpaySubscriptionOptions {
  planId: 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'yearly';
  onSuccess?: (response: any) => void;
  onFailure?: (error: Error) => void;
}

export interface RazorpayResponse {
  razorpay_payment_id: string;
  razorpay_subscription_id: string;
  razorpay_order_id?: string;
  razorpay_signature: string;
}

export const useRazorpay = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const { data: session } = useSession();

  /**
   * Load Razorpay script
   */
  const loadRazorpayScript = useCallback((): Promise<boolean> => {
    return new Promise(resolve => {
      if (window.Razorpay) {
        setIsScriptLoaded(true);
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        setIsScriptLoaded(true);
        resolve(true);
      };
      script.onerror = () => {
        console.error('Failed to load Razorpay script');
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }, []);

  /**
   * Process subscription payment
   */
  const processSubscriptionPayment = useCallback(
    async ({ planId, billingCycle, onSuccess, onFailure }: RazorpaySubscriptionOptions) => {
      if (!session?.user) {
        const error = new Error('Please sign in to upgrade your plan');
        onFailure?.(error);
        toast.error(error.message);
        return;
      }

      setIsLoading(true);

      try {
        // Step 1: Load Razorpay script
        const scriptLoaded = await loadRazorpayScript();
        if (!scriptLoaded) {
          throw new Error('Failed to load payment gateway');
        }

        // Step 2: Create transaction and order
        const transactionData = await TransactionService.createTransaction({
          planId,
          billingCycle,
          type: 'subscription_payment',
          metadata: {
            source: 'web',
          },
        });

        // Step 3: Get plan config for display
        const planConfig = SubscriptionService.getPlanConfig(planId);

        if (!planConfig) {
          throw new Error('Invalid plan selected');
        }

        // Step 4: Configure Razorpay options
        const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
        if (!razorpayKey) {
          throw new Error('Payment gateway not configured');
        }

        const options = {
          key: razorpayKey,
          order_id: transactionData.razorpayOrder.id,
          amount: transactionData.razorpayOrder.amount,
          currency: transactionData.razorpayOrder.currency,
          name: 'TaskMantra',
          description: transactionData.transaction.description,
          image: '/logo.png', // Add your logo
          handler: async (response: RazorpayResponse) => {
            try {
              // Verify payment on server
              await TransactionService.verifyPayment({
                transactionId: transactionData.transaction.transactionId,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id || '',
                razorpay_signature: response.razorpay_signature,
              });

              toast.success('Payment successful! Your plan has been upgraded.');
              onSuccess?.(response);
            } catch (error) {
              console.error('Payment verification error:', error);
              const errorMessage =
                error instanceof Error ? error.message : 'Payment verification failed';
              toast.error(errorMessage);
              onFailure?.(new Error(errorMessage));
            }
          },

          prefill: {
            name: session.user.name || '',
            email: session.user.email || '',
            contact: '', // Add phone number if available
          },
          theme: {
            color: '#3399cc',
          },
          modal: {
            ondismiss: () => {
              const error = new Error('Payment cancelled by user');
              onFailure?.(error);
              toast.info('Payment cancelled');
            },
            confirm_close: true,
            animation: true,
          },
          retry: {
            enabled: true,
            max_count: 3,
          },

          // Configure payment methods for Indian market
          config: {
            display: {
              blocks: {
                utib: {
                  name: 'Pay using UPI',
                  instruments: [
                    {
                      method: 'upi',
                    },
                  ],
                },
                banks: {
                  name: 'Net Banking',
                  instruments: [
                    {
                      method: 'netbanking',
                    },
                  ],
                },
                cards: {
                  name: 'Cards',
                  instruments: [
                    {
                      method: 'card',
                    },
                  ],
                },
                wallets: {
                  name: 'Wallets',
                  instruments: [
                    {
                      method: 'wallet',
                    },
                  ],
                },
              },
              sequence: ['block.utib', 'block.banks', 'block.cards', 'block.wallets'],
              preferences: {
                show_default_blocks: false,
              },
            },
          },
          notes: {
            planId,
            billingCycle,
            organizationId: session.user.organizationId,
          },
        };

        // Step 5: Open Razorpay checkout
        const rzp = new window.Razorpay(options);
        rzp.open();
      } catch (error) {
        console.error('Payment processing error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
        toast.error(errorMessage);
        onFailure?.(new Error(errorMessage));
      } finally {
        setIsLoading(false);
      }
    },
    [session, loadRazorpayScript]
  );

  /**
   * Check if Razorpay is available
   */
  const isRazorpayAvailable = useCallback((): boolean => {
    return !!process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
  }, []);

  return {
    processSubscriptionPayment,
    isLoading,
    isScriptLoaded,
    isRazorpayAvailable,
    loadRazorpayScript,
  };
};
