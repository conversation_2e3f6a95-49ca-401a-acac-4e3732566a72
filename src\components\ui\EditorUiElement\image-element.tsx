'use client';

import * as React from 'react';

import type { TImageElement } from '@udecode/plate-media';
import type { PlateElementProps } from '@udecode/plate/react';

import { useDraggable } from '@udecode/plate-dnd';
import { Image, ImagePlugin, useMediaState } from '@udecode/plate-media/react';
import { ResizableProvider, useResizableValue } from '@udecode/plate-resizable';
import { PlateElement, withHOC } from '@udecode/plate/react';

import { cn } from '@/lib/utils';

import { Caption, CaptionTextarea } from './caption';
import { MediaPopover } from './media-popover';
import { mediaResizeHandleVariants, Resizable, ResizeHandle } from './resize-handle';

export const ImageElement = withHOC(
  ResizableProvider,
  function ImageElement(props: PlateElementProps<TImageElement>) {
    const { align = 'center', focused, readOnly, selected } = useMediaState();
    const width = useResizableValue('width');

    // Custom hook to safely use draggable with error boundary
    function useSafeDraggable() {
      const fallbackHandleRef = React.useRef<HTMLImageElement>(null);

      try {
        return useDraggable({
          element: props.element,
        });
      } catch (error) {
        // Return fallback values when DND context is not available
        return {
          isDragging: false,
          handleRef: fallbackHandleRef,
        };
      }
    }

    const { isDragging, handleRef } = useSafeDraggable();

    return (
      <MediaPopover plugin={ImagePlugin}>
        <PlateElement {...props} className="py-2.5">
          <figure className="group relative m-0" contentEditable={false}>
            <Resizable
              align={align}
              options={{
                align,
                readOnly,
              }}
            >
              <ResizeHandle
                className={mediaResizeHandleVariants({ direction: 'left' })}
                options={{ direction: 'left' }}
              />
              <Image
                ref={handleRef}
                className={cn(
                  'block w-full max-w-full cursor-pointer object-cover px-0',
                  'rounded-sm transition-all duration-200',
                  'hover:shadow-lg',
                  focused && selected && 'ring-2 ring-ring ring-offset-2',
                  isDragging && 'opacity-50',
                  // Responsive sizing
                  'max-h-[80vh] sm:max-h-[70vh]',
                  // Mobile optimizations
                  'touch-manipulation'
                )}
                alt={(props.attributes as any).alt || 'Image'}
                loading="lazy"
              />
              <ResizeHandle
                className={mediaResizeHandleVariants({
                  direction: 'right',
                })}
                options={{ direction: 'right' }}
              />
            </Resizable>

            <Caption style={{ width }} align={align}>
              <CaptionTextarea
                readOnly={readOnly}
                onFocus={e => {
                  e.preventDefault();
                }}
                placeholder="Write a caption..."
              />
            </Caption>
          </figure>

          {props.children}
        </PlateElement>
      </MediaPopover>
    );
  }
);
