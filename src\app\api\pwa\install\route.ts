import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { userAgent, platform, outcome } = await request.json();

    // Log PWA installation analytics
    console.log('PWA Install Event:', {
      userAgent,
      platform,
      outcome,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      message: 'Install event tracked',
    });
  } catch (error) {
    console.error('PWA install tracking error:', error);
    return NextResponse.json({ error: 'Failed to track install event' }, { status: 500 });
  }
}
