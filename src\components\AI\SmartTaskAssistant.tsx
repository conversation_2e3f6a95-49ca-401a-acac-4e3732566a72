'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Clock,
  Target,
  TrendingUp,
  Lightbulb,
  Zap,
  Send,
  Mic,
  MicOff,
  Wand2,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SmartTaskAssistantProps {
  className?: string;
  onTaskCreate?: (task: any) => void;
}

export const SmartTaskAssistant: React.FC<SmartTaskAssistantProps> = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock AI suggestions
  const suggestions = [
    {
      type: 'priority',
      icon: <Target className="h-4 w-4" />,
      title: 'High Priority Tasks',
      description: 'You have 3 urgent tasks due today. Focus on "API Integration" first.',
      action: 'View Tasks',
      color: 'text-red-600 dark:text-red-400',
    },
    {
      type: 'time',
      icon: <Clock className="h-4 w-4" />,
      title: 'Time Optimization',
      description:
        'Based on your patterns, schedule coding tasks in the morning for 40% better efficiency.',
      action: 'Apply Schedule',
      color: 'text-blue-600 dark:text-blue-400',
    },
    {
      type: 'productivity',
      icon: <TrendingUp className="h-4 w-4" />,
      title: 'Productivity Boost',
      description: 'Take a 15-minute break. Your focus has been declining for the past hour.',
      action: 'Start Break',
      color: 'text-green-600 dark:text-green-400',
    },
    {
      type: 'insight',
      icon: <Lightbulb className="h-4 w-4" />,
      title: 'Smart Insight',
      description: 'Similar tasks usually take 2.5 hours. Consider breaking down large tasks.',
      action: 'Learn More',
      color: 'text-purple-600 dark:text-purple-400',
    },
  ];

  const quickActions = [
    'Create a task for tomorrow',
    'Schedule my day optimally',
    'What should I focus on now?',
    'Analyze my productivity',
    'Set up automated workflows',
  ];

  const handleVoiceInput = () => {
    setIsListening(!isListening);
    // Implement voice recognition here
  };

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setIsProcessing(true);
    // Simulate AI processing
    setTimeout(() => {
      setIsProcessing(false);
      setMessage('');
    }, 2000);
  };

  const handleQuickAction = (action: string) => {
    setMessage(action);
  };

  return (
    <>
      {/* AI Assistant Trigger Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-50"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: 'spring', stiffness: 260, damping: 20 }}
      >
        <Button
          onClick={() => setIsOpen(true)}
          className="h-14 w-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl theme-transition"
        >
          <Sparkles className="h-6 w-6 text-white" />
        </Button>
      </motion.div>

      {/* AI Assistant Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed top-0 right-0 h-full w-96 bg-background border-l border-border z-50 shadow-2xl"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="p-4 border-b border-border bg-gradient-to-r from-purple-600/10 to-blue-600/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-2 rounded-lg">
                      <Brain className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold theme-text-primary">AI Assistant</h3>
                      <p className="text-xs theme-text-secondary">Your productivity companion</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="theme-button-ghost"
                  >
                    ×
                  </Button>
                </div>
              </div>

              {/* AI Suggestions */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                <div>
                  <h4 className="font-medium theme-text-primary mb-3 flex items-center gap-2">
                    <Zap className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                    Smart Suggestions
                  </h4>
                  <div className="space-y-3">
                    {suggestions.map((suggestion, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className="theme-surface-elevated hover-reveal cursor-pointer">
                          <CardContent className="p-3">
                            <div className="flex items-start gap-3">
                              <div className={cn('mt-0.5', suggestion.color)}>
                                {suggestion.icon}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h5 className="font-medium text-sm theme-text-primary">
                                  {suggestion.title}
                                </h5>
                                <p className="text-xs theme-text-secondary mt-1">
                                  {suggestion.description}
                                </p>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="mt-2 h-6 px-2 text-xs theme-button-ghost"
                                >
                                  {suggestion.action}
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Quick Actions */}
                <div>
                  <h4 className="font-medium theme-text-primary mb-3 flex items-center gap-2">
                    <Wand2 className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    Quick Actions
                  </h4>
                  <div className="space-y-2">
                    {quickActions.map((action, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickAction(action)}
                        className="w-full justify-start text-xs theme-button-secondary"
                      >
                        {action}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Chat Interface */}
              <div className="p-4 border-t border-border">
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Ask me anything about your tasks..."
                      value={message}
                      onChange={e => setMessage(e.target.value)}
                      onKeyPress={e => e.key === 'Enter' && handleSendMessage()}
                      className="flex-1 theme-input"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleVoiceInput}
                      className={cn(
                        'theme-button-ghost',
                        isListening && 'bg-red-500/10 text-red-600 dark:text-red-400'
                      )}
                    >
                      {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!message.trim() || isProcessing}
                      className="theme-button-primary"
                    >
                      {isProcessing ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                        >
                          <Brain className="h-4 w-4" />
                        </motion.div>
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {isListening && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex items-center gap-2 text-xs theme-text-secondary"
                    >
                      <div className="flex gap-1">
                        {[0, 1, 2].map(i => (
                          <motion.div
                            key={i}
                            className="w-1 h-3 bg-red-500 rounded-full"
                            animate={{ scaleY: [1, 2, 1] }}
                            transition={{
                              duration: 0.5,
                              repeat: Infinity,
                              delay: i * 0.1,
                            }}
                          />
                        ))}
                      </div>
                      Listening...
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
