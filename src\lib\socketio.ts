import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { publisher, subscriber } from './redis';

let io: SocketIOServer | null = null;

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    name?: string;
    email?: string;
    organizationId?: string | null;
  };
}

export function initializeSocketIO(httpServer: HTTPServer): SocketIOServer {
  if (io) {
    return io;
  }

  io = new SocketIOServer(httpServer, {
    cors: {
      origin: process.env.NODE_ENV === 'production'
        ? [process.env.NEXT_PUBLIC_APP_URL, process.env.NEXTAUTH_URL].filter((url): url is string => Boolean(url))
        : ['http://localhost:4000'],
      methods: ['GET', 'POST'],
      credentials: true,
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
    connectTimeout: 45000,
    allowEIO3: true,
    upgradeTimeout: 30000,
    maxHttpBufferSize: 1e6,
    allowRequest: (_, callback) => {
      callback(null, true);
    },
  });

  io.adapter(createAdapter(publisher, subscriber));

  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      const userId = socket.handshake.auth.userId;

      if (!token || !userId || token !== 'authenticated') {
        return next(new Error('Authentication required'));
      }

      socket.userId = userId;
      socket.user = {
        id: userId,
        name: 'User',
        email: '',
        organizationId: null
      };

      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    if (!socket.user) {
      return;
    }

    const userId = socket.user.id;

    socket.join(`user:${userId}`);

    if (socket.user.organizationId) {
      socket.join(`org:${socket.user.organizationId}`);
    }

    socket.emit('connected', {
      message: 'Successfully connected to Socket.IO server',
      userId: userId,
      timestamp: new Date().toISOString()
    });

    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date().toISOString() });
    });

    socket.on('disconnect', () => { });
    socket.on('error', () => { });
  });

  return io;
}

export function getSocketIOServer(): SocketIOServer | null {
  return io;
}

export function broadcastToUser(userId: string, event: string, data: any): void {
  if (!io) return;
  io.to(`user:${userId}`).emit(event, data);
}

export function broadcastToOrganization(orgId: string, event: string, data: any): void {
  if (!io) return;
  io.to(`org:${orgId}`).emit(event, data);
}

export function publishNotification(channel: string, data: any): void {
  publisher?.publish(channel, JSON.stringify(data));
}

export function subscribeToNotifications(): void {
  if (!subscriber) {
    return;
  }

  subscriber.subscribe('notifications').catch(() => { });

  subscriber.on('message', async (channel, message) => {
    if (channel === 'notifications' && io) {
      try {
        const data = JSON.parse(message);

        if (data.userId) {
          const userSockets = io.sockets.adapter.rooms.get(`user:${data.userId}`);
          const isUserOnline = userSockets && userSockets.size > 0;

          if (isUserOnline) {
            broadcastToUser(data.userId, 'notification', data);
          } else {
            try {
              const { PushNotificationService } = await import('@/services/PushNotification.service');

              if (PushNotificationService.isConfigured() && data.notification) {
                await PushNotificationService.sendToUser(data.userId, {
                  title: data.notification.title || 'New Notification',
                  body: data.notification.description || 'You have a new notification',
                  icon: '/icons/icon-192x192.png',
                  badge: '/icons/badge-72x72.png',
                  data: {
                    url: data.notification.link || '/notifications',
                    notificationId: data.notification._id,
                  },
                  tag: data.notification._id || 'notification',
                  requireInteraction: false,
                });
              }
            } catch (pushError) {
              console.error('Failed to send push notification:', pushError);
            }
          }
        }

        if (data.organizationId) {
          broadcastToOrganization(data.organizationId, 'notification', data);
        }
      } catch (error) {
        console.error('Error processing notification message:', error);
      }
    }
  });
}

export function initializeNotificationSystem(): void {
  if (typeof window !== 'undefined') return;

  try {
    setTimeout(() => {
      subscribeToNotifications();
    }, 1000);
  } catch (error) {
    console.error('Failed to initialize notification system:', error);
  }
}