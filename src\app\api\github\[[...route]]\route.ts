import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Integration } from '@/models/Integration';
import { GitHubRepository } from '@/models/GitHubRepository';
import { GitHubWebhook } from '@/models/GitHubWebhook';
import { GitHubService } from '@/services/GitHub.service';
import { NotificationService } from '@/services/Notification.service';
import { GitHubAuthHelper } from '@/helpers/github/auth.helper';
import { GitHubRepositoryHelper } from '@/helpers/github/repository.helper';
import { GitHubTaskHelper } from '@/helpers/github/task.helper';
import crypto from 'crypto';
import axios from 'axios';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/github');
app.use('*', logger());
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// OAuth authorization
app.get('/oauth/authorize', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    const isPopup = c.req.query('popup') === 'true';
    const { url, state } = GitHubAuthHelper.generateOAuthUrl(user.id, user.organizationId, isPopup);

    return c.json({ url, state, popup: isPopup });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// OAuth callback
app.get('/installation/callback', async c => {
  try {
    await connectDB();

    const installationId = c.req.query('installation_id');
    const state = c.req.query('state');

    if (!installationId) {
      return c.json({ error: 'Installation ID not provided' }, 400);
    }

    if (!state) {
      return c.json({ error: 'State parameter not provided' }, 400);
    }

    // Parse and validate state
    const stateData = GitHubAuthHelper.parseState(state);
    const { popup } = stateData;

    // Handle installation callback
    await GitHubAuthHelper.handleInstallationCallback(installationId, stateData);

    // Return success page for popup
    if (popup) {
      return c.html(GitHubAuthHelper.generateSuccessPopupHTML(installationId));
    }

    return c.redirect('/github-hub');
  } catch (error: any) {
    const state = c.req.query('state');
    let popup = false;
    try {
      const stateData = GitHubAuthHelper.parseState(state || '');
      popup = stateData.popup;
    } catch (e) {
      popup = false;
    }

    if (popup) {
      return c.html(GitHubAuthHelper.generateErrorPopupHTML(error.message));
    }

    return c.json({ error: error.message }, 500);
  }
});

// Check connection status
app.get('/status', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const integration = await Integration.findOne({
      userId: user.id,
      provider: 'github',
    });

    if (!integration) {
      return c.json({ connected: false });
    }

    return c.json({
      connected: true,
      user: integration.metadata,
      lastSyncedAt: integration.lastSyncedAt,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Disconnect GitHub integration
app.delete('/disconnect', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    await Integration.deleteOne({
      userId: user.id,
      provider: 'github',
    });

    await GitHubRepository.deleteMany({
      userId: user.id,
    });

    await GitHubWebhook.deleteMany({
      userId: user.id,
    });

    await NotificationService.createNotification({
      userId: user.id,
      title: 'GitHub Disconnected',
      description: 'GitHub integration has been disconnected successfully',
      type: 'integration',
    });

    return c.json({ success: true });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/repositories/available', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const page = parseInt(c.req.query('page') || '1');
    const perPage = parseInt(c.req.query('per_page') || '30');
    const searchQuery = c.req.query('search') || '';

    const result = await GitHubRepositoryHelper.getAvailableRepositoriesWithStatus(
      user.id,
      page,
      perPage,
      searchQuery
    );

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/repositories', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const result = await GitHubRepositoryHelper.getConnectedRepositories(user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/repositories/connect', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const { repositoryId } = await c.req.json();

    if (!repositoryId) {
      return c.json({ error: 'Repository ID is required' }, 400);
    }

    const repository = await GitHubRepositoryHelper.connectRepository(
      user.id,
      user.organizationId || '',
      repositoryId
    );

    return c.json({ repository });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/repositories/disconnect', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const { repositoryId } = await c.req.json();

    if (!repositoryId) {
      return c.json({ error: 'Repository ID is required' }, 400);
    }

    const result = await GitHubRepositoryHelper.disconnectRepository(user.id, repositoryId);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get repository pull requests by repository ID
app.get('/repositories/:repositoryId/pulls', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');
    const page = parseInt(c.req.query('page') || '1');
    const state = c.req.query('state') || 'open';

    const { repository, integration } = await GitHubRepositoryHelper.getRepositoryWithIntegration(
      user.id,
      repositoryId
    );

    const [owner, repo] = repository.fullName.split('/');
    const { pullRequests, hasMore } = await GitHubService.getRepositoryPullRequests(
      integration.accessToken,
      owner,
      repo,
      page,
      30,
      state as 'open' | 'closed' | 'all'
    );

    return c.json({
      pullRequests,
      hasMore,
      page,
      repository: GitHubRepositoryHelper.formatRepositoryResponse(repository),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get repository issues by repository ID
app.get('/repositories/:repositoryId/issues', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');
    const page = parseInt(c.req.query('page') || '1');
    const state = c.req.query('state') || 'open';
    const searchQuery = c.req.query('search') || '';

    const { repository, integration } = await GitHubRepositoryHelper.getRepositoryWithIntegration(
      user.id,
      repositoryId
    );
    // Fetch issues from GitHub
    const [owner, repo] = repository.fullName.split('/');
    const { issues, hasMore } = await GitHubService.getRepositoryIssues(
      integration.accessToken,
      owner,
      repo,
      page,
      30,
      state as 'open' | 'closed' | 'all',
      searchQuery
    );
    return c.json({
      issues,
      hasMore,
      page,
      repository: GitHubRepositoryHelper.formatRepositoryResponse(repository),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get repository settings by repository ID
app.get('/repositories/:repositoryId/settings', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');

    const repository = await GitHubRepository.findOne({
      repositoryId,
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    // Convert Map to plain object for JSON serialization
    const repositoryData = repository.toObject();
    if (repositoryData.syncSettings?.labelMapping instanceof Map) {
      repositoryData.syncSettings.labelMapping = Object.fromEntries(
        repositoryData.syncSettings.labelMapping
      );
    }

    return c.json({ repository: repositoryData });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update repository settings
app.patch('/repositories/:repositoryId/settings', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    const repositoryId = c.req.param('repositoryId');
    if (!repositoryId) {
      return c.json({ error: 'Repository ID is required' }, 400);
    }

    const body = await c.req.json();

    const { settings } = body;

    await connectDB();

    const repository = await GitHubRepository.findOne({
      repositoryId,
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    const updateData: any = {};

    if (settings.autoCreateTasks !== undefined) {
      updateData['syncSettings.autoCreateTasks'] = settings.autoCreateTasks;
    }

    if (settings.syncIssues !== undefined) {
      updateData['syncSettings.syncIssues'] = settings.syncIssues;
    }

    if (settings.syncPullRequests !== undefined) {
      updateData['syncSettings.syncPullRequests'] = settings.syncPullRequests;
    }

    if (settings.labelMapping !== undefined) {
      updateData['syncSettings.labelMapping'] = new Map(Object.entries(settings.labelMapping));
    }

    const updatedRepository = await GitHubRepository.findByIdAndUpdate(
      repository._id,
      { $set: updateData },
      { new: true }
    );

    if (!updatedRepository) {
      return c.json({ error: 'Failed to update repository settings' }, 500);
    }

    const repositoryData = updatedRepository.toObject();
    if (repositoryData.syncSettings?.labelMapping instanceof Map) {
      repositoryData.syncSettings.labelMapping = Object.fromEntries(
        repositoryData.syncSettings.labelMapping
      );
    }

    return c.json({
      success: true,
      message: 'Repository settings updated successfully',
      repository: repositoryData,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get all issues from connected repositories
app.get('/issues/all', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const page = parseInt(c.req.query('page') || '1');
    const state = c.req.query('state') || 'open';
    const searchQuery = c.req.query('search') || '';

    const connectedRepositories = await GitHubRepository.find({
      userId: user.id,
      isConnected: true,
    }).populate('integrationId');

    if (connectedRepositories.length === 0) {
      return c.json({
        issues: [],
        hasMore: false,
        page,
        totalRepositories: 0,
      });
    }
    const allIssues: any[] = [];
    const repositoryData: any[] = [];

    for (const repository of connectedRepositories) {
      const integration = repository.integrationId as any;
      if (!integration?.accessToken) continue;

      const [owner, repo] = repository.fullName.split('/');
      const { issues } = await GitHubService.getRepositoryIssues(
        integration.accessToken,
        owner,
        repo,
        1,
        30,
        state as 'open' | 'closed' | 'all',
        searchQuery
      );

      const issuesWithRepo = issues.map((issue: any) => ({
        ...issue,
        repository: {
          id: repository.repositoryId,
          name: repository.name,
          fullName: repository.fullName,
          htmlUrl: repository.htmlUrl,
          private: repository.private,
          language: repository.language,
        },
      }));

      allIssues.push(...issuesWithRepo);
      repositoryData.push({
        id: repository.repositoryId,
        name: repository.name,
        fullName: repository.fullName,
        issueCount: issues.length,
      });
    }

    allIssues.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());

    const perPage = 30;
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedIssues = allIssues.slice(startIndex, endIndex);
    const hasMore = endIndex < allIssues.length;
    const totalPages = Math.ceil(allIssues.length / perPage);

    return c.json({
      issues: paginatedIssues,
      hasMore,
      page,
      totalPages,
      totalIssues: allIssues.length,
      totalRepositories: connectedRepositories.length,
      repositories: repositoryData,
      perPage,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get all pull requests from connected repositories
app.get('/pulls/all', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const page = parseInt(c.req.query('page') || '1');
    const state = c.req.query('state') || 'open';

    const connectedRepositories = await GitHubRepository.find({
      userId: user.id,
      isConnected: true,
    }).populate('integrationId');

    if (connectedRepositories.length === 0) {
      return c.json({
        pullRequests: [],
        hasMore: false,
        page,
        totalRepositories: 0,
      });
    }

    const allPullRequests: any[] = [];
    const repositoryData: any[] = [];

    for (const repository of connectedRepositories) {
      const integration = repository.integrationId as any;
      if (!integration?.accessToken) continue;

      const [owner, repo] = repository.fullName.split('/');
      const { pullRequests } = await GitHubService.getRepositoryPullRequests(
        integration.accessToken,
        owner,
        repo,
        1,
        30,
        state as 'open' | 'closed' | 'all'
      );

      const pullRequestsWithRepo = pullRequests.map((pr: any) => ({
        ...pr,
        repository: {
          id: repository.repositoryId,
          name: repository.name,
          fullName: repository.fullName,
          htmlUrl: repository.htmlUrl,
          private: repository.private,
          language: repository.language,
        },
      }));

      allPullRequests.push(...pullRequestsWithRepo);
      repositoryData.push({
        id: repository.repositoryId,
        name: repository.name,
        fullName: repository.fullName,
        pullRequestCount: pullRequests.length,
      });
    }

    // Sort by updated date (most recent first)
    allPullRequests.sort(
      (a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    );

    // Implement pagination
    const perPage = 30;
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedPullRequests = allPullRequests.slice(startIndex, endIndex);
    const hasMore = endIndex < allPullRequests.length;
    const totalPages = Math.ceil(allPullRequests.length / perPage);

    return c.json({
      pullRequests: paginatedPullRequests,
      hasMore,
      page,
      totalPages,
      totalPullRequests: allPullRequests.length,
      totalRepositories: connectedRepositories.length,
      repositories: repositoryData,
      perPage,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Create task from GitHub issue
app.post('/create-task', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const { repositoryId, issueNumber } = await c.req.json();

    if (!repositoryId || !issueNumber) {
      return c.json({ error: 'Repository ID and issue number are required' }, 400);
    }

    const { repository, integration } = await GitHubRepositoryHelper.getRepositoryWithIntegration(
      user.id,
      repositoryId
    );

    // Create task from GitHub issue
    const result = await GitHubTaskHelper.createTaskFromIssue(
      user.id,
      user.organizationId || '',
      issueNumber,
      repository,
      integration
    );

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Webhook management endpoints

// Create webhook for repository
app.post('/repositories/:repositoryId/webhook', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');
    const { events = ['issues', 'pull_request'] } = await c.req.json();

    // Find repository by GitHub repository ID
    const repository = await GitHubRepository.findOne({
      repositoryId: repositoryId.toString(),
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    // Get integration
    let integration = await Integration.findOne({
      userId: user.id,
      provider: 'github',
    });

    if (!integration) {
      return c.json({ error: 'GitHub not connected' }, 404);
    }

    // Ensure valid token
    integration = await GitHubRepositoryHelper.ensureValidToken(integration);

    // Check if webhook already exists
    const existingWebhook = await GitHubWebhook.findOne({
      repositoryId: repository._id,
      active: true,
    });

    if (existingWebhook) {
      return c.json({ error: 'Webhook already exists for this repository' }, 400);
    }

    // Generate webhook secret
    const webhookSecret = crypto.randomBytes(32).toString('hex');
    const webhookUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/webhooks/github`;

    // Create webhook on GitHub
    const [owner, repo] = repository.fullName.split('/');

    try {
      const githubWebhook = await GitHubService.createWebhook(
        integration.accessToken,
        owner,
        repo,
        webhookUrl,
        webhookSecret,
        events
      );

      // Save webhook to database
      const webhook = new GitHubWebhook({
        repositoryId: repository._id,
        userId: user.id,
        organizationId: user.organizationId,
        githubWebhookId: githubWebhook.id.toString(),
        webhookUrl,
        secret: webhookSecret,
        events,
        active: true,
        config: {
          url: webhookUrl,
          contentType: 'application/json',
          insecureSsl: false,
        },
      });

      await webhook.save();

      // Update repository with webhook info
      repository.webhookId = githubWebhook.id.toString();
      repository.webhookSecret = webhookSecret;
      await repository.save();

      await NotificationService.createNotification({
        userId: user.id,
        title: 'Webhook Created',
        description: `Webhook created for repository ${repository.fullName}`,
        type: 'integration',
      });

      return c.json({
        success: true,
        webhook: {
          id: webhook._id,
          githubWebhookId: webhook.githubWebhookId,
          events: webhook.events,
          active: webhook.active,
          deliveryCount: webhook.deliveryCount,
          errorCount: webhook.errorCount,
          lastDeliveryAt: webhook.lastDeliveryAt,
        },
      });
    } catch (webhookError: any) {
      // Enhanced error handling for webhook creation
      let errorMessage = 'Failed to create webhook';

      if (webhookError.response?.status === 403) {
        errorMessage =
          'Permission denied: GitHub App lacks webhook management permissions. Please check app permissions.';
      } else if (webhookError.response?.status === 422) {
        errorMessage = 'Webhook validation failed: Invalid webhook configuration.';
      } else if (webhookError.response?.data?.message) {
        errorMessage = `GitHub API Error: ${webhookError.response.data.message}`;
      } else {
        errorMessage = webhookError.message;
      }

      return c.json({ error: errorMessage }, 500);
    }
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get webhook details for repository
app.get('/repositories/:repositoryId/webhook', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');

    // Find repository by GitHub repository ID, not MongoDB _id
    const repository = await GitHubRepository.findOne({
      repositoryId: repositoryId.toString(),
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    const webhook = await GitHubWebhook.findOne({
      repositoryId: repository._id,
    });

    if (!webhook) {
      return c.json({ webhook: null });
    }

    return c.json({
      webhook: {
        id: webhook._id,
        githubWebhookId: webhook.githubWebhookId,
        events: webhook.events,
        active: webhook.active,
        deliveryCount: webhook.deliveryCount,
        errorCount: webhook.errorCount,
        lastDeliveryAt: webhook.lastDeliveryAt,
        lastError: webhook.lastError,
        config: webhook.config,
        createdAt: webhook.createdAt,
        updatedAt: webhook.updatedAt,
      },
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete webhook for repository
app.delete('/repositories/:repositoryId/webhook', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');

    // Find repository by GitHub repository ID
    const repository = await GitHubRepository.findOne({
      repositoryId: repositoryId.toString(),
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    // Get integration
    let integration = await Integration.findOne({
      userId: user.id,
      provider: 'github',
    });

    if (!integration) {
      return c.json({ error: 'GitHub not connected' }, 404);
    }

    // Ensure valid token
    integration = await GitHubRepositoryHelper.ensureValidToken(integration);

    const webhook = await GitHubWebhook.findOne({
      repositoryId: repository._id,
    });

    if (!webhook) {
      return c.json({ error: 'Webhook not found' }, 404);
    }

    // Delete webhook from GitHub
    const [owner, repo] = repository.fullName.split('/');
    await GitHubService.deleteWebhook(
      integration.accessToken,
      owner,
      repo,
      webhook.githubWebhookId
    );

    // Delete webhook from database
    await GitHubWebhook.deleteOne({ _id: webhook._id });

    // Update repository
    repository.webhookId = undefined;
    repository.webhookSecret = undefined;
    await repository.save();

    await NotificationService.createNotification({
      userId: user.id,
      title: 'Webhook Deleted',
      description: `Webhook deleted for repository ${repository.fullName}`,
      type: 'integration',
    });

    return c.json({ success: true });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Test repository permissions
app.get('/repositories/:repositoryId/permissions', async c => {
  const user = c.get('user');
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }

  try {
    await connectDB();

    const repositoryId = c.req.param('repositoryId');

    // Find repository by GitHub repository ID
    const repository = await GitHubRepository.findOne({
      repositoryId: repositoryId.toString(),
      userId: user.id,
      isConnected: true,
    });

    if (!repository) {
      return c.json({ error: 'Repository not found or not connected' }, 404);
    }

    // Get integration
    let integration = await Integration.findOne({
      userId: user.id,
      provider: 'github',
    });

    if (!integration) {
      return c.json({ error: 'GitHub not connected' }, 404);
    }

    // Ensure valid token
    integration = await GitHubRepositoryHelper.ensureValidToken(integration);

    // Check repository permissions
    const [owner, repo] = repository.fullName.split('/');
    const repoResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}`, {
      headers: {
        Authorization: `Bearer ${integration.accessToken}`,
        Accept: 'application/vnd.github.v3+json',
      },
    });

    return c.json({
      repository: repository.fullName,
      permissions: repoResponse.data.permissions,
      canCreateWebhooks: repoResponse.data.permissions?.admin || false,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const PATCH = handle(app);
export const DELETE = handle(app);
