import mongoose, { Schema, Document } from 'mongoose';

export interface IGitHubRepository extends Document {
  integrationId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  organizationId?: mongoose.Types.ObjectId;
  repositoryId: string; // GitHub repository ID
  name: string;
  fullName: string; // owner/repo format
  description?: string;
  private: boolean;
  htmlUrl: string;
  cloneUrl: string;
  defaultBranch: string;
  language?: string;
  stargazersCount: number;
  forksCount: number;
  openIssuesCount: number;
  owner: {
    login: string;
    id: string;
    avatarUrl: string;
    type: string;
  };
  permissions: {
    admin: boolean;
    maintain: boolean;
    push: boolean;
    triage: boolean;
    pull: boolean;
  };
  webhookId?: string; // GitHub webhook ID if configured
  webhookSecret?: string; // Webhook secret for verification
  isConnected: boolean;
  lastSyncedAt?: Date;
  syncSettings: {
    autoCreateTasks: boolean;
    syncIssues: boolean;
    syncPullRequests: boolean;
    labelMapping: Map<string, string>; // GitHub label -> Task label mapping
  };
  metadata?: any;
}

const GitHubRepositorySchema = new Schema<IGitHubRepository>(
  {
    integrationId: {
      type: Schema.Types.ObjectId,
      ref: 'Integration',
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    organizationId: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
    },
    repositoryId: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    fullName: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    private: {
      type: Boolean,
      required: true,
    },
    htmlUrl: {
      type: String,
      required: true,
    },
    cloneUrl: {
      type: String,
      required: true,
    },
    defaultBranch: {
      type: String,
      required: true,
    },
    language: {
      type: String,
    },
    stargazersCount: {
      type: Number,
      default: 0,
    },
    forksCount: {
      type: Number,
      default: 0,
    },
    openIssuesCount: {
      type: Number,
      default: 0,
    },
    owner: {
      login: {
        type: String,
        required: true,
      },
      id: {
        type: String,
        required: true,
      },
      avatarUrl: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
    },
    permissions: {
      admin: {
        type: Boolean,
        default: false,
      },
      maintain: {
        type: Boolean,
        default: false,
      },
      push: {
        type: Boolean,
        default: false,
      },
      triage: {
        type: Boolean,
        default: false,
      },
      pull: {
        type: Boolean,
        default: false,
      },
    },
    webhookId: {
      type: String,
    },
    webhookSecret: {
      type: String,
    },
    isConnected: {
      type: Boolean,
      default: true,
    },
    lastSyncedAt: {
      type: Date,
    },
    syncSettings: {
      autoCreateTasks: {
        type: Boolean,
        default: true,
      },
      syncIssues: {
        type: Boolean,
        default: true,
      },
      syncPullRequests: {
        type: Boolean,
        default: false,
      },
      labelMapping: {
        type: Map,
        of: String,
        default: new Map(),
      },
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
GitHubRepositorySchema.index({ integrationId: 1, repositoryId: 1 }, { unique: true });
GitHubRepositorySchema.index({ userId: 1, isConnected: 1 });
GitHubRepositorySchema.index({ organizationId: 1, isConnected: 1 });

export const GitHubRepository =
  mongoose.models.GitHubRepository ||
  mongoose.model<IGitHubRepository>('GitHubRepository', GitHubRepositorySchema);
