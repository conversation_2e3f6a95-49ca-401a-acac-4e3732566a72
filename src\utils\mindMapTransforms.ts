import { Node, Edge } from 'reactflow';
import { MindMapNode, MindMapConnection } from '@/types/MindMapsTypes';

export function toReactFlowNode(mindMapNode: MindMapNode): Node {
  return {
    id: mindMapNode.id,
    type: mindMapNode.type,
    position: mindMapNode.position,
    data: {
      ...mindMapNode.content,
      size: mindMapNode.size,
      style: mindMapNode.style,
      title: mindMapNode.content.text,
      text: mindMapNode.content.text,
    },
    selected: mindMapNode.selected,
    dragging: mindMapNode.dragging,
  };
}
export function toMindMapNode(reactFlowNode: Node): MindMapNode {
  return {
    id: reactFlowNode.id,
    type: (reactFlowNode.type as MindMapNode['type']) || 'branch',
    position: reactFlowNode.position,
    size: reactFlowNode.data?.size || { width: 200, height: 100 },
    content: {
      text: reactFlowNode.data?.text || reactFlowNode.data?.title || 'New Node',
      color: reactFlowNode.data?.color,
      backgroundColor: reactFlowNode.data?.backgroundColor,
      fontSize: reactFlowNode.data?.fontSize,
      fontWeight: reactFlowNode.data?.fontWeight,
      url: reactFlowNode.data?.url,
      description: reactFlowNode.data?.description,
    },
    style: reactFlowNode.data?.style || {
      borderColor: '#cccccc',
      borderWidth: 1,
      borderRadius: 8,
      opacity: 1,
      shadow: false,
    },
    parentId: reactFlowNode.data?.parentId,
    childIds: reactFlowNode.data?.childIds || [],
    collapsed: reactFlowNode.data?.collapsed || false,
    zIndex: reactFlowNode.data?.zIndex || 1,
    selected: reactFlowNode.selected || false,
    dragging: reactFlowNode.dragging || false,
  };
}
export function toReactFlowEdge(connection: MindMapConnection): Edge {
  return {
    id: connection.id,
    source: connection.sourceNodeId,
    target: connection.targetNodeId,
    type: connection.type,
    style: connection.style,
    label: connection.label,
    labelStyle: connection.labelStyle,
    animated: connection.style?.animated || false,
  };
}
export function toMindMapConnection(edge: Edge): MindMapConnection {
  return {
    id: edge.id,
    sourceNodeId: edge.source,
    targetNodeId: edge.target,
    type: (edge.type as MindMapConnection['type']) || 'curved',
    style: edge.style
      ? {
          stroke: edge.style.stroke as string,
          strokeWidth: edge.style.strokeWidth as number,
          strokeDasharray: edge.style.strokeDasharray as string,
          animated: Boolean(edge.animated),
        }
      : undefined,
    label: edge.label as string,
    labelStyle: edge.labelStyle
      ? {
          fontSize: edge.labelStyle.fontSize as number,
          color: edge.labelStyle.color as string,
          backgroundColor: edge.labelStyle.backgroundColor as string,
        }
      : undefined,
  };
}
export function getDefaultNodeData(type: string): Record<string, any> {
  const baseData = {
    title: 'New Node',
    text: 'New Node',
    fontSize: 14,
    color: '#333333',
    backgroundColor: '#ffffff',
  };

  switch (type) {
    case 'task':
      return {
        ...baseData,
        title: 'New Task',
        text: 'New Task',
        description: 'Click to add description',
        priority: 'medium',
        status: 'todo',
        assignee: '',
        createdAt: new Date().toISOString(),
      };
    case 'note':
      return {
        ...baseData,
        title: 'New Note',
        text: 'New Note',
        content: 'Click to add content',
        createdAt: new Date().toISOString(),
      };
    case 'text':
      return {
        ...baseData,
        text: 'Double-click to edit',
        fontSize: 16,
      };
    case 'milestone':
      return {
        ...baseData,
        title: 'New Milestone',
        text: 'New Milestone',
        description: '',
        dueDate: '',
        status: 'pending',
      };
    case 'decision':
      return {
        ...baseData,
        title: 'New Decision',
        text: 'New Decision',
        description: 'What needs to be decided?',
        options: ['Option A', 'Option B'],
        selected: null,
      };
    case 'resource':
      return {
        ...baseData,
        title: 'New Resource',
        text: 'New Resource',
        url: '',
        type: 'link',
      };
    case 'timeline':
      return {
        ...baseData,
        title: 'New Timeline',
        text: 'New Timeline',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'scheduled',
      };
    case 'team':
      return {
        ...baseData,
        title: 'New Team Member',
        text: 'New Team Member',
        role: '',
        email: '',
        status: 'active',
      };
    case 'project':
      return {
        ...baseData,
        title: 'New Project',
        text: 'New Project',
        description: 'Click to add description',
        status: 'active',
        progress: 0,
        tasks: [],
      };
    case 'deadline':
      return {
        ...baseData,
        title: 'New Deadline',
        text: 'New Deadline',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        timeRemaining: '',
        urgency: 'medium',
      };
    case 'status':
      return {
        ...baseData,
        title: 'New Status',
        text: 'New Status',
        statuses: [
          { label: 'Not Started', color: 'gray' },
          { label: 'In Progress', color: 'blue' },
          { label: 'Completed', color: 'green' },
        ],
        currentStatus: 'Not Started',
      };
    case 'annotation':
      return {
        ...baseData,
        title: 'New Annotation',
        text: 'New Annotation',
        content: '',
        author: '',
      };
    case 'process':
      return {
        ...baseData,
        title: 'New Process',
        text: 'New Process',
        steps: [],
        status: 'pending',
      };
    default:
      return baseData;
  }
}
export function validateNodeData(node: MindMapNode): boolean {
  if (!node.id || !node.type || !node.position) {
    return false;
  }

  if (!node.content || !node.content.text) {
    return false;
  }

  return true;
}
export function sanitizeNodeContent(content: string): string {
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
    .trim();
}
