'use client';

interface ErrorFallbackProps {
  error: Error;
}

export default function ErrorFallback({ error }: ErrorFallbackProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-5 text-center">
      <h2 className="text-2xl font-bold mb-4">Something went wrong</h2>
      <p className="text-muted-foreground mb-4">The application encountered an unexpected error.</p>
      {error && (
        <pre className="bg-muted text-left p-3 rounded mb-4 max-w-xl overflow-x-auto text-sm text-red-600">
          {error.message}
        </pre>
      )}
      <button
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
      >
        Try again
      </button>
    </div>
  );
}
