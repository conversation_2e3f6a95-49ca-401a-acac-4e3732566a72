import axios from 'axios';
import { Client } from '@notionhq/client';
import { Integration } from '@/models/Integration';
import { Task } from '@/models/Task';
import { Project } from '@/models/Project';
import { connectDB } from '@/Utility/db';

export interface NotionConfig {
  accessToken?: string;
  baseUrl?: string;
}

export interface NotionPage {
  id: string;
  title: string;
  icon?: string;
  lastUpdated: Date;
  url: string;
  properties?: Record<string, any>;
  parent?: {
    type: string;
    database_id?: string;
    page_id?: string;
  };
  content?: string;
  status?: string;
  tags?: string[];
}

export interface NotionDatabase {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  url: string;
  properties: Record<string, any>;
  lastUpdated: Date;
}

export interface NotionBlock {
  id: string;
  type: string;
  content: string;
  hasChildren: boolean;
  children?: NotionBlock[];
}

export interface SyncResult {
  success: boolean;
  pagesProcessed: number;
  tasksCreated: number;
  tasksUpdated: number;
  errors: string[];
  lastSyncTime: Date;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export interface RateLimit {
  requestsPerSecond: number;
  burstLimit: number;
  windowSize: number;
}

export class NotionService {
  private static instance: NotionService;
  private rateLimitMap: Map<string, { count: number; resetTime: number; tokens: number }> =
    new Map();
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  };
  private defaultRateLimit: RateLimit = {
    requestsPerSecond: 3,
    burstLimit: 10,
    windowSize: 60000,
  };

  static getInstance(): NotionService {
    if (!NotionService.instance) {
      NotionService.instance = new NotionService();
    }
    return NotionService.instance;
  }

  private async getNotionClient(userId: string): Promise<Client | null> {
    try {
      await connectDB();
      const integration = await Integration.findOne({
        userId,
        provider: 'notion',
        isActive: true,
      });

      if (!integration || !integration.accessToken) {
        throw new Error('Notion integration not found or access token missing');
      }

      return new Client({
        auth: integration.accessToken,
        timeoutMs: 30000,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error getting Notion client:', error);
      return null;
    }
  }

  private async checkRateLimit(userId: string): Promise<boolean> {
    const now = Date.now();
    const key = `notion_${userId}`;
    const limit = this.rateLimitMap.get(key);

    if (!limit) {
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + this.defaultRateLimit.windowSize,
        tokens: this.defaultRateLimit.burstLimit - 1,
      });
      return true;
    }

    // Reset window if expired
    if (now >= limit.resetTime) {
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + this.defaultRateLimit.windowSize,
        tokens: this.defaultRateLimit.burstLimit - 1,
      });
      return true;
    }

    // Check if we have tokens available
    if (limit.tokens > 0) {
      limit.count++;
      limit.tokens--;
      return true;
    }

    // Check requests per second limit
    const requestsInWindow = limit.count;
    const maxRequestsInWindow =
      (this.defaultRateLimit.requestsPerSecond * this.defaultRateLimit.windowSize) / 1000;

    if (requestsInWindow >= maxRequestsInWindow) {
      return false;
    }

    limit.count++;
    return true;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    let lastError: any;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // Don't retry on certain errors
        if (error.code === 'unauthorized' || error.code === 'forbidden' || error.status === 404) {
          throw error;
        }

        if (attempt < retryConfig.maxRetries) {
          const delay = Math.min(
            retryConfig.baseDelay * Math.pow(retryConfig.backoffMultiplier, attempt),
            retryConfig.maxDelay
          );

          // eslint-disable-next-line no-console
          console.warn(
            `Notion API error, retrying in ${delay}ms (attempt ${attempt + 1}/${retryConfig.maxRetries + 1}):`,
            error.message
          );
          await this.delay(delay);
        }
      }
    }

    throw lastError;
  }

  async getPages(
    userId: string,
    options?: {
      databaseId?: string;
      limit?: number;
      cursor?: string;
      filter?: any;
      sorts?: any[];
    }
  ): Promise<{ pages: NotionPage[]; nextCursor?: string }> {
    const canProceed = await this.checkRateLimit(userId);
    if (!canProceed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return this.retryWithBackoff(async () => {
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        throw new Error('Failed to initialize Notion client');
      }

      let pages: NotionPage[] = [];
      let nextCursor: string | undefined;

      if (options?.databaseId) {
        // Query database
        const response = await notion.databases.query({
          database_id: options.databaseId,
          page_size: options.limit || 100,
          start_cursor: options.cursor,
          filter: options.filter,
          sorts: options.sorts,
        });

        pages = await Promise.all(
          response.results.map(async (page: any) => this.transformPageData(page, notion))
        );
        nextCursor = response.next_cursor || undefined;
      } else {
        // Search all pages
        const response = await notion.search({
          query: '',
          filter: { property: 'object', value: 'page' },
          page_size: options?.limit || 100,
          start_cursor: options?.cursor,
        });

        pages = await Promise.all(
          response.results.map(async (page: any) => this.transformPageData(page, notion))
        );
        nextCursor = response.next_cursor || undefined;
      }

      return { pages, nextCursor };
    });
  }

  async getDatabases(userId: string): Promise<NotionDatabase[]> {
    const canProceed = await this.checkRateLimit(userId);
    if (!canProceed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return this.retryWithBackoff(async () => {
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        throw new Error('Failed to initialize Notion client');
      }

      const response = await notion.search({
        filter: { property: 'object', value: 'database' },
        page_size: 100,
      });

      return response.results.map((db: any) => this.transformDatabaseData(db));
    });
  }

  async getPageContent(userId: string, pageId: string): Promise<NotionBlock[]> {
    const canProceed = await this.checkRateLimit(userId);
    if (!canProceed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return this.retryWithBackoff(async () => {
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        throw new Error('Failed to initialize Notion client');
      }

      const blocks = await notion.blocks.children.list({
        block_id: pageId,
        page_size: 100,
      });

      return await Promise.all(
        blocks.results.map(async (block: any) => this.transformBlockData(block, notion))
      );
    });
  }

  async importPageAsTask(userId: string, pageId: string, projectId?: string): Promise<any> {
    const canProceed = await this.checkRateLimit(userId);
    if (!canProceed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return this.retryWithBackoff(async () => {
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        throw new Error('Failed to initialize Notion client');
      }

      // Get page details
      const page = (await notion.pages.retrieve({ page_id: pageId })) as any;
      const content = await this.getPageContent(userId, pageId);

      await connectDB();

      // Extract page data
      const pageData = await this.transformPageData(page, notion);
      const description = content.map(block => block.content).join('\n\n');

      // Find or create project
      let project;
      if (projectId) {
        project = await Project.findById(projectId);
      } else {
        project = await Project.findOne({
          userId,
          name: 'Notion Imports',
        });

        if (!project) {
          project = new Project({
            name: 'Notion Imports',
            userId,
            description: 'Tasks imported from Notion',
          });
          await project.save();
        }
      }

      // Create task
      const task = new Task({
        title: pageData.title || 'Untitled',
        description: description || `Imported from Notion: ${pageData.url}`,
        status: this.mapNotionStatusToTaskStatus(pageData.status),
        priority: this.extractPriorityFromProperties(page.properties),
        projectId: project._id,
        userId,
        dueDate: this.extractDateFromProperties(page.properties, 'due_date'),
        tags: pageData.tags,
        metadata: {
          notionPageId: pageId,
          notionPageUrl: pageData.url,
          notionProperties: page.properties,
          lastSyncedAt: new Date(),
        },
      });

      await task.save();

      // Update sync record
      await this.updateSyncRecord(userId, 'import', {
        pageId,
        taskId: task._id.toString(),
        action: 'imported',
        timestamp: new Date(),
      });

      return {
        success: true,
        task: {
          id: task._id,
          title: task.title,
          status: task.status,
          projectId: task.projectId,
        },
      };
    });
  }

  async exportTaskToNotion(userId: string, taskId: string, databaseId?: string): Promise<any> {
    const canProceed = await this.checkRateLimit(userId);
    if (!canProceed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return this.retryWithBackoff(async () => {
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        throw new Error('Failed to initialize Notion client');
      }

      await connectDB();
      const task = await Task.findById(taskId).populate('projectId');

      if (!task) {
        throw new Error('Task not found');
      }

      // Create or update Notion page
      const pageProperties = this.buildNotionPageProperties(task);

      let notionPage;
      const existingPageId = task.metadata?.notionPageId;

      if (existingPageId) {
        // Update existing page
        try {
          notionPage = await notion.pages.update({
            page_id: existingPageId,
            properties: pageProperties,
          });
        } catch (error: any) {
          if (error.status === 404) {
            // Page was deleted, create new one
            notionPage = await this.createNotionPage(notion, pageProperties, task, databaseId);
          } else {
            throw error;
          }
        }
      } else {
        // Create new page
        notionPage = await this.createNotionPage(notion, pageProperties, task, databaseId);
      }

      // Update task metadata
      await Task.findByIdAndUpdate(taskId, {
        'metadata.notionPageId': notionPage.id,
        'metadata.notionPageUrl': notionPage.url,
        'metadata.lastSyncedAt': new Date(),
      });

      // Update sync record
      await this.updateSyncRecord(userId, 'export', {
        taskId,
        pageId: notionPage.id,
        action: existingPageId ? 'updated' : 'created',
        timestamp: new Date(),
      });

      return {
        success: true,
        page: {
          id: notionPage.id,
          url: notionPage.url,
        },
      };
    });
  }

  async syncBidirectional(
    userId: string,
    options?: {
      databaseId?: string;
      projectId?: string;
      direction?: 'import' | 'export' | 'both';
      dryRun?: boolean;
    }
  ): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      pagesProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      errors: [],
      lastSyncTime: new Date(),
    };

    try {
      const direction = options?.direction || 'both';

      if (direction === 'import' || direction === 'both') {
        const importResult = await this.performImportSync(userId, options);
        result.pagesProcessed += importResult.pagesProcessed;
        result.tasksCreated += importResult.tasksCreated;
        result.tasksUpdated += importResult.tasksUpdated;
        result.errors.push(...importResult.errors);
      }

      if (direction === 'export' || direction === 'both') {
        const exportResult = await this.performExportSync(userId, options);
        result.pagesProcessed += exportResult.pagesProcessed;
        result.tasksUpdated += exportResult.tasksUpdated;
        result.errors.push(...exportResult.errors);
      }

      // Update integration last sync time
      await connectDB();
      await Integration.findOneAndUpdate(
        { userId, provider: 'notion' },
        { lastSyncedAt: result.lastSyncTime }
      );
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message || 'Unknown sync error');
    }

    return result;
  }

  private async performImportSync(userId: string, options?: any): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      pagesProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      errors: [],
      lastSyncTime: new Date(),
    };

    try {
      const { pages } = await this.getPages(userId, {
        databaseId: options?.databaseId,
        limit: 50,
      });

      for (const page of pages) {
        try {
          // Check if task already exists
          await connectDB();
          const existingTask = await Task.findOne({
            'metadata.notionPageId': page.id,
            userId,
          });

          if (existingTask) {
            // Update existing task if page was modified
            if (new Date(page.lastUpdated) > (existingTask.metadata?.lastSyncedAt || new Date(0))) {
              if (!options?.dryRun) {
                await this.updateTaskFromNotionPage(existingTask._id, page);
              }
              result.tasksUpdated++;
            }
          } else {
            // Create new task
            if (!options?.dryRun) {
              await this.importPageAsTask(userId, page.id, options?.projectId);
            }
            result.tasksCreated++;
          }

          result.pagesProcessed++;
        } catch (error: any) {
          result.errors.push(`Failed to process page ${page.title}: ${error.message}`);
        }
      }
    } catch (error: any) {
      result.success = false;
      result.errors.push(`Import sync failed: ${error.message}`);
    }

    return result;
  }

  private async performExportSync(userId: string, options?: any): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      pagesProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      errors: [],
      lastSyncTime: new Date(),
    };

    try {
      await connectDB();

      // Find tasks that need syncing
      const query: any = { userId };
      if (options?.projectId) {
        query.projectId = options.projectId;
      }

      const tasks = await Task.find(query).populate('projectId');

      for (const task of tasks) {
        try {
          // Check if task was modified since last sync
          const lastSync = task.metadata?.lastSyncedAt;
          if (lastSync && task.updatedAt <= lastSync) {
            continue; // Skip if not modified
          }

          if (!options?.dryRun) {
            await this.exportTaskToNotion(userId, task._id.toString(), options?.databaseId);
          }

          result.tasksUpdated++;
          result.pagesProcessed++;
        } catch (error: any) {
          result.errors.push(`Failed to export task ${task.title}: ${error.message}`);
        }
      }
    } catch (error: any) {
      result.success = false;
      result.errors.push(`Export sync failed: ${error.message}`);
    }

    return result;
  }

  private async transformPageData(page: any, _notion: Client): Promise<NotionPage> {
    let title = 'Untitled';
    let status = '';
    let tags: string[] = [];

    // Extract title
    if (page.properties) {
      for (const [key, propertyUnknown] of Object.entries(page.properties as any)) {
        const property = propertyUnknown as Record<string, any>;
        if (property.type === 'title' && property.title?.length > 0) {
          title = property.title.map((t: any) => t.plain_text).join('');
        } else if (property.type === 'select' && key.toLowerCase().includes('status')) {
          status = property.select?.name || '';
        } else if (property.type === 'multi_select' && key.toLowerCase().includes('tag')) {
          tags = property.multi_select?.map((tag: any) => tag.name) || [];
        }
      }
    }

    return {
      id: page.id,
      title,
      icon: page.icon?.emoji || page.icon?.file?.url || page.icon?.external?.url,
      lastUpdated: new Date(page.last_edited_time),
      url: page.url,
      properties: page.properties,
      parent: page.parent,
      status,
      tags,
    };
  }

  private transformDatabaseData(db: any): NotionDatabase {
    let title = 'Untitled Database';
    if (db.title && db.title.length > 0) {
      title = db.title.map((t: any) => t.plain_text).join('');
    }

    return {
      id: db.id,
      title,
      description: db.description?.map((d: any) => d.plain_text).join('') || '',
      icon: db.icon?.emoji || db.icon?.file?.url || db.icon?.external?.url,
      url: db.url,
      properties: db.properties,
      lastUpdated: new Date(db.last_edited_time),
    };
  }

  private async transformBlockData(block: any, notion: Client): Promise<NotionBlock> {
    let content = '';

    switch (block.type) {
      case 'paragraph':
        content = block.paragraph?.rich_text?.map((t: any) => t.plain_text).join('') || '';
        break;
      case 'heading_1':
        content = `# ${block.heading_1?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'heading_2':
        content = `## ${block.heading_2?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'heading_3':
        content = `### ${block.heading_3?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'bulleted_list_item':
        content = `• ${block.bulleted_list_item?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'numbered_list_item':
        content = `1. ${block.numbered_list_item?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'to_do':
        // eslint-disable-next-line no-case-declarations
        const checked = block.to_do?.checked ? '[x]' : '[ ]';
        content = `${checked} ${block.to_do?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      case 'code':
        content = `\`\`\`${block.code?.language || ''}\n${block.code?.rich_text?.map((t: any) => t.plain_text).join('') || ''}\n\`\`\``;
        break;
      case 'quote':
        content = `> ${block.quote?.rich_text?.map((t: any) => t.plain_text).join('') || ''}`;
        break;
      default:
        content = JSON.stringify(block);
    }

    const notionBlock: NotionBlock = {
      id: block.id,
      type: block.type,
      content,
      hasChildren: block.has_children,
    };

    // Recursively get children if any
    if (block.has_children) {
      try {
        const children = await notion.blocks.children.list({
          block_id: block.id,
        });
        notionBlock.children = await Promise.all(
          children.results.map(async (child: any) => this.transformBlockData(child, notion))
        );
      } catch (error) {
        // eslint-disable-next-line no-console
        console.warn(`Failed to load children for block ${block.id}:`, error);
      }
    }

    return notionBlock;
  }

  private mapNotionStatusToTaskStatus(notionStatus?: string): string {
    if (!notionStatus) return 'todo';

    const status = notionStatus.toLowerCase();
    if (status.includes('done') || status.includes('complete')) return 'done';
    if (status.includes('progress') || status.includes('doing')) return 'in-progress';
    if (status.includes('todo') || status.includes('pending')) return 'todo';

    return 'todo';
  }

  private extractPriorityFromProperties(properties: any): string {
    for (const [key, propertyUnknown] of Object.entries(properties || {})) {
      const property = propertyUnknown as Record<string, any>;
      if (key.toLowerCase().includes('priority') && property.type === 'select') {
        const priority = property.select?.name?.toLowerCase();
        if (priority?.includes('high')) return 'high';
        if (priority?.includes('medium')) return 'medium';
        if (priority?.includes('low')) return 'low';
      }
    }
    return 'medium';
  }

  private extractDateFromProperties(properties: any, fieldName: string): Date | undefined {
    for (const [key, propertyUnknown] of Object.entries(properties || {})) {
      const property = propertyUnknown as Record<string, any>;
      if (key.toLowerCase().includes(fieldName) && property.type === 'date') {
        return property.date?.start ? new Date(property.date.start) : undefined;
      }
    }
    return undefined;
  }

  private buildNotionPageProperties(task: any): any {
    const properties: any = {
      Name: {
        title: [
          {
            text: {
              content: task.title,
            },
          },
        ],
      },
    };

    if (task.description) {
      properties.Description = {
        rich_text: [
          {
            text: {
              content: task.description,
            },
          },
        ],
      };
    }

    if (task.status) {
      properties.Status = {
        select: {
          name: this.mapTaskStatusToNotion(task.status),
        },
      };
    }

    if (task.priority) {
      properties.Priority = {
        select: {
          name: task.priority.charAt(0).toUpperCase() + task.priority.slice(1),
        },
      };
    }

    if (task.dueDate) {
      properties['Due Date'] = {
        date: {
          start: task.dueDate.toISOString().split('T')[0],
        },
      };
    }

    if (task.tags && task.tags.length > 0) {
      properties.Tags = {
        multi_select: task.tags.map((tag: string) => ({ name: tag })),
      };
    }

    return properties;
  }

  private async createNotionPage(
    notion: Client,
    properties: any,
    task: any,
    databaseId?: string
  ): Promise<any> {
    const pageData: any = {
      properties,
    };

    if (databaseId) {
      pageData.parent = { database_id: databaseId };
    } else {
      // Create as a page in the workspace
      pageData.parent = { type: 'workspace', workspace: true };
    }

    // Add content if description exists
    if (task.description) {
      pageData.children = [
        {
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [
              {
                type: 'text',
                text: {
                  content: task.description,
                },
              },
            ],
          },
        },
      ];
    }

    return await notion.pages.create(pageData);
  }

  private mapTaskStatusToNotion(taskStatus: string): string {
    switch (taskStatus.toLowerCase()) {
      case 'todo':
        return 'To Do';
      case 'in-progress':
        return 'In Progress';
      case 'done':
        return 'Done';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'To Do';
    }
  }

  private async updateTaskFromNotionPage(taskId: string, pageData: NotionPage): Promise<void> {
    const updateData: any = {
      title: pageData.title,
      status: this.mapNotionStatusToTaskStatus(pageData.status),
      'metadata.lastSyncedAt': new Date(),
    };

    if (pageData.tags && pageData.tags.length > 0) {
      updateData.tags = pageData.tags;
    }

    await Task.findByIdAndUpdate(taskId, updateData);
  }

  private async updateSyncRecord(
    userId: string,
    type: 'import' | 'export',
    data: any
  ): Promise<void> {
    try {
      await connectDB();
      await Integration.findOneAndUpdate(
        { userId, provider: 'notion' },
        {
          $push: {
            'metadata.syncHistory': {
              type,
              ...data,
              timestamp: new Date(),
            },
          },
          lastSyncedAt: new Date(),
        }
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to update sync record:', error);
    }
  }

  async getConnectionStatus(userId: string): Promise<{
    connected: boolean;
    workspaceName?: string;
    botId?: string;
    lastSync?: Date;
    error?: string;
  }> {
    try {
      await connectDB();
      const integration = await Integration.findOne({
        userId,
        provider: 'notion',
        isActive: true,
      });

      if (!integration) {
        return { connected: false, error: 'No integration found' };
      }

      // Test the connection
      const notion = await this.getNotionClient(userId);
      if (!notion) {
        return {
          connected: false,
          error: 'Failed to initialize client',
          lastSync: integration.lastSyncedAt,
        };
      }

      // Try to get user info to verify connection
      try {
        const response = await notion.users.me({});
        return {
          connected: true,
          workspaceName: integration.metadata?.workspaceName,
          botId: response.id,
          lastSync: integration.lastSyncedAt,
        };
      } catch (error: any) {
        return {
          connected: false,
          error: error.message || 'Connection test failed',
          lastSync: integration.lastSyncedAt,
        };
      }
    } catch (error: any) {
      return {
        connected: false,
        error: error.message || 'Status check failed',
      };
    }
  }

  async disconnect(userId: string): Promise<boolean> {
    try {
      await connectDB();
      await Integration.findOneAndUpdate(
        { userId, provider: 'notion' },
        { isActive: false, accessToken: null }
      );
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error disconnecting Notion:', error);
      return false;
    }
  }

  // Legacy methods for backward compatibility
  static async getPages(): Promise<NotionPage[]> {
    try {
      const response = await axios.get('/api/notion/pages');
      return response.data.pages;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch Notion pages');
    }
  }

  static async importPageAsTask(pageId: string): Promise<any> {
    try {
      const response = await axios.post('/api/notion/import-page', { pageId });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to import Notion page');
    }
  }
}

export default NotionService.getInstance();
