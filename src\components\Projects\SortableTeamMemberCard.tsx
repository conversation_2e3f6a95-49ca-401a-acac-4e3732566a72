import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { TeamMemberCard } from './TeamMemberCard';
import { TeamMember, ConflictInfo } from './ResourceAllocationGrid';
import { IResourceAllocation } from '@/models/ResourceAllocation';

interface SortableTeamMemberCardProps {
  member: TeamMember;
  conflicts: { [allocationId: string]: ConflictInfo[] };
  onAllocationEdit: (allocation: IResourceAllocation) => void;
  onAllocationDelete: (allocationId: string) => void;
  onViewSchedule: () => void;
  onEditMember: () => void;
}

export function SortableTeamMemberCard({
  member,
  conflicts,
  onAllocationEdit,
  onAllocationDelete,
  onViewSchedule,
  onEditMember,
}: SortableTeamMemberCardProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: member.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners} className="touch-none">
      <TeamMemberCard
        member={member}
        conflicts={conflicts}
        onAllocationEdit={onAllocationEdit}
        onAllocationDelete={onAllocationDelete}
        onViewSchedule={onViewSchedule}
        onEditMember={onEditMember}
      />
    </div>
  );
}
