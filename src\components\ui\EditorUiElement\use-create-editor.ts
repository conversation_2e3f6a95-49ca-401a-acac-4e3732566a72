'use client';

import type { Value } from '@udecode/plate';

import { withProps } from '@udecode/cn';
import {
  BoldPlugin,
  CodePlugin,
  ItalicPlugin,
  StrikethroughPlugin,
  SubscriptPlugin,
  SuperscriptPlugin,
  UnderlinePlugin,
} from '@udecode/plate-basic-marks/react';
import { BlockquotePlugin } from '@udecode/plate-block-quote/react';
import { CodeBlockPlugin, CodeLinePlugin, CodeSyntaxPlugin } from '@udecode/plate-code-block/react';
import { CommentsPlugin } from '@udecode/plate-comments/react';
import { DatePlugin } from '@udecode/plate-date/react';
import { EmojiInputPlugin } from '@udecode/plate-emoji/react';
import { HEADING_KEYS } from '@udecode/plate-heading';
import { TocPlugin } from '@udecode/plate-heading/react';
import { HighlightPlugin } from '@udecode/plate-highlight/react';
import { HorizontalRulePlugin } from '@udecode/plate-horizontal-rule/react';
import { KbdPlugin } from '@udecode/plate-kbd/react';
import { ColumnItemPlugin, ColumnPlugin } from '@udecode/plate-layout/react';
import { LinkPlugin } from '@udecode/plate-link/react';
import { EquationPlugin, InlineEquationPlugin } from '@udecode/plate-math/react';
import { ImagePlugin } from '@udecode/plate-media/react';
import { SlashInputPlugin } from '@udecode/plate-slash-command/react';
import {
  TableCellHeaderPlugin,
  TableCellPlugin,
  TablePlugin,
  TableRowPlugin,
} from '@udecode/plate-table/react';
import { TogglePlugin } from '@udecode/plate-toggle/react';
import {
  type CreatePlateEditorOptions,
  ParagraphPlugin,
  PlateLeaf,
  usePlateEditor,
} from '@udecode/plate/react';

import { editorPlugins } from '@/components/editor/plugins/editor-plugins';
import { FixedToolbarPlugin } from '@/components/editor/plugins/fixed-toolbar-plugin';
import { FloatingToolbarPlugin } from '@/components/editor/plugins/floating-toolbar-plugin';

import { BlockquoteElement } from '@/components/ui/EditorUiElement/blockquote-element';
import { CodeBlockElement } from '@/components/ui/EditorUiElement/code-block-element';
import { CodeLeaf } from '@/components/ui/EditorUiElement/code-leaf';
import { CodeLineElement } from '@/components/ui/EditorUiElement/code-line-element';
import { CodeSyntaxLeaf } from '@/components/ui/EditorUiElement/code-syntax-leaf';
import { ColumnElement } from '@/components/ui/EditorUiElement/column-element';
import { ColumnGroupElement } from '@/components/ui/EditorUiElement/column-group-element';
import { CommentLeaf } from '@/components/ui/EditorUiElement/comment-leaf';
import { DateElement } from '@/components/ui/EditorUiElement/date-element';
import { EmojiInputElement } from '@/components/ui/EditorUiElement/emoji-input-element';
import { EquationElement } from '@/components/ui/EditorUiElement/equation-element';
import { HeadingElement } from '@/components/ui/EditorUiElement/heading-element';
import { HighlightLeaf } from '@/components/ui/EditorUiElement/highlight-leaf';
import { HrElement } from '@/components/ui/EditorUiElement/hr-element';
import { ImageElement } from '@/components/ui/EditorUiElement/image-element';
import { InlineEquationElement } from '@/components/ui/EditorUiElement/inline-equation-element';
import { KbdLeaf } from '@/components/ui/EditorUiElement/kbd-leaf';
import { LinkElement } from '@/components/ui/EditorUiElement/link-element';
import { ParagraphElement } from '@/components/ui/EditorUiElement/paragraph-element';
import { withPlaceholders } from '@/components/ui/EditorUiElement/placeholder';
import { SlashInputElement } from '@/components/ui/EditorUiElement/slash-input-element';
import {
  TableCellElement,
  TableCellHeaderElement,
} from '@/components/ui/EditorUiElement/table-cell-element';
import { TableElement } from '@/components/ui/EditorUiElement/table-element';
import { TableRowElement } from '@/components/ui/EditorUiElement/table-row-element';
import { TocElement } from '@/components/ui/EditorUiElement/toc-element';
import { ToggleElement } from '@/components/ui/EditorUiElement/toggle-element';

export const viewComponents = {
  [BlockquotePlugin.key]: BlockquoteElement,
  [BoldPlugin.key]: withProps(PlateLeaf, { as: 'strong' }),
  [CodeBlockPlugin.key]: CodeBlockElement,
  [CodeLinePlugin.key]: CodeLineElement,
  [CodePlugin.key]: CodeLeaf,
  [CodeSyntaxPlugin.key]: CodeSyntaxLeaf,
  [ColumnItemPlugin.key]: ColumnElement,
  [ColumnPlugin.key]: ColumnGroupElement,
  [CommentsPlugin.key]: CommentLeaf,
  [DatePlugin.key]: DateElement,
  [EquationPlugin.key]: EquationElement,
  [HEADING_KEYS.h1]: withProps(HeadingElement, { variant: 'h1' }),
  [HEADING_KEYS.h2]: withProps(HeadingElement, { variant: 'h2' }),
  [HEADING_KEYS.h3]: withProps(HeadingElement, { variant: 'h3' }),
  [HEADING_KEYS.h4]: withProps(HeadingElement, { variant: 'h4' }),
  [HEADING_KEYS.h5]: withProps(HeadingElement, { variant: 'h5' }),
  [HEADING_KEYS.h6]: withProps(HeadingElement, { variant: 'h6' }),
  [HighlightPlugin.key]: HighlightLeaf,
  [HorizontalRulePlugin.key]: HrElement,
  [ImagePlugin.key]: ImageElement,
  [InlineEquationPlugin.key]: InlineEquationElement,
  [ItalicPlugin.key]: withProps(PlateLeaf, { as: 'em' }),
  [KbdPlugin.key]: KbdLeaf,
  [LinkPlugin.key]: LinkElement,
  [ParagraphPlugin.key]: ParagraphElement,
  [StrikethroughPlugin.key]: withProps(PlateLeaf, { as: 's' }),
  [SubscriptPlugin.key]: withProps(PlateLeaf, { as: 'sub' }),
  [SuperscriptPlugin.key]: withProps(PlateLeaf, { as: 'sup' }),
  [TableCellHeaderPlugin.key]: TableCellHeaderElement,
  [TableCellPlugin.key]: TableCellElement,
  [TablePlugin.key]: TableElement,
  [TableRowPlugin.key]: TableRowElement,
  [TocPlugin.key]: TocElement,
  [TogglePlugin.key]: ToggleElement,
  [UnderlinePlugin.key]: withProps(PlateLeaf, { as: 'u' }),
};

export const editorComponents = {
  ...viewComponents,
  [EmojiInputPlugin.key]: EmojiInputElement,
  [SlashInputPlugin.key]: SlashInputElement,
};

export const useCreateEditor = (
  {
    components,
    override,
    placeholders,
    readOnly,
    ...options
  }: {
    components?: Record<string, any>;
    placeholders?: boolean;
    plugins?: any[];
    readOnly?: boolean;
  } & Omit<CreatePlateEditorOptions, 'plugins'> = {},
  deps: any[] = []
) => {
  return usePlateEditor<Value, (typeof editorPlugins)[number]>(
    {
      override: {
        components: {
          ...(readOnly
            ? viewComponents
            : placeholders
              ? withPlaceholders(editorComponents)
              : editorComponents),
          ...components,
        },
        ...override,
      },
      plugins: [...editorPlugins, FixedToolbarPlugin, FloatingToolbarPlugin],
      value: [
        {
          children: [{ text: 'Start writing...' }],
          type: 'h5',
        },
      ],
      ...options,
    },
    deps
  );
};
