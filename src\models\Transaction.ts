import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  organizationId: mongoose.Types.ObjectId;
  subscriptionId?: mongoose.Types.ObjectId;

  // Transaction Details
  transactionId: string; // Unique transaction ID
  razorpayPaymentId?: string;
  razorpayOrderId?: string;
  razorpaySignature?: string;

  // Transaction Info
  type:
    | 'subscription_payment'
    | 'plan_upgrade'
    | 'plan_downgrade'
    | 'subscription_renewal'
    | 'refund';
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  amount: number; // in paise (Razorpay format)
  currency: string;

  // Plan Information
  planId: 'free' | 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'yearly';
  previousPlanId?: 'free' | 'basic' | 'professional' | 'enterprise';

  // Payment Method
  paymentMethod: 'upi' | 'netbanking' | 'card' | 'wallet' | 'emi';
  paymentMethodDetails?: {
    last4?: string;
    brand?: string;
    bankName?: string;
    upiId?: string;
    walletName?: string;
  };

  // Billing Period
  billingPeriodStart: Date;
  billingPeriodEnd: Date;

  // Metadata
  description: string;
  invoiceNumber?: string;
  receiptUrl?: string;

  // Timestamps
  initiatedAt: Date;
  completedAt?: Date;
  failedAt?: Date;

  // Error handling
  errorCode?: string;
  errorMessage?: string;

  // Refund information
  refundId?: string;
  refundAmount?: number;
  refundReason?: string;
  refundedAt?: Date;

  // Metadata for analytics
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    source?: string; // 'web', 'mobile', 'api'
    campaign?: string;
  };
}

const transactionSchema = new Schema<ITransaction>(
  {
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
      index: true,
    },
    subscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Subscription',
      index: true,
    },

    // Transaction Details
    transactionId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    razorpayPaymentId: {
      type: String,
      index: true,
    },
    razorpayOrderId: {
      type: String,
      index: true,
    },
    razorpaySignature: {
      type: String,
    },

    // Transaction Info
    type: {
      type: String,
      enum: [
        'subscription_payment',
        'plan_upgrade',
        'plan_downgrade',
        'subscription_renewal',
        'refund',
      ],
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
      default: 'pending',
      required: true,
      index: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'INR',
      required: true,
    },

    // Plan Information
    planId: {
      type: String,
      enum: ['free', 'basic', 'professional', 'enterprise'],
      required: true,
      index: true,
    },
    billingCycle: {
      type: String,
      enum: ['monthly', 'yearly'],
      required: true,
    },
    previousPlanId: {
      type: String,
      enum: ['free', 'basic', 'professional', 'enterprise'],
    },

    // Payment Method
    paymentMethod: {
      type: String,
      enum: ['upi', 'netbanking', 'card', 'wallet', 'emi'],
      required: true,
    },
    paymentMethodDetails: {
      last4: String,
      brand: String,
      bankName: String,
      upiId: String,
      walletName: String,
    },

    // Billing Period
    billingPeriodStart: {
      type: Date,
      required: true,
    },
    billingPeriodEnd: {
      type: Date,
      required: true,
    },

    // Metadata
    description: {
      type: String,
      required: true,
    },
    invoiceNumber: {
      type: String,
      unique: true,
      sparse: true,
    },
    receiptUrl: {
      type: String,
    },

    // Timestamps
    initiatedAt: {
      type: Date,
      default: Date.now,
      required: true,
    },
    completedAt: {
      type: Date,
    },
    failedAt: {
      type: Date,
    },

    // Error handling
    errorCode: {
      type: String,
    },
    errorMessage: {
      type: String,
    },

    // Refund information
    refundId: {
      type: String,
    },
    refundAmount: {
      type: Number,
    },
    refundReason: {
      type: String,
    },
    refundedAt: {
      type: Date,
    },

    // Metadata for analytics
    metadata: {
      userAgent: String,
      ipAddress: String,
      source: {
        type: String,
        enum: ['web', 'mobile', 'api'],
        default: 'web',
      },
      campaign: String,
    },
  },
  {
    timestamps: true,
    collection: 'transactions',
  }
);

// Indexes for better performance
transactionSchema.index({ organizationId: 1, createdAt: -1 });
transactionSchema.index({ status: 1, createdAt: -1 });
transactionSchema.index({ type: 1, status: 1 });
transactionSchema.index({ razorpayPaymentId: 1 }, { sparse: true });
transactionSchema.index({ razorpayOrderId: 1 }, { sparse: true });

// Virtual for formatted amount
transactionSchema.virtual('formattedAmount').get(function () {
  return `₹${(this.amount / 100).toFixed(2)}`;
});

// Virtual for billing period duration
transactionSchema.virtual('billingPeriodDuration').get(function () {
  const start = new Date(this.billingPeriodStart);
  const end = new Date(this.billingPeriodEnd);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Static method to generate transaction ID
transactionSchema.statics.generateTransactionId = function () {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `TXN_${timestamp}_${randomStr}`.toUpperCase();
};

// Static method to generate invoice number
transactionSchema.statics.generateInvoiceNumber = function () {
  const year = new Date().getFullYear();
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
  const timestamp = Date.now().toString().slice(-6);
  return `INV-${year}${month}-${timestamp}`;
};

// Instance method to mark as completed
transactionSchema.methods.markCompleted = function (paymentDetails: any) {
  this.status = 'completed';
  this.completedAt = new Date();
  if (paymentDetails.razorpay_payment_id) {
    this.razorpayPaymentId = paymentDetails.razorpay_payment_id;
  }
  if (paymentDetails.razorpay_signature) {
    this.razorpaySignature = paymentDetails.razorpay_signature;
  }
  return this.save();
};

// Instance method to mark as failed
transactionSchema.methods.markFailed = function (errorCode: string, errorMessage: string) {
  this.status = 'failed';
  this.failedAt = new Date();
  this.errorCode = errorCode;
  this.errorMessage = errorMessage;
  return this.save();
};

export const Transaction =
  mongoose.models.Transaction || mongoose.model<ITransaction>('Transaction', transactionSchema);
