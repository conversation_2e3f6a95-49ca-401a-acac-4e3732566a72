import React, { useState } from 'react';
import Image from 'next/image';

interface UserAvatarProps {
    src?: string;
    alt: string;
    size: 'sm' | 'md' | 'lg';
    fallbackText: string;
    className?: string;
}

const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
};

export const UserAvatar: React.FC<UserAvatarProps> = ({
    src,
    alt,
    size,
    fallbackText,
    className = '',
}) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    const sizeClass = sizeClasses[size];

    if (!src || imageError) {
        return (
            <div
                className={`${sizeClass} rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center font-medium text-white ${className}`}
            >
                {fallbackText.charAt(0)?.toUpperCase() || '?'}
            </div>
        );
    }

    return (
        <div className={`${sizeClass} rounded-full overflow-hidden relative ${className}`}>
            <Image
                src={src}
                alt={alt}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
                onLoad={() => setImageLoading(false)}
                sizes={size === 'sm' ? '32px' : size === 'md' ? '40px' : '48px'}
            />
            {imageLoading && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-full" />
            )}
        </div>
    );
};