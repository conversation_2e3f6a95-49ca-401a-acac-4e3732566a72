import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import ActivityLogService from '@/services/ActivityLog.service';

export async function activityLogMiddleware(req: NextRequest) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    if (!token || !token.sub) {
      return NextResponse.json({ error: 'Unauthorized access', success: false }, { status: 401 });
    }

    const clientIP =
      req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';
    const logService = ActivityLogService.getInstance();
    logService.trackAPICall(token.sub, req.nextUrl.pathname, req.method, 200, 0, {
      ipAddress: clientIP,
      userAgent: userAgent,
      query: Object.fromEntries(req.nextUrl.searchParams.entries()),
    });

    return NextResponse.next();
  } catch (error) {
    console.error('Activity log middleware error:', error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: ['/api/activity-logs/:path*'],
};
