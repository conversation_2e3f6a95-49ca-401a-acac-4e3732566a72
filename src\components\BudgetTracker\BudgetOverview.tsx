import { IBudget } from '@/models/Budget';
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { CreditCard, DollarSign, Settings, Target, Wallet } from 'lucide-react';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { CURRENCY_OPTIONS } from '@/constant/Currency';

export const BudgetOverview: React.FC<{
  budget: IBudget;
  onEditBudget: () => void;
}> = ({ budget, onEditBudget }) => {
  const utilizationPercentage =
    budget.totalBudget > 0 ? (budget.spentAmount / budget.totalBudget) * 100 : 0;
  const remainingAmount = budget.totalBudget - budget.spentAmount;
  const currencySymbol = CURRENCY_OPTIONS.find(c => c.value === budget.currency)?.symbol || '$';

  const getStatusColor = () => {
    if (utilizationPercentage >= 100) return 'text-red-500';
    if (utilizationPercentage >= 80) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusText = () => {
    if (utilizationPercentage >= 100) return 'Over Budget';
    if (utilizationPercentage >= 80) return 'Near Limit';
    return 'On Track';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
          <Wallet className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {currencySymbol}
            {budget.totalBudget.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Allocated across {budget.categories.length} categories
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Spent Amount</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {currencySymbol}
            {budget.spentAmount.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            {utilizationPercentage.toFixed(1)}% of total budget
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Remaining</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${remainingAmount < 0 ? 'text-red-500' : ''}`}>
            {currencySymbol}
            {remainingAmount.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            {(100 - utilizationPercentage).toFixed(1)}% remaining
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Status</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-lg font-semibold ${getStatusColor()}`}>{getStatusText()}</div>
          <Progress value={Math.min(utilizationPercentage, 100)} className="mt-2" />
          <Button variant="outline" size="sm" onClick={onEditBudget} className="mt-2 w-full">
            <Settings className="h-3 w-3 mr-1" />
            Edit Budget
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
