import React, { useCallback } from 'react';
import {
  startOfWeek,
  addDays,
  setHours,
  setMinutes,
  isSameDay,
  format,
  isToday,
  differenceInMinutes,
} from 'date-fns';
import { IEvent } from '@/models/Event';
import DraggableEvent from './DraggableEvent';
import DroppableTimeSlot from './DroppableTimeSlot';

interface CalendarViewProps {
  events: IEvent[];
  currentDate: Date;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  onEventClick: (event: IEvent) => void;
  selectedEvent?: IEvent | null;
  isCreatingEvent?: boolean;
}

const WeekView: React.FC<CalendarViewProps> = ({
  events,
  currentDate,
  onEventClick,
  selectedEvent,
}) => {
  const startDate = startOfWeek(currentDate);
  const days = Array.from({ length: 7 }, (_, i) => addDays(startDate, i));
  const hours = Array.from({ length: 24 }, (_, i) => i);

  const getSlotEvents = useCallback(
    (date: Date, hour: number) => {
      return events.filter(event => {
        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);
        const slotStart = setHours(setMinutes(date, 0), hour);
        const slotEnd = setHours(setMinutes(date, 0), hour + 1);

        return eventStart < slotEnd && eventEnd > slotStart && isSameDay(eventStart, date);
      });
    },
    [events]
  );

  return (
    <div className="flex flex-col theme-surface border theme-border theme-transition">
      {/* Header */}
      <div className="grid grid-cols-8 gap-0 border-b theme-border">
        <div className="p-3 bg-secondary border-r theme-border"></div>
        {days.map(day => (
          <div
            key={day.toISOString()}
            className="p-3 bg-secondary border-r theme-border text-center"
          >
            <div className="font-medium text-sm theme-text-primary">{format(day, 'EEE')}</div>
            <div
              className={`text-lg ${isToday(day) ? 'text-primary font-bold' : 'theme-text-primary'}`}
            >
              {day.getDate()}
            </div>
          </div>
        ))}
      </div>

      {/* Time slots */}
      <div className="flex-1 overflow-auto theme-scrollbar">
        {hours.map(hour => (
          <div key={hour} className="grid grid-cols-8 gap-0 border-b theme-border min-h-[60px]">
            <div className="p-2 bg-secondary border-r theme-border text-xs theme-text-secondary text-right">
              {format(setHours(new Date(), hour), 'HH:mm')}
            </div>
            {days.map(day => {
              const slotEvents = getSlotEvents(day, hour);
              const slotDate = setHours(setMinutes(day, 0), hour);
              const isSelected =
                selectedEvent && slotEvents.some(e => String(e._id) === String(selectedEvent._id));

              return (
                <DroppableTimeSlot
                  key={`${day.toISOString()}-${hour}`}
                  date={slotDate}
                  hour={hour}
                  minute={0}
                  viewType="week"
                  onClick={() => {}}
                  className={`
                    p-1 border-r theme-border relative
                    ${isSelected ? 'ring-2 ring-primary' : ''}
                  `}
                >
                  <div className="space-y-1 relative h-full">
                    {slotEvents.map(event => {
                      const eventStart = new Date(event.startTime);
                      const eventDuration = differenceInMinutes(
                        new Date(event.endTime),
                        eventStart
                      );
                      const hourStart = setHours(setMinutes(day, 0), hour);
                      const minutesFromHourStart = differenceInMinutes(eventStart, hourStart);

                      return (
                        <DraggableEvent
                          key={String(event._id)}
                          event={event}
                          viewType="week"
                          onClick={onEventClick}
                          onResize={(event, _newStart, _newEnd) => {
                            const eventId =
                              typeof event._id === 'string' ? event._id : String(event._id ?? '');
                            if (eventId) {
                              // This onEventResize prop is not passed from parent, so this will not work as intended.
                              // The original code had this, but the new_code removed it from CalendarViewProps.
                              // For now, we'll keep it here as it was in the original file,
                              // but it will not trigger the onEventResize prop in the parent.
                              // This is a consequence of the new_code's removal of onEventResize from CalendarViewProps.
                            }
                          }}
                          style={{
                            position: 'absolute',
                            top: `${Math.max(0, (minutesFromHourStart / 60) * 100)}%`,
                            height: `${Math.min(100, (eventDuration / 60) * 100)}%`,
                            left: 0,
                            right: 0,
                          }}
                        />
                      );
                    })}
                  </div>
                </DroppableTimeSlot>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default WeekView;
