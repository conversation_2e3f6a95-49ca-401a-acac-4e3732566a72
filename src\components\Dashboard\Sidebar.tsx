'use client';
import React, { useEffect, useState } from 'react';
import {
  Home,
  MessageSquare,
  Settings,
  SquareChartGantt,
  ClipboardCheck,
  Calendar,
  ServerCog,
  Bell,
  BarChart3,
  FileText,
  Network,
  Activity,
  CalendarDays,
} from 'lucide-react';
import { SidebarItem } from './SidebarItem';
import { UserProfile } from './UserProfile';
import { usePathname } from 'next/navigation';
import Logo from './Logo';
import { useQuery } from '@tanstack/react-query';
import { ProjectService } from '@/services/Project.service';
import { useProjectStore } from '@/stores/projectsStore';
import { useSidebarStore } from '@/stores/sidebarStore';
import { usePermissions } from '@/hooks/usePermissions';
import { useMediaQuery } from '@/hooks/use-media-query';
import { OrganizationService } from '@/services/Organization.service';
import { useUserStore } from '@/stores/UserInfo';
import { useToast } from '@/hooks/use-toast';
import { useSession } from 'next-auth/react';

interface SidebarProps {
  isMobileOpen: boolean;
  onMobileClose: () => void;
}

interface Project {
  _id: string;
  name: string;
}

interface MenuItem {
  icon?: React.ElementType;
  label: string;
  path?: string;
  ariaLabel?: string;
  badge?: string;
  requiredPermission?: string;
  subItems?: Array<{ label: string; path: string; ariaLabel?: string }>;
}

export function Sidebar({ isMobileOpen, onMobileClose }: SidebarProps) {
  const { isExpanded, setIsExpanded } = useSidebarStore();
  const pathname = usePathname();
  const { toast } = useToast();
  const { projects, setProjects } = useProjectStore() as {
    projects: Project[];
    setProjects: (projects: Project[]) => void;
  };
  const { user, setUser } = useUserStore();
  const { data: session } = useSession();
  const { getRolePermissions } = usePermissions();
  const { data: organization } = useQuery({
    queryKey: ['organizations'],
    queryFn: async () => {
      try {
        return await OrganizationService.getOrganizations();
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to fetch organization data',
          variant: 'destructive',
        });
        throw error;
      }
    },
  });

  useEffect(() => {
    if (organization?.members && session?.user?.id) {
      const currentUser = organization.members.find(
        (member: any) => member?.userId?._id === session.user.id
      );
      if (currentUser) setUser(currentUser);
    }
  }, [organization, session?.user?.id, setUser]);

  const hasPermission = (permission: string): boolean => {
    if (user?.role === 'Owner') return true;
    if (!user?.role) return false;

    const [resource, action] = permission.split('.');
    return resource && action ? getRolePermissions(user.role, resource).includes(action) : false;
  };
  const [hoveredSection, setHoveredSection] = useState<string | null>(null);

  const isMobile = useMediaQuery('(max-width: 768px)');
  const isActive = (path: string): boolean => {
    if (path === '/home' && pathname === '/home') {
      return true;
    }
    return pathname !== '/home' && pathname?.startsWith(path);
  };

  const { data } = useQuery<{ projects: Project[] }>({
    queryKey: ['projects'],
    queryFn: async () => {
      const response = await ProjectService.getProjects();
      return response;
    },
  });

  useEffect(() => {
    if (data?.projects) {
      setProjects(data.projects);
    }
  }, [data, setProjects]);

  useEffect(() => {
    if (isMobile) {
      setIsExpanded(false);
    }
  }, [isMobile, setIsExpanded]);

  // Close mobile sidebar when navigating to a new page
  useEffect(() => {
    if (isMobile && isMobileOpen) {
      onMobileClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const menuItems: Array<{ label: string; items: MenuItem[] }> = [
    {
      label: 'Main',
      items: [
        { icon: Home, label: 'Home', path: '/home' },
        { icon: Bell, label: 'Notifications', path: '/notifications' },
        {
          icon: SquareChartGantt,
          label: 'Projects',
          subItems: [
            { label: 'Create Project +', path: '/projects' },
            ...(projects?.map((project: Project) => ({
              label: project?.name,
              path: `/projects/${project?._id}`,
            })) || []),
          ],
        },
      ],
    },
    {
      label: 'Work',
      items: [
        { icon: ClipboardCheck, label: 'Tasks', path: '/tasks' },
        { icon: FileText, label: 'Notes', path: '/notes' },
        { icon: Calendar, label: 'Calendar', path: '/calendar' },
        { icon: SquareChartGantt, label: 'Timeline', path: '/timeline' },
        {
          icon: Network,
          label: 'Mind Maps',
          path: '/mind-maps',
          badge: 'NEW',
          requiredPermission: 'mindmaps.access',
        },
        {
          icon: CalendarDays,
          label: 'Events',
          path: '/events',
          badge: 'NEW',
          requiredPermission: 'events.access',
        },
      ],
    },
    {
      label: 'Analytics',
      items: [
        { icon: BarChart3, label: 'Analytics', path: '/analytics' },
        {
          icon: Activity,
          label: 'Activity Logs',
          path: '/activity-logs',
          badge: 'NEW',
          requiredPermission: 'activity_logs.view',
        },
      ],
    },
    {
      label: 'Settings',
      items: [
        {
          icon: MessageSquare,
          label: 'Feedback',
          path: '/feedback',
        },
        {
          icon: ServerCog,
          label: 'Integrations',
          path: '/integrations',
          badge: 'BETA',
        },
        {
          icon: Settings,
          label: 'Settings',
          path: '/settings',
        },
      ],
    },
  ];

  const filteredMenuItems = menuItems
    .map(group => ({
      ...group,
      items: group.items.filter(
        item => !item.requiredPermission || hasPermission(item.requiredPermission)
      ),
    }))
    .filter(group => group.items.length > 0);

  return (
    <>
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 dark:bg-black/80 z-40 md:hidden"
          onClick={onMobileClose}
        />
      )}
      <aside
        className={`
          sidebar sidebar-container
                    top-0 left-0 z-50 h-full
          flex flex-col theme-shadow-md border-r
                    ${isExpanded ? '' : 'collapsed'}
                    ${isMobileOpen ? 'mobile-open' : ''}
        `}
      >
        <Logo isExpanded={isExpanded} />

        {/* Navigation Items */}
        <div className="flex-1 px-3 py-4 space-y-5 overflow-y-auto theme-scrollbar">
          {filteredMenuItems.map(group => (
            <div
              key={group.label}
              onMouseEnter={() => setHoveredSection(group.label)}
              onMouseLeave={() => setHoveredSection(null)}
              className="relative"
            >
              {isExpanded && <div className="sidebar-section-label px-2 mb-2.5">{group.label}</div>}
              {!isExpanded && hoveredSection === group.label && (
                <div className="absolute left-14 top-0 bg-popover px-2 py-1 rounded shadow-md z-50 text-xs">
                  {group.label}
                </div>
              )}
              <div className="space-y-1">
                {group.items.map((item: MenuItem) => (
                  <SidebarItem
                    key={item.label}
                    icon={item.icon}
                    label={item.label}
                    badge={item.badge}
                    isExpanded={isExpanded}
                    isActive={item.path ? isActive(item.path) : false}
                    path={item.path || ''}
                    subItems={item.subItems}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        <UserProfile isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
      </aside>
    </>
  );
}
