import {
  FileText,
  Github<PERSON>con as <PERSON><PERSON><PERSON>,
  GitlabIcon as <PERSON><PERSON><PERSON><PERSON>,
  SlackIcon as <PERSON>lack,
  <PERSON>relloIcon as <PERSON><PERSON><PERSON>,
} from 'lucide-react';

export interface Integration {
  id: string;
  name: string;
  description: string;
  icon: any;
  popular: boolean;
  featured?: boolean;
  available: boolean;
  category: string;
  scopes?: string[];
  authType: string;
  webhookSupported: boolean;
  syncDirection: string;
  features?: string[];
}

export const AVAILABLE_INTEGRATIONS: { [key: string]: Integration } = {
  notion: {
    id: 'notion',
    name: 'Notion',
    description: 'Import pages and databases from Notion.',
    icon: FileText,
    popular: true,
    featured: true,
    available: true,
    category: 'productivity',
    scopes: ['read'],
    authType: 'oauth2',
    webhookSupported: true,
    syncDirection: 'bidirectional',
  },
  github: {
    id: 'github',
    name: 'GitHub',
    description: 'Link GitHub issues and pull requests to your tasks.',
    icon: Github,
    popular: true,
    featured: true,
    available: true,
    category: 'development',
    scopes: ['repo', 'read:user'],
    authType: 'oauth2',
    webhookSupported: true,
    syncDirection: 'bidirectional',
  },
  slack: {
    id: 'slack',
    name: 'Slack',
    description: 'Send notifications and updates to your Slack channels.',
    icon: Slack,
    popular: true,
    available: false,
    category: 'communication',
    scopes: ['chat:write', 'channels:read'],
    authType: 'oauth2',
    webhookSupported: true,
    syncDirection: 'outbound',
  },
  gitlab: {
    id: 'gitlab',
    name: 'GitLab',
    description: 'Link GitLab issues and merge requests to your tasks.',
    icon: Gitlab,
    popular: false,
    available: false,
    category: 'development',
    scopes: ['api', 'read_user'],
    authType: 'oauth2',
    webhookSupported: true,
    syncDirection: 'bidirectional',
  },
  trello: {
    id: 'trello',
    name: 'Trello',
    description: 'Import boards and cards from Trello.',
    icon: Trello,
    popular: false,
    available: false,
    category: 'productivity',
    scopes: ['read', 'write'],
    authType: 'oauth1',
    webhookSupported: true,
    syncDirection: 'import',
  },
};

export const INTEGRATION_CATEGORIES = {
  productivity: {
    name: 'Productivity',
    description: 'Tools to enhance productivity and organization',
    color: 'blue',
  },
  development: {
    name: 'Development',
    description: 'Development and version control tools',
    color: 'green',
  },
  communication: {
    name: 'Communication',
    description: 'Communication and notification tools',
    color: 'purple',
  },
  analytics: {
    name: 'Analytics',
    description: 'Data analysis and reporting tools',
    color: 'orange',
  },
};

export const INTEGRATION_FEATURES = {
  REAL_TIME_SYNC: 'real_time_sync',
  WEBHOOK_SUPPORT: 'webhook_support',
  BIDIRECTIONAL_SYNC: 'bidirectional_sync',
  BULK_IMPORT: 'bulk_import',
  CUSTOM_FIELDS: 'custom_fields',
  AUTOMATION: 'automation',
  TEMPLATES: 'templates',
  NOTIFICATIONS: 'notifications',
};

export const SYNC_DIRECTIONS = {
  INBOUND: 'inbound',
  OUTBOUND: 'outbound',
  BIDIRECTIONAL: 'bidirectional',
  IMPORT: 'import',
};

export const AUTH_TYPES = {
  OAUTH1: 'oauth1',
  OAUTH2: 'oauth2',
  API_KEY: 'api_key',
  TOKEN: 'token',
};

export const getIntegrationsByCategory = (category: string) => {
  return Object.values(AVAILABLE_INTEGRATIONS).filter(
    integration => integration.category === category
  );
};

export const getPopularIntegrations = () => {
  return Object.values(AVAILABLE_INTEGRATIONS).filter(integration => integration.popular);
};

export const getFeaturedIntegrations = () => {
  return Object.values(AVAILABLE_INTEGRATIONS).filter(integration => integration.featured);
};

export const getAvailableIntegrations = () => {
  return Object.values(AVAILABLE_INTEGRATIONS).filter(integration => integration.available);
};

export const getIntegrationById = (id: string) => {
  return AVAILABLE_INTEGRATIONS[id] || null;
};
