import React, { useState } from 'react';
import { MultiSelect, Option } from '../Global/MultiSelect';
import { SKILL_CATEGORIES } from '@/constant/SkillCategory';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Plus } from 'lucide-react';

interface CustomSkillsSelectProps {
  selectedSkills: string[];
  onChange: (skills: string[]) => void;
  placeholder?: string;
}

export function CustomSkillsSelect({
  selectedSkills,
  onChange,
  placeholder = 'Select skills...',
}: CustomSkillsSelectProps) {
  const [isAddingSkill, setIsAddingSkill] = useState(false);
  const [newSkill, setNewSkill] = useState('');
  const [customSkills, setCustomSkills] = useState<Option[]>([]);

  // Convert skill strings to Option objects for MultiSelect
  const selectedOptions = selectedSkills.map(skill => ({
    value: skill,
    label: skill,
  }));

  // Combine predefined skills with custom skills
  const allSkillOptions = [
    ...SKILL_CATEGORIES.map(cat => ({
      value: cat.value,
      label: cat.label,
    })),
    ...customSkills,
  ];

  const handleSkillChange = (selected: Option[]) => {
    onChange(selected.map(option => option.value));
  };

  const handleAddCustomSkill = () => {
    if (newSkill.trim()) {
      const skillToAdd = {
        value: newSkill.trim(),
        label: newSkill.trim(),
      };

      setCustomSkills(prev => [...prev, skillToAdd]);

      // Also add to selected skills
      onChange([...selectedSkills, newSkill.trim()]);

      // Reset and close dialog
      setNewSkill('');
      setIsAddingSkill(false);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <div className="flex-1">
          <MultiSelect
            options={allSkillOptions}
            selectedValues={selectedOptions}
            onChange={handleSkillChange}
            placeholder={placeholder}
            searchable={true}
            creatable={true} // Enable built-in creation
          />
        </div>
        <Button type="button" variant="outline" size="sm" onClick={() => setIsAddingSkill(true)}>
          <Plus className="h-4 w-4 mr-1" />
          Add Skill
        </Button>
      </div>

      {/* Dialog for adding custom skills */}
      <Dialog open={isAddingSkill} onOpenChange={setIsAddingSkill}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Custom Skill</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Enter skill name"
              value={newSkill}
              onChange={e => setNewSkill(e.target.value)}
              className="w-full"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingSkill(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddCustomSkill}>Add Skill</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
