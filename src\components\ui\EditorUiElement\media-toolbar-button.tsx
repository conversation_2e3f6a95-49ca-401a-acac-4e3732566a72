'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { isUrl } from '@udecode/plate';
import {
  AudioPlugin,
  FilePlugin,
  ImagePlugin,
  PlaceholderPlugin,
  VideoPlugin,
} from '@udecode/plate-media/react';
import { useEditorRef } from '@udecode/plate/react';
import {
  AudioLinesIcon,
  FileUpIcon,
  FilmIcon,
  ImageIcon,
  LinkIcon,
  ChevronDownIcon,
} from 'lucide-react';
import { toast } from 'sonner';
import { useFilePicker } from 'use-file-picker';
import axios from 'axios';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

const MEDIA_CONFIG: Record<
  string,
  {
    accept: string[];
    icon: React.ReactNode;
    title: string;
    tooltip: string;
    description: string;
    color: string;
  }
> = {
  [AudioPlugin.key]: {
    accept: ['audio/*'],
    icon: <AudioLinesIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    title: 'Insert Audio',
    tooltip: 'Audio',
    description: 'Add audio files to your content',
    color: 'text-purple-600 dark:text-purple-400',
  },
  [FilePlugin.key]: {
    accept: ['*'],
    icon: <FileUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    title: 'Insert File',
    tooltip: 'File',
    description: 'Upload any type of file',
    color: 'text-blue-600 dark:text-blue-400',
  },
  [ImagePlugin.key]: {
    accept: ['image/*'],
    icon: <ImageIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    title: 'Insert Image',
    tooltip: 'Image',
    description: 'Add images to your content',
    color: 'text-green-600 dark:text-green-400',
  },
  [VideoPlugin.key]: {
    accept: ['video/*'],
    icon: <FilmIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    title: 'Insert Video',
    tooltip: 'Video',
    description: 'Embed video files',
    color: 'text-red-600 dark:text-red-400',
  },
};

export function MediaToolbarButton({
  nodeType,
  ...props
}: DropdownMenuProps & { nodeType: string }) {
  const currentConfig = MEDIA_CONFIG[nodeType];

  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [uploading, setUploading] = React.useState(false);

  // Enhanced file upload handler
  const handleFileUpload = async (files: File[]) => {
    if (!files.length) return;

    setUploading(true);
    const uploadPromises = files.map(async file => {
      try {
        // Validate file type and size
        if (nodeType === ImagePlugin.key) {
          const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
          if (!allowedTypes.includes(file.type)) {
            throw new Error(`Unsupported image type: ${file.type}`);
          }

          const maxSize = 10 * 1024 * 1024; // 10MB
          if (file.size > maxSize) {
            throw new Error('Image too large. Maximum size is 10MB.');
          }
        }

        // Create FormData for upload
        const formData = new FormData();
        formData.append('image', file);

        // Upload to backend
        const response = await axios.post('/api/notes/upload-image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data.success) {
          // Insert the uploaded image into the editor
          editor.tf.insertNodes({
            children: [{ text: '' }],
            type: nodeType,
            url: response.data.file.url,
            width: response.data.file.width,
            height: response.data.file.height,
            alt: file.name,
            caption: '',
          });

          toast.success(`${file.name} uploaded successfully`);
        } else {
          throw new Error(response.data.error || 'Upload failed');
        }
      } catch (error: any) {
        console.error('Upload error:', error);
        toast.error(`Failed to upload ${file.name}: ${error.message}`);
      }
    });

    await Promise.all(uploadPromises);
    setUploading(false);
  };

  const { openFilePicker } = useFilePicker({
    accept: currentConfig.accept,
    multiple: true,
    onFilesSelected: (data: any) => {
      if (data.plainFiles) {
        if (nodeType === ImagePlugin.key) {
          // Use enhanced upload for images
          handleFileUpload(data.plainFiles);
        } else {
          // Use default behavior for other media types
          editor.getTransforms(PlaceholderPlugin).insert.media(data.plainFiles);
        }
      }
    },
  });

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
        <DropdownMenuTrigger asChild>
          <ToolbarButton
            pressed={open}
            tooltip={currentConfig.tooltip}
            isDropdown
            className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
          >
            <div className="flex items-center gap-1 sm:gap-2">
              <div className={cn('theme-transition', currentConfig.color)}>
                {currentConfig.icon}
              </div>
              <span className="hidden md:inline font-medium text-xs sm:text-sm theme-text-primary">
                {currentConfig.tooltip}
              </span>
              <ChevronDownIcon
                className={cn(
                  'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                  open && 'rotate-180'
                )}
              />
            </div>
          </ToolbarButton>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
          align="start"
          sideOffset={8}
        >
          <DropdownMenuItem
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
              uploading && 'opacity-50 cursor-not-allowed'
            )}
            onSelect={() => {
              if (!uploading) {
                openFilePicker();
                setOpen(false);
              }
            }}
            disabled={uploading}
          >
            <div className={cn('theme-transition', currentConfig.color)}>
              {uploading ? (
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-2 border-current border-t-transparent" />
              ) : (
                currentConfig.icon
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">
                  {uploading ? 'Uploading...' : 'Upload from computer'}
                </span>
              </div>
              <p className="text-xs theme-text-secondary">
                {uploading
                  ? 'Please wait while files are being uploaded'
                  : currentConfig.description}
              </p>
            </div>
          </DropdownMenuItem>

          <DropdownMenuItem
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg'
            )}
            onSelect={() => {
              setDialogOpen(true);
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', 'text-blue-600 dark:text-blue-400')}>
              <LinkIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">Insert via URL</span>
              </div>
              <p className="text-xs theme-text-secondary">Embed media from a web URL</p>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog
        open={dialogOpen}
        onOpenChange={value => {
          setDialogOpen(value);
        }}
      >
        <AlertDialogContent className="gap-6">
          <MediaUrlDialogContent
            currentConfig={currentConfig}
            nodeType={nodeType}
            setOpen={setDialogOpen}
          />
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

function MediaUrlDialogContent({
  currentConfig,
  nodeType,
  setOpen,
}: {
  currentConfig: (typeof MEDIA_CONFIG)[string];
  nodeType: string;
  setOpen: (value: boolean) => void;
}) {
  const editor = useEditorRef();
  const [url, setUrl] = React.useState('');

  const embedMedia = React.useCallback(() => {
    if (!isUrl(url)) return toast.error('Invalid URL');

    setOpen(false);
    editor.tf.insertNodes({
      children: [{ text: '' }],
      name: nodeType === FilePlugin.key ? url.split('/').pop() : undefined,
      type: nodeType,
      url,
    });
  }, [url, editor, nodeType, setOpen]);

  return (
    <>
      <AlertDialogHeader>
        <AlertDialogTitle>{currentConfig.title}</AlertDialogTitle>
      </AlertDialogHeader>

      <AlertDialogDescription className="group relative w-full">
        <label
          className="absolute top-1/2 block -translate-y-1/2 cursor-text px-1 text-sm text-muted-foreground/70 transition-all group-focus-within:pointer-events-none group-focus-within:top-0 group-focus-within:cursor-default group-focus-within:text-xs group-focus-within:font-medium group-focus-within:text-foreground has-[+input:not(:placeholder-shown)]:pointer-events-none has-[+input:not(:placeholder-shown)]:top-0 has-[+input:not(:placeholder-shown)]:cursor-default has-[+input:not(:placeholder-shown)]:text-xs has-[+input:not(:placeholder-shown)]:font-medium has-[+input:not(:placeholder-shown)]:text-foreground"
          htmlFor="url"
        >
          <span className="inline-flex bg-background px-2">URL</span>
        </label>
        <Input
          id="url"
          className="w-full"
          value={url}
          onChange={e => setUrl(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') embedMedia();
          }}
          placeholder=""
          type="url"
          autoFocus
        />
      </AlertDialogDescription>

      <AlertDialogFooter>
        <AlertDialogCancel>Cancel</AlertDialogCancel>
        <AlertDialogAction
          onClick={e => {
            e.preventDefault();
            embedMedia();
          }}
        >
          Accept
        </AlertDialogAction>
      </AlertDialogFooter>
    </>
  );
}
