declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id?: string;
  handler: (response: RazorpayResponse) => void;
  prefill?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  theme?: {
    color?: string;
  };
  modal?: {
    ondismiss?: () => void;
  };
}

export interface RazorpayResponse {
  razorpay_payment_id: string;
  razorpay_order_id?: string;
  razorpay_signature?: string;
}

export interface CreateOrderRequest {
  amount: number; // Amount in rupees
  currency?: string;
  receipt?: string;
  notes?: Record<string, string>;
}

export interface CreateOrderResponse {
  id: string;
  amount: number;
  currency: string;
  receipt: string;
  status: string;
  created_at: number;
  notes: Record<string, string>;
}

export const loadRazorpayScript = (): Promise<boolean> => {
  return new Promise(resolve => {
    if (window.Razorpay) {
      resolve(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });
};

export const createRazorpayOrder = async (
  orderData: CreateOrderRequest
): Promise<CreateOrderResponse> => {
  const response = await fetch('/api/payment/create-order', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(orderData),
  });

  if (!response.ok) {
    throw new Error('Failed to create order');
  }

  const data = await response.json();
  return data.order;
};

export const openRazorpayCheckout = async (options: RazorpayOptions): Promise<void> => {
  const isLoaded = await loadRazorpayScript();

  if (!isLoaded) {
    throw new Error('Failed to load Razorpay script');
  }

  const rzp = new window.Razorpay(options);
  rzp.open();
};

export const verifyPaymentSignature = async (paymentData: RazorpayResponse): Promise<boolean> => {
  try {
    const response = await fetch('/api/payment/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    const result = await response.json();
    return result.verified === true;
  } catch (error) {
    return false;
  }
};

export const formatAmountForRazorpay = (amountInRupees: number): number => {
  return Math.round(amountInRupees * 100);
};

export const formatAmountForDisplay = (amountInPaise: number): number => {
  return amountInPaise / 100;
};

export const getRazorpayKeyId = (): string => {
  const keyId = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
  if (!keyId) {
    throw new Error('Razorpay key ID not found in environment variables');
  }
  return keyId;
};

export const initiateStoragePayment = async ({
  storageMB,
  totalPrice,
  userInfo,
  onSuccess,
  onFailure,
}: {
  storageMB: number;
  totalPrice: number;
  userInfo: {
    name: string;
    email: string;
  };
  onSuccess: (response: RazorpayResponse) => void;
  onFailure: (error: any) => void;
}): Promise<void> => {
  try {
    const order = await createRazorpayOrder({
      amount: totalPrice,
      currency: 'INR',
      receipt: `storage_${Date.now()}`,
      notes: {
        storageMB: storageMB.toString(),
        type: 'storage_purchase',
      },
    });

    const options: RazorpayOptions = {
      key: getRazorpayKeyId(),
      amount: order.amount,
      currency: order.currency,
      name: 'TaskFluxio',
      description: `${storageMB}MB Storage Purchase`,
      order_id: order.id,
      handler: onSuccess,
      prefill: {
        name: userInfo.name,
        email: userInfo.email,
      },
      theme: {
        color: '#3399cc',
      },
      modal: {
        ondismiss: () => {
          onFailure(new Error('Payment cancelled by user'));
        },
      },
    };

    await openRazorpayCheckout(options);
  } catch (error) {
    onFailure(error);
  }
};

export const initiateTestPayment = async ({
  amount,
  description,
  onSuccess,
  onFailure,
}: {
  amount: number;
  description: string;
  onSuccess: (response: RazorpayResponse) => void;
  onFailure: (error: any) => void;
}): Promise<void> => {
  try {
    const options: RazorpayOptions = {
      key: getRazorpayKeyId(),
      amount: formatAmountForRazorpay(amount),
      currency: 'INR',
      name: 'TaskFluxio',
      description,
      handler: onSuccess,
      prefill: {
        name: 'Test User',
        email: '<EMAIL>',
      },
      theme: {
        color: '#3399cc',
      },
      modal: {
        ondismiss: () => {
          onFailure(new Error('Payment cancelled by user'));
        },
      },
    };

    await openRazorpayCheckout(options);
  } catch (error) {
    onFailure(error);
  }
};
