'use client';

import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Save } from 'lucide-react';

interface NoteNodeData {
  title: string;
  content: string;
  createdAt?: string;
}

interface NoteNodeFormProps {
  data: NoteNodeData;
  onSave: (data: Partial<NoteNodeData>) => void;
  onCancel: () => void;
}

export function NoteNodeForm({ data, onSave, onCancel }: NoteNodeFormProps) {
  const [formData, setFormData] = useState<NoteNodeData>({
    title: data.title || 'New Note',
    content: data.content || '',
    createdAt: data.createdAt || new Date().toISOString(),
  });

  const handleChange = (field: keyof NoteNodeData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="min-w-[400px] p-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Note Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => handleChange('title', e.target.value)}
            className="mt-1"
            placeholder="Enter note title"
          />
        </div>

        <div>
          <Label htmlFor="content">Content</Label>
          <Textarea
            id="content"
            value={formData.content}
            onChange={e => handleChange('content', e.target.value)}
            className="mt-1"
            rows={8}
            placeholder="Write your note content here..."
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
