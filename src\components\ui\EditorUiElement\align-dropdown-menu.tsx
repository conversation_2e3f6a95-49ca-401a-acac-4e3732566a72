'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { type Alignment, setAlign } from '@udecode/plate-alignment';
import { useEditorRef, useSelectionFragmentProp } from '@udecode/plate/react';
import {
  AlignCenterIcon,
  AlignJustifyIcon,
  AlignLeftIcon,
  AlignRightIcon,
  ChevronDownIcon,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { STRUCTURAL_TYPES } from '@/components/editor/transforms';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

const items: Record<
  string,
  {
    icon: React.ReactNode;
    label: string;
    description: string;
    color: string;
  }
> = {
  left: {
    icon: <AlignLeftIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Left',
    description: 'Align text to the left',
    color: 'text-blue-600 dark:text-blue-400',
  },
  center: {
    icon: <AlignCenterIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Center',
    description: 'Center align text',
    color: 'text-green-600 dark:text-green-400',
  },
  right: {
    icon: <AlignRightIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Right',
    description: 'Align text to the right',
    color: 'text-purple-600 dark:text-purple-400',
  },
  justify: {
    icon: <AlignJustifyIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
    label: 'Justify',
    description: 'Justify text alignment',
    color: 'text-orange-600 dark:text-orange-400',
  },
};

export function AlignDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorRef();
  const value =
    useSelectionFragmentProp({
      defaultValue: 'left',
      structuralTypes: STRUCTURAL_TYPES,
      getProp: node => node.align,
    }) || 'left';

  const [open, setOpen] = React.useState(false);
  const currentItem = items[value] || items.left;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Align"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', currentItem.color)}>{currentItem.icon}</div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        {Object.entries(items).map(([itemValue, item]) => (
          <DropdownMenuItem
            key={itemValue}
            className={cn(
              'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
              value === itemValue && 'theme-surface'
            )}
            onSelect={() => {
              setAlign(editor, { value: itemValue as Alignment });
              editor.tf.focus();
              setOpen(false);
            }}
          >
            <div className={cn('theme-transition', item.color)}>{item.icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm theme-text-primary">{item.label}</span>
              </div>
              <p className="text-xs theme-text-secondary">{item.description}</p>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
