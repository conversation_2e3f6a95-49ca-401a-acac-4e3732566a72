'use client';

import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save } from 'lucide-react';

interface DeadlineNodeData {
  title: string;
  date: string;
  timeRemaining: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

interface DeadlineNodeFormProps {
  data: DeadlineNodeData;
  onSave: (data: Partial<DeadlineNodeData>) => void;
  onCancel: () => void;
}

export function DeadlineNodeForm({ data, onSave, onCancel }: DeadlineNodeFormProps) {
  const [formData, setFormData] = useState<DeadlineNodeData>({
    title: data.title || 'New Deadline',
    date: data.date || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    timeRemaining: data.timeRemaining || '',
    urgency: data.urgency || 'medium',
  });

  const handleChange = (field: keyof DeadlineNodeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="min-w-[300px]g">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => handleChange('title', e.target.value)}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="date">Deadline Date</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={e => handleChange('date', e.target.value)}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="urgency">Urgency</Label>
          <Select
            value={formData.urgency}
            onValueChange={value => handleChange('urgency', value as DeadlineNodeData['urgency'])}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select urgency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
