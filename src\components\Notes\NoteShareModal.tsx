'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import Modal from '../Global/Modal';
import { Input } from '@/components/ui/input';
import { Share2, Copy, Check } from 'lucide-react';
import { toast } from 'sonner';

interface NoteShareDialogProps {
  noteId: string;
  noteTitle: string;
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
}

export const NoteShareModal: React.FC<NoteShareDialogProps> = ({
  noteId,
  noteTitle,
  isOpen,
  setIsOpen,
}) => {
  const [copied, setCopied] = useState(false);

  const shareUrl = `${typeof window !== 'undefined' ? window.location.origin : ''}/notes/view/${noteId}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast.success('Link copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const handleNativeShare = async () => {
    if (!navigator.share) {
      handleCopyLink();
      return;
    }

    const shareData = {
      title: noteTitle,
      text: `Check out this note: ${noteTitle}`,
      url: shareUrl,
    };

    try {
      if (navigator.canShare && !navigator.canShare(shareData)) {
        handleCopyLink();
        return;
      }

      await navigator.share(shareData);
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        handleCopyLink();
      }
    }
  };

  const shareOptions = [
    {
      id: 'copy',
      label: 'Copy Link',
      description: 'Copy the note link to clipboard',
      icon: copied ? Check : Copy,
      action: handleCopyLink,
      color: copied ? 'text-success' : 'text-primary',
    },
  ];

  if (typeof window !== 'undefined' && 'share' in navigator) {
    shareOptions.splice(1, 0, {
      id: 'native',
      label: 'Share via Device',
      description: "Use your device's native sharing options",
      icon: Share2,
      action: handleNativeShare,
      color: 'text-green-600 dark:text-green-400',
    });
  }

  return (
    <Modal isOpen={isOpen} onClose={() => setIsOpen(false)} size="lg">
      <div className="p-6">
        <h2 className="text-2xl font-bold tracking-tight theme-text-primary">Share Note</h2>
        <p className="text-sm theme-text-secondary mt-1.5">
          Share "{noteTitle}" with others using the options below.
        </p>
      </div>
      <div className="space-y-4 mt-6">
        <div>
          <label className="form-label text-sm mb-2 block">Note URL</label>
          <div className="flex gap-2">
            <Input value={shareUrl} readOnly className="theme-input flex-1 font-mono text-sm" />
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyLink}
              className="theme-button-secondary shrink-0"
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Share Options */}
        <div className="space-y-3">
          <label className="form-label text-sm">Share Options</label>
          {shareOptions.map(option => {
            const IconComponent = option.icon;

            return (
              <div
                key={option.id}
                className="flex items-center justify-between p-3 theme-surface rounded-lg theme-border hover:theme-surface-hover theme-transition cursor-pointer"
                onClick={option.action}
              >
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-muted/50">
                    <IconComponent className={`h-4 w-4 ${option.color}`} />
                  </div>
                  <div>
                    <p className="font-medium theme-text-primary text-sm">{option.label}</p>
                    <p className="text-xs theme-text-secondary">{option.description}</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="theme-button-ghost">
                  <IconComponent className={`h-4 w-4 ${option.color}`} />
                </Button>
              </div>
            );
          })}
        </div>
      </div>

      <div className="flex items-center justify-between mt-6 pt-4 theme-divider">
        <div className="text-xs theme-text-secondary">
          <p>Anyone with this link can view the note</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(false)}
          className="theme-button-ghost"
        >
          Close
        </Button>
      </div>
    </Modal>
  );
};

export default NoteShareModal;
