import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/options';
import { createProject, getAllProjects, getProjectById, updateTask } from '@/routes/Project/route';
import {
  getOrganizationRoles,
  createRole,
  updateRole,
  deleteRole,
  assignUsersToRole,
  getOrganizationUsers,
} from '@/routes/Role/route';
import { User } from '@/models/User';
import { Project } from '@/models/Project';
import { CacheService } from '@/services/Cache.service';
import { RedisHelper } from '@/lib/redis';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api');

app.use('*', logger());

// Middleware to inject user details with caching
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      // Try to get user from cache first
      let cachedUser = null;
      if (session.user.id) {
        const userCacheKey = CacheService.generateKey('user', session.user.id);
        cachedUser = await CacheService.get(userCacheKey);
      }

      const userData = cachedUser || {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
        systemRole: session.user.systemRole || 'User',
      };

      // Cache user data if not already cached (12 hours TTL)
      if (!cachedUser && session.user.id) {
        const userCacheKey = CacheService.generateKey('user', session.user.id);
        await CacheService.set(userCacheKey, userData, 12 * 60 * 60);
      }

      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Error in user middleware:', error);
    throw new Error(error.message);
  }
  await next();
});

const createTask = async c => {
  return c.json({ message: 'Task created successfully' });
};

const createProjectController = async c => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const result = await createProject(body, user.id, user.organizationId);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Invalidate user's projects cache
    const userProjectsCacheKey = CacheService.generateKey('projects', 'user', user.id);
    await CacheService.delete(userProjectsCacheKey);

    return c.json({ message: 'Project created successfully', project: result });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const getAllProjectsByUserController = async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('projects', 'user', user.id);
    const cachedProjects = await CacheService.get(cacheKey);

    if (cachedProjects) {
      return c.json({ projects: cachedProjects, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getAllProjects(user.id);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Cache the results for 10 minutes
    await CacheService.setMedium(cacheKey, result);

    return c.json({ projects: result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const getProjectByIdController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const projectId = c.req.param('projectId');

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('project', 'detail', projectId);
    const cachedProject = await CacheService.get(cacheKey);

    if (cachedProject) {
      return c.json({ project: cachedProject, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getProjectById(projectId, user.id);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Cache the result for 15 minutes
    await CacheService.set(cacheKey, result, 15 * 60);

    return c.json({ project: result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const updateTaskController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const taskId = c.req.param('taskId');
    const data = await c.req.json();
    const result = await updateTask(taskId, data);

    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Invalidate related caches
    await RedisHelper.deleteByPattern(`task:*:${taskId}`);
    await RedisHelper.deleteByPattern(`tasks:*`);
    await RedisHelper.deleteByPattern(`projects:user:${user.id}`);

    return c.json({ task: result });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

app.post('/create-task', createTask);
app.post('/create-project', createProjectController);
app.get('/get-all-projects', getAllProjectsByUserController);
app.get('/get-project/:projectId', getProjectByIdController);
app.patch('/update-task-status/:taskId', updateTaskController);
app.get('/projects/:projectId/members', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const projectId = c.req.param('projectId');

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('project', 'members', projectId);
    const cachedMembers = await CacheService.get(cacheKey);

    if (cachedMembers) {
      return c.json({ members: cachedMembers, cached: true });
    }

    // Cache miss - fetch from database
    const project = await Project.findOne({ _id: projectId, organizationId: user.organizationId });
    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    // Assume project.members is an array of user IDs
    const members = await User.find({ _id: { $in: project.members } }).select(
      'name email image _id'
    );

    // Cache the members for 5 minutes
    await CacheService.setShort(cacheKey, members);

    return c.json({ members, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Role Controllers
const getRolesController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const organizationId = c.req.query('organizationId') || user.organizationId;
    if (!organizationId) {
      return c.json({ error: 'Organization ID is required' }, 400);
    }

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('roles', 'organization', organizationId);
    const cachedRoles = await CacheService.get(cacheKey);

    if (cachedRoles) {
      return c.json({ ...cachedRoles, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getOrganizationRoles(organizationId, user.id);

    // Cache the results for 5 minutes
    await CacheService.setShort(cacheKey, result);

    return c.json({ ...result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const createRoleController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const body = await c.req.json();
    const roleData = {
      ...body,
      organizationId: body.organizationId || user.organizationId,
    };

    const result = await createRole(roleData, user.id);

    // Invalidate roles cache
    const cacheKey = CacheService.generateKey('roles', 'organization', roleData.organizationId);
    await CacheService.delete(cacheKey);

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const updateRoleController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const roleId = c.req.param('roleId');
    const updates = await c.req.json();

    const result = await updateRole(roleId, updates, user.id);

    // Invalidate roles cache
    if (user.organizationId) {
      const cacheKey = CacheService.generateKey('roles', 'organization', user.organizationId);
      await CacheService.delete(cacheKey);
    }

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const deleteRoleController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const roleId = c.req.param('roleId');

    const result = await deleteRole(roleId, user.id);

    // Invalidate roles cache
    if (user.organizationId) {
      const cacheKey = CacheService.generateKey('roles', 'organization', user.organizationId);
      await CacheService.delete(cacheKey);
    }

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const assignUsersToRoleController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const roleId = c.req.param('roleId');
    const { userIds } = await c.req.json();

    const result = await assignUsersToRole(roleId, userIds, user.id);

    // Invalidate roles cache
    if (user.organizationId) {
      const cacheKey = CacheService.generateKey('roles', 'organization', user.organizationId);
      await CacheService.delete(cacheKey);
    }

    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const getUsersController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const organizationId = c.req.query('organizationId') || user.organizationId;
    const search = c.req.query('search');

    if (!organizationId) {
      return c.json({ error: 'Organization ID is required' }, 400);
    }

    // Try to get from cache first
    const cacheKey = CacheService.generateKey(
      'users',
      'organization',
      organizationId,
      search || 'all'
    );
    const cachedUsers = await CacheService.get(cacheKey);

    if (cachedUsers) {
      return c.json({ ...cachedUsers, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getOrganizationUsers(organizationId, user.id, search);

    // Cache the results for 5 minutes
    await CacheService.setShort(cacheKey, result);

    return c.json({ ...result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

// Role Routes
app.get('/roles', getRolesController);
app.post('/roles', createRoleController);
app.put('/roles/:roleId', updateRoleController);
app.delete('/roles/:roleId', deleteRoleController);
app.post('/roles/:roleId/assign-users', assignUsersToRoleController);

// User Routes
app.get('/users', getUsersController);

export const GET = handle(app);
export const POST = handle(app);
export const DELETE = handle(app);
export const PUT = handle(app);
export const PATCH = handle(app);
