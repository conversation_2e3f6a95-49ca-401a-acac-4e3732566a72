import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { IExpense } from '@/models/Budget';
import { CURRENCY_OPTIONS } from '@/constant/Currency';
import { format } from 'date-fns';
import { Edit, FileText, Filter, Search, Trash2, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { DataTable, Column } from '../Global/DataTable';
import { Badge } from '../ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Label } from '../ui/label';
import { DatePicker } from '../ui/date-picker';
import { Separator } from '../ui/separator';

interface ExpenseFilters {
  category?: string;
  dateRange?: { startDate: Date; endDate: Date };
  minAmount?: number;
  maxAmount?: number;
  vendor?: string;
}
export const ExpenseHistoryTable: React.FC<{
  expenses: IExpense[];
  onEditExpense: (expense: IExpense) => void;
  onDeleteExpense: (expenseId: string) => void;
  currency: string;
}> = ({ expenses, onEditExpense, onDeleteExpense, currency }) => {
  const [filters, setFilters] = useState<ExpenseFilters>({});
  // Function to update filters that will be used by the Filter button
  const updateFilters = (newFilters: Partial<ExpenseFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };
  const [searchTerm, setSearchTerm] = useState('');

  const currencySymbol = CURRENCY_OPTIONS.find(c => c.value === currency)?.symbol || '$';

  const filteredExpenses = useMemo(() => {
    return expenses.filter(expense => {
      // Search filter
      if (
        searchTerm &&
        !expense.description?.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !expense.vendor?.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !expense.category.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      // Category filter
      if (filters.category && expense.category !== filters.category) {
        return false;
      }

      // Date range filter
      if (filters.dateRange) {
        const expenseDate = new Date(expense.date);
        if (expenseDate < filters.dateRange.startDate || expenseDate > filters.dateRange.endDate) {
          return false;
        }
      }

      // Amount range filter
      if (filters.minAmount && expense.amount < filters.minAmount) {
        return false;
      }
      if (filters.maxAmount && expense.amount > filters.maxAmount) {
        return false;
      }

      // Vendor filter
      if (filters.vendor && !expense.vendor?.toLowerCase().includes(filters.vendor.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [expenses, filters, searchTerm]);

  const columns: Column<IExpense>[] = [
    {
      key: 'date' as keyof IExpense,
      label: 'Date',
      render: (_value, row) => format(new Date(row.date), 'MMM dd, yyyy'),
    },
    {
      key: 'description' as keyof IExpense,
      label: 'Description',
      render: (_value, row) => (
        <div>
          <div className="font-medium">{row.description || 'No description'}</div>
          {row.receiptNumber && (
            <div className="text-sm text-muted-foreground">#{row.receiptNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'category' as keyof IExpense,
      label: 'Category',
      render: (_value, row) => <Badge variant="outline">{row.category}</Badge>,
    },
    {
      key: 'vendor' as keyof IExpense,
      label: 'Vendor',
      render: (_value, row) => row.vendor || '-',
    },
    {
      key: 'amount' as keyof IExpense,
      label: 'Amount',
      render: (_value, row) => (
        <div className="font-medium">
          {currencySymbol}
          {row.amount.toLocaleString()}
        </div>
      ),
    },
    {
      key: 'attachments' as keyof IExpense,
      label: 'Attachments',
      render: (_value, row) => (
        <div className="flex items-center gap-1">
          {row.attachments?.length || 0}
          <FileText className="h-4 w-4 text-muted-foreground" />
        </div>
      ),
    },
    {
      key: 'id' as keyof IExpense,
      label: 'Actions',
      render: (_value, row) => (
        <div className="flex gap-1">
          <Button variant="ghost" size="sm" onClick={() => onEditExpense(row)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => onDeleteExpense(row._id.toString())}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Expense History
        </CardTitle>
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search expenses..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-1" />
                Filters{' '}
                {Object.keys(filters).length > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {Object.keys(filters).length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <div className="font-medium">Filter Expenses</div>
                <Separator />

                {/* Category filter */}
                <div className="space-y-2">
                  <Label htmlFor="category-filter">Category</Label>
                  <div className="flex gap-2">
                    <Input
                      id="category-filter"
                      placeholder="Filter by category"
                      value={filters.category || ''}
                      onChange={e => updateFilters({ category: e.target.value || undefined })}
                    />
                    {filters.category && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => updateFilters({ category: undefined })}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Vendor filter */}
                <div className="space-y-2">
                  <Label htmlFor="vendor-filter">Vendor</Label>
                  <div className="flex gap-2">
                    <Input
                      id="vendor-filter"
                      placeholder="Filter by vendor"
                      value={filters.vendor || ''}
                      onChange={e => updateFilters({ vendor: e.target.value || undefined })}
                    />
                    {filters.vendor && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => updateFilters({ vendor: undefined })}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Amount range filter */}
                <div className="space-y-2">
                  <Label>Amount Range</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.minAmount || ''}
                      onChange={e =>
                        updateFilters({
                          minAmount: e.target.value ? Number(e.target.value) : undefined,
                        })
                      }
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.maxAmount || ''}
                      onChange={e =>
                        updateFilters({
                          maxAmount: e.target.value ? Number(e.target.value) : undefined,
                        })
                      }
                    />
                    {(filters.minAmount || filters.maxAmount) && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() =>
                          updateFilters({ minAmount: undefined, maxAmount: undefined })
                        }
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Date range filter */}
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <DatePicker
                        value={
                          filters.dateRange
                            ? {
                                from: filters.dateRange.startDate,
                                to: filters.dateRange.endDate,
                              }
                            : undefined
                        }
                        onChange={dateRange => {
                          if (dateRange?.from && dateRange?.to) {
                            updateFilters({
                              dateRange: {
                                startDate: dateRange.from,
                                endDate: dateRange.to,
                              },
                            });
                          } else if (!dateRange) {
                            updateFilters({ dateRange: undefined });
                          }
                        }}
                        placeholder="Select date range"
                      />
                    </div>
                    {filters.dateRange && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => updateFilters({ dateRange: undefined })}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Reset all filters */}
                <div className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    onClick={() => setFilters({})}
                    disabled={Object.keys(filters).length === 0}
                  >
                    Reset All
                  </Button>
                  <Button onClick={() => document.body.click()}>Apply</Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={columns}
          data={filteredExpenses}
          searchable={false}
          pagination={false}
        />
      </CardContent>
    </Card>
  );
};
