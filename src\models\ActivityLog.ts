import mongoose, { Document, Schema } from 'mongoose';
import type { PipelineStage } from 'mongoose';

export interface IActivityLog extends Document {
  userId: mongoose.Types.ObjectId;
  userEmail?: string;
  userName?: string;
  userImage?: string;
  action:
    | 'create'
    | 'update'
    | 'delete'
    | 'login'
    | 'logout'
    | 'view'
    | 'export'
    | 'import'
    | 'share'
    | 'archive';
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'report'
    | 'timeline'
    | 'notification'
    | 'file'
    | 'comment'
    | 'budget';
  resourceId?: mongoose.Types.ObjectId;
  resourceName?: string;
  description: string;
  metadata?: {
    previousData?: Record<string, any>;
    newData?: Record<string, any>;
    changes?: Record<string, { from: any; to: any }>;
    projectId?: mongoose.Types.ObjectId;
    organizationId?: mongoose.Types.ObjectId;
    sessionId?: string;
    userAgent?: string;
    source?: 'web' | 'mobile' | 'api' | 'webhook' | 'system';
    duration?: number;
    fileSize?: number;
    errorMessage?: string;
    additionalContext?: Record<string, any>;
  };
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  deviceInfo?: {
    browser?: string;
    browserVersion?: string;
    os?: string;
    osVersion?: string;
    device?: string;
    isMobile?: boolean;
    isTablet?: boolean;
    isDesktop?: boolean;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ActivityLogSchema = new Schema<IActivityLog>(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    userEmail: {
      type: String,
      required: false,
      index: true,
    },
    userName: {
      type: String,
      required: false,
    },
    action: {
      type: String,
      enum: [
        'create',
        'update',
        'delete',
        'login',
        'logout',
        'view',
        'export',
        'import',
        'share',
        'archive',
      ],
      required: true,
      index: true,
    },
    resourceType: {
      type: String,
      enum: [
        'task',
        'project',
        'note',
        'user',
        'organization',
        'integration',
        'report',
        'timeline',
        'notification',
        'file',
        'comment',
        'budget',
      ],
      required: true,
      index: true,
    },
    resourceId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      index: true,
    },
    resourceName: {
      type: String,
      required: false,
    },
    description: {
      type: String,
      required: true,
      maxlength: 500,
    },
    metadata: {
      previousData: {
        type: Schema.Types.Mixed,
        required: false,
      },
      newData: {
        type: Schema.Types.Mixed,
        required: false,
      },
      changes: {
        type: Schema.Types.Mixed,
        required: false,
      },
      projectId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Project',
        required: false,
        index: true,
      },
      organizationId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Organization',
        required: false,
        index: true,
      },
      sessionId: {
        type: String,
        required: false,
      },
      userAgent: {
        type: String,
        required: false,
      },
      source: {
        type: String,
        enum: ['web', 'mobile', 'api', 'webhook', 'system'],
        default: 'web',
      },
      duration: {
        type: Number,
        required: false,
      },
      fileSize: {
        type: Number,
        required: false,
      },
      errorMessage: {
        type: String,
        required: false,
      },
      additionalContext: {
        type: Schema.Types.Mixed,
        required: false,
      },
    },
    ipAddress: {
      type: String,
      required: false,
      index: true,
    },
    userAgent: {
      type: String,
      required: false,
    },
    location: {
      country: {
        type: String,
        required: false,
      },
      region: {
        type: String,
        required: false,
      },
      city: {
        type: String,
        required: false,
      },
      coordinates: {
        latitude: {
          type: Number,
          required: false,
        },
        longitude: {
          type: Number,
          required: false,
        },
      },
    },
    deviceInfo: {
      browser: {
        type: String,
        required: false,
      },
      browserVersion: {
        type: String,
        required: false,
      },
      os: {
        type: String,
        required: false,
      },
      osVersion: {
        type: String,
        required: false,
      },
      device: {
        type: String,
        required: false,
      },
      isMobile: {
        type: Boolean,
        default: false,
      },
      isTablet: {
        type: Boolean,
        default: false,
      },
      isDesktop: {
        type: Boolean,
        default: true,
      },
    },
    timestamp: {
      type: Date,
      default: Date.now,
      required: true,
      index: true,
    },
  },
  {
    timestamps: true,
    collection: 'activitylogs',
  }
);

// Compound indexes for efficient querying
ActivityLogSchema.index({ userId: 1, timestamp: -1 });
ActivityLogSchema.index({ resourceType: 1, resourceId: 1, timestamp: -1 });
ActivityLogSchema.index({ action: 1, timestamp: -1 });
ActivityLogSchema.index({ 'metadata.projectId': 1, timestamp: -1 });
ActivityLogSchema.index({ 'metadata.organizationId': 1, timestamp: -1 });
ActivityLogSchema.index({ ipAddress: 1, timestamp: -1 });
ActivityLogSchema.index({ timestamp: -1 }); // For time-based queries

// TTL index to automatically delete old logs (optional - 1 year retention)
ActivityLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

// Static methods
ActivityLogSchema.statics.createLog = async function (
  logData: Partial<IActivityLog>
): Promise<IActivityLog> {
  const log = new this({
    ...logData,
    timestamp: logData.timestamp || new Date(),
  });
  return await log.save();
};

ActivityLogSchema.statics.getLogsByUser = async function (
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    startDate?: Date;
    endDate?: Date;
    actions?: string[];
    resourceTypes?: string[];
  } = {}
): Promise<IActivityLog[]> {
  const query: any = { userId };

  if (options.startDate || options.endDate) {
    query.timestamp = {};
    if (options.startDate) query.timestamp.$gte = options.startDate;
    if (options.endDate) query.timestamp.$lte = options.endDate;
  }

  if (options.actions && options.actions.length > 0) {
    query.action = { $in: options.actions };
  }

  if (options.resourceTypes && options.resourceTypes.length > 0) {
    query.resourceType = { $in: options.resourceTypes };
  }

  return await this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 100)
    .skip(options.offset || 0)
    .populate('userId', 'name email')
    .lean();
};

ActivityLogSchema.statics.getLogsByResource = async function (
  resourceType: string,
  resourceId: string,
  options: {
    limit?: number;
    offset?: number;
    startDate?: Date;
    endDate?: Date;
    actions?: string[];
  } = {}
): Promise<IActivityLog[]> {
  const query: any = { resourceType, resourceId };

  if (options.startDate || options.endDate) {
    query.timestamp = {};
    if (options.startDate) query.timestamp.$gte = options.startDate;
    if (options.endDate) query.timestamp.$lte = options.endDate;
  }

  if (options.actions && options.actions.length > 0) {
    query.action = { $in: options.actions };
  }

  return await this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 50)
    .skip(options.offset || 0)
    .populate('userId', 'name email')
    .lean();
};

ActivityLogSchema.statics.getAnalytics = async function (
  userId?: string,
  organizationId?: string,
  dateRange?: { start: Date; end: Date }
): Promise<{
  totalActions: number;
  actionBreakdown: Record<string, number>;
  resourceBreakdown: Record<string, number>;
  dailyActivity: Array<{ date: string; count: number }>;
  topUsers: Array<{ userId: string; userName: string; count: number }>;
  recentActivity: IActivityLog[];
}> {
  const matchStage: any = {};
  if (userId) matchStage.userId = new mongoose.Types.ObjectId(userId);
  if (organizationId)
    matchStage['metadata.organizationId'] = new mongoose.Types.ObjectId(organizationId);
  if (dateRange) {
    matchStage.timestamp = {
      $gte: dateRange.start,
      $lte: dateRange.end,
    };
  }
  const pipeline: PipelineStage[] = [
    { $match: matchStage },
    {
      $facet: {
        totalActions: [{ $count: 'count' }],
        actionBreakdown: [
          { $group: { _id: '$action', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
        ],
        resourceBreakdown: [
          { $group: { _id: '$resourceType', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
        ],
        dailyActivity: [
          {
            $group: {
              _id: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
              count: { $sum: 1 },
            },
          },
          { $sort: { _id: 1 } },
        ],
        topUsers: [
          { $group: { _id: '$userId', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          {
            $lookup: {
              from: 'users',
              localField: '_id',
              foreignField: '_id',
              as: 'user',
            },
          },
        ],
        recentActivity: [{ $sort: { timestamp: -1 } }, { $limit: 20 }],
      },
    },
  ];
  const results = await (this as any).aggregate(pipeline).exec();
  const data = results[0];
  return {
    totalActions: data.totalActions[0]?.count || 0,
    actionBreakdown: data.actionBreakdown.reduce((acc: any, item: any) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
    resourceBreakdown: data.resourceBreakdown.reduce((acc: any, item: any) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
    dailyActivity: data.dailyActivity.map((item: any) => ({
      date: item._id,
      count: item.count,
    })),
    topUsers: data.topUsers.map((item: any) => ({
      userId: item._id.toString(),
      userName: item.user[0]?.name || 'Unknown User',
      count: item.count,
    })),
    recentActivity: data.recentActivity,
  };
};

// Instance methods
ActivityLogSchema.methods.toSafeObject = function (): Partial<IActivityLog> {
  const obj = this.toObject();
  // Remove sensitive information if needed
  delete obj.ipAddress;
  delete obj.deviceInfo;
  delete obj.location;
  return obj;
};

export const ActivityLog =
  mongoose.models.ActivityLog || mongoose.model<IActivityLog>('ActivityLog', ActivityLogSchema);
