import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WebhookService } from '@/services/webhook.service';
import { toast } from 'sonner';

export const useWebhook = (repositoryId: string, isConnected: boolean = false) => {
  const queryClient = useQueryClient();

  const {
    data: webhookData,
    isLoading: isLoadingWebhook,
    error: webhookError,
    refetch: refetchWebhook,
  } = useQuery({
    queryKey: ['webhook', repositoryId],
    queryFn: () => WebhookService.getWebhookDetails(repositoryId),
    enabled: !!repositoryId && isConnected,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Create webhook mutation
  const createWebhookMutation = useMutation({
    mutationFn: (events?: string[]) => WebhookService.createWebhook(repositoryId, events),
    onSuccess: () => {
      toast.success('Webhook created successfully');
      queryClient.invalidateQueries({ queryKey: ['webhook', repositoryId] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories'] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to create webhook: ${error.message}`);
    },
  });

  // Delete webhook mutation
  const deleteWebhookMutation = useMutation({
    mutationFn: () => WebhookService.deleteWebhook(repositoryId),
    onSuccess: () => {
      toast.success('Webhook deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['webhook', repositoryId] });
      queryClient.invalidateQueries({ queryKey: ['github-repositories'] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete webhook: ${error.message}`);
    },
  });

  const webhook = webhookData?.webhook || null;
  const webhookStatus = WebhookService.formatWebhookStatus(webhook);
  const webhookHealth = WebhookService.getWebhookHealth(webhook);

  return {
    // Data
    webhook,
    webhookStatus,
    webhookHealth,

    // Loading states
    isLoadingWebhook,
    isCreatingWebhook: createWebhookMutation.isPending,
    isDeletingWebhook: deleteWebhookMutation.isPending,

    // Error states
    webhookError,
    createError: createWebhookMutation.error,
    deleteError: deleteWebhookMutation.error,

    // Actions
    createWebhook: createWebhookMutation.mutate,
    deleteWebhook: deleteWebhookMutation.mutate,
    refetchWebhook,

    // Computed states
    hasWebhook: !!webhook,
    isWebhookActive: webhook?.active || false,
    hasWebhookErrors: (webhook?.errorCount || 0) > 0,
  };
};

export type UseWebhookReturn = ReturnType<typeof useWebhook>;
