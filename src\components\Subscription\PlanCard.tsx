'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap, Shield, HardDrive } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PlanCardProps {
  plan: any;
  currentPlan?: string;
  billingCycle: 'monthly' | 'yearly';
  onSelectPlan: (planId: string, billingCycle: 'monthly' | 'yearly') => void;
  isLoading?: boolean;
  canUpgrade?: boolean;
  canDowngrade?: boolean;
}

export const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  currentPlan,
  billingCycle,
  onSelectPlan,
  isLoading = false,
  canUpgrade = true,
  canDowngrade = true,
}) => {
  const isCurrentPlan = currentPlan === plan.id;
  const price = billingCycle === 'yearly' ? plan.price.yearly : plan.price.monthly;
  const monthlyPrice = billingCycle === 'yearly' ? plan.price.yearly / 12 : plan.price.monthly;

  const getPlanIcon = () => {
    switch (plan.id) {
      case 'free':
        return <HardDrive className="h-5 w-5" />;
      case 'starter':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="h-5 w-5 text-orange-500" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  const getButtonText = () => {
    if (isCurrentPlan) return 'Current Plan';
    if (plan.id === 'free') return 'Downgrade to Free';
    return canUpgrade ? 'Upgrade' : 'Select Plan';
  };

  const isButtonDisabled = () => {
    if (isCurrentPlan) return true;
    if (isLoading) return true;
    if (plan.id === 'free') return !canDowngrade;
    return !canUpgrade;
  };

  return (
    <Card
      className={cn(
        'relative transition-all duration-200 hover:shadow-lg',
        isCurrentPlan && 'ring-2 ring-primary',
        plan.popular && 'border-blue-500',
        plan.recommended && 'border-purple-500'
      )}
    >
      {/* Popular/Recommended Badge */}
      {(plan.popular || plan.recommended) && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge
            variant="default"
            className={cn(
              'px-3 py-1',
              plan.popular && 'bg-blue-500 hover:bg-blue-600',
              plan.recommended && 'bg-purple-500 hover:bg-purple-600'
            )}
          >
            {plan.popular ? 'Most Popular' : 'Recommended'}
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <div className="flex items-center justify-center mb-2">{getPlanIcon()}</div>
        <CardTitle className="text-xl">{plan.name}</CardTitle>
        <CardDescription className="text-sm">{plan.description}</CardDescription>

        {/* Pricing */}
        <div className="mt-4">
          <div className="flex items-baseline justify-center">
            <span className="text-3xl font-bold">₹{price.toLocaleString('en-IN')}</span>
            {plan.id !== 'free' && (
              <span className="text-muted-foreground ml-1">
                /{billingCycle === 'yearly' ? 'year' : 'month'}
              </span>
            )}
          </div>

          {billingCycle === 'yearly' && plan.price.yearlyDiscount > 0 && (
            <div className="mt-1">
              <span className="text-sm text-muted-foreground line-through">
                ₹{(plan.price.monthly * 12).toLocaleString('en-IN')}/year
              </span>
              <Badge variant="secondary" className="ml-2 text-xs">
                Save {plan.price.yearlyDiscount}%
              </Badge>
            </div>
          )}

          {billingCycle === 'yearly' && plan.id !== 'free' && (
            <p className="text-sm text-muted-foreground mt-1">
              ₹{Math.round(monthlyPrice).toLocaleString('en-IN')}/month billed yearly
            </p>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Features List */}
        <div className="space-y-3">
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
            <span className="text-sm">
              {typeof plan.features.users === 'number'
                ? `${plan.features.users} team members`
                : 'Unlimited team members'}
            </span>
          </div>

          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
            <span className="text-sm">
              {typeof plan.features.projects === 'number'
                ? `${plan.features.projects} projects`
                : 'Unlimited projects'}
            </span>
          </div>

          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
            <span className="text-sm">{plan.features.storage} storage</span>
          </div>

          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
            <span className="text-sm">
              {typeof plan.features.integrations === 'number'
                ? `${plan.features.integrations} integrations`
                : 'Unlimited integrations'}
            </span>
          </div>

          {plan.features.advancedAnalytics && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">Advanced analytics</span>
            </div>
          )}

          {plan.features.customIntegrations && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">Custom integrations</span>
            </div>
          )}

          {plan.features.prioritySupport && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">Priority support</span>
            </div>
          )}

          {plan.features.ssoEnabled && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">SSO & advanced security</span>
            </div>
          )}

          {plan.features.apiAccess && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">API access</span>
            </div>
          )}

          {plan.features.customBranding && (
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-sm">Custom branding</span>
            </div>
          )}
        </div>

        {/* Action Button */}
        <Button
          className="w-full mt-6"
          variant={isCurrentPlan ? 'secondary' : 'default'}
          disabled={isButtonDisabled()}
          onClick={() => !isButtonDisabled() && onSelectPlan(plan.id, billingCycle)}
        >
          {isLoading ? 'Processing...' : getButtonText()}
        </Button>

        {isCurrentPlan && (
          <p className="text-center text-sm text-muted-foreground">You're currently on this plan</p>
        )}
      </CardContent>
    </Card>
  );
};
