'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  X,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Pin,
  Star,
  Archive,
  Hash,
  Tag,
  RefreshCw,
} from 'lucide-react';
import ReactSelect from '@/components/Global/ReactSelect';
import { cn } from '@/lib/utils';
import { FilterMode, SortBy, type NoteFilters } from '@/hooks/useNote';

interface NoteFiltersProps {
  filters: NoteFilters;
  onFiltersChange: (filters: Partial<NoteFilters>) => void;
  onResetFilters: () => void;
  availableCategories: string[];
  availableTags: string[];
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  className?: string;
}

const filterModeOptions = [
  { value: 'all', label: 'All Notes', icon: Hash },
  { value: 'active', label: 'Active', icon: Hash },
  { value: 'pinned', label: 'Pinned', icon: Pin },
  { value: 'favorites', label: 'Favorites', icon: Star },
  { value: 'archived', label: 'Archived', icon: Archive },
];

const sortOptions = [
  { value: 'updatedAt', label: 'Last Updated' },
  { value: 'createdAt', label: 'Date Created' },
  { value: 'title', label: 'Title' },
  { value: 'wordCount', label: 'Word Count' },
];

const NoteFiltersComponent: React.FC<NoteFiltersProps> = ({
  filters,
  onFiltersChange,
  onResetFilters,
  availableCategories,
  availableTags,
  viewMode,
  onViewModeChange,
  className,
}) => {
  const hasActiveFilters =
    filters.searchQuery ||
    filters.category ||
    filters.tags.length > 0 ||
    filters.filterMode !== 'all';

  const categoryOptions = availableCategories.map(cat => ({
    value: cat,
    label: cat.charAt(0).toUpperCase() + cat.slice(1),
  }));

  const tagOptions = availableTags.map(tag => ({
    value: tag,
    label: tag,
  }));

  return (
    <Card className={cn('', className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Search and View Mode */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notes..."
                value={filters.searchQuery}
                onChange={e => onFiltersChange({ searchQuery: e.target.value })}
                className="pl-10"
              />
            </div>

            <div className="flex items-center bg-muted/30 dark:bg-muted/20 rounded-lg p-1 border border-border/50">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('grid')}
                className={`h-8 w-8 p-0 rounded-md transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-background text-foreground shadow-sm border border-border/50'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('list')}
                className={`h-8 w-8 p-0 rounded-md transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-background text-foreground shadow-sm border border-border/50'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filter Mode Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {filterModeOptions.map(option => {
              const Icon = option.icon;
              const isActive = filters.filterMode === option.value;

              return (
                <Button
                  key={option.value}
                  variant={isActive ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onFiltersChange({ filterMode: option.value as FilterMode })}
                  className="h-8"
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {option.label}
                </Button>
              );
            })}
          </div>

          <Separator />

          {/* Advanced Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Category Filter */}
            <div>
              <label className="text-xs font-medium text-muted-foreground mb-2 block">
                Category
              </label>
              <ReactSelect
                options={[{ value: '', label: 'All Categories' }, ...categoryOptions]}
                value={
                  categoryOptions.find(opt => opt.value === filters.category) || {
                    value: '',
                    label: 'All Categories',
                  }
                }
                onChange={option => {
                  const singleOption = option as { value: string; label: string } | null;
                  onFiltersChange({ category: singleOption?.value || '' });
                }}
                placeholder="Select category"
              />
            </div>

            {/* Tags Filter */}
            <div>
              <label className="text-xs font-medium text-muted-foreground mb-2 block">Tags</label>
              <ReactSelect
                isMulti
                options={tagOptions}
                value={filters.tags.map(tag => ({ value: tag, label: tag }))}
                onChange={options => {
                  const multiOptions = options as { value: string; label: string }[] | null;
                  onFiltersChange({ tags: multiOptions?.map(opt => opt.value) || [] });
                }}
                placeholder="Filter by tags"
              />
            </div>

            {/* Sort Options */}
            <div>
              <label className="text-xs font-medium text-muted-foreground mb-2 block">
                Sort By
              </label>
              <div className="flex gap-2">
                <ReactSelect
                  options={sortOptions}
                  value={sortOptions.find(opt => opt.value === filters.sortBy) || null}
                  onChange={option => {
                    const singleOption = option as { value: string; label: string } | null;
                    onFiltersChange({ sortBy: (singleOption?.value as SortBy) || 'updatedAt' });
                  }}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onFiltersChange({
                      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc',
                    })
                  }
                  className="h-10 w-10 p-0"
                >
                  {filters.sortOrder === 'asc' ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <>
              <Separator />
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-xs font-medium text-muted-foreground">Active filters:</span>

                {filters.searchQuery && (
                  <Badge variant="secondary" className="gap-1">
                    <Search className="h-3 w-3" />
                    {filters.searchQuery}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => onFiltersChange({ searchQuery: '' })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}

                {filters.category && (
                  <Badge variant="secondary" className="gap-1">
                    <Hash className="h-3 w-3" />
                    {filters.category}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => onFiltersChange({ category: '' })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}

                {filters.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="gap-1">
                    <Tag className="h-3 w-3" />
                    {tag}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() =>
                        onFiltersChange({
                          tags: filters.tags.filter(t => t !== tag),
                        })
                      }
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}

                {filters.filterMode !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    <Filter className="h-3 w-3" />
                    {filterModeOptions.find(opt => opt.value === filters.filterMode)?.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => onFiltersChange({ filterMode: 'all' })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}

                <Button variant="ghost" size="sm" onClick={onResetFilters} className="h-6 text-xs">
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Clear all
                </Button>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default NoteFiltersComponent;
