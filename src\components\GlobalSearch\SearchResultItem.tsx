import { SearchResult } from '@/services/GlobalSearch.service';
import { CheckSquare, FolderOpen, FileText, User, Calendar, Tag, ArrowUpRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface SearchResultItemProps {
  result: SearchResult;
  onSelect: () => void;
}

export const SearchResultItem = ({ result, onSelect }: SearchResultItemProps) => {
  const router = useRouter();

  const getIcon = () => {
    switch (result.type) {
      case 'task':
        return <CheckSquare className="w-4 h-4 text-blue-500" />;
      case 'project':
        return <FolderOpen className="w-4 h-4 text-green-500" />;
      case 'note':
        return <FileText className="w-4 h-4 text-purple-500" />;
      case 'user':
        return <User className="w-4 h-4 text-orange-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeLabel = () => {
    return result.type.charAt(0).toUpperCase() + result.type.slice(1);
  };

  const handleClick = () => {
    router.push(result.url);
    onSelect();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div
      onClick={handleClick}
      className="p-4 interactive-hover cursor-pointer theme-transition group hover-reveal"
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">{getIcon()}</div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="theme-badge-secondary">{getTypeLabel()}</span>
            {result.relevanceScore > 5 && <span className="theme-badge-success">High Match</span>}
          </div>

          <h3
            className="font-medium theme-text-primary truncate group-hover:text-primary theme-transition"
            dangerouslySetInnerHTML={{ __html: result.highlights?.title?.[0] || result.title }}
          />

          {result.description && (
            <p
              className="text-sm theme-text-secondary mt-1 line-clamp-2"
              dangerouslySetInnerHTML={{
                __html: result.highlights?.content?.[0] || result.description,
              }}
            />
          )}

          <div className="flex items-center gap-4 mt-2 text-xs theme-text-secondary">
            {result.metadata.status && (
              <span className="flex items-center gap-1">
                <Tag className="w-3 h-3" />
                {result.metadata.status}
              </span>
            )}
            {result.metadata.assignee && (
              <span className="flex items-center gap-1">
                <User className="w-3 h-3" />
                {result.metadata.assignee}
              </span>
            )}
            {result.metadata.projectName && (
              <span className="flex items-center gap-1">
                <FolderOpen className="w-3 h-3" />
                {result.metadata.projectName}
              </span>
            )}
            {result.metadata.createdAt && (
              <span className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDate(result.metadata.createdAt)}
              </span>
            )}
          </div>
        </div>

        <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 theme-transition">
          <ArrowUpRight className="w-4 h-4 theme-text-secondary" />
        </div>
      </div>
    </div>
  );
};
