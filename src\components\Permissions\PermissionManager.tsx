'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { EmptyState } from '@/components/Global/EmptyState';
import { ConfirmDialog } from '@/components/Global/ConfirmDialog';
import {
  Users,
  Shield,
  Crown,
  User,
  UserCheck,
  Edit,
  Trash2,
  Search,
  RefreshCw,
} from 'lucide-react';
import { usePermissions, OrganizationMember } from '@/hooks/usePermissions';
import Image from 'next/image';

interface PermissionManagerProps {
  organizationId: string;
  className?: string;
}

export const PermissionManager: React.FC<PermissionManagerProps> = ({
  organizationId,
  className = '',
}) => {
  const { toast } = useToast();
  const {
    loading,
    error,
    getOrganizationMembers,
    updateUserRole,
    removeMember,
    checkPermission,
    isAuthenticated,
  } = usePermissions();

  // State
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [pagination, setPagination] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [selectedMember, setSelectedMember] = useState<OrganizationMember | null>(null);
  const [newRole, setNewRole] = useState<'Owner' | 'Member' | 'Guest'>('Member');
  const [canManageUsers, setCanManageUsers] = useState(false);

  // Load members
  const loadMembers = async () => {
    if (!isAuthenticated) return;

    const result = await getOrganizationMembers(organizationId, searchTerm, currentPage);
    if (result) {
      setMembers(result.members);
      setPagination(result.pagination);
    }
  };

  // Check if current user can manage users
  const checkManagePermissions = async () => {
    const canManage = await checkPermission('user', 'manage', undefined, organizationId);
    setCanManageUsers(canManage);
  };

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadMembers();
    checkManagePermissions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, searchTerm, currentPage, isAuthenticated]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle role update
  const handleUpdateRole = async () => {
    if (!selectedMember || !canManageUsers) return;

    const success = await updateUserRole(selectedMember.userId._id, organizationId, newRole);

    if (success) {
      toast({
        title: 'Success',
        description: `Updated ${selectedMember.userId.name}'s role to ${newRole}`,
      });
      setShowRoleDialog(false);
      loadMembers();
    } else {
      toast({
        title: 'Error',
        description: error || 'Failed to update user role',
        variant: 'destructive',
      });
    }
  };

  // Handle member removal
  const handleRemoveMember = async () => {
    if (!selectedMember || !canManageUsers) return;

    const success = await removeMember(selectedMember.userId._id, organizationId);

    if (success) {
      toast({
        title: 'Success',
        description: `Removed ${selectedMember.userId.name} from organization`,
      });
      setShowRemoveDialog(false);
      loadMembers();
    } else {
      toast({
        title: 'Error',
        description: error || 'Failed to remove member',
        variant: 'destructive',
      });
    }
  };

  // Get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'Owner':
        return 'default';
      case 'Member':
        return 'secondary';
      case 'Guest':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'Owner':
        return <Crown className="w-4 h-4" />;
      case 'Member':
        return <UserCheck className="w-4 h-4" />;
      case 'Guest':
        return <User className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  if (!isAuthenticated) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">Please sign in to view permissions</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <CardTitle>Organization Members</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={loadMembers} disabled={loading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search members..."
                value={searchTerm}
                onChange={e => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Members List */}
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner />
            </div>
          ) : members.length === 0 ? (
            <EmptyState
              icon={<Users className="w-8 h-8" />}
              title="No members found"
              description={
                searchTerm ? 'No members match your search' : 'This organization has no members yet'
              }
            />
          ) : (
            <div className="space-y-2">
              {members.map(member => (
                <div
                  key={member.userId._id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                      {member.userId.image ? (
                        <Image
                          width={40}
                          height={40}
                          src={member.userId.image}
                          alt={member.userId.name}
                          className="w-10 h-10 rounded-full"
                        />
                      ) : (
                        <User className="w-5 h-5 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{member.userId.name}</p>
                      <p className="text-sm text-muted-foreground">{member.userId.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant={getRoleBadgeVariant(member.role)}>
                      {getRoleIcon(member.role)}
                      <span className="ml-1">{member.role}</span>
                    </Badge>

                    {canManageUsers && member.role !== 'Owner' && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedMember(member);
                            setNewRole(member.role);
                            setShowRoleDialog(true);
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedMember(member);
                            setShowRemoveDialog(true);
                          }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Showing {(currentPage - 1) * pagination.limit + 1} to{' '}
                {Math.min(currentPage * pagination.limit, pagination.total)} of {pagination.total}{' '}
                members
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Update Dialog */}
      <Dialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update User Role</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>User</Label>
              <p className="text-sm text-muted-foreground">
                {selectedMember?.userId.name} ({selectedMember?.userId.email})
              </p>
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select
                value={newRole}
                onValueChange={(value: 'Owner' | 'Member' | 'Guest') => setNewRole(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Owner">Owner</SelectItem>
                  <SelectItem value="Member">Member</SelectItem>
                  <SelectItem value="Guest">Guest</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowRoleDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateRole}>Update Role</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <ConfirmDialog
        isOpen={showRemoveDialog}
        onClose={() => setShowRemoveDialog(false)}
        title="Remove Member"
        description={`Are you sure you want to remove ${selectedMember?.userId.name} from the organization? This action cannot be undone.`}
        onConfirm={handleRemoveMember}
        confirmText="Remove"
        cancelText="Cancel"
      />
    </div>
  );
};
