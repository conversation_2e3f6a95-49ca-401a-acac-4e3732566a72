// Client-side notification service - safe for browser use
export interface NotificationData {
  userId?: string;
  title: string;
  description: string;
  type:
    | 'mention'
    | 'task'
    | 'team'
    | 'system'
    | 'onboarding'
    | 'integration'
    | 'success'
    | 'info'
    | 'error'
    | 'assigned';
  link?: string;
  metadata?: Record<string, any>;
}

export class NotificationClientService {
  static async getNotificationsClient(
    page = 0,
    limit = 10,
    filter = 'all',
    search = ''
  ): Promise<any> {
    const response = await fetch('/api/notifications?' + new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      filter,
      search
    }));
    return response.json();
  }

  static async markAsReadClient(notificationId: string): Promise<any> {
    const response = await fetch(`/api/notifications/${notificationId}/read`, {
      method: 'PATCH'
    });
    return response.json();
  }

  static async markAllAsReadClient(): Promise<any> {
    const response = await fetch('/api/notifications/mark-all-read', {
      method: 'PATCH'
    });
    return response.json();
  }

  static async deleteNotificationClient(notificationId: string): Promise<any> {
    const response = await fetch(`/api/notifications/${notificationId}`, {
      method: 'DELETE'
    });
    return response.json();
  }

  static async clearAllNotificationsClient(): Promise<any> {
    const response = await fetch('/api/notifications/clear-all', {
      method: 'DELETE'
    });
    return response.json();
  }

  static async getUnreadCountClient(): Promise<number> {
    const response = await fetch('/api/notifications/unread-count');
    const data = await response.json();
    return data.count;
  }

  static async createNotificationClient(data: Omit<NotificationData, 'userId'>): Promise<any> {
    const notification = {
      title: data.title,
      description: data.description,
      type: data.type,
      link: data.link || '',
      metadata: data.metadata || {},
    };

    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notification)
    });
    return response.json();
  }
}
