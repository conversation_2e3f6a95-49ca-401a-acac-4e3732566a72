import mongoose, { Schema, Document } from 'mongoose';
import { CallbackError } from 'mongoose';

interface IAvailabilitySchedule {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  isAvailable: boolean;
}

interface IWorkloadTracking {
  currentUtilization: number; // Percentage 0-100
  allocatedHours: number;
  actualHours: number;
  lastUpdated: Date;
}

interface IResourceAllocation extends Document {
  allocationId: string;
  userId: mongoose.Schema.Types.ObjectId;
  projectId: mongoose.Schema.Types.ObjectId;
  role: string;
  capacity: number; // Percentage 0-100
  startDate: Date;
  endDate: Date;
  skills: string[];
  hourlyRate: number;
  availabilitySchedule: IAvailabilitySchedule[];
  workloadTracking: IWorkloadTracking;
  organizationId: mongoose.Schema.Types.ObjectId;
  status: 'active' | 'inactive' | 'completed' | 'cancelled';
  notes?: string;
  createdBy: mongoose.Schema.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const availabilityScheduleSchema = new Schema({
  dayOfWeek: {
    type: Number,
    required: true,
    min: 0,
    max: 6,
  },
  startTime: {
    type: String,
    required: true,
    validate: {
      validator: function (time: string) {
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'Start time must be in HH:MM format',
    },
  },
  endTime: {
    type: String,
    required: true,
    validate: {
      validator: function (time: string) {
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
      },
      message: 'End time must be in HH:MM format',
    },
  },
  isAvailable: {
    type: Boolean,
    default: true,
  },
});

const workloadTrackingSchema = new Schema({
  currentUtilization: {
    type: Number,
    min: 0,
    max: 100,
    default: 0,
  },
  allocatedHours: {
    type: Number,
    min: 0,
    default: 0,
  },
  actualHours: {
    type: Number,
    min: 0,
    default: 0,
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
});

const resourceAllocationSchema = new Schema<IResourceAllocation>(
  {
    allocationId: {
      type: String,
      unique: true,
      required: true,
      default: () => new mongoose.Types.ObjectId().toString(),
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    projectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      required: true,
    },
    role: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    capacity: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
      validate: {
        validator: function (endDate: Date) {
          return endDate > this.startDate;
        },
        message: 'End date must be after start date',
      },
    },
    skills: {
      type: [String],
      validate: {
        validator: function (skills: string[]) {
          return skills.length <= 20;
        },
        message: 'Cannot have more than 20 skills',
      },
    },
    hourlyRate: {
      type: Number,
      min: 0,
      default: 0,
    },
    availabilitySchedule: {
      type: [availabilityScheduleSchema],
      validate: {
        validator: function (schedule: IAvailabilitySchedule[]) {
          const uniqueDays = new Set(schedule.map(s => s.dayOfWeek));
          return uniqueDays.size === schedule.length;
        },
        message: 'Cannot have duplicate days in availability schedule',
      },
    },
    workloadTracking: {
      type: workloadTrackingSchema,
      default: {},
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'completed', 'cancelled'],
      default: 'active',
    },
    notes: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
resourceAllocationSchema.index({ userId: 1, projectId: 1 });
resourceAllocationSchema.index({ organizationId: 1, status: 1 });
resourceAllocationSchema.index({ startDate: 1, endDate: 1 });
resourceAllocationSchema.index({ 'workloadTracking.currentUtilization': 1 });

// Virtual for allocation duration in days
resourceAllocationSchema.virtual('durationDays').get(function () {
  const timeDiff = this.endDate.getTime() - this.startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
});

// Virtual for total weekly hours based on availability schedule
resourceAllocationSchema.virtual('weeklyHours').get(function () {
  return this.availabilitySchedule.reduce((total, schedule) => {
    if (!schedule.isAvailable) return total;

    const start = schedule.startTime.split(':').map(Number);
    const end = schedule.endTime.split(':').map(Number);
    const startMinutes = start[0] * 60 + start[1];
    const endMinutes = end[0] * 60 + end[1];
    const dailyHours = (endMinutes - startMinutes) / 60;

    return total + dailyHours;
  }, 0);
});

// Virtual for over-allocation status
resourceAllocationSchema.virtual('isOverAllocated').get(function () {
  return this.workloadTracking.currentUtilization > 100;
});

// Pre-save middleware to generate allocationId if not provided
resourceAllocationSchema.pre('save', function (next) {
  if (!this.allocationId) {
    this.allocationId = new mongoose.Types.ObjectId().toString();
  }
  next();
});

// Pre-save middleware to validate capacity limits
resourceAllocationSchema.pre('save', async function (next) {
  try {
    // Check for overlapping allocations for the same user
    const overlappingAllocations = await mongoose.model('ResourceAllocation').find({
      userId: this.userId,
      _id: { $ne: this._id },
      status: 'active',
      $or: [
        {
          startDate: { $lte: this.endDate },
          endDate: { $gte: this.startDate },
        },
      ],
    });

    // Calculate total capacity for overlapping period
    const totalCapacity = overlappingAllocations.reduce((sum, allocation) => {
      return sum + allocation.capacity;
    }, this.capacity);

    if (totalCapacity > 100) {
      const error = new Error(
        `Total capacity allocation (${totalCapacity}%) exceeds 100% for user during this period`
      );
      return next(error);
    }

    next();
  } catch (error) {
    next(error as CallbackError);
  }
});

// Method to check for allocation conflicts
resourceAllocationSchema.methods.checkConflicts = async function () {
  const conflicts = await mongoose
    .model('ResourceAllocation')
    .find({
      userId: this.userId,
      _id: { $ne: this._id },
      status: 'active',
      $or: [
        {
          startDate: { $lte: this.endDate },
          endDate: { $gte: this.startDate },
        },
      ],
    })
    .populate('projectId', 'name');

  return conflicts.map(conflict => ({
    allocationId: conflict.allocationId,
    projectName: conflict.projectId.name,
    capacity: conflict.capacity,
    startDate: conflict.startDate,
    endDate: conflict.endDate,
    overlapDays: this.calculateOverlapDays(conflict.startDate, conflict.endDate),
  }));
};

// Method to calculate overlap days
resourceAllocationSchema.methods.calculateOverlapDays = function (
  otherStart: Date,
  otherEnd: Date
) {
  const overlapStart = new Date(Math.max(this.startDate.getTime(), otherStart.getTime()));
  const overlapEnd = new Date(Math.min(this.endDate.getTime(), otherEnd.getTime()));

  if (overlapStart >= overlapEnd) return 0;

  const timeDiff = overlapEnd.getTime() - overlapStart.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
};

export const ResourceAllocation =
  mongoose.models.ResourceAllocation ||
  mongoose.model<IResourceAllocation>('ResourceAllocation', resourceAllocationSchema);
export type { IResourceAllocation, IAvailabilitySchedule, IWorkloadTracking };
