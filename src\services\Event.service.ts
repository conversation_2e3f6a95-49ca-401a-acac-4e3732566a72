import axios from 'axios';
export interface EventFilters {
  userId?: string;
  organizationId?: string;
  projectId?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
}

export interface CreateEventData {
  title: string;
  description: string;
  location: string;
  startTime: Date;
  endTime: Date;
  isAllDay: boolean;
  timezone: string;
  attendees: any[];
  reminders: any[];
  taskIds: string[];
  colorId: string;
  category: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  visibility: 'default' | 'public' | 'private' | 'confidential';
  userId?: string;
  organizationId?: string;
  projectId?: string;
  recurrence?: any;
  attachments?: any[];
}
export interface UpdateEventData {
  startTime?: Date;
  endTime?: Date;
  [key: string]: any;
}

export class EventService {
  static async getEvents(params?: any) {
    try {
      const response = await axios.get('/api/events', { params });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch events');
    }
  }

  static async createEvent(eventData: CreateEventData) {
    try {
      const response = await axios.post('/api/events', eventData);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create event');
    }
  }

  static async updateEvent(eventId: string, updates: UpdateEventData) {
    try {
      const response = await axios.put(`/api/events/${eventId}`, updates);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to update event');
    }
  }

  static async deleteEvent(eventId: string) {
    try {
      const response = await axios.delete(`/api/events/${eventId}`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to delete event');
    }
  }

  static async detectConflicts(eventData: any, excludeEventId?: string) {
    try {
      const response = await axios.post('/api/events/conflicts', {
        ...eventData,
        excludeEventId,
      });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to detect conflicts');
    }
  }

  static async scheduleTaskDeadlines(projectId: string) {
    try {
      const response = await axios.post('/api/events/schedule-tasks', { projectId });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to schedule task deadlines');
    }
  }
}

export default EventService;
