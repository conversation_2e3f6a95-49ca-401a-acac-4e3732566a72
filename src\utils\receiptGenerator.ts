export interface ReceiptData {
  receiptNumber: string;
  transactionId: string;
  date: string;
  organization: {
    name: string;
    email: string;
  };
  customer: {
    name: string;
    email: string;
  };
  items: {
    description: string;
    quantity: number;
    unit: string;
    pricePerUnit: number;
    amount: number;
  }[];
  payment: {
    method: string;
    status: string;
    paymentId?: string;
    orderId?: string;
  };
  totals: {
    subtotal: number;
    tax: number;
    total: number;
  };
  currency: string;
}

export class ReceiptGenerator {
  static generateReceiptHTML(data: ReceiptData): string {
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    };

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: data.currency,
        minimumFractionDigits: 0,
      }).format(amount);
    };

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - ${data.receiptNumber}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .receipt-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .receipt-number {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .info-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-block h3 {
            color: #667eea;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }
        
        .info-block p {
            margin-bottom: 5px;
            color: #555;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .items-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 15px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }
        
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .items-table tr:last-child td {
            border-bottom: none;
        }
        
        .text-right {
            text-align: right;
        }
        
        .payment-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .payment-info h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .payment-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .totals-section {
            border-top: 2px solid #e9ecef;
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 16px;
        }
        
        .total-row.final {
            border-top: 2px solid #667eea;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: bold;
            font-size: 20px;
            color: #667eea;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .receipt-footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .receipt-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
        
        @media (max-width: 768px) {
            .info-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .payment-details {
                grid-template-columns: 1fr;
            }
            
            .items-table {
                font-size: 14px;
            }
            
            .items-table th,
            .items-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-number">Receipt #${data.receiptNumber}</div>
        </div>
        
        <div class="receipt-body">
            <div class="info-section">
                <div class="info-block">
                    <h3>Bill To</h3>
                    <p><strong>${data.customer.name}</strong></p>
                    <p>${data.customer.email}</p>
                    <p><strong>Organization:</strong> ${data.organization.name}</p>
                </div>
                
                <div class="info-block">
                    <h3>Transaction Details</h3>
                    <p><strong>Date:</strong> ${formatDate(data.date)}</p>
                    <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
                    <p><strong>Status:</strong> 
                        <span class="status-badge status-${data.payment.status}">
                            ${data.payment.status}
                        </span>
                    </p>
                </div>
            </div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-right">Quantity</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.items
                      .map(
                        item => `
                        <tr>
                            <td>${item.description}</td>
                            <td class="text-right">${item.quantity} ${item.unit}</td>
                            <td class="text-right">${formatCurrency(item.pricePerUnit)}/${item.unit}</td>
                            <td class="text-right">${formatCurrency(item.amount)}</td>
                        </tr>
                    `
                      )
                      .join('')}
                </tbody>
            </table>
            
            <div class="payment-info">
                <h3>Payment Information</h3>
                <div class="payment-details">
                    <div>
                        <p><strong>Payment Method:</strong> ${data.payment.method.toUpperCase()}</p>
                        <p><strong>Payment Status:</strong> 
                            <span class="status-badge status-${data.payment.status}">
                                ${data.payment.status}
                            </span>
                        </p>
                    </div>
                    <div>
                        ${data.payment.paymentId ? `<p><strong>Payment ID:</strong> ${data.payment.paymentId}</p>` : ''}
                        ${data.payment.orderId ? `<p><strong>Order ID:</strong> ${data.payment.orderId}</p>` : ''}
                    </div>
                </div>
            </div>
            
            <div class="totals-section">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span>${formatCurrency(data.totals.subtotal)}</span>
                </div>
                ${
                  data.totals.tax > 0
                    ? `
                <div class="total-row">
                    <span>Tax:</span>
                    <span>${formatCurrency(data.totals.tax)}</span>
                </div>
                `
                    : ''
                }
                <div class="total-row final">
                    <span>Total Amount:</span>
                    <span>${formatCurrency(data.totals.total)}</span>
                </div>
            </div>
        </div>
        
        <div class="receipt-footer">
            <p>Thank you for your business! This is a computer-generated receipt.</p>
            <p>For any queries, please contact support at ${data.organization.email}</p>
        </div>
    </div>
</body>
</html>`;
  }

  static downloadReceipt(data: ReceiptData): void {
    const html = this.generateReceiptHTML(data);
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `receipt-${data.receiptNumber}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  static printReceipt(data: ReceiptData): void {
    const html = this.generateReceiptHTML(data);
    const printWindow = window.open('', '_blank');

    if (printWindow) {
      printWindow.document.write(html);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);
      };
    }
  }
}
