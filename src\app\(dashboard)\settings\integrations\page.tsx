'use client';

import { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Check, ExternalLink, Zap, Settings } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useIntegrations } from '@/hooks/useIntegrations';
import { useSubscription } from '@/hooks/useSubscription';
import { AVAILABLE_INTEGRATIONS } from '@/constant/Integration';
import { toast } from 'sonner';

export default function IntegrationsPage() {
  const [activeTab, setActiveTab] = useState('connected');
  const {
    connectedIntegrations,
    isLoading: isLoadingIntegrations,
    disconnect,
    connect,
    isConnecting,
  } = useIntegrations();

  const { subscription, isLimitReached, currentPlan } = useSubscription();

  const connectedProviders = new Set(
    connectedIntegrations?.map((integration: any) => integration.provider) || []
  );
  const availableIntegrations = Object.values(AVAILABLE_INTEGRATIONS).filter(
    integration => integration.available && !connectedProviders.has(integration.id)
  );

  const handleConnect = async (provider: string) => {
    if (provider === 'github') {
      try {
        const response = await fetch('/api/github/oauth/authorize?popup=true');
        const data = await response.json();
        if (data.url) {
          window.open(data.url, 'github-auth', 'width=600,height=700');
        }
      } catch (error) {
        toast.error('Failed to initiate GitHub connection');
      }
    } else if (provider === 'notion') {
      connect({ provider, data: {} });
    } else {
      connect({ provider, data: {} });
    }
  };

  return (
    <div className="space-y-6 px-4">
      <div>
        <h3 className="text-lg font-medium theme-text-primary">Integrations</h3>
        <p className="theme-text-secondary">
          Connect TaskFluxio with your favorite tools and services
        </p>
      </div>
      <Separator className="theme-divider" />

      {/* Integration Limit Alert */}
      {subscription && (
        <Alert className="theme-surface-elevated theme-border">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle className="theme-text-primary">Integration Usage</AlertTitle>
          <AlertDescription className="theme-text-secondary">
            Your {currentPlan} plan allows integrations based on your subscription limits.
            {isLimitReached('integrations') && (
              <span className="text-destructive font-medium">
                {' '}
                Limit reached - upgrade to add more integrations.
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={activeTab} className="space-y-4" onValueChange={setActiveTab}>
        <TabsList className="theme-surface-elevated theme-border">
          <TabsTrigger value="connected" className="hover-reveal theme-transition">
            Connected ({connectedIntegrations?.length || 0})
          </TabsTrigger>
          <TabsTrigger value="available" className="hover-reveal theme-transition">
            Available ({availableIntegrations.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="connected" className="space-y-4">
          {isLoadingIntegrations ? (
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <Card key={i} className="theme-surface-elevated theme-border">
                  <CardHeader>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-md" />
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-4 w-48" />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <Skeleton className="h-12 w-full" />
                      <Skeleton className="h-12 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : connectedIntegrations && connectedIntegrations.length > 0 ? (
            connectedIntegrations.map((integration: any) => {
              const integrationMeta =
                AVAILABLE_INTEGRATIONS[integration.provider as keyof typeof AVAILABLE_INTEGRATIONS];
              const IconComponent = integrationMeta?.icon || Settings;

              return (
                <Card
                  key={integration._id}
                  className="theme-surface-elevated theme-border hover-reveal glow-on-hover theme-transition"
                >
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="flex items-center space-x-4">
                      <div className="bg-gray-100 p-2 rounded-md">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-xl theme-text-primary">
                          {integrationMeta?.name || integration.provider}
                        </CardTitle>
                        <CardDescription className="theme-text-secondary">
                          {integrationMeta?.description || `${integration.provider} integration`}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline" className="border-success text-success">
                      Connected
                    </Badge>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <p className="text-sm font-medium">Connected</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(integration.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Last Sync</p>
                        <p className="text-sm text-muted-foreground">
                          {integration.lastSyncedAt
                            ? new Date(integration.lastSyncedAt).toLocaleDateString()
                            : 'Never'}
                        </p>
                      </div>
                      {integration.workspaceName && (
                        <div>
                          <p className="text-sm font-medium">Workspace</p>
                          <p className="text-sm text-muted-foreground">
                            {integration.workspaceName}
                          </p>
                        </div>
                      )}
                      {integration.metadata && Object.keys(integration.metadata).length > 0 && (
                        <div>
                          <p className="text-sm font-medium">Details</p>
                          <p className="text-sm text-muted-foreground">
                            {JSON.stringify(integration.metadata, null, 2).slice(0, 50)}...
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t border-border/20 pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="hover-reveal theme-transition"
                      onClick={() => {
                        // Navigate to integration hub
                        if (integration.provider === 'github') {
                          window.location.href = '/github-hub';
                        } else if (integration.provider === 'notion') {
                          window.location.href = '/notion-hub';
                        }
                      }}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Manage
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="theme-transition"
                      onClick={() => disconnect(integration.provider)}
                    >
                      Disconnect
                    </Button>
                  </CardFooter>
                </Card>
              );
            })
          ) : (
            <div className="text-center py-12">
              <Zap className="h-12 w-12 mx-auto text-primary mb-4" />
              <h3 className="text-lg font-medium theme-text-primary mb-2">
                No integrations connected
              </h3>
              <p className="theme-text-secondary mb-4">
                Connect your favorite tools to streamline your workflow.
              </p>
              <Button onClick={() => setActiveTab('available')}>
                Browse Available Integrations
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="available" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {availableIntegrations.map(integration => (
              <Card
                key={integration.id}
                className="flex flex-col theme-surface-elevated theme-border hover-reveal glow-on-hover theme-transition"
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="bg-gray-100 p-2 rounded-md">
                        <integration.icon className="h-5 w-5 text-primary" />
                      </div>
                      <CardTitle className="text-lg theme-text-primary">
                        {integration.name}
                      </CardTitle>
                    </div>
                    {integration.popular && (
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                        Popular
                      </Badge>
                    )}
                  </div>
                  <CardDescription className="mt-2 theme-text-secondary">
                    {integration.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow">
                  <ul className="list-disc list-inside text-sm space-y-1 theme-text-secondary">
                    <li>Automatic synchronization</li>
                    <li>Two-way updates</li>
                    <li>Custom notifications</li>
                  </ul>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button
                    className="w-full bg-primary hover-reveal theme-transition"
                    onClick={() => handleConnect(integration.id)}
                    disabled={isConnecting || (subscription && isLimitReached('integrations'))}
                  >
                    {isConnecting ? 'Connecting...' : 'Connect'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-8 space-y-6">
        <div>
          <h3 className="text-xl font-bold theme-text-primary">Integration Settings</h3>
          <p className="theme-text-secondary">Configure global settings for all integrations</p>
        </div>

        <Card className="theme-surface-elevated theme-border hover-reveal glow-on-hover theme-transition">
          <CardHeader>
            <CardTitle className="theme-text-primary">Synchronization</CardTitle>
            <CardDescription className="theme-text-secondary">
              Control how and when your integrations sync data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-sync">Automatic Synchronization</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically sync data from connected integrations
                </p>
              </div>
              <Switch id="auto-sync" defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="sync-frequency">Sync Frequency</Label>
                <p className="text-sm text-muted-foreground">
                  How often to sync data from integrations
                </p>
              </div>
              <div className="w-[180px]">
                <select
                  id="sync-frequency"
                  className="w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm"
                  defaultValue="30"
                >
                  <option value="15">Every 15 minutes</option>
                  <option value="30">Every 30 minutes</option>
                  <option value="60">Every hour</option>
                  <option value="360">Every 6 hours</option>
                  <option value="720">Every 12 hours</option>
                  <option value="1440">Once a day</option>
                </select>
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notifications">Integration Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications about integration activities
                </p>
              </div>
              <Switch id="notifications" defaultChecked />
            </div>
          </CardContent>
        </Card>

        <div className="theme-surface-elevated p-4 theme-border">
          <div className="flex items-start space-x-4">
            <div className="bg-gray-100 p-2 rounded-full">
              <Check className="h-5 w-5 text-success" />
            </div>
            <div>
              <h4 className="font-medium theme-text-primary">Need a custom integration?</h4>
              <p className="text-sm theme-text-secondary mt-1">
                We can build custom integrations for your specific needs. Contact our team to
                discuss your requirements.
              </p>
              <Button
                variant="link"
                className="p-0 h-auto mt-2 flex items-center hover-reveal theme-transition"
                asChild
              >
                <a href="#" target="_blank" rel="noopener noreferrer">
                  Learn more <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
