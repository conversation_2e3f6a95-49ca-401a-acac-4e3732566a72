'use client';

import React from 'react';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Bell,
  Star,
  Users,
  Shield,
  Calendar,
  TrendingUp,
  Crown,
  Zap,
  HardDrive,
} from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { UpgradePrompt } from '@/components/Upgrade/UpgradePrompt';
import { useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { useRazorpay } from '@/hooks/useRazorpay';
import axios from 'axios';
import { useProjectStore } from '@/stores/projectsStore';

export default function SettingsPage() {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { data: session } = useSession();
  const {
    subscription,
    usage,
    currentPlan,
    isLoading: isLoadingSubscription,
    formatUsageDisplay,
    getUsagePercentage,
    shouldShowUpgradePrompt,
  } = useSubscription();
  const { processSubscriptionPayment } = useRazorpay();

  const { projects } = useProjectStore();

  const { data: tasks = [], isLoading: isLoadingTasks } = useQuery({
    queryKey: ['tasks'],
    queryFn: async () => {
      const response = await axios.get('/api/tasks');
      return response.data.tasks || [];
    },
  });

  const { data: notes = [], isLoading: isLoadingNotes } = useQuery({
    queryKey: ['notes'],
    queryFn: async () => {
      const response = await axios.get('/api/notes');
      return response.data.notes || [];
    },
  });

  const { data: organization, isLoading: isLoadingOrg } = useQuery({
    queryKey: ['organization'],
    queryFn: async () => {
      const response = await axios.get('/api/organizations');
      return response.data;
    },
  });

  // Calculate stats from real data
  const stats = {
    totalProjects: projects.length,
    totalTasks: tasks.length,
    completedTasks: tasks.filter((task: any) => task.status === 'Completed').length,
    totalNotes: Array.isArray(notes) ? notes.length : 0,
    teamMembers: organization?.members?.length || 0,
  };

  const isLoading = isLoadingTasks || isLoadingNotes || isLoadingOrg || isLoadingSubscription;

  // Handle upgrade
  const handleUpgrade = async (planId: string) => {
    try {
      await processSubscriptionPayment({
        planId: planId as 'basic' | 'professional' | 'enterprise',
        billingCycle: 'monthly',
        onSuccess: () => {
          setShowUpgradeModal(false);
        },
        onFailure: error => {
          console.error('Upgrade failed:', error);
        },
      });
    } catch (error) {
      console.error('Upgrade error:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium theme-text-primary">Settings Overview</h3>
        <p className="text-sm theme-text-secondary">
          Manage your account settings, preferences, and workspace configurations
        </p>
      </div>
      <Separator className="theme-divider" />

      {/* Subscription Usage Warning */}
      {shouldShowUpgradePrompt() && (
        <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <TrendingUp className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                  Upgrade Recommended
                </h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  You're approaching your plan limits. Upgrade to continue growing.
                </p>
              </div>
              <Button
                size="sm"
                onClick={() => setShowUpgradeModal(true)}
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
              >
                Upgrade Now
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
        <div className="flex-1">
          {/* Account Overview Stats */}
          <div className="grid gap-6 grid-cols-2 md:grid-cols-4 mb-6">
            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-xs theme-text-secondary">Projects</p>
                    <p className="text-lg font-semibold theme-text-primary">
                      {isLoading ? <Skeleton className="h-6 w-8" /> : stats.totalProjects}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-xs theme-text-secondary">Tasks</p>
                    <p className="text-lg font-semibold theme-text-primary">
                      {isLoading ? <Skeleton className="h-6 w-8" /> : stats.totalTasks}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <Users className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-xs theme-text-secondary">Team</p>
                    <p className="text-lg font-semibold theme-text-primary">
                      {isLoading ? <Skeleton className="h-6 w-8" /> : stats.teamMembers}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <Shield className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <p className="text-xs theme-text-secondary">Security</p>
                    <p className="text-lg font-semibold theme-text-primary">
                      {isLoading ? <Skeleton className="h-6 w-8" /> : '85%'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
            {/* Quick Actions */}
            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 theme-text-primary">
                  <Star className="h-5 w-5 text-yellow-500" />
                  Quick Actions
                </CardTitle>
                <CardDescription className="theme-text-secondary">
                  Frequently used settings and actions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/settings/notifications">
                  <Button
                    variant="outline"
                    className="w-full justify-start theme-button-ghost interactive-hover"
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Notification Settings
                  </Button>
                </Link>
                <Link href="/settings/members">
                  <Button
                    variant="outline"
                    className="w-full justify-start theme-button-ghost interactive-hover"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Team Members
                  </Button>
                </Link>
                <Link href="/settings/email-templates">
                  <Button
                    variant="outline"
                    className="w-full justify-start theme-button-ghost interactive-hover"
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Email Templates
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="w-full justify-start theme-button-ghost interactive-hover"
                  onClick={() => setShowUpgradeModal(true)}
                >
                  <Crown className="mr-2 h-4 w-4" />
                  Upgrade Plan
                </Button>
              </CardContent>
            </Card>

            {/* Subscription Status */}
            <Card className="theme-surface-elevated hover-reveal theme-transition">
              <CardHeader>
                <CardTitle className="theme-text-primary">Subscription Status</CardTitle>
                <CardDescription className="theme-text-secondary">
                  Your current plan and usage information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium theme-text-primary">Current Plan</span>
                  <Badge
                    variant={currentPlan === 'free' ? 'secondary' : 'default'}
                    className="theme-badge-secondary capitalize"
                  >
                    {currentPlan === 'free' ? (
                      <>
                        <HardDrive className="mr-1 h-3 w-3" />
                        Free
                      </>
                    ) : currentPlan === 'basic' ? (
                      <>
                        <Zap className="mr-1 h-3 w-3" />
                        Personal Pro
                      </>
                    ) : currentPlan === 'professional' ? (
                      <>
                        <Crown className="mr-1 h-3 w-3" />
                        Team
                      </>
                    ) : (
                      <>
                        <Shield className="mr-1 h-3 w-3" />
                        Business
                      </>
                    )}
                  </Badge>
                </div>

                {isLoading ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Account Type</span>
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Team Members</span>
                      <Skeleton className="h-5 w-12" />
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Account Type</span>
                      <Badge variant="outline" className="theme-badge-secondary">
                        {session?.user?.systemRole === 'Admin' ? 'Administrator' : 'User'}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Team Members</span>
                      <Badge variant="outline" className="theme-badge-secondary">
                        {stats.teamMembers}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Organization</span>
                      <Badge variant="outline" className="theme-badge-secondary">
                        {organization?.name || 'No Organization'}
                      </Badge>
                    </div>
                  </>
                )}

                {isLoadingSubscription ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Projects</span>
                      <Skeleton className="h-5 w-20" />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Storage</span>
                      <Skeleton className="h-5 w-20" />
                    </div>
                  </>
                ) : usage ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Projects</span>
                      <Badge
                        variant={getUsagePercentage('projects') >= 90 ? 'destructive' : 'outline'}
                        className="theme-badge-primary"
                      >
                        {formatUsageDisplay('projects')}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Storage</span>
                      <Badge
                        variant={getUsagePercentage('storage') >= 90 ? 'destructive' : 'outline'}
                        className="theme-badge-primary"
                      >
                        {formatUsageDisplay('storage')}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium theme-text-primary">Team Members</span>
                      <Badge
                        variant={getUsagePercentage('users') >= 90 ? 'destructive' : 'outline'}
                        className="theme-badge-primary"
                      >
                        {formatUsageDisplay('users')}
                      </Badge>
                    </div>
                    {subscription?.billingCycle && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium theme-text-primary">
                          Billing Cycle
                        </span>
                        <Badge variant="outline" className="theme-badge-secondary capitalize">
                          {subscription.billingCycle}
                        </Badge>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium theme-text-primary">Usage Data</span>
                    <Badge variant="outline" className="theme-badge-primary">
                      Unable to load
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="md:col-span-2 theme-surface-elevated hover-reveal theme-transition">
              <CardHeader>
                <CardTitle className="theme-text-primary">Recent Activity</CardTitle>
                <CardDescription className="theme-text-secondary">
                  Your recent account and system activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    // Loading skeleton
                    Array.from({ length: 3 }).map((_, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center py-3 theme-border border-b last:border-0"
                      >
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                          <Skeleton className="h-3 w-1/4" />
                        </div>
                        <Skeleton className="h-6 w-16" />
                      </div>
                    ))
                  ) : (
                    // Real activity data based on actual user data
                    <>
                      {projects?.slice(0, 2).map((project: any, index: number) => (
                        <div
                          key={`project-${project._id || index}`}
                          className="flex justify-between items-center py-3 theme-border border-b last:border-0 interactive-hover rounded-md px-3 theme-transition"
                        >
                          <div className="flex-1">
                            <p className="text-sm font-medium theme-text-primary">
                              Project Created
                            </p>
                            <p className="text-xs theme-text-secondary">
                              Created project "{project.name}"
                            </p>
                            <p className="text-xs theme-text-secondary mt-1">
                              {formatDistanceToNow(new Date(project.createdAt || new Date()), {
                                addSuffix: true,
                              })}
                            </p>
                          </div>
                          <Badge
                            variant="outline"
                            className="theme-badge-secondary bg-orange-50 dark:bg-orange-950 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800"
                          >
                            project
                          </Badge>
                        </div>
                      ))}

                      {tasks.slice(0, 2).map((task: any, index: number) => (
                        <div
                          key={`task-${task._id || index}`}
                          className="flex justify-between items-center py-3 theme-border border-b last:border-0 interactive-hover rounded-md px-3 theme-transition"
                        >
                          <div className="flex-1">
                            <p className="text-sm font-medium theme-text-primary">
                              {task.status === 'Completed' ? 'Task Completed' : 'Task Created'}
                            </p>
                            <p className="text-xs theme-text-secondary">
                              {task.status === 'Completed' ? 'Completed' : 'Created'} task "
                              {task.name}"
                            </p>
                            <p className="text-xs theme-text-secondary mt-1">
                              {formatDistanceToNow(
                                new Date(task.updatedAt || task.createdAt || new Date()),
                                { addSuffix: true }
                              )}
                            </p>
                          </div>
                          <Badge
                            variant="outline"
                            className="theme-badge-secondary bg-indigo-50 dark:bg-indigo-950 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800"
                          >
                            task
                          </Badge>
                        </div>
                      ))}

                      <div className="flex justify-between items-center py-3 theme-border border-b last:border-0 interactive-hover rounded-md px-3 theme-transition">
                        <div className="flex-1">
                          <p className="text-sm font-medium theme-text-primary">
                            Settings Accessed
                          </p>
                          <p className="text-xs theme-text-secondary">Accessed settings overview</p>
                          <p className="text-xs theme-text-secondary mt-1">
                            {formatDistanceToNow(new Date(), { addSuffix: true })}
                          </p>
                        </div>
                        <Badge
                          variant="outline"
                          className="theme-badge-secondary bg-purple-50 dark:bg-purple-950 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800"
                        >
                          settings
                        </Badge>
                      </div>

                      {subscription && (
                        <div className="flex justify-between items-center py-3 theme-border border-b last:border-0 interactive-hover rounded-md px-3 theme-transition">
                          <div className="flex-1">
                            <p className="text-sm font-medium theme-text-primary">
                              Subscription Updated
                            </p>
                            <p className="text-xs theme-text-secondary">
                              Plan: {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)} -{' '}
                              {subscription.billingCycle}
                            </p>
                            <p className="text-xs theme-text-secondary mt-1">
                              {formatDistanceToNow(new Date(), { addSuffix: true })}
                            </p>
                          </div>
                          <Badge
                            variant="outline"
                            className="theme-badge-secondary bg-purple-50 dark:bg-purple-950 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800"
                          >
                            subscription
                          </Badge>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      <UpgradePrompt
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={handleUpgrade}
        currentPlan={currentPlan}
        usageSummary={usage as any}
        triggerReason="recommendation"
      />
    </div>
  );
}
