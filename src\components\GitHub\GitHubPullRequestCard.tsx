'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  GitPullRequest,
  GitMerge,
  GitBranch,
  ExternalLink,
  User,
  Calendar,
  MessageSquare,
  Tag,
  XCircle,
  Github,
  Lock,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface GitHubPullRequest {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed' | 'merged';
  html_url: string;
  user: {
    login: string;
    avatar_url: string;
  };
  assignees: Array<{
    login: string;
    avatar_url: string;
  }>;
  labels: Array<{
    name: string;
    color: string;
    description?: string;
  }>;
  head: {
    ref: string;
    sha: string;
  };
  base: {
    ref: string;
    sha: string;
  };
  created_at: string;
  updated_at: string;
  closed_at?: string;
  merged_at?: string;
  comments?: number;
  repository?: {
    id: string;
    name: string;
    fullName: string;
    htmlUrl: string;
    private: boolean;
    language?: string;
  };
}

interface GitHubPullRequestCardProps {
  pullRequest: GitHubPullRequest;
  index: number;
  className?: string;
}

export const GitHubPullRequestCard: React.FC<GitHubPullRequestCardProps> = ({
  pullRequest,
  index,
  className,
}) => {
  const isMerged = pullRequest.state === 'merged';
  const isClosed = pullRequest.state === 'closed' && !isMerged;
  const hasAssignees = pullRequest.assignees && pullRequest.assignees.length > 0;
  const hasLabels = pullRequest.labels && pullRequest.labels.length > 0;

  const getStatusIcon = () => {
    if (isMerged) {
      return <GitMerge className="h-4 w-4 text-purple-600 dark:text-purple-400 theme-transition" />;
    }
    if (isClosed) {
      return <XCircle className="h-4 w-4 text-destructive theme-transition" />;
    }
    return <GitPullRequest className="h-4 w-4 text-success theme-transition" />;
  };

  const getStatusBadge = () => {
    if (isMerged) {
      return (
        <Badge className="text-xs bg-purple-500/10 text-purple-600 dark:text-purple-400 border-purple-500/20 theme-transition">
          <GitMerge className="mr-1 h-3 w-3" />
          Merged
        </Badge>
      );
    }
    if (isClosed) {
      return (
        <Badge variant="destructive" className="text-xs theme-transition">
          <XCircle className="mr-1 h-3 w-3" />
          Closed
        </Badge>
      );
    }
    return (
      <Badge className="text-xs bg-success/10 text-success border-success/20 theme-transition">
        <GitPullRequest className="mr-1 h-3 w-3" />
        Open
      </Badge>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.03, duration: 0.3 }}
      className={className}
    >
      <Card className="theme-surface-elevated hover-reveal glow-on-hover theme-transition">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            {/* PR Status Icon */}
            <div className="mt-1">
              <div
                className={cn(
                  'p-1 rounded-full',
                  isMerged ? 'bg-purple-500/10' : isClosed ? 'bg-destructive/10' : 'bg-success/10'
                )}
              >
                {getStatusIcon()}
              </div>
            </div>

            {/* PR Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  {/* Repository Badge (if available) */}
                  {pullRequest.repository && (
                    <div className="flex items-center gap-2 mb-2">
                      <Badge
                        variant="secondary"
                        className="text-xs shrink-0 theme-transition bg-violet-500/10 text-violet-600 dark:text-violet-400 border-violet-500/20"
                      >
                        <Github className="h-3 w-3 mr-1" />
                        {pullRequest.repository.name}
                      </Badge>
                      {pullRequest.repository.private && (
                        <Badge variant="outline" className="text-xs shrink-0 theme-transition">
                          <Lock className="h-2 w-2 mr-1" />
                          Private
                        </Badge>
                      )}
                      {pullRequest.repository.language && (
                        <Badge variant="outline" className="text-xs shrink-0 theme-transition">
                          {pullRequest.repository.language}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Title and Number */}
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold theme-text-primary line-clamp-2 text-sm">
                      {pullRequest.title}
                    </h3>
                    <Badge variant="outline" className="text-xs shrink-0 theme-transition">
                      #{pullRequest.number}
                    </Badge>
                  </div>

                  {/* Branch Information */}
                  <div className="flex items-center gap-2 mb-2 text-xs theme-text-secondary">
                    <div className="flex items-center gap-1">
                      <GitBranch className="h-3 w-3" />
                      <code className="bg-muted px-1 py-0.5 rounded text-xs">
                        {pullRequest.head.ref}
                      </code>
                    </div>
                    <span>→</span>
                    <code className="bg-muted px-1 py-0.5 rounded text-xs">
                      {pullRequest.base.ref}
                    </code>
                  </div>

                  {/* Description */}
                  {pullRequest.body && (
                    <p className="text-xs theme-text-secondary line-clamp-2 mb-3">
                      {pullRequest.body.replace(/[#*`]/g, '').substring(0, 120)}
                      {pullRequest.body.length > 120 && '...'}
                    </p>
                  )}

                  {/* Labels */}
                  {hasLabels && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {pullRequest.labels.slice(0, 3).map(label => (
                        <TooltipProvider key={label.name}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Badge
                                variant="secondary"
                                className="text-xs px-2 py-0.5 theme-transition"
                                style={{
                                  backgroundColor: `#${label.color}20`,
                                  color: `#${label.color}`,
                                  borderColor: `#${label.color}40`,
                                }}
                              >
                                <Tag className="h-2 w-2 mr-1" />
                                {label.name}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{label.description || label.name}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ))}
                      {pullRequest.labels.length > 3 && (
                        <Badge variant="outline" className="text-xs theme-transition">
                          +{pullRequest.labels.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Meta Information */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 text-xs theme-text-secondary">
                      {/* Author */}
                      <div className="flex items-center space-x-1">
                        <Avatar className="h-4 w-4">
                          <AvatarImage
                            src={pullRequest.user.avatar_url}
                            alt={pullRequest.user.login}
                          />
                          <AvatarFallback className="text-xs">
                            {pullRequest.user.login.slice(0, 1).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{pullRequest.user.login}</span>
                      </div>

                      {/* Created/Merged Date */}
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {isMerged && pullRequest.merged_at
                            ? `Merged ${formatDistanceToNow(new Date(pullRequest.merged_at), { addSuffix: true })}`
                            : isClosed && pullRequest.closed_at
                              ? `Closed ${formatDistanceToNow(new Date(pullRequest.closed_at), { addSuffix: true })}`
                              : formatDistanceToNow(new Date(pullRequest.created_at), {
                                  addSuffix: true,
                                })}
                        </span>
                      </div>

                      {/* Comments */}
                      {pullRequest.comments !== undefined && pullRequest.comments > 0 && (
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3 w-3" />
                          <span>{pullRequest.comments}</span>
                        </div>
                      )}
                    </div>

                    {/* Assignees */}
                    {hasAssignees && (
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3 theme-text-secondary" />
                        <div className="flex -space-x-1">
                          {pullRequest.assignees.slice(0, 3).map(assignee => (
                            <TooltipProvider key={assignee.login}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Avatar className="h-5 w-5 ring-2 ring-background">
                                    <AvatarImage src={assignee.avatar_url} alt={assignee.login} />
                                    <AvatarFallback className="text-xs">
                                      {assignee.login.slice(0, 1).toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>@{assignee.login}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ))}
                          {pullRequest.assignees.length > 3 && (
                            <div className="h-5 w-5 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                              <span className="text-xs theme-text-secondary">
                                +{pullRequest.assignees.length - 3}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status Badge */}
                {getStatusBadge()}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between mt-4 pt-3 border-t border-border/20">
                <a
                  href={pullRequest.html_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-primary hover:text-primary/80 flex items-center theme-transition"
                >
                  View on GitHub
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>

                <div className="flex items-center gap-2 text-xs theme-text-secondary">
                  <span>{pullRequest.head.sha.substring(0, 7)}</span>
                  <span>→</span>
                  <span>{pullRequest.base.sha.substring(0, 7)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
