'use client';

import * as React from 'react';

import { useLinkToolbarButton, useLinkToolbarButtonState } from '@udecode/plate-link/react';
import { Link } from 'lucide-react';

import { ToolbarButton } from './toolbar';
import { cn } from '@/lib/utils';

export function LinkToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const state = useLinkToolbarButtonState();
  const { props: buttonProps } = useLinkToolbarButton(state);

  return (
    <ToolbarButton {...props} {...buttonProps} data-plate-focus tooltip="Link">
      <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
        <Link className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
