import mongoose, { Schema, Document } from 'mongoose';

interface IBudgetCategory {
  name: string;
  allocatedAmount: number;
  spentAmount: number;
  description?: string;
}

interface IExpense {
  _id: mongoose.Schema.Types.ObjectId;
  date: Date;
  amount: number;
  category: string;
  description?: string;
  attachments?: Array<{
    name: string;
    url: string;
    public_id: string;
  }>;
  createdBy: mongoose.Schema.Types.ObjectId;
  receiptNumber?: string;
  vendor?: string;
}

interface IAlertConfig {
  thresholdPercentage: number;
  notifyUsers: mongoose.Schema.Types.ObjectId[];
  enabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

interface IBudget extends Document {
  budgetId: string;
  projectId: mongoose.Schema.Types.ObjectId;
  totalBudget: number;
  spentAmount: number;
  categories: IBudgetCategory[];
  expenses: IExpense[];
  currency: string;
  alerts: IAlertConfig;
  createdBy: mongoose.Schema.Types.ObjectId;
  organizationId: mongoose.Schema.Types.ObjectId;
  status: 'Active' | 'Completed' | 'Exceeded' | 'Frozen';
  fiscalYear?: number;
  createdAt: Date;
  updatedAt: Date;
}

const budgetCategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  allocatedAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  spentAmount: {
    type: Number,
    default: 0,
    min: 0,
  },
  description: {
    type: String,
    trim: true,
  },
});

const expenseSchema = new Schema({
  date: {
    type: Date,
    required: true,
    default: Date.now,
  },
  amount: {
    type: Number,
    required: true,
    min: 0.01,
  },
  category: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  attachments: [
    {
      name: { type: String, required: true },
      url: { type: String, required: true },
      public_id: { type: String, required: true },
    },
  ],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  receiptNumber: {
    type: String,
    trim: true,
  },
  vendor: {
    type: String,
    trim: true,
  },
});

const alertConfigSchema = new Schema({
  thresholdPercentage: {
    type: Number,
    required: true,
    min: 1,
    max: 100,
    default: 80,
  },
  notifyUsers: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  enabled: {
    type: Boolean,
    default: true,
  },
  emailNotifications: {
    type: Boolean,
    default: true,
  },
  pushNotifications: {
    type: Boolean,
    default: true,
  },
});

const budgetSchema = new Schema<IBudget>(
  {
    budgetId: {
      type: String,
      unique: true,
      required: true,
      default: () => new mongoose.Types.ObjectId().toString(),
    },
    projectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      required: true,
    },
    totalBudget: {
      type: Number,
      required: true,
      min: 0,
    },
    spentAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    categories: {
      type: [budgetCategorySchema],
      validate: {
        validator: function (categories: IBudgetCategory[]) {
          const totalAllocated = categories.reduce((sum, cat) => sum + cat.allocatedAmount, 0);
          return totalAllocated <= this.totalBudget;
        },
        message: 'Total allocated amount cannot exceed total budget',
      },
    },
    expenses: {
      type: [expenseSchema],
      validate: {
        validator: function (expenses: IExpense[]) {
          return expenses.length <= 1000;
        },
        message: 'Budget cannot have more than 1000 expenses',
      },
    },
    currency: {
      type: String,
      required: true,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR'],
    },
    alerts: {
      type: alertConfigSchema,
      default: {},
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
    },
    status: {
      type: String,
      enum: ['Active', 'Completed', 'Exceeded', 'Frozen'],
      default: 'Active',
    },
    fiscalYear: {
      type: Number,
      min: 2000,
      max: 3000,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
budgetSchema.index({ projectId: 1 });
budgetSchema.index({ organizationId: 1, createdBy: 1 });
budgetSchema.index({ status: 1 });
budgetSchema.index({ fiscalYear: 1 });

// Virtual for remaining budget
budgetSchema.virtual('remainingBudget').get(function () {
  return this.totalBudget - this.spentAmount;
});

// Virtual for budget utilization percentage
budgetSchema.virtual('utilizationPercentage').get(function () {
  return this.totalBudget > 0 ? (this.spentAmount / this.totalBudget) * 100 : 0;
});

// Virtual for budget status based on utilization
budgetSchema.virtual('budgetStatus').get(function () {
  const utilization = this.totalBudget > 0 ? (this.spentAmount / this.totalBudget) * 100 : 0;
  if (utilization >= 100) return 'Exceeded';
  if (utilization >= this.alerts.thresholdPercentage) return 'Warning';
  return 'On Track';
});

// Pre-save middleware to update spentAmount based on expenses
budgetSchema.pre('save', function (next) {
  if (!this.budgetId) {
    this.budgetId = new mongoose.Types.ObjectId().toString();
  }

  // Calculate total spent amount from expenses
  this.spentAmount = this.expenses.reduce((total, expense) => total + expense.amount, 0);

  // Update category spent amounts
  this.categories.forEach(category => {
    const categoryExpenses = this.expenses.filter(expense => expense.category === category.name);
    category.spentAmount = categoryExpenses.reduce((total, expense) => total + expense.amount, 0);
  });

  next();
});

export const Budget = mongoose.models.Budget || mongoose.model<IBudget>('Budget', budgetSchema);
export type { IBudget, IBudgetCategory, IExpense, IAlertConfig };
