import mongoose, { Document, Schema } from 'mongoose';

export interface IEventAttendee {
  email: string;
  name?: string;
  userId?: mongoose.Types.ObjectId;
  responseStatus: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  isOptional?: boolean;
  comment?: string;
  addedAt: Date;
}

export interface IEventReminder {
  method: 'email' | 'popup' | 'sms';
  minutes: number;
  isActive: boolean;
}

export interface IRecurrenceRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number;
  count?: number;
  until?: Date;
  byWeekDay?: number[];
  byMonthDay?: number[];
  byMonth?: number[];
  exceptions?: Date[];
}

export interface IDragDropPosition {
  x: number;
  y: number;
  width?: number;
  height?: number;
  viewType: 'month' | 'week' | 'day' | 'agenda';
  columnId?: string;
  rowId?: string;
  lastUpdated: Date;
  updatedBy: mongoose.Types.ObjectId;
}

export interface IEvent extends Document {
  title: string;
  description?: string;
  location?: string;
  startTime: Date;
  endTime: Date;
  isAllDay: boolean;
  timezone: string;
  userId: mongoose.Types.ObjectId;
  organizationId?: mongoose.Types.ObjectId;
  projectId?: mongoose.Types.ObjectId;
  taskIds: mongoose.Types.ObjectId[];
  attendees: IEventAttendee[];
  reminders: IEventReminder[];
  recurrence?: IRecurrenceRule;
  parentEventId?: mongoose.Types.ObjectId;
  isRecurring: boolean;
  status: 'confirmed' | 'tentative' | 'cancelled';
  visibility: 'default' | 'public' | 'private' | 'confidential';
  colorId?: string;
  category?: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  dragDropPosition?: IDragDropPosition;
  attachments: Array<{
    filename: string;
    url: string;
    size?: number;
    mimeType?: string;
    uploadedBy: mongoose.Types.ObjectId;
    uploadedAt: Date;
  }>;
  notifications: Array<{
    type: 'created' | 'updated' | 'cancelled' | 'reminder';
    sentAt: Date;
    sentTo: mongoose.Types.ObjectId[];
    method: 'email' | 'push' | 'sms';
    status: 'sent' | 'failed' | 'pending';
  }>;
  metadata?: {
    source?: 'manual' | 'outlook' | 'import' | 'task_conversion';
    importId?: string;
    externalIds?: Record<string, string>;
    customFields?: Record<string, any>;
    conflictsWith?: mongoose.Types.ObjectId[];
    isTemplate?: boolean;
    templateId?: mongoose.Types.ObjectId;
  };
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const EventAttendeeSchema = new Schema<IEventAttendee>(
  {
    email: {
      type: String,
      required: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    name: {
      type: String,
      trim: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    responseStatus: {
      type: String,
      enum: ['needsAction', 'declined', 'tentative', 'accepted'],
      default: 'needsAction',
    },
    isOptional: {
      type: Boolean,
      default: false,
    },
    comment: {
      type: String,
      maxlength: 500,
    },
    addedAt: {
      type: Date,
      default: Date.now,
    },
  },
  { _id: false }
);

const EventReminderSchema = new Schema<IEventReminder>(
  {
    method: {
      type: String,
      enum: ['email', 'popup', 'sms'],
      required: true,
    },
    minutes: {
      type: Number,
      required: true,
      min: 0,
      max: 40320, // 4 weeks in minutes
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { _id: false }
);

const RecurrenceRuleSchema = new Schema<IRecurrenceRule>(
  {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly'],
      required: true,
    },
    interval: {
      type: Number,
      required: true,
      min: 1,
      max: 999,
      default: 1,
    },
    count: {
      type: Number,
      min: 1,
      max: 999,
    },
    until: {
      type: Date,
    },
    byWeekDay: {
      type: [Number],
      validate: {
        validator: function (days: number[]) {
          return days.every(day => day >= 0 && day <= 6);
        },
        message: 'Week days must be between 0 (Sunday) and 6 (Saturday)',
      },
    },
    byMonthDay: {
      type: [Number],
      validate: {
        validator: function (days: number[]) {
          return days.every(day => day >= 1 && day <= 31);
        },
        message: 'Month days must be between 1 and 31',
      },
    },
    byMonth: {
      type: [Number],
      validate: {
        validator: function (months: number[]) {
          return months.every(month => month >= 1 && month <= 12);
        },
        message: 'Months must be between 1 and 12',
      },
    },
    exceptions: {
      type: [Date],
      default: [],
    },
  },
  { _id: false }
);

const DragDropPositionSchema = new Schema<IDragDropPosition>(
  {
    x: {
      type: Number,
      required: true,
    },
    y: {
      type: Number,
      required: true,
    },
    width: {
      type: Number,
      min: 1,
    },
    height: {
      type: Number,
      min: 1,
    },
    viewType: {
      type: String,
      enum: ['month', 'week', 'day', 'agenda'],
      required: true,
    },
    columnId: {
      type: String,
    },
    rowId: {
      type: String,
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  { _id: false }
);

const EventSchema = new Schema<IEvent>(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
      index: true,
    },
    description: {
      type: String,
      maxlength: 2000,
    },
    location: {
      type: String,
      maxlength: 500,
    },
    startTime: {
      type: Date,
      required: true,
      index: true,
    },
    endTime: {
      type: Date,
      required: true,
      index: true,
      validate: {
        validator: function (endTime: Date) {
          return !this.startTime || endTime >= this.startTime;
        },
        message: 'End time must be after start time',
      },
    },
    isAllDay: {
      type: Boolean,
      default: false,
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      index: true,
    },
    projectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      index: true,
    },
    taskIds: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Task',
      },
    ],
    attendees: [EventAttendeeSchema],
    reminders: [EventReminderSchema],
    recurrence: RecurrenceRuleSchema,
    parentEventId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Event',
      index: true,
    },
    isRecurring: {
      type: Boolean,
      default: false,
      index: true,
    },
    status: {
      type: String,
      enum: ['confirmed', 'tentative', 'cancelled'],
      default: 'confirmed',
      index: true,
    },
    visibility: {
      type: String,
      enum: ['default', 'public', 'private', 'confidential'],
      default: 'default',
    },
    colorId: {
      type: String,
      match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
    },
    category: {
      type: String,
      maxlength: 50,
    },
    tags: [
      {
        type: String,
        trim: true,
        maxlength: 30,
      },
    ],
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
    dragDropPosition: DragDropPositionSchema,
    attachments: [
      {
        filename: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
        size: {
          type: Number,
        },
        mimeType: {
          type: String,
        },
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    notifications: [
      {
        type: {
          type: String,
          enum: ['created', 'updated', 'cancelled', 'reminder'],
          required: true,
        },
        sentAt: {
          type: Date,
          default: Date.now,
        },
        sentTo: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
          },
        ],
        method: {
          type: String,
          enum: ['email', 'push', 'sms'],
          required: true,
        },
        status: {
          type: String,
          enum: ['sent', 'failed', 'pending'],
          default: 'pending',
        },
      },
    ],
    metadata: {
      source: {
        type: String,
        enum: ['manual', 'outlook', 'import', 'task_conversion'],
        default: 'manual',
      },
      importId: {
        type: String,
      },
      externalIds: {
        type: Schema.Types.Mixed,
      },
      customFields: {
        type: Schema.Types.Mixed,
      },
      conflictsWith: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Event',
        },
      ],
      isTemplate: {
        type: Boolean,
        default: false,
      },
      templateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Event',
      },
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
    deletedAt: {
      type: Date,
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    collection: 'events',
  }
);

// Compound indexes for efficient querying
EventSchema.index({ userId: 1, startTime: 1, endTime: 1 });
EventSchema.index({ organizationId: 1, startTime: 1, endTime: 1 });
EventSchema.index({ projectId: 1, startTime: 1, endTime: 1 });
EventSchema.index({ taskIds: 1, startTime: 1 });
EventSchema.index({ isRecurring: 1, parentEventId: 1 });
EventSchema.index({ status: 1, startTime: 1, isDeleted: 1 });
EventSchema.index({ startTime: 1, endTime: 1, isDeleted: 1 });
EventSchema.index({ createdAt: -1, userId: 1 });
EventSchema.index({ tags: 1, startTime: 1 });
EventSchema.index({ category: 1, startTime: 1 });

// Text search index
EventSchema.index({
  title: 'text',
  description: 'text',
  location: 'text',
  category: 'text',
  tags: 'text',
});

// Static methods
EventSchema.statics.findByUser = async function (
  userId: string,
  options: {
    startDate?: Date;
    endDate?: Date;
    status?: string;
    includeDeleted?: boolean;
    limit?: number;
    offset?: number;
  } = {}
): Promise<IEvent[]> {
  const query: any = { userId: new mongoose.Types.ObjectId(userId) };

  if (!options.includeDeleted) {
    query.isDeleted = false;
  }

  if (options.status) {
    query.status = options.status;
  }

  if (options.startDate || options.endDate) {
    query.$and = [];
    if (options.startDate) {
      query.$and.push({ endTime: { $gte: options.startDate } });
    }
    if (options.endDate) {
      query.$and.push({ startTime: { $lte: options.endDate } });
    }
  }

  return await this.find(query)
    .sort({ startTime: 1 })
    .limit(options.limit || 100)
    .skip(options.offset || 0)
    .populate('userId', 'name email')
    .populate('projectId', 'name')
    .populate('taskIds', 'name status')
    .populate('createdBy', 'name email')
    .lean();
};

EventSchema.statics.findByProject = async function (
  projectId: string,
  options: {
    startDate?: Date;
    endDate?: Date;
    includeDeleted?: boolean;
  } = {}
): Promise<IEvent[]> {
  const query: any = { projectId: new mongoose.Types.ObjectId(projectId) };

  if (!options.includeDeleted) {
    query.isDeleted = false;
  }

  if (options.startDate || options.endDate) {
    query.$and = [];
    if (options.startDate) {
      query.$and.push({ endTime: { $gte: options.startDate } });
    }
    if (options.endDate) {
      query.$and.push({ startTime: { $lte: options.endDate } });
    }
  }

  return await this.find(query)
    .sort({ startTime: 1 })
    .populate('userId', 'name email')
    .populate('taskIds', 'name status')
    .lean();
};

EventSchema.statics.findConflicts = async function (
  userId: string,
  startTime: Date,
  endTime: Date,
  excludeEventId?: string
): Promise<IEvent[]> {
  const query: any = {
    userId: new mongoose.Types.ObjectId(userId),
    isDeleted: false,
    status: { $ne: 'cancelled' },
    $and: [{ startTime: { $lt: endTime } }, { endTime: { $gt: startTime } }],
  };

  if (excludeEventId) {
    query._id = { $ne: new mongoose.Types.ObjectId(excludeEventId) };
  }

  return await this.find(query).sort({ startTime: 1 });
};

EventSchema.statics.findRecurringInstances = async function (
  parentEventId: string,
  startDate?: Date,
  endDate?: Date
): Promise<IEvent[]> {
  const query: any = {
    parentEventId: new mongoose.Types.ObjectId(parentEventId),
    isDeleted: false,
  };

  if (startDate || endDate) {
    query.$and = [];
    if (startDate) {
      query.$and.push({ startTime: { $gte: startDate } });
    }
    if (endDate) {
      query.$and.push({ startTime: { $lte: endDate } });
    }
  }

  return await this.find(query).sort({ startTime: 1 });
};

// Instance methods
EventSchema.methods.addAttendee = function (attendeeData: Partial<IEventAttendee>): void {
  const existingAttendee = this.attendees.find(attendee => attendee.email === attendeeData.email);

  if (!existingAttendee) {
    this.attendees.push({
      email: attendeeData.email!,
      name: attendeeData.name,
      userId: attendeeData.userId,
      responseStatus: attendeeData.responseStatus || 'needsAction',
      isOptional: attendeeData.isOptional || false,
      comment: attendeeData.comment,
      addedAt: new Date(),
    });
  }
};

EventSchema.methods.updateAttendeeResponse = function (
  email: string,
  responseStatus: IEventAttendee['responseStatus'],
  comment?: string
): boolean {
  const attendee = this.attendees.find(a => a.email === email);
  if (attendee) {
    attendee.responseStatus = responseStatus;
    if (comment) attendee.comment = comment;
    return true;
  }
  return false;
};

EventSchema.methods.removeAttendee = function (email: string): boolean {
  const attendeeIndex = this.attendees.findIndex(a => a.email === email);
  if (attendeeIndex > -1) {
    this.attendees.splice(attendeeIndex, 1);
    return true;
  }
  return false;
};

EventSchema.methods.addReminder = function (reminderData: IEventReminder): void {
  this.reminders.push(reminderData);
};

EventSchema.methods.removeReminder = function (method: string, minutes: number): boolean {
  const reminderIndex = this.reminders.findIndex(r => r.method === method && r.minutes === minutes);
  if (reminderIndex > -1) {
    this.reminders.splice(reminderIndex, 1);
    return true;
  }
  return false;
};

EventSchema.methods.linkTask = function (taskId: string): void {
  const taskObjectId = new mongoose.Types.ObjectId(taskId);
  if (!this.taskIds.some(id => id.equals(taskObjectId))) {
    this.taskIds.push(taskObjectId);
  }
};

EventSchema.methods.unlinkTask = function (taskId: string): boolean {
  const taskObjectId = new mongoose.Types.ObjectId(taskId);
  const taskIndex = this.taskIds.findIndex(id => id.equals(taskObjectId));
  if (taskIndex > -1) {
    this.taskIds.splice(taskIndex, 1);
    return true;
  }
  return false;
};

EventSchema.methods.updateDragDropPosition = function (
  position: Partial<IDragDropPosition>,
  updatedBy: string
): void {
  this.dragDropPosition = {
    x: position.x!,
    y: position.y!,
    width: position.width,
    height: position.height,
    viewType: position.viewType!,
    columnId: position.columnId,
    rowId: position.rowId,
    lastUpdated: new Date(),
    updatedBy: new mongoose.Types.ObjectId(updatedBy),
  };
};

EventSchema.methods.isRecurringEvent = function (): boolean {
  return this.isRecurring && !!this.recurrence;
};

EventSchema.methods.isRecurringInstance = function (): boolean {
  return !!this.parentEventId;
};

EventSchema.methods.hasConflicts = async function (): Promise<IEvent[]> {
  return await (this.constructor as any).findConflicts(
    this.userId.toString(),
    this.startTime,
    this.endTime,
    this._id.toString()
  );
};

EventSchema.methods.addNotification = function (notificationData: {
  type: 'created' | 'updated' | 'cancelled' | 'reminder';
  sentTo: string[];
  method: 'email' | 'push' | 'sms';
}): void {
  this.notifications.push({
    type: notificationData.type,
    sentAt: new Date(),
    sentTo: notificationData.sentTo.map(id => new mongoose.Types.ObjectId(id)),
    method: notificationData.method,
    status: 'pending',
  });
};

EventSchema.methods.canUserEdit = function (userId: string): boolean {
  if (this.userId.equals(new mongoose.Types.ObjectId(userId))) {
    return true;
  }

  if (this.createdBy.equals(new mongoose.Types.ObjectId(userId))) {
    return true;
  }

  const attendee = this.attendees.find(
    a => a.userId && a.userId.equals(new mongoose.Types.ObjectId(userId))
  );

  return attendee ? attendee.responseStatus !== 'declined' : false;
};

EventSchema.methods.getDuration = function (): number {
  return this.endTime.getTime() - this.startTime.getTime();
};

EventSchema.methods.getDurationInMinutes = function (): number {
  return Math.round(this.getDuration() / (1000 * 60));
};

EventSchema.methods.getDurationInHours = function (): number {
  return Math.round((this.getDuration() / (1000 * 60 * 60)) * 100) / 100;
};

EventSchema.methods.toICalString = function (): string {
  const formatDate = (date: Date) => {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  };

  let ical = 'BEGIN:VEVENT\n';
  ical += `UID:${this._id}@TaskFluxio.app\n`;
  ical += `DTSTART:${formatDate(this.startTime)}\n`;
  ical += `DTEND:${formatDate(this.endTime)}\n`;
  ical += `SUMMARY:${this.title}\n`;

  if (this.description) {
    ical += `DESCRIPTION:${this.description.replace(/\n/g, '\\n')}\n`;
  }

  if (this.location) {
    ical += `LOCATION:${this.location}\n`;
  }

  ical += `STATUS:${this.status.toUpperCase()}\n`;
  ical += `CREATED:${formatDate(this.createdAt)}\n`;
  ical += `LAST-MODIFIED:${formatDate(this.updatedAt)}\n`;
  ical += 'END:VEVENT\n';

  return ical;
};

export const Event = mongoose.models.Event || mongoose.model<IEvent>('Event', EventSchema);
