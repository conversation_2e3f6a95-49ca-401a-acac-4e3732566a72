import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface Permission {
  id: string;
  userId: string;
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'report'
    | 'timeline'
    | 'notification'
    | 'file'
    | 'comment'
    | 'budget'
    | 'global';
  resourceId?: string;
  actions: {
    read: boolean;
    write: boolean;
    delete: boolean;
    admin: boolean;
    share?: boolean;
    export?: boolean;
    import?: boolean;
    manage?: boolean;
  };
  scope: 'global' | 'organization' | 'project' | 'resource';
  isActive: boolean;
  expiresAt?: Date;
  grantedAt: Date;
  reason?: string;
}

export interface OrganizationMember {
  userId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  role: 'Owner' | 'Member' | 'Guest';
  joinedAt: Date;
}

export interface UserPermissions {
  permissions: Permission[];
  organizationRole: string;
  userId: string;
}

export interface PermissionCheckResult {
  success: boolean;
  hasPermission: boolean;
  userId: string;
  resourceType: string;
  action: string;
  resourceId?: string;
  organizationId?: string;
}

export const usePermissions = () => {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkPermission = useCallback(
    async (
      resourceType: string,
      action: string,
      resourceId?: string,
      organizationId?: string,
      userId?: string
    ): Promise<boolean> => {
      if (!session?.user?.id) return false;

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          userId: userId || session.user.id,
          resourceType,
          action,
        });

        if (resourceId) params.append('resourceId', resourceId);
        if (organizationId) params.append('organizationId', organizationId);

        const response = await fetch(`/api/permissions/check?${params}`);
        const result: PermissionCheckResult = await response.json();

        if (!response.ok) {
          // result may not have error property, so fallback to generic message
          throw new Error((result as any).error || 'Failed to check permission');
        }

        return result.hasPermission;
      } catch (err: any) {
        setError(err.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const getUserPermissions = useCallback(
    async (
      targetUserId?: string,
      resourceType?: string,
      resourceId?: string,
      organizationId?: string
    ): Promise<UserPermissions | null> => {
      if (!session?.user?.id) return null;

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (targetUserId) params.append('userId', targetUserId);
        if (resourceType) params.append('resourceType', resourceType);
        if (resourceId) params.append('resourceId', resourceId);
        if (organizationId) params.append('organizationId', organizationId);

        const response = await fetch(`/api/permissions/user-permissions?${params}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch user permissions');
        }

        return result;
      } catch (err: any) {
        setError(err.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const getOrganizationMembers = useCallback(
    async (
      organizationId: string,
      search?: string,
      page: number = 1,
      limit: number = 50
    ): Promise<{ members: OrganizationMember[]; pagination: any } | null> => {
      if (!session?.user?.id) return null;

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          organizationId,
          page: page.toString(),
          limit: limit.toString(),
        });

        if (search) params.append('search', search);

        const response = await fetch(`/api/permissions/organization-members?${params}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch organization members');
        }

        return result;
      } catch (err: any) {
        setError(err.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const updateUserRole = useCallback(
    async (
      userId: string,
      organizationId: string,
      role: 'Owner' | 'Member' | 'Guest'
    ): Promise<boolean> => {
      if (!session?.user?.id) return false;

      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/permissions/update-user-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            organizationId,
            role,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to update user role');
        }

        return true;
      } catch (err: any) {
        setError(err.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const removeMember = useCallback(
    async (userId: string, organizationId: string): Promise<boolean> => {
      if (!session?.user?.id) return false;

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          userId,
          organizationId,
        });

        const response = await fetch(`/api/permissions/remove-member?${params}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to remove member');
        }

        return true;
      } catch (err: any) {
        setError(err.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const grantPermission = useCallback(
    async (permissionData: {
      userId?: string;
      resourceType: string;
      resourceId?: string;
      actions: {
        read?: boolean;
        write?: boolean;
        delete?: boolean;
        admin?: boolean;
        share?: boolean;
        export?: boolean;
        import?: boolean;
        manage?: boolean;
      };
      scope?: string;
      reason?: string;
    }): Promise<Permission | null> => {
      if (!session?.user?.id) return null;

      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/permissions/grant', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(permissionData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to grant permission');
        }

        return result.permission;
      } catch (err: any) {
        setError(err.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  const revokePermission = useCallback(
    async (permissionId: string): Promise<boolean> => {
      if (!session?.user?.id) return false;

      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/permissions/permissions/${permissionId}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to revoke permission');
        }

        return true;
      } catch (err: any) {
        setError(err.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [session?.user?.id]
  );

  // Helper function to check if user has a specific action
  const hasAction = useCallback((permissions: Permission[], action: string): boolean => {
    return permissions.some(
      permission =>
        permission.isActive &&
        // Removed isExpired check as it does not exist on Permission
        permission.actions[action as keyof typeof permission.actions]
    );
  }, []);

  // Helper function to get role-based permissions
  const getRolePermissions = useCallback((role: string, resourceType: string): string[] => {
    const rolePermissions = {
      Owner: {
        task: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        project: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        note: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        user: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        organization: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        integration: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        report: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        timeline: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        notification: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        file: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        comment: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        budget: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        global: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
      },
      Member: {
        task: ['read', 'write', 'share', 'export'],
        project: ['read', 'write', 'share', 'export'],
        note: ['read', 'write', 'share', 'export'],
        user: ['read'],
        organization: ['read'],
        integration: ['read', 'write'],
        report: ['read', 'export'],
        timeline: ['read', 'write'],
        notification: ['read', 'write'],
        file: ['read', 'write', 'share'],
        comment: ['read', 'write'],
        budget: ['read'],
        global: ['read'],
      },
      Guest: {
        task: ['read'],
        project: ['read'],
        note: ['read'],
        user: ['read'],
        organization: ['read'],
        integration: ['read'],
        report: ['read'],
        timeline: ['read'],
        notification: ['read'],
        file: ['read'],
        comment: ['read'],
        budget: ['read'],
        global: ['read'],
      },
    };

    const permissions = rolePermissions[role as keyof typeof rolePermissions];
    if (!permissions) return [];

    return permissions[resourceType as keyof typeof permissions] || [];
  }, []);

  return {
    loading,
    error,
    checkPermission,
    getUserPermissions,
    getOrganizationMembers,
    updateUserRole,
    removeMember,
    grantPermission,
    revokePermission,
    hasAction,
    getRolePermissions,
    isAuthenticated: !!session?.user?.id,
  };
};
