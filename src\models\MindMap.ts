import mongoose, { Schema, Document } from 'mongoose';

export interface IMindMapNode {
  id: string;
  type:
    | 'root'
    | 'branch'
    | 'leaf'
    | 'note'
    | 'image'
    | 'link'
    | 'task'
    | 'milestone'
    | 'decision'
    | 'resource'
    | 'timeline'
    | 'team'
    | 'project'
    | 'deadline'
    | 'status'
    | 'annotation'
    | 'process'
    | 'text';
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  content: {
    text: string;
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    fontWeight?: string;
    imageUrl?: string;
    linkUrl?: string;
    notes?: string;
    // Additional properties for different node types
    title?: string;
    content?: string;
    completed?: boolean;
    achieved?: boolean;
    decided?: boolean;
    url?: string;
    date?: string;
    members?: string[];
    progress?: number;
    status?: string;
    steps?: Array<{ number: number; description: string }>;
    currentStep?: number;
    [key: string]: any; // Allow additional properties
  };
  style?: {
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    opacity?: number;
    shadow?: boolean;
  };
  parentId?: string;
  childIds: string[];
  collapsed?: boolean;
  zIndex?: number;
  selected?: boolean;
  dragging?: boolean;
}

export interface IMindMapConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  type: 'straight' | 'curved' | 'step' | 'smoothstep';
  style?: {
    stroke?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
    animated?: boolean;
  };
  label?: string;
  labelStyle?: {
    fontSize?: number;
    color?: string;
    backgroundColor?: string;
  };
}

export interface IMindMapViewport {
  x: number;
  y: number;
  zoom: number;
}

export interface IMindMapAnalytics {
  viewCount: number;
  editCount: number;
  lastViewed?: Date;
  lastEdited?: Date;
}

export interface IMindMap extends Document {
  title: string;
  description?: string;
  userId: mongoose.Types.ObjectId;
  projectId?: mongoose.Types.ObjectId;
  organizationId?: mongoose.Types.ObjectId;

  // Mind map content
  nodes: IMindMapNode[];
  connections: IMindMapConnection[];
  viewport: IMindMapViewport;

  // Metadata
  tags: string[];
  status: 'draft' | 'active' | 'archived' | 'template';

  // Analytics
  analytics: IMindMapAnalytics;

  // Soft delete
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: mongoose.Types.ObjectId;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const mindMapNodeSchema = new Schema(
  {
    id: { type: String, required: true },
    type: {
      type: String,
      enum: [
        'root',
        'branch',
        'leaf',
        'note',
        'image',
        'link',
        'task',
        'milestone',
        'decision',
        'resource',
        'timeline',
        'team',
        'project',
        'deadline',
        'status',
        'annotation',
        'process',
        'text',
      ],
      default: 'branch',
    },
    position: {
      x: { type: Number, required: true },
      y: { type: Number, required: true },
    },
    size: {
      width: { type: Number, default: 200 },
      height: { type: Number, default: 100 },
    },
    content: {
      text: { type: String, required: true },
      color: { type: String, default: '#000000' },
      backgroundColor: { type: String, default: '#ffffff' },
      fontSize: { type: Number, default: 14 },
      fontWeight: { type: String, default: 'normal' },
      imageUrl: String,
      linkUrl: String,
      notes: String,
      // Additional properties for different node types
      title: String,
      content: String,
      completed: Boolean,
      achieved: Boolean,
      decided: Boolean,
      url: String,
      date: String,
      members: [String],
      progress: Number,
      status: String,
      steps: [
        {
          number: Number,
          description: String,
        },
      ],
      currentStep: Number,
    },
    style: {
      borderColor: { type: String, default: '#cccccc' },
      borderWidth: { type: Number, default: 1 },
      borderRadius: { type: Number, default: 8 },
      opacity: { type: Number, default: 1 },
      shadow: { type: Boolean, default: false },
    },
    parentId: String,
    childIds: [String],
    collapsed: { type: Boolean, default: false },
    zIndex: { type: Number, default: 1 },
    selected: { type: Boolean, default: false },
    dragging: { type: Boolean, default: false },
  },
  { strict: false }
); // Allow additional fields

const mindMapConnectionSchema = new Schema({
  id: { type: String, required: true },
  sourceNodeId: { type: String, required: true },
  targetNodeId: { type: String, required: true },
  type: {
    type: String,
    enum: ['straight', 'curved', 'step', 'smoothstep'],
    default: 'curved',
  },
  style: {
    stroke: { type: String, default: '#999999' },
    strokeWidth: { type: Number, default: 2 },
    strokeDasharray: String,
    animated: { type: Boolean, default: false },
  },
  label: String,
  labelStyle: {
    fontSize: { type: Number, default: 12 },
    color: { type: String, default: '#666666' },
    backgroundColor: { type: String, default: '#ffffff' },
  },
});

const mindMapSchema = new Schema(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    projectId: { type: mongoose.Schema.Types.ObjectId, ref: 'Project' },
    organizationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Organization' },

    // Mind map content
    nodes: [mindMapNodeSchema],
    connections: [mindMapConnectionSchema],
    viewport: {
      x: { type: Number, default: 0 },
      y: { type: Number, default: 0 },
      zoom: { type: Number, default: 1 },
    },

    // Metadata
    tags: [{ type: String, trim: true }],
    status: {
      type: String,
      enum: ['draft', 'active', 'archived', 'template'],
      default: 'draft',
    },

    // Analytics
    analytics: {
      viewCount: { type: Number, default: 0 },
      editCount: { type: Number, default: 0 },
      lastViewed: Date,
      lastEdited: Date,
    },

    // Soft delete
    isDeleted: { type: Boolean, default: false },
    deletedAt: Date,
    deletedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
mindMapSchema.index({ userId: 1, updatedAt: -1 });
mindMapSchema.index({ projectId: 1, updatedAt: -1 });
mindMapSchema.index({ organizationId: 1, updatedAt: -1 });
mindMapSchema.index({ status: 1, updatedAt: -1 });
mindMapSchema.index({ tags: 1 });
mindMapSchema.index({ title: 'text', description: 'text' });
mindMapSchema.index({ isDeleted: 1, updatedAt: -1 });

// No additional methods needed

export default mongoose.models.MindMap || mongoose.model<IMindMap>('MindMap', mindMapSchema);
