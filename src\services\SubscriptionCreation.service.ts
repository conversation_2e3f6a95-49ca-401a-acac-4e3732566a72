import { Subscription } from '@/models/Subscription';
import { SubscriptionService } from './Subscription.service';
import mongoose from 'mongoose';

export interface CreateSubscriptionOptions {
  organizationId: string | mongoose.Types.ObjectId;
  plan?: 'free' | 'basic' | 'professional' | 'enterprise';
  billingCycle?: 'monthly' | 'yearly';
  trialDays?: number;
}

export class SubscriptionCreationService {
  /**
   * Create a default free subscription for a new organization
   */
  static async createDefaultSubscription(
    organizationId: string | mongoose.Types.ObjectId
  ): Promise<any> {
    try {
      // Check if subscription already exists
      const existingSubscription = await Subscription.findOne({ organizationId });
      if (existingSubscription) {
        console.log(`Subscription already exists for organization: ${organizationId}`);
        return existingSubscription;
      }

      // Create free plan subscription
      const subscriptionData = this.buildSubscriptionData({
        organizationId,
        plan: 'free',
        billingCycle: 'monthly',
      });

      const subscription = new Subscription(subscriptionData);
      await subscription.save();

      console.log(`Created free subscription for organization: ${organizationId}`);
      return subscription;
    } catch (error: any) {
      console.error('Error creating default subscription:', error);
      throw new Error(`Failed to create subscription: ${error.message}`);
    }
  }

  /**
   * Create a subscription with custom options
   */
  static async createSubscription(options: CreateSubscriptionOptions): Promise<any> {
    try {
      const { organizationId, plan = 'free', billingCycle = 'monthly', trialDays } = options;

      // Check if subscription already exists
      const existingSubscription = await Subscription.findOne({ organizationId });
      if (existingSubscription) {
        throw new Error('Subscription already exists for this organization');
      }

      // Build subscription data
      const subscriptionData = this.buildSubscriptionData({
        organizationId,
        plan,
        billingCycle,
        trialDays,
      });

      const subscription = new Subscription(subscriptionData);
      await subscription.save();

      console.log(`Created ${plan} subscription for organization: ${organizationId}`);
      return subscription;
    } catch (error: any) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Build subscription data object
   */
  private static buildSubscriptionData(options: CreateSubscriptionOptions): any {
    const { organizationId, plan = 'free', billingCycle = 'monthly', trialDays } = options;

    // Get plan configuration
    const features = SubscriptionService.getPlanFeatures(plan);
    const limits = SubscriptionService.getPlanLimits(plan);
    const pricing = SubscriptionService.calculatePlanPrice(plan, billingCycle);

    // Calculate trial period if specified
    let trialStart: Date | undefined;
    let trialEnd: Date | undefined;
    let status = 'active';

    if (trialDays && trialDays > 0) {
      trialStart = new Date();
      trialEnd = new Date();
      trialEnd.setDate(trialEnd.getDate() + trialDays);
      status = 'trialing';
    }

    // Calculate current period
    const currentPeriodStart = new Date();
    const currentPeriodEnd =
      plan === 'free'
        ? null
        : SubscriptionService.calculateNextBillingDate(currentPeriodStart, billingCycle);

    return {
      organizationId: new mongoose.Types.ObjectId(organizationId),
      plan,
      status,

      // Plan limits
      userLimit: limits.users || 1,
      projectLimit: limits.projects || 3,
      integrationLimit: limits.integrations || 0,
      automationLimit: limits.automations || 0,
      apiCallLimit: limits.apiCalls || 0,

      // Billing
      billingCycle,
      amount: pricing.finalPrice,
      originalAmount: pricing.originalPrice,
      currency: 'INR',
      discountPercentage: pricing.discountPercentage,

      // Periods
      currentPeriodStart,
      currentPeriodEnd,
      trialStart,
      trialEnd,

      // Features
      features,

      // Usage tracking (initialize to zero)
      usage: {
        projectsCreated: 0,
        usersInvited: 1, // Count the organization owner
        integrationsConnected: 0,
        automationsCreated: 0,
        apiCallsThisMonth: 0,
        lastUsageReset: new Date(),
      },

      // Initialize empty arrays
      invoices: [],
      planHistory: [],
      metadata: {
        createdBy: 'system',
        creationReason: 'organization_created',
        createdAt: new Date(),
      },
    };
  }

  /**
   * Update subscription usage when organization members are added
   */
  static async updateUsageOnMemberAdd(
    organizationId: string | mongoose.Types.ObjectId
  ): Promise<void> {
    try {
      const subscription = await Subscription.findOne({ organizationId });
      if (!subscription) {
        console.warn(`No subscription found for organization: ${organizationId}`);
        return;
      }

      // Increment users invited count
      subscription.usage.usersInvited += 1;
      await subscription.save();

      console.log(`Updated subscription usage for organization: ${organizationId}`);
    } catch (error: any) {
      console.error('Error updating subscription usage:', error);
    }
  }

  /**
   * Update subscription usage when projects are created
   */
  static async updateUsageOnProjectCreate(
    organizationId: string | mongoose.Types.ObjectId
  ): Promise<void> {
    try {
      const subscription = await Subscription.findOne({ organizationId });
      if (!subscription) {
        console.warn(`No subscription found for organization: ${organizationId}`);
        return;
      }

      // Increment projects created count
      subscription.usage.projectsCreated += 1;
      await subscription.save();

      console.log(`Updated project usage for organization: ${organizationId}`);
    } catch (error: any) {
      console.error('Error updating project usage:', error);
    }
  }

  /**
   * Check if organization can perform an action based on subscription limits
   */
  static async checkUsageLimit(
    organizationId: string | mongoose.Types.ObjectId,
    limitType: 'users' | 'projects' | 'integrations' | 'automations' | 'apiCalls'
  ): Promise<{
    allowed: boolean;
    currentUsage: number;
    limit: number | 'unlimited';
    message?: string;
  }> {
    try {
      const subscription = await Subscription.findOne({ organizationId });
      if (!subscription) {
        return {
          allowed: false,
          currentUsage: 0,
          limit: 0,
          message: 'No subscription found',
        };
      }

      let currentUsage: number;
      let limit: number | 'unlimited';

      switch (limitType) {
        case 'users':
          currentUsage = subscription.usage.usersInvited;
          limit = subscription.userLimit;
          break;
        case 'projects':
          currentUsage = subscription.usage.projectsCreated;
          limit = subscription.projectLimit;
          break;
        case 'integrations':
          currentUsage = subscription.usage.integrationsConnected;
          limit = subscription.integrationLimit;
          break;
        case 'automations':
          currentUsage = subscription.usage.automationsCreated;
          limit = subscription.automationLimit;
          break;
        case 'apiCalls':
          currentUsage = subscription.usage.apiCallsThisMonth;
          limit = subscription.apiCallLimit;
          break;
        default:
          throw new Error(`Invalid limit type: ${limitType}`);
      }

      if (limit === 'unlimited') {
        return { allowed: true, currentUsage, limit };
      }

      const allowed = currentUsage < (limit as number);
      const message = allowed ? undefined : `${limitType} limit reached (${currentUsage}/${limit})`;

      return { allowed, currentUsage, limit, message };
    } catch (error: any) {
      console.error('Error checking usage limit:', error);
      return {
        allowed: false,
        currentUsage: 0,
        limit: 0,
        message: 'Error checking limits',
      };
    }
  }

  /**
   * Get subscription summary for an organization
   */
  static async getSubscriptionSummary(
    organizationId: string | mongoose.Types.ObjectId
  ): Promise<any> {
    try {
      const subscription = await Subscription.findOne({ organizationId }).populate(
        'organizationId',
        'name'
      );

      if (!subscription) {
        return null;
      }

      return {
        id: subscription._id,
        organizationId: subscription.organizationId,
        plan: subscription.plan,
        status: subscription.status,
        billingCycle: subscription.billingCycle,
        amount: subscription.amount,
        currency: subscription.currency,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        features: subscription.features,
        limits: {
          users: subscription.userLimit,
          projects: subscription.projectLimit,
          integrations: subscription.integrationLimit,
          automations: subscription.automationLimit,
          apiCalls: subscription.apiCallLimit,
        },
        usage: subscription.usage,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt,
      };
    } catch (error: any) {
      console.error('Error getting subscription summary:', error);
      throw error;
    }
  }
}
