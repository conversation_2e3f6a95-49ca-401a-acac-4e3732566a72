'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/use-toast';
import { DollarSign, Plus, Download, FileSpreadsheet, FileSpreadsheetIcon } from 'lucide-react';
import { IExpense } from '@/models/Budget';
import { BudgetOverview } from '../BudgetTracker/BudgetOverview';
import { CategoryAllocationChart } from '../BudgetTracker/CategoryAllocationChart';
import { ExpenseHistoryTable } from '../BudgetTracker/ExpenseHistoryTable';
import { BudgetForecasting } from '../BudgetTracker/BudgetForecasting';
import { BudgetAlerts } from '../BudgetTracker/BudgetAlerts';
import { ExpenseEntryForm } from '../BudgetTracker/ExpenseEntryForm';
import { BudgetForm } from '../BudgetTracker/BudgetForm';
import { exportBudgetToCSV, exportBudgetToPDF } from '../../utils/exportBudgetData';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '../ui/skeleton';
import { BudgetService } from '@/services/Budget.service';
import { IBudget, IExpense as ModelIExpense } from '@/models/Budget';
import mongoose from 'mongoose';
import { ScrollArea } from '../ui/scroll-area';

interface BudgetTrackerProps {
  projectId: string;
}

export default function BudgetTracker({ projectId }: BudgetTrackerProps) {
  const [isExpenseFormOpen, setIsExpenseFormOpen] = useState(false);
  const [isBudgetFormOpen, setIsBudgetFormOpen] = useState(false);
  const [budgetFormMode, setBudgetFormMode] = useState<'create' | 'edit'>('edit');
  const [editingExpense, setEditingExpense] = useState<IExpense | null>(null);
  const [selectedTab, setSelectedTab] = useState('overview');

  const queryClient = useQueryClient();

  const { data: budgetData, isLoading: budgetLoading } = useQuery({
    queryKey: ['budget', projectId],
    queryFn: async () => {
      const response = await BudgetService.getBudgetByProject(projectId);
      return response.budget;
    },
    retry: 1,
  });

  const { data: expensesData = { expenses: [], total: 0, page: 1, totalPages: 1 } } = useQuery({
    queryKey: ['expenses', projectId],
    queryFn: async () => {
      const response = await BudgetService.getExpenses(projectId);
      return response;
    },
    enabled: !!budgetData,
  });

  const deleteExpenseMutation = useMutation({
    mutationFn: async (expenseId: string) => {
      return BudgetService.deleteExpense(expenseId);
    },
    onSuccess: () => {
      toast({ title: 'Expense deleted successfully!' });
      queryClient.invalidateQueries({ queryKey: ['expenses', projectId] });
      queryClient.invalidateQueries({ queryKey: ['budget', projectId] });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete expense',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleExpenseAdded = () => {
    queryClient.invalidateQueries({ queryKey: ['expenses', projectId] });
    queryClient.invalidateQueries({ queryKey: ['budget', projectId] });
  };

  const handleEditExpense = (expense: IExpense) => {
    setEditingExpense(expense);
    setIsExpenseFormOpen(true);
  };

  const handleDeleteExpense = (expenseId: string) => {
    if (confirm('Are you sure you want to delete this expense?')) {
      deleteExpenseMutation.mutate(expenseId);
    }
  };

  const handleCloseExpenseForm = () => {
    setIsExpenseFormOpen(false);
    setEditingExpense(null);
  };

  const handleCloseBudgetForm = () => {
    setIsBudgetFormOpen(false);
  };

  const handleEditBudget = () => {
    console.log('Editing budget:', budgetData);
    setBudgetFormMode('edit');
    setIsBudgetFormOpen(true);
  };

  const handleCreateBudget = () => {
    setBudgetFormMode('create');
    setIsBudgetFormOpen(true);
  };

  // Add type guard for budgetData
  const isValidBudget = (budget: any): budget is IBudget => {
    return (
      budget && typeof budget === 'object' && 'totalBudget' in budget && 'categories' in budget
    );
  };

  // Convert _id from string to ObjectId if needed
  const normalizeExpenses = (expenses: any[]): ModelIExpense[] => {
    return expenses
      .filter(exp => exp._id !== undefined)
      .map(exp => ({
        ...exp,
        _id:
          typeof exp._id === 'string' && mongoose.Types.ObjectId.isValid(exp._id)
            ? new mongoose.Types.ObjectId(exp._id)
            : exp._id,
      }));
  };

  // Defensive fallback for expenses
  const rawExpenses = Array.isArray(expensesData.expenses) ? expensesData.expenses : [];
  const expenses: ModelIExpense[] = normalizeExpenses(rawExpenses);

  if (budgetLoading) {
    return (
      <div className="space-y-6 h-full">
        {/* Budget Overview Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="transition-all duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 rounded w-1/3" />
                <Skeleton className="h-4 w-4 rounded-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 rounded w-1/2 mb-2" />
                <Skeleton className="h-3 rounded w-full" />
                {i === 3 && (
                  <>
                    <Skeleton className="h-2 rounded w-full mt-2" />
                    <Skeleton className="h-8 rounded w-full mt-2" />
                  </>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tabs Structure Skeleton */}
        <div className="h-[calc(100vh-24rem)]">
          {/* Tabs Navigation Skeleton */}
          <Skeleton className="h-10 w-full rounded mb-4" />

          {/* Scrollable Content Area Skeleton */}
          <ScrollArea className="h-[calc(100vh-28rem)]">
            <div className="space-y-6 p-1">
              {/* Content Skeleton - Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-5 w-5 rounded" />
                      <Skeleton className="h-5 w-40 rounded" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-[300px] w-full rounded" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-5 w-5 rounded" />
                      <Skeleton className="h-5 w-40 rounded" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-[300px] w-full rounded" />
                  </CardContent>
                </Card>
              </div>

              {/* Expense Table Skeleton */}
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-5 w-40 rounded" />
                  </div>
                  <div className="flex gap-4 items-center">
                    <Skeleton className="h-10 w-full rounded" />
                    <Skeleton className="h-10 w-24 rounded" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-md">
                      <div className="grid grid-cols-7 p-4 border-b">
                        {Array.from({ length: 7 }).map((_, i) => (
                          <Skeleton key={i} className="h-4 w-20 rounded" />
                        ))}
                      </div>
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="grid grid-cols-7 p-4 border-b">
                          {Array.from({ length: 7 }).map((_, j) => (
                            <Skeleton key={j} className="h-4 w-20 rounded" />
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        </div>
      </div>
    );
  }

  if (!isValidBudget(budgetData)) {
    return (
      <div className="text-center py-12">
        <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Budget Found</h3>
        <p className="text-muted-foreground mb-4">
          Create a budget to start tracking project expenses
        </p>
        <Button onClick={handleCreateBudget}>
          <Plus className="h-4 w-4 mr-2" />
          Create Budget
        </Button>

        <BudgetForm
          isOpen={isBudgetFormOpen}
          onClose={handleCloseBudgetForm}
          projectId={projectId}
          onBudgetSaved={() => {
            queryClient.invalidateQueries({ queryKey: ['budget', projectId] });
          }}
          mode="create"
        />
      </div>
    );
  }

  const categories = budgetData.categories || [];
  const currency = budgetData.currency || 'USD';

  return (
    <div className="space-y-6 h-full">
      <div className="flex flex-col sm:flex-row justify-end items-start sm:items-center gap-4">
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  if (budgetData && expenses) {
                    try {
                      exportBudgetToCSV(budgetData, expenses);
                      toast({
                        title: 'Export Successful',
                        description: 'Budget data exported as CSV',
                      });
                    } catch (error) {
                      toast({
                        title: 'Export Failed',
                        description: 'Failed to export budget data as CSV',
                        variant: 'destructive',
                      });
                    }
                  }
                }}
              >
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={async () => {
                  if (budgetData && expenses.length) {
                    try {
                      await exportBudgetToPDF(budgetData, expenses);
                      toast({
                        title: 'Export Successful',
                        description: 'Budget data exported as PDF',
                      });
                    } catch (error) {
                      toast({
                        title: 'Export Failed',
                        description:
                          error instanceof Error ? error.message : 'Failed to export as PDF',
                        variant: 'destructive',
                      });
                    }
                  }
                }}
              >
                <FileSpreadsheetIcon className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => setIsExpenseFormOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Expense
          </Button>
        </div>
      </div>

      <BudgetOverview budget={budgetData} onEditBudget={handleEditBudget} />
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="h-[calc(100vh-24rem)]">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <ScrollArea className="h-[calc(100vh-28rem)]">
          <TabsContent value="overview" className="space-y-6 p-1">
            <CategoryAllocationChart categories={categories} currency={currency} />
          </TabsContent>

          <TabsContent value="expenses" className="space-y-6 p-1">
            <ExpenseHistoryTable
              expenses={expenses}
              onEditExpense={handleEditExpense}
              onDeleteExpense={handleDeleteExpense}
              currency={currency}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6 p-1">
            <CategoryAllocationChart categories={categories} currency={currency} />
          </TabsContent>

          <TabsContent value="forecasting" className="space-y-6 p-1">
            <BudgetForecasting budget={budgetData} expenses={expenses} />
          </TabsContent>

          <TabsContent value="alerts" className="space-y-6 p-1">
            <BudgetAlerts budget={budgetData} onUpdateAlerts={() => {}} />
          </TabsContent>
        </ScrollArea>
      </Tabs>

      <ExpenseEntryForm
        isOpen={isExpenseFormOpen}
        onClose={handleCloseExpenseForm}
        categories={categories}
        onExpenseAdded={handleExpenseAdded}
        editingExpense={editingExpense || undefined}
        projectId={projectId}
      />

      <BudgetForm
        isOpen={isBudgetFormOpen}
        onClose={handleCloseBudgetForm}
        projectId={projectId}
        budget={budgetData}
        onBudgetSaved={handleExpenseAdded}
        mode={budgetFormMode}
      />
    </div>
  );
}
