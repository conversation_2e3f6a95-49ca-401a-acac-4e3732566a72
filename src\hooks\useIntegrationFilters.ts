'use client';

import { useMemo } from 'react';
import { AVAILABLE_INTEGRATIONS } from '@/constant/Integration';

interface UseIntegrationFiltersProps {
  integrations: any[];
  searchQuery: string;
}

export function useIntegrationFilters({ integrations, searchQuery }: UseIntegrationFiltersProps) {
  const connectedProviders = useMemo(
    () => new Set(integrations.map((i: any) => i.provider)),
    [integrations]
  );

  const filteredIntegrations = useMemo(() => {
    const available = Object.values(AVAILABLE_INTEGRATIONS).filter(
      integration =>
        !connectedProviders.has(integration.id) &&
        (searchQuery === '' ||
          integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          integration.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          integration.category?.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    const connected = integrations.filter(
      (integration: any) =>
        searchQuery === '' ||
        integration.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
        AVAILABLE_INTEGRATIONS[integration.provider]?.name
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase())
    );

    return { available, connected };
  }, [integrations, connectedProviders, searchQuery]);

  return {
    connectedProviders,
    availableIntegrations: filteredIntegrations.available,
    connectedIntegrations: filteredIntegrations.connected,
  };
}

export function useIntegrationActions() {
  const handleGitHubDisconnect = async () => {
    try {
      const response = await fetch('/api/github/disconnect', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to disconnect GitHub');
      }

      return { success: true };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to disconnect GitHub');
    }
  };

  return {
    handleGitHubDisconnect,
  };
}
