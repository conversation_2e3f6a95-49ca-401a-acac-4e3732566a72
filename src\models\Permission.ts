import mongoose, { Document, Schema, Model } from 'mongoose';

export interface IPermission extends Document {
  userId?: mongoose.Types.ObjectId;
  roleId?: mongoose.Types.ObjectId;
  organizationId?: mongoose.Types.ObjectId;
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'report'
    | 'timeline'
    | 'notification'
    | 'file'
    | 'comment'
    | 'budget'
    | 'global';
  resourceId?: mongoose.Types.ObjectId;
  actions: {
    read: boolean;
    write: boolean;
    delete: boolean;
    admin: boolean;
    share?: boolean;
    export?: boolean;
    import?: boolean;
    manage?: boolean;
  };
  scope: 'global' | 'organization' | 'project' | 'resource';
  conditions?: {
    timeRestriction?: {
      startTime?: Date;
      endTime?: Date;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestriction?: string[];
    locationRestriction?: {
      countries?: string[];
      regions?: string[];
    };
    deviceRestriction?: {
      allowedDevices?: string[];
      blockedDevices?: string[];
    };
  };
  inheritance?: {
    inheritsFrom?: mongoose.Types.ObjectId;
    inheritanceType?: 'role' | 'parent_resource' | 'organization';
    overrides?: {
      [key: string]: boolean;
    };
  };
  isActive: boolean;
  expiresAt?: Date;
  grantedBy?: mongoose.Types.ObjectId;
  grantedAt: Date;
  revokedBy?: mongoose.Types.ObjectId;
  revokedAt?: Date;
  reason?: string;
  metadata?: {
    source?: 'manual' | 'role_assignment' | 'inheritance' | 'system';
    context?: Record<string, any>;
    auditTrail?: Array<{
      action: 'granted' | 'modified' | 'revoked' | 'inherited';
      by: mongoose.Types.ObjectId;
      at: Date;
      reason?: string;
      changes?: Record<string, any>;
    }>;
  };
  createdAt: Date;
  updatedAt: Date;
}

const PermissionSchema = new Schema<IPermission>(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false,
      index: true,
    },
    roleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Role',
      required: false,
      index: true,
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: false,
      index: true,
    },
    resourceType: {
      type: String,
      enum: [
        'task',
        'project',
        'note',
        'user',
        'organization',
        'integration',
        'report',
        'timeline',
        'notification',
        'file',
        'comment',
        'budget',
        'global',
      ],
      required: true,
      index: true,
    },
    resourceId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      index: true,
    },
    actions: {
      read: {
        type: Boolean,
        default: false,
      },
      write: {
        type: Boolean,
        default: false,
      },
      delete: {
        type: Boolean,
        default: false,
      },
      admin: {
        type: Boolean,
        default: false,
      },
      share: {
        type: Boolean,
        default: false,
      },
      export: {
        type: Boolean,
        default: false,
      },
      import: {
        type: Boolean,
        default: false,
      },
      manage: {
        type: Boolean,
        default: false,
      },
    },
    scope: {
      type: String,
      enum: ['global', 'organization', 'project', 'resource'],
      required: true,
      default: 'resource',
      index: true,
    },
    conditions: {
      timeRestriction: {
        startTime: {
          type: Date,
          required: false,
        },
        endTime: {
          type: Date,
          required: false,
        },
        daysOfWeek: {
          type: [Number],
          required: false,
          validate: {
            validator: function (days: number[]) {
              return days.every(day => day >= 0 && day <= 6);
            },
            message: 'Days of week must be between 0 (Sunday) and 6 (Saturday)',
          },
        },
        timezone: {
          type: String,
          required: false,
        },
      },
      ipRestriction: {
        type: [String],
        required: false,
      },
      locationRestriction: {
        countries: {
          type: [String],
          required: false,
        },
        regions: {
          type: [String],
          required: false,
        },
      },
      deviceRestriction: {
        allowedDevices: {
          type: [String],
          required: false,
        },
        blockedDevices: {
          type: [String],
          required: false,
        },
      },
    },
    inheritance: {
      inheritsFrom: {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
        index: true,
      },
      inheritanceType: {
        type: String,
        enum: ['role', 'parent_resource', 'organization'],
        required: false,
      },
      overrides: {
        type: Schema.Types.Mixed,
        required: false,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    expiresAt: {
      type: Date,
      required: false,
      index: true,
    },
    grantedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    grantedAt: {
      type: Date,
      default: Date.now,
      required: true,
    },
    revokedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    revokedAt: {
      type: Date,
      required: false,
    },
    reason: {
      type: String,
      required: false,
      maxlength: 500,
    },
    metadata: {
      source: {
        type: String,
        enum: ['manual', 'role_assignment', 'inheritance', 'system'],
        default: 'manual',
      },
      context: {
        type: Schema.Types.Mixed,
        required: false,
      },
      auditTrail: [
        {
          action: {
            type: String,
            enum: ['granted', 'modified', 'revoked', 'inherited'],
            required: true,
          },
          by: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
          },
          at: {
            type: Date,
            default: Date.now,
            required: true,
          },
          reason: {
            type: String,
            required: false,
          },
          changes: {
            type: Schema.Types.Mixed,
            required: false,
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    collection: 'permissions',
  }
);

// Compound indexes for efficient querying
PermissionSchema.index({ userId: 1, resourceType: 1, resourceId: 1 });
PermissionSchema.index({ roleId: 1, resourceType: 1, resourceId: 1 });
PermissionSchema.index({ organizationId: 1, resourceType: 1, scope: 1 });
PermissionSchema.index({ resourceType: 1, resourceId: 1, isActive: 1 });
PermissionSchema.index({ expiresAt: 1, isActive: 1 });
PermissionSchema.index({ scope: 1, isActive: 1 });
PermissionSchema.index({ 'inheritance.inheritsFrom': 1 });

// TTL index for expired permissions
PermissionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Instance methods
PermissionSchema.methods.isExpired = function (): boolean {
  return this.expiresAt ? new Date() > this.expiresAt : false;
};

PermissionSchema.methods.hasAction = function (action: string): boolean {
  return this.actions[action] === true;
};

PermissionSchema.methods.addAuditEntry = function (
  action: 'granted' | 'modified' | 'revoked' | 'inherited',
  by: string,
  reason?: string,
  changes?: Record<string, any>
): void {
  if (!this.metadata) {
    this.metadata = { auditTrail: [] };
  }

  if (!this.metadata.auditTrail) {
    this.metadata.auditTrail = [];
  }

  this.metadata.auditTrail.push({
    action,
    by: new mongoose.Types.ObjectId(by),
    at: new Date(),
    reason,
    changes,
  });
};

PermissionSchema.methods.evaluateConditions = function (context: {
  ip?: string;
  userAgent?: string;
  location?: { country?: string; region?: string };
  timestamp?: Date;
}): boolean {
  if (!this.conditions) return true;

  const now = context.timestamp || new Date();

  // Check time restrictions
  if (this.conditions.timeRestriction) {
    const timeRestriction = this.conditions.timeRestriction;

    if (timeRestriction.startTime && now < timeRestriction.startTime) return false;
    if (timeRestriction.endTime && now > timeRestriction.endTime) return false;

    if (timeRestriction.daysOfWeek && timeRestriction.daysOfWeek.length > 0) {
      const dayOfWeek = now.getDay();
      if (!timeRestriction.daysOfWeek.includes(dayOfWeek)) return false;
    }
  }

  // Check IP restrictions
  if (this.conditions.ipRestriction && context.ip) {
    if (!this.conditions.ipRestriction.includes(context.ip)) return false;
  }

  // Check location restrictions
  if (this.conditions.locationRestriction && context.location) {
    const locationRestriction = this.conditions.locationRestriction;

    if (locationRestriction.countries && context.location.country) {
      if (!locationRestriction.countries.includes(context.location.country)) return false;
    }

    if (locationRestriction.regions && context.location.region) {
      if (!locationRestriction.regions.includes(context.location.region)) return false;
    }
  }

  return true;
};

// Add static methods to PermissionSchema
export interface PermissionModel extends Model<IPermission> {
  getUserPermissions(userId: string, options?: any): Promise<IPermission[]>;
  getResourcePermissions(
    resourceType: string,
    resourceId: string,
    options?: any
  ): Promise<IPermission[]>;
  grantPermission(permissionData: any): Promise<IPermission>;
  bulkGrantPermissions(permissions: any[], grantedBy: string): Promise<any>;
  revokePermission(permissionId: string, revokedBy: string): Promise<boolean>;
}

PermissionSchema.statics.getUserPermissions = async function (userId: string, options: any = {}) {
  const query: any = { userId };
  if (options.resourceType) query.resourceType = options.resourceType;
  if (options.resourceId) query.resourceId = options.resourceId;
  if (options.organizationId) query.organizationId = options.organizationId;
  query.isActive = true;
  return this.find(query);
};

PermissionSchema.statics.getResourcePermissions = async function (
  resourceType: string,
  resourceId: string,
  options: any = {}
) {
  const query: any = { resourceType, resourceId };
  if (options.userId) query.userId = options.userId;
  if (options.organizationId) query.organizationId = options.organizationId;
  query.isActive = true;
  return this.find(query);
};

PermissionSchema.statics.grantPermission = async function (permissionData: any) {
  // Upsert: if permission exists for user/resource, update; else create
  const query: any = {
    userId: permissionData.userId,
    resourceType: permissionData.resourceType,
    resourceId: permissionData.resourceId,
    organizationId: permissionData.organizationId,
    isActive: true,
  };
  let permission = await this.findOne(query);
  if (permission) {
    Object.assign(permission, permissionData);
    permission.addAuditEntry(
      'modified',
      permissionData.grantedBy?.toString?.() || permissionData.grantedBy || '',
      'Permission updated'
    );
    await permission.save();
  } else {
    permission = await this.create({ ...permissionData, grantedAt: new Date() });
    permission.addAuditEntry(
      'granted',
      permissionData.grantedBy?.toString?.() || permissionData.grantedBy || '',
      'Permission granted'
    );
    await permission.save();
  }
  return permission;
};

PermissionSchema.statics.bulkGrantPermissions = async function (
  permissions: any[],
  grantedBy: string
) {
  let granted = 0,
    updated = 0;
  const errors: string[] = [];
  for (const perm of permissions) {
    try {
      // Use the correct 'this' context for static method
      const result = await (this as any).grantPermission({ ...perm, grantedBy });
      if (result) granted++;
      else updated++;
    } catch (error: any) {
      errors.push(`Failed to grant permission: ${error.message}`);
    }
  }
  return { granted, updated, errors };
};

PermissionSchema.statics.revokePermission = async function (
  permissionId: string,
  revokedBy: string
) {
  const permission = await this.findById(permissionId);
  if (!permission) return false;
  permission.isActive = false;
  permission.revokedBy = revokedBy;
  permission.revokedAt = new Date();
  permission.addAuditEntry('revoked', revokedBy, 'Permission revoked');
  await permission.save();
  return true;
};

// No static methods or custom model interface needed
export const Permission =
  (mongoose.models.Permission as PermissionModel) ||
  mongoose.model<IPermission, PermissionModel>('Permission', PermissionSchema);
