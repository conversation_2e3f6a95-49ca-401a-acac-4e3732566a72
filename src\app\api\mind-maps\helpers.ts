import MindMap from '@/models/MindMap';
import mongoose from 'mongoose';

// TypeScript interfaces for input and output
interface MindMapData {
  title: string;
  description?: string;
  projectId?: string;
  tags?: string[];
}

interface HelperError {
  error: true;
  message: string;
  status?: number;
}

// Helper to check if a value is a valid string
function isValidString(val: any): val is string {
  return typeof val === 'string' && val.trim().length > 0;
}

// Helper to check permissions
function isOwner(mindMap: any, userId?: string): boolean {
  return mindMap.userId && userId && mindMap.userId.toString() === userId;
}

/**
 * Get Mind Maps for a user, with optional filters
 */
export async function getMindMapsHelper(
  searchParams: URLSearchParams,
  userId: string
): Promise<any[] | HelperError> {
  const projectId = searchParams.get('projectId');
  const status = searchParams.get('status');
  const search = searchParams.get('search');

  const query: any = {
    isDeleted: false,
    userId: new mongoose.Types.ObjectId(userId),
  };

  if (projectId && projectId !== 'all') {
    query.projectId = new mongoose.Types.ObjectId(projectId);
  }

  if (status && status !== 'all') {
    query.status = status;
  }

  if (search) {
    query.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } },
    ];
  }

  console.log('getMindMapsHelper - Query:', JSON.stringify(query));
  console.log('getMindMapsHelper - User ID:', userId);

  const mindMaps = await MindMap.find(query).sort({ updatedAt: -1 }).limit(50).lean();
  console.log('getMindMapsHelper - Found mind maps:', mindMaps?.length || 0);

  return mindMaps;
}

/**
 * Create a new Mind Map for a user
 */
export async function createMindMapHelper(
  data: MindMapData,
  userId: string
): Promise<any | HelperError> {
  const { title, description, projectId, tags } = data;

  if (!isValidString(title)) {
    return { error: true, message: 'Title is required', status: 400 };
  }
  if (!userId) {
    return { error: true, message: 'User not authenticated', status: 401 };
  }

  const mindMap = new MindMap({
    title: title.trim(),
    description: description?.trim(),
    projectId: projectId ? new mongoose.Types.ObjectId(projectId) : undefined,
    tags: tags || [],
    userId: new mongoose.Types.ObjectId(userId),
    nodes: [],
    connections: [],
    viewport: { x: 0, y: 0, zoom: 1 },
    status: 'draft',
    analytics: {
      viewCount: 0,
      editCount: 0,
    },
  });

  await mindMap.save();
  return mindMap;
}

/**
 * Update a Mind Map (only by owner)
 */
export async function updateMindMapHelper(
  id: string,
  data: Partial<MindMapData>,
  userId: string
): Promise<any | HelperError> {
  if (!userId) {
    return { error: true, message: 'User not authenticated', status: 401 };
  }
  const mindMap = await MindMap.findById(id);
  if (!mindMap) {
    return { error: true, message: 'Mind map not found', status: 404 };
  }
  if (!isOwner(mindMap, userId)) {
    return { error: true, message: 'Permission denied', status: 403 };
  }

  Object.assign(mindMap, data);
  if (mindMap.analytics) {
    mindMap.analytics.editCount++;
    mindMap.analytics.lastEdited = new Date();
  }

  await mindMap.save();
  return mindMap;
}

/**
 * Soft delete a Mind Map (only by owner)
 */
export async function deleteMindMapHelper(
  id: string,
  userId: string
): Promise<{ message: string } | HelperError> {
  if (!userId) {
    return { error: true, message: 'User not authenticated', status: 401 };
  }
  const mindMap = await MindMap.findById(id);
  if (!mindMap) {
    return { error: true, message: 'Mind map not found', status: 404 };
  }
  if (!isOwner(mindMap, userId)) {
    return { error: true, message: 'Permission denied', status: 403 };
  }

  mindMap.isDeleted = true;
  mindMap.deletedAt = new Date();
  mindMap.deletedBy = userId;
  await mindMap.save();

  return { message: 'Mind map deleted successfully' };
}

/**
 * Duplicate a Mind Map (only by owner)
 */
export async function duplicateMindMapHelper(
  id: string,
  title: string,
  userId: string
): Promise<any | HelperError> {
  if (!userId) {
    return { error: true, message: 'User not authenticated', status: 401 };
  }
  if (!isValidString(title)) {
    return { error: true, message: 'Title is required', status: 400 };
  }
  const originalMindMap = await MindMap.findById(id);
  if (!originalMindMap) {
    return { error: true, message: 'Original mind map not found', status: 404 };
  }
  if (!isOwner(originalMindMap, userId)) {
    return { error: true, message: 'Permission denied', status: 403 };
  }

  const duplicatedMindMap = new MindMap({
    ...originalMindMap.toObject(),
    _id: undefined,
    title: title.trim(),
    userId: new mongoose.Types.ObjectId(userId),
    createdAt: new Date(),
    updatedAt: new Date(),
    analytics: {
      viewCount: 0,
      editCount: 0,
    },
  });

  await duplicatedMindMap.save();
  return duplicatedMindMap;
}
