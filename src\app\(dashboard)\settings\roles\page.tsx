'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'sonner';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Edit2, Plus, Shield, Trash2, Users } from 'lucide-react';
import { RoleService } from '@/services/Role.service';
import { OrganizationService } from '@/services/Organization.service';
import { RolesSkeleton } from '@/components/Roles/RolesSkeleton';
import { IRole, IOrganization } from '@/types/Role';
import Modal from '@/components/Global/Modal';
import { ConfirmDialog } from '@/components/Global/ConfirmDialog';

const roleSchema = z.object({
  name: z.string().min(2, { message: 'Role name must be at least 2 characters.' }),
  displayName: z.string().min(2, { message: 'Display name must be at least 2 characters.' }),
  description: z.string().optional(),
  permissions: z
    .object({
      tasks: z
        .object({
          read: z.boolean().default(true),
          write: z.boolean().default(false),
          delete: z.boolean().default(false),
          assign: z.boolean().default(false),
          manage: z.boolean().default(false),
        })
        .default({}),
      projects: z
        .object({
          read: z.boolean().default(true),
          write: z.boolean().default(false),
          delete: z.boolean().default(false),
          create: z.boolean().default(false),
          manage: z.boolean().default(false),
          archive: z.boolean().default(false),
        })
        .default({}),
      users: z
        .object({
          read: z.boolean().default(true),
          write: z.boolean().default(false),
          delete: z.boolean().default(false),
          invite: z.boolean().default(false),
          manage: z.boolean().default(false),
        })
        .default({}),
      organization: z
        .object({
          read: z.boolean().default(false),
          write: z.boolean().default(false),
          manage: z.boolean().default(false),
          billing: z.boolean().default(false),
        })
        .default({}),
    })
    .default({}),
  metadata: z
    .object({
      color: z.string().default('#64748b'),
      icon: z.string().default('👥'),
    })
    .default({}),
});

const PERMISSION_GROUPS = [
  {
    key: 'tasks',
    label: 'Tasks',
    icon: '📝',
    description: 'Manage tasks and assignments',
    actions: [
      { key: 'read', label: 'View Tasks', description: 'Can view tasks and their details' },
      { key: 'write', label: 'Edit Tasks', description: 'Can create and edit tasks' },
      { key: 'delete', label: 'Delete Tasks', description: 'Can delete tasks' },
      { key: 'assign', label: 'Assign Tasks', description: 'Can assign tasks to team members' },
      { key: 'manage', label: 'Manage Tasks', description: 'Full task management permissions' },
    ],
  },
  {
    key: 'projects',
    label: 'Projects',
    icon: '📁',
    description: 'Manage projects and workflows',
    actions: [
      { key: 'read', label: 'View Projects', description: 'Can view projects and their details' },
      { key: 'write', label: 'Edit Projects', description: 'Can edit project details' },
      { key: 'delete', label: 'Delete Projects', description: 'Can delete projects' },
      { key: 'create', label: 'Create Projects', description: 'Can create new projects' },
      {
        key: 'manage',
        label: 'Manage Projects',
        description: 'Full project management permissions',
      },
      { key: 'archive', label: 'Archive Projects', description: 'Can archive/unarchive projects' },
    ],
  },
  {
    key: 'users',
    label: 'Users',
    icon: '👥',
    description: 'Manage team members and access',
    actions: [
      { key: 'read', label: 'View Users', description: 'Can view team member profiles' },
      { key: 'write', label: 'Edit Users', description: 'Can edit user profiles and settings' },
      { key: 'delete', label: 'Remove Users', description: 'Can remove users from organization' },
      { key: 'invite', label: 'Invite Users', description: 'Can invite new team members' },
      { key: 'manage', label: 'Manage Users', description: 'Full user management permissions' },
    ],
  },
  {
    key: 'organization',
    label: 'Organization',
    icon: '🏢',
    description: 'Manage organization settings',
    actions: [
      { key: 'read', label: 'View Settings', description: 'Can view organization settings' },
      { key: 'write', label: 'Edit Settings', description: 'Can edit organization settings' },
      { key: 'manage', label: 'Manage Organization', description: 'Full organization management' },
      {
        key: 'billing',
        label: 'Manage Billing',
        description: 'Can manage billing and subscriptions',
      },
    ],
  },
];

const ROLE_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
  '#64748b',
];

const ROLE_ICONS = ['👑', '⭐', '🛡️', '🔧', '📝', '👥', '🎯', '🚀', '💎', '🔥', '⚡', '🌟'];

export default function RolesSettings() {
  const [selectedRole, setSelectedRole] = useState<IRole | null>(null);
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [organizationId, setOrganizationId] = useState<string>('');
  const [isOwner, setIsOwner] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof roleSchema>>({
    resolver: zodResolver(roleSchema),
    defaultValues: {
      name: '',
      displayName: '',
      description: '',
      permissions: {
        tasks: {
          read: true,
          write: false,
          delete: false,
          assign: false,
          manage: false,
        },
        projects: {
          read: true,
          write: false,
          delete: false,
          create: false,
          manage: false,
          archive: false,
        },
        users: {
          read: true,
          write: false,
          delete: false,
          invite: false,
          manage: false,
        },
        organization: {
          read: false,
          write: false,
          manage: false,
          billing: false,
        },
      },
      metadata: {
        color: '#64748b',
        icon: '👥',
      },
    },
  });

  // Fetch organization data
  const { data: organization } = useQuery({
    queryKey: ['organization'],
    queryFn: async () => {
      const orgs = await OrganizationService.getOrganizations();
      return orgs.organizations?.[0] as IOrganization;
    },
  });

  // Fetch roles
  const {
    data: rolesData,
    isLoading: rolesLoading,
    error: rolesError,
  } = useQuery({
    queryKey: ['roles', organizationId],
    queryFn: () => RoleService.getOrganizationRoles(organizationId),
    enabled: !!organizationId,
  });

  // Create/Update role mutation
  const saveRoleMutation = useMutation({
    mutationFn: async (values: z.infer<typeof roleSchema>) => {
      if (isEditMode && selectedRole) {
        return RoleService.updateRole(selectedRole._id || selectedRole.id || '', {
          ...values,
          description: values.description || '',
          type: 'custom',
          restrictions: {},
        });
      } else {
        return RoleService.createRole({
          ...values,
          description: values.description || '',
          type: 'custom',
          restrictions: {},
          organizationId,
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      setShowRoleDialog(false);
      setSelectedRole(null);
      setIsEditMode(false);
      form.reset();
      toast.success(isEditMode ? 'Role updated successfully!' : 'Role created successfully!');
    },
    onError: (error: Error) => {
      toast.error(
        error.message || (isEditMode ? 'Failed to update role' : 'Failed to create role')
      );
    },
  });

  // Delete role mutation
  const deleteRoleMutation = useMutation({
    mutationFn: (roleId: string) => RoleService.deleteRole(roleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      setShowDeleteDialog(false);
      setSelectedRole(null);
      toast.success('Role deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete role');
    },
  });

  const { data: session } = useSession();

  // Set organization ID and check if user is owner
  useEffect(() => {
    if (organization && session?.user?.id) {
      setOrganizationId(organization._id);

      // Check if user is the original owner OR has Owner role in members
      const isOriginalOwner = organization.ownerId._id === session.user.id;
      const hasOwnerRole = organization.members.some(
        member => member.userId._id === session.user.id && member.role === 'Owner'
      );

      setIsOwner(isOriginalOwner || hasOwnerRole);
    }
  }, [organization, session]);

  const handleCreateRole = useCallback(() => {
    setIsEditMode(false);
    setSelectedRole(null);
    form.reset({
      name: '',
      displayName: '',
      description: '',
      permissions: {
        tasks: { read: true, write: false, delete: false, assign: false, manage: false },
        projects: {
          read: true,
          write: false,
          delete: false,
          create: false,
          manage: false,
          archive: false,
        },
        users: { read: true, write: false, delete: false, invite: false, manage: false },
        organization: { read: false, write: false, manage: false, billing: false },
      },
      metadata: { color: '#64748b', icon: '👥' },
    });
    setShowRoleDialog(true);
  }, [form]);

  const handleEditRole = useCallback(
    (role: IRole) => {
      setIsEditMode(true);
      setSelectedRole(role);
      form.reset({
        name: role.name,
        displayName: role.displayName,
        description: role.description || '',
        permissions: role.permissions as any,
        metadata: role.metadata || { color: '#64748b', icon: '👥' },
      });
      setShowRoleDialog(true);
    },
    [form]
  );

  const handleSaveRole = useCallback(
    async (values: z.infer<typeof roleSchema>) => {
      if (!isEditMode && !organizationId) {
        toast.error('Organization not found');
        return;
      }
      saveRoleMutation.mutate(values);
    },
    [isEditMode, organizationId, saveRoleMutation]
  );

  const handleDeleteRole = useCallback((role: IRole) => {
    setSelectedRole(role);
    setShowDeleteDialog(true);
  }, []);

  const confirmDeleteRole = useCallback(() => {
    if (!selectedRole) return;
    deleteRoleMutation.mutate(selectedRole._id || selectedRole.id || '');
  }, [selectedRole, deleteRoleMutation]);

  const roles = rolesData?.roles || [];

  if (rolesLoading) {
    return <RolesSkeleton />;
  }

  if (rolesError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-destructive">Failed to load roles</p>
          <Button
            variant="outline"
            onClick={() => queryClient.invalidateQueries({ queryKey: ['roles'] })}
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">No organization found</p>
          <p className="text-sm text-muted-foreground mt-1">
            Please create or join an organization to manage roles.
          </p>
        </div>
      </div>
    );
  }

  const renderPermissionForm = () => (
    <div className="space-y-6">
      {PERMISSION_GROUPS.map(group => (
        <Card key={group.key}>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <span className="text-lg">{group.icon}</span>
              <div>
                <CardTitle className="text-base">{group.label}</CardTitle>
                <CardDescription className="text-sm">{group.description}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {group.actions.map(action => (
                <FormField
                  key={`${group.key}.${action.key}`}
                  control={form.control}
                  name={`permissions.${group.key}.${action.key}` as any}
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">{action.label}</FormLabel>
                        <FormDescription className="text-xs">{action.description}</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6 px-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Roles & Permissions</h3>
          <p className="text-sm text-muted-foreground">
            Manage roles and their permissions. Only organization owners can create and manage
            custom roles.
          </p>
        </div>
        {isOwner && (
          <Button onClick={handleCreateRole}>
            <Plus className="h-4 w-4 mr-2" />
            Create Role
          </Button>
        )}
      </div>
      <Separator />

      {/* Roles List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Available Roles
          </CardTitle>
          <CardDescription>
            View and manage roles in your organization. System roles cannot be modified.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map(role => (
                <TableRow key={role._id || role.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div
                        className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-bold"
                        style={{ backgroundColor: role.metadata?.color || '#64748b' }}
                      >
                        {role.metadata?.icon || role.displayName.charAt(0)}
                      </div>
                      <div>
                        <div className="font-medium">{role.displayName}</div>
                        <div className="text-sm text-muted-foreground">{role.name}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate">{role.description}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      {role.assignedUsers?.length || 0}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={role.isSystem ? 'secondary' : 'outline'}>
                      {role.isSystem ? 'System' : 'Custom'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {!role.isSystem && isOwner && (
                        <>
                          <Button variant="ghost" size="icon" onClick={() => handleEditRole(role)}>
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-destructive"
                            onClick={() => handleDeleteRole(role)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dynamic Role Dialog (Create/Edit) */}
      <Modal
        isOpen={showRoleDialog}
        onClose={() => {
          setShowRoleDialog(false);
          setSelectedRole(null);
          setIsEditMode(false);
        }}
      >
        <div className="max-w-4xl max-h-[calc(100vh-8rem)] overflow-y-auto">
          <div className="mb-6">
            <h2 className="text-xl font-semibold">
              {isEditMode ? 'Edit Role' : 'Create New Role'}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {isEditMode
                ? 'Update the role permissions and settings.'
                : 'Create a custom role with specific permissions for your organization.'}
            </p>
          </div>

          {/* Show Default Roles Section */}
          <div className="mb-6 p-4 bg-muted/50 rounded-lg">
            <h3 className="text-sm font-medium mb-3">Default System Roles</h3>
            <div className="grid grid-cols-3 gap-3">
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <div className="w-6 h-6 rounded bg-red-500 flex items-center justify-center text-white text-xs font-bold">
                  👑
                </div>
                <div>
                  <div className="text-xs font-medium">Owner</div>
                  <div className="text-xs text-muted-foreground">Full access</div>
                </div>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <div className="w-6 h-6 rounded bg-green-500 flex items-center justify-center text-white text-xs font-bold">
                  👥
                </div>
                <div>
                  <div className="text-xs font-medium">Member</div>
                  <div className="text-xs text-muted-foreground">Standard access</div>
                </div>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <div className="w-6 h-6 rounded bg-blue-500 flex items-center justify-center text-white text-xs font-bold">
                  👁️
                </div>
                <div>
                  <div className="text-xs font-medium">Guest</div>
                  <div className="text-xs text-muted-foreground">View only</div>
                </div>
              </div>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSaveRole)} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., editor, viewer" />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="displayName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., Editor, Viewer" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Describe what this role can do" />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="metadata.color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role Color</FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {ROLE_COLORS.map(color => (
                          <button
                            key={color}
                            type="button"
                            className={`w-6 h-6 rounded border-2 ${
                              field.value === color ? 'border-gray-400' : 'border-gray-200'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => field.onChange(color)}
                          />
                        ))}
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metadata.icon"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role Icon</FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {ROLE_ICONS.map(icon => (
                          <button
                            key={icon}
                            type="button"
                            className={`w-8 h-8 rounded border-2 flex items-center justify-center ${
                              field.value === icon ? 'border-gray-400' : 'border-gray-200'
                            }`}
                            onClick={() => field.onChange(icon)}
                          >
                            {icon}
                          </button>
                        ))}
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {renderPermissionForm()}

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowRoleDialog(false);
                    setSelectedRole(null);
                    setIsEditMode(false);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={saveRoleMutation.isPending}>
                  {saveRoleMutation.isPending
                    ? isEditMode
                      ? 'Updating...'
                      : 'Creating...'
                    : isEditMode
                      ? 'Update Role'
                      : 'Create Role'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDeleteRole}
        title="Delete Role"
        description={`
              Are you sure you want to delete the role "{selectedRole?.displayName}"? This action cannot be undone.
              ${
                selectedRole?.assignedUsers &&
                selectedRole.assignedUsers.length > 0 && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-sm text-yellow-800">
                      This role is assigned to {selectedRole.assignedUsers.length} user(s). Please
                      reassign these users before deleting the role.
                    </p>
                  </div>
                )
              }
         `}
        confirmText="Delete Role"
        cancelText="Cancel"
        variant="destructive"
        loading={deleteRoleMutation.isPending}
        preventClose={selectedRole?.assignedUsers && selectedRole.assignedUsers.length > 0}
      />
    </div>
  );
}
