// Server-Sent Events utility functions for notifications
import { PushNotificationService } from '@/services/PushNotification.service';

// Import the SSE functions from the API route
// Note: These functions are available on the server side only
let sseModule: any = null;

async function getSSEModule() {
  if (!sseModule && typeof window === 'undefined') {
    try {
      sseModule = await import('@/app/api/notifications/sse/route');
    } catch (error) {
      console.error('Failed to import SSE module:', error);
    }
  }
  return sseModule;
}

export async function broadcastToUser(userId: string, event: string, data: any): Promise<void> {
  if (typeof window !== 'undefined') {
    console.warn('broadcastToUser should only be called on the server side');
    return;
  }

  try {
    const sse = await getSSEModule();
    if (sse && sse.broadcastToUser) {
      const isUserOnline = sse.isUserConnected(userId);
      
      if (isUserOnline) {
        // User is online, send via SSE
        sse.broadcastToUser(userId, {
          event,
          ...data
        });
      } else {
        // User is offline, send push notification if available
        try {
          if (PushNotificationService.isConfigured() && data.notification) {
            await PushNotificationService.sendToUser(userId, {
              title: data.notification.title || 'New Notification',
              body: data.notification.description || 'You have a new notification',
              icon: '/icons/icon-192x192.png',
              badge: '/icons/badge-72x72.png',
              data: {
                url: data.notification.link || '/notifications',
                notificationId: data.notification._id,
              },
              tag: data.notification._id || 'notification',
              requireInteraction: false,
            });
          }
        } catch (pushError) {
          console.error('Failed to send push notification:', pushError);
        }
      }
    }
  } catch (error) {
    console.error('Error broadcasting to user:', error);
  }
}

export async function broadcastToOrganization(orgId: string, event: string, data: any): Promise<void> {
  if (typeof window !== 'undefined') {
    console.warn('broadcastToOrganization should only be called on the server side');
    return;
  }

  try {
    const sse = await getSSEModule();
    if (sse && sse.broadcastToOrganization) {
      sse.broadcastToOrganization(orgId, {
        event,
        ...data
      });
    }
  } catch (error) {
    console.error('Error broadcasting to organization:', error);
  }
}

export async function broadcastToAll(event: string, data: any): Promise<void> {
  if (typeof window !== 'undefined') {
    console.warn('broadcastToAll should only be called on the server side');
    return;
  }

  try {
    const sse = await getSSEModule();
    if (sse && sse.broadcastToAll) {
      sse.broadcastToAll({
        event,
        ...data
      });
    }
  } catch (error) {
    console.error('Error broadcasting to all:', error);
  }
}

export async function getActiveConnectionsCount(): Promise<number> {
  if (typeof window !== 'undefined') {
    return 0;
  }

  try {
    const sse = await getSSEModule();
    if (sse && sse.getActiveConnectionsCount) {
      return sse.getActiveConnectionsCount();
    }
  } catch (error) {
    console.error('Error getting active connections count:', error);
  }
  
  return 0;
}

export async function isUserConnected(userId: string): Promise<boolean> {
  if (typeof window !== 'undefined') {
    return false;
  }

  try {
    const sse = await getSSEModule();
    if (sse && sse.isUserConnected) {
      return sse.isUserConnected(userId);
    }
  } catch (error) {
    console.error('Error checking user connection:', error);
  }
  
  return false;
}

// Notification publishing function (replaces Redis pub/sub)
export async function publishNotification(data: any): Promise<void> {
  if (typeof window !== 'undefined') {
    console.warn('publishNotification should only be called on the server side');
    return;
  }

  try {
    // Direct notification broadcasting without Redis pub/sub
    if (data.userId) {
      await broadcastToUser(data.userId, 'notification', data);
    }

    if (data.organizationId) {
      await broadcastToOrganization(data.organizationId, 'notification', data);
    }
  } catch (error) {
    console.error('Error publishing notification:', error);
  }
}

// Initialize notification system (simplified without Redis)
export function initializeNotificationSystem(): void {
  if (typeof window !== 'undefined') return;

  console.log('✅ SSE notification system initialized');
  // No Redis subscription needed anymore
}
