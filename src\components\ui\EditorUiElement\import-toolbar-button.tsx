'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { getEditorDOMFromHtmlString } from '@udecode/plate';
import { MarkdownPlugin } from '@udecode/plate-markdown';
import { useEditorRef } from '@udecode/plate/react';
import { ArrowUpToLineIcon, ChevronDownIcon, FileTextIcon, CodeIcon } from 'lucide-react';
import { useFilePicker } from 'use-file-picker';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

type ImportType = 'html' | 'markdown';

export function ImportToolbarButton(props: DropdownMenuProps) {
  const editor = useEditorRef();
  const [open, setOpen] = React.useState(false);

  const getFileNodes = (text: string, type: ImportType) => {
    if (type === 'html') {
      const editorNode = getEditorDOMFromHtmlString(text);
      const nodes = editor.api.html.deserialize({
        element: editorNode,
      });

      return nodes;
    }

    if (type === 'markdown') {
      return editor.getApi(MarkdownPlugin).markdown.deserialize(text);
    }

    return [];
  };

  const { openFilePicker: openMdFilePicker } = useFilePicker({
    accept: ['.md', '.mdx'],
    multiple: false,
    onFilesSelected: async data => {
      if (!data.plainFiles?.length) return;
      const text = await data.plainFiles[0].text();

      const nodes = getFileNodes(text, 'markdown');

      editor.tf.insertNodes(nodes);
    },
  });

  const { openFilePicker: openHtmlFilePicker } = useFilePicker({
    accept: ['text/html'],
    multiple: false,
    onFilesSelected: async data => {
      if (!data.plainFiles?.length) return;
      const text = await data.plainFiles[0].text();

      const nodes = getFileNodes(text, 'html');

      editor.tf.insertNodes(nodes);
    },
  });

  const importItems = {
    html: {
      icon: <CodeIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Import from HTML',
      description: 'Import content from HTML files',
      color: 'text-orange-600 dark:text-orange-400',
      action: openHtmlFilePicker,
    },
    markdown: {
      icon: <FileTextIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Import from Markdown',
      description: 'Import content from Markdown files',
      color: 'text-blue-600 dark:text-blue-400',
      action: openMdFilePicker,
    },
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Import"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
              <ArrowUpToLineIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        <DropdownMenuItem
          className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg"
          onSelect={() => {
            importItems.html.action();
            setOpen(false);
          }}
        >
          <div className={cn('theme-transition', importItems.html.color)}>
            {importItems.html.icon}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm theme-text-primary">
                {importItems.html.label}
              </span>
            </div>
            <p className="text-xs theme-text-secondary">{importItems.html.description}</p>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg"
          onSelect={() => {
            importItems.markdown.action();
            setOpen(false);
          }}
        >
          <div className={cn('theme-transition', importItems.markdown.color)}>
            {importItems.markdown.icon}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm theme-text-primary">
                {importItems.markdown.label}
              </span>
            </div>
            <p className="text-xs theme-text-secondary">{importItems.markdown.description}</p>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
