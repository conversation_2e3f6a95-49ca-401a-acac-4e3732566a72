'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAInstallState {
  canInstall: boolean;
  isInstalled: boolean;
  isInstalling: boolean;
  installError: string | null;
  deferredPrompt: BeforeInstallPromptEvent | null;
}

interface PWAUpdateState {
  updateAvailable: boolean;
  isUpdating: boolean;
  updateError: string | null;
  newServiceWorker: ServiceWorker | null;
}

interface PWAOfflineState {
  isOnline: boolean;
  wasOffline: boolean;
  offlineReady: boolean;
  needRefresh: boolean;
}

interface PWANotificationState {
  isSubscribed: boolean;
  isSubscribing: boolean;
  subscription: PushSubscription | null;
  notificationError: string | null;
  permission: NotificationPermission;
}

interface PWABackgroundSyncState {
  isRegistered: boolean;
  pendingRequests: number;
  lastSyncTime: Date | null;
  syncError: string | null;
}

interface PWACapabilities {
  hasServiceWorker: boolean;
  hasPushNotifications: boolean;
  hasBackgroundSync: boolean;
  hasPeriodicSync: boolean;
  hasWebShare: boolean;
  hasBeforeInstallPrompt: boolean;
  hasNotificationAPI: boolean;
  hasStorageAPI: boolean;
  isStandalone: boolean;
  isDisplayModeStandalone: boolean;
}

interface PWAAnalyticsEvent {
  event: string;
  data: Record<string, any>;
  timestamp: string;
}

interface UsePWAOptions {
  enableAnalytics?: boolean;
  enableBackgroundSync?: boolean;
  enablePushNotifications?: boolean;
  autoRegisterServiceWorker?: boolean;
  serviceWorkerPath?: string;
  onInstallPrompt?: (canInstall: boolean) => void;
  onInstallSuccess?: () => void;
  onInstallError?: (error: string) => void;
  onUpdateAvailable?: (registration: ServiceWorkerRegistration) => void;
  onOfflineStatusChange?: (isOnline: boolean) => void;
  onNotificationPermissionChange?: (permission: NotificationPermission) => void;
}

type NotificationPermission = 'default' | 'denied' | 'granted';

const usePWA = (options: UsePWAOptions = {}) => {
  const {
    enableAnalytics = true,
    enableBackgroundSync = true,
    autoRegisterServiceWorker = true,
    serviceWorkerPath = '/sw.js',
    onInstallPrompt,
    onInstallSuccess,
    onInstallError,
    onUpdateAvailable,
    onOfflineStatusChange,
    onNotificationPermissionChange,
  } = options;

  // State management
  const [installState, setInstallState] = useState<PWAInstallState>({
    canInstall: false,
    isInstalled: false,
    isInstalling: false,
    installError: null,
    deferredPrompt: null,
  });

  const [updateState, setUpdateState] = useState<PWAUpdateState>({
    updateAvailable: false,
    isUpdating: false,
    updateError: null,
    newServiceWorker: null,
  });

  const [offlineState, setOfflineState] = useState<PWAOfflineState>({
    isOnline: navigator.onLine,
    wasOffline: false,
    offlineReady: false,
    needRefresh: false,
  });

  const [notificationState, setNotificationState] = useState<PWANotificationState>({
    isSubscribed: false,
    isSubscribing: false,
    subscription: null,
    notificationError: null,
    permission: 'default',
  });

  const [backgroundSyncState, setBackgroundSyncState] = useState<PWABackgroundSyncState>({
    isRegistered: false,
    pendingRequests: 0,
    lastSyncTime: null,
    syncError: null,
  });

  const [capabilities, setCapabilities] = useState<PWACapabilities>({
    hasServiceWorker: false,
    hasPushNotifications: false,
    hasBackgroundSync: false,
    hasPeriodicSync: false,
    hasWebShare: false,
    hasBeforeInstallPrompt: false,
    hasNotificationAPI: false,
    hasStorageAPI: false,
    isStandalone: false,
    isDisplayModeStandalone: false,
  });

  const registrationRef = useRef<ServiceWorkerRegistration | null>(null);
  const analyticsQueueRef = useRef<PWAAnalyticsEvent[]>([]);

  // Detect PWA capabilities
  const detectCapabilities = useCallback(() => {
    const newCapabilities: PWACapabilities = {
      hasServiceWorker: 'serviceWorker' in navigator,
      hasPushNotifications: 'PushManager' in window && 'Notification' in window,
      hasBackgroundSync: 'serviceWorker' in navigator && 'SyncManager' in window,
      hasPeriodicSync: 'serviceWorker' in navigator && 'PeriodicSyncManager' in window,
      hasWebShare: 'share' in navigator,
      hasBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window,
      hasNotificationAPI: 'Notification' in window,
      hasStorageAPI: 'storage' in navigator && 'estimate' in navigator.storage,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      isDisplayModeStandalone:
        window.matchMedia('(display-mode: standalone)').matches ||
        (window.navigator as any).standalone === true,
    };

    setCapabilities(newCapabilities);

    if (enableAnalytics) {
      trackAnalytics('pwa_capabilities_detected', newCapabilities);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enableAnalytics]);

  // Analytics tracking
  const trackAnalytics = useCallback(
    (event: string, data: Record<string, any>) => {
      if (!enableAnalytics) return;

      const analyticsEvent: PWAAnalyticsEvent = {
        event,
        data: {
          ...data,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        },
        timestamp: new Date().toISOString(),
      };

      analyticsQueueRef.current.push(analyticsEvent);

      // Store in localStorage for persistence
      try {
        const existingEvents = JSON.parse(localStorage.getItem('pwa_analytics') || '[]');
        existingEvents.push(analyticsEvent);
        localStorage.setItem('pwa_analytics', JSON.stringify(existingEvents));
      } catch (error) {
        try {
          // toast.warning expects a string, not an error object
          toast.warning(
            'Failed to store analytics event: ' +
              (error instanceof Error ? error.message : String(error))
          );
        } catch (e) {
          // fallback: ignore
        }
      }

      // Try to send analytics if online
      if (navigator.onLine) {
        sendAnalytics();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [enableAnalytics]
  );

  // Send analytics to server
  const sendAnalytics = useCallback(async () => {
    if (!enableAnalytics || analyticsQueueRef.current.length === 0) return;

    try {
      const events = [...analyticsQueueRef.current];
      analyticsQueueRef.current = [];

      const response = await fetch('/api/analytics/pwa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ events }),
      });

      if (response.ok) {
        // Clear stored events on successful send
        const storedEvents = JSON.parse(localStorage.getItem('pwa_analytics') || '[]');
        const remainingEvents = storedEvents.slice(events.length);
        localStorage.setItem('pwa_analytics', JSON.stringify(remainingEvents));
      } else {
        // Put events back in queue if sending failed
        analyticsQueueRef.current.unshift(...events);
      }
    } catch (error: any) {
      toast.warning('Failed to send analytics:', error);
      // Events remain in localStorage for retry
    }
  }, [enableAnalytics]);

  // Service Worker registration
  const registerServiceWorker = useCallback(async () => {
    if (!capabilities.hasServiceWorker) {
      toast.warning('Service Worker not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register(serviceWorkerPath, {
        scope: '/',
      });

      registrationRef.current = registration;

      // Check for existing service worker
      if (registration.active) {
        setOfflineState(prev => ({ ...prev, offlineReady: true }));
      }

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          setUpdateState(prev => ({
            ...prev,
            updateAvailable: true,
            newServiceWorker: newWorker,
          }));

          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              onUpdateAvailable?.(registration);
              trackAnalytics('pwa_update_available', { version: 'unknown' });
            }
          });
        }
      });

      // Listen for controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        setOfflineState(prev => ({ ...prev, needRefresh: true }));
        trackAnalytics('pwa_controller_changed', {});
      });

      trackAnalytics('pwa_service_worker_registered', {
        scope: registration.scope,
        updateViaCache: registration.updateViaCache,
      });

      return registration;
    } catch (error: any) {
      toast.error('Service Worker registration failed:', error);
      trackAnalytics('pwa_service_worker_registration_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }, [capabilities.hasServiceWorker, serviceWorkerPath, onUpdateAvailable, trackAnalytics]);

  // Handle install prompt
  const handleInstallPrompt = useCallback(
    (event: BeforeInstallPromptEvent) => {
      event.preventDefault();
      setInstallState(prev => ({
        ...prev,
        canInstall: true,
        deferredPrompt: event,
      }));

      onInstallPrompt?.(true);
      trackAnalytics('pwa_install_prompt_shown', {
        platforms: event.platforms,
      });
    },
    [onInstallPrompt, trackAnalytics]
  );

  // Install PWA
  const installPWA = useCallback(async () => {
    if (!installState.deferredPrompt) {
      const error = 'No install prompt available';
      setInstallState(prev => ({ ...prev, installError: error }));
      onInstallError?.(error);
      return false;
    }

    setInstallState(prev => ({ ...prev, isInstalling: true, installError: null }));

    try {
      await installState.deferredPrompt.prompt();
      const { outcome } = await installState.deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        setInstallState(prev => ({
          ...prev,
          isInstalling: false,
          canInstall: false,
          isInstalled: true,
          deferredPrompt: null,
        }));
        onInstallSuccess?.();
        trackAnalytics('pwa_installed', { outcome });
        return true;
      } else {
        setInstallState(prev => ({
          ...prev,
          isInstalling: false,
          canInstall: false,
          deferredPrompt: null,
        }));
        trackAnalytics('pwa_install_dismissed', { outcome });
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Installation failed';
      setInstallState(prev => ({
        ...prev,
        isInstalling: false,
        installError: errorMessage,
      }));
      onInstallError?.(errorMessage);
      trackAnalytics('pwa_install_error', { error: errorMessage });
      return false;
    }
  }, [installState.deferredPrompt, onInstallSuccess, onInstallError, trackAnalytics]);

  // Update PWA
  const updatePWA = useCallback(async () => {
    if (!updateState.newServiceWorker) {
      const error = 'No update available';
      setUpdateState(prev => ({ ...prev, updateError: error }));
      return false;
    }

    setUpdateState(prev => ({ ...prev, isUpdating: true, updateError: null }));

    try {
      updateState.newServiceWorker.postMessage({ type: 'SKIP_WAITING' });

      // Wait for controller change
      await new Promise<void>(resolve => {
        const handleControllerChange = () => {
          navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
          resolve();
        };
        navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
      });

      setUpdateState(prev => ({
        ...prev,
        isUpdating: false,
        updateAvailable: false,
        newServiceWorker: null,
      }));

      trackAnalytics('pwa_updated', {});

      // Refresh the page to use new service worker
      window.location.reload();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Update failed';
      setUpdateState(prev => ({
        ...prev,
        isUpdating: false,
        updateError: errorMessage,
      }));
      trackAnalytics('pwa_update_error', { error: errorMessage });
      return false;
    }
  }, [updateState.newServiceWorker, trackAnalytics]);

  // Push notification management
  const subscribeToPushNotifications = useCallback(async () => {
    if (!capabilities.hasPushNotifications || !registrationRef.current) {
      const error = 'Push notifications not supported';
      setNotificationState(prev => ({ ...prev, notificationError: error }));
      return null;
    }

    setNotificationState(prev => ({ ...prev, isSubscribing: true, notificationError: null }));

    try {
      // Request permission
      const permission = await Notification.requestPermission();
      setNotificationState(prev => ({ ...prev, permission }));
      onNotificationPermissionChange?.(permission);

      if (permission !== 'granted') {
        const error = 'Notification permission denied';
        setNotificationState(prev => ({
          ...prev,
          isSubscribing: false,
          notificationError: error,
        }));
        trackAnalytics('pwa_notification_permission_denied', { permission });
        return null;
      }

      // Get VAPID public key from server
      const vapidResponse = await fetch('/api/push/vapid-key');
      const { publicKey } = await vapidResponse.json();

      // Subscribe to push notifications
      const subscription = await registrationRef.current.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: publicKey,
      });

      // Send subscription to server
      await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscription),
      });

      setNotificationState(prev => ({
        ...prev,
        isSubscribing: false,
        isSubscribed: true,
        subscription,
      }));

      trackAnalytics('pwa_push_subscribed', {
        endpoint: subscription.endpoint,
      });

      return subscription;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Subscription failed';
      setNotificationState(prev => ({
        ...prev,
        isSubscribing: false,
        notificationError: errorMessage,
      }));
      trackAnalytics('pwa_push_subscription_error', { error: errorMessage });
      return null;
    }
  }, [capabilities.hasPushNotifications, onNotificationPermissionChange, trackAnalytics]);

  // Unsubscribe from push notifications
  const unsubscribeFromPushNotifications = useCallback(async () => {
    if (!notificationState.subscription) return false;

    try {
      await notificationState.subscription.unsubscribe();

      // Notify server
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ endpoint: notificationState.subscription.endpoint }),
      });

      setNotificationState(prev => ({
        ...prev,
        isSubscribed: false,
        subscription: null,
      }));

      trackAnalytics('pwa_push_unsubscribed', {});
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unsubscription failed';
      setNotificationState(prev => ({ ...prev, notificationError: errorMessage }));
      trackAnalytics('pwa_push_unsubscription_error', { error: errorMessage });
      return false;
    }
  }, [notificationState.subscription, trackAnalytics]);

  // Background sync management
  const registerBackgroundSync = useCallback(
    async (tag: string) => {
      if (!capabilities.hasBackgroundSync || !registrationRef.current) {
        const error = 'Background sync not supported';
        setBackgroundSyncState(prev => ({ ...prev, syncError: error }));
        if (options.onInstallError) options.onInstallError(error); // Use callback if provided
        return false;
      }

      try {
        // Type guard for sync
        const reg = registrationRef.current as any;
        if (reg.sync && typeof reg.sync.register === 'function') {
          await reg.sync.register(tag);
          setBackgroundSyncState(prev => ({
            ...prev,
            isRegistered: true,
            syncError: null,
          }));
          trackAnalytics('pwa_background_sync_registered', { tag });
          return true;
        } else {
          const error = 'Background sync not supported by this browser';
          setBackgroundSyncState(prev => ({ ...prev, syncError: error }));
          if (options.onInstallError) options.onInstallError(error);
          return false;
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Background sync registration failed';
        setBackgroundSyncState(prev => ({ ...prev, syncError: errorMessage }));
        if (options.onInstallError) options.onInstallError(errorMessage);
        trackAnalytics('pwa_background_sync_error', { error: errorMessage, tag });
        return false;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [capabilities.hasBackgroundSync, trackAnalytics, options.onInstallError]
  );

  // Update pending requests count
  const updatePendingRequests = useCallback(() => {
    try {
      const offlineRequests = JSON.parse(localStorage.getItem('offline_requests') || '[]');
      setBackgroundSyncState(prev => ({
        ...prev,
        pendingRequests: offlineRequests.length,
      }));
    } catch (error: any) {
      toast.warning('Failed to update pending requests count:', error);
    }
  }, []);

  // Handle online/offline events
  const handleOnlineStatusChange = useCallback(
    (isOnline: boolean) => {
      setOfflineState(prev => ({
        ...prev,
        isOnline,
        wasOffline: !isOnline || prev.wasOffline,
      }));

      onOfflineStatusChange?.(isOnline);
      trackAnalytics('pwa_connection_change', { isOnline });

      if (isOnline) {
        // Send queued analytics
        sendAnalytics();

        // Update pending requests
        updatePendingRequests();

        // Register background sync for pending requests
        if (enableBackgroundSync) {
          registerBackgroundSync('offline-requests');
        }
      }
    },
    [
      onOfflineStatusChange,
      trackAnalytics,
      sendAnalytics,
      updatePendingRequests,
      enableBackgroundSync,
      registerBackgroundSync,
    ]
  );

  // Initialize PWA
  useEffect(() => {
    detectCapabilities();
  }, [detectCapabilities]);

  // Service worker registration
  useEffect(() => {
    if (autoRegisterServiceWorker && capabilities.hasServiceWorker) {
      registerServiceWorker();
    }
  }, [autoRegisterServiceWorker, capabilities.hasServiceWorker, registerServiceWorker]);

  // Install prompt event listener
  useEffect(() => {
    const handleBeforeInstallPrompt = (event: Event) => {
      handleInstallPrompt(event as BeforeInstallPromptEvent);
    };

    const handleAppInstalled = () => {
      setInstallState(prev => ({
        ...prev,
        isInstalled: true,
        canInstall: false,
        deferredPrompt: null,
      }));
      trackAnalytics('pwa_app_installed', {});
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [handleInstallPrompt, trackAnalytics]);

  // Online/offline event listeners
  useEffect(() => {
    const handleOnline = () => handleOnlineStatusChange(true);
    const handleOffline = () => handleOnlineStatusChange(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial status check
    handleOnlineStatusChange(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleOnlineStatusChange]);

  // Check notification permission on mount
  useEffect(() => {
    if (capabilities.hasNotificationAPI) {
      const permission = Notification.permission;
      setNotificationState(prev => ({ ...prev, permission }));

      // Check if already subscribed
      if (registrationRef.current) {
        registrationRef.current.pushManager.getSubscription().then(subscription => {
          if (subscription) {
            setNotificationState(prev => ({
              ...prev,
              isSubscribed: true,
              subscription,
            }));
          }
        });
      }
    }
  }, [capabilities.hasNotificationAPI]);

  // Service worker message listener
  useEffect(() => {
    if (!capabilities.hasServiceWorker) return;

    const handleMessage = (event: MessageEvent) => {
      const { data } = event;

      if (data?.type === 'SYNC_COMPLETE') {
        setBackgroundSyncState(prev => ({
          ...prev,
          lastSyncTime: new Date(),
          syncError: null,
        }));
        updatePendingRequests();
        trackAnalytics('pwa_sync_complete', data);
      } else if (data?.type === 'SYNC_FAILED') {
        setBackgroundSyncState(prev => ({
          ...prev,
          syncError: data.error || 'Sync failed',
        }));
        trackAnalytics('pwa_sync_failed', data);
      }
    };

    navigator.serviceWorker.addEventListener('message', handleMessage);

    return () => {
      navigator.serviceWorker.removeEventListener('message', handleMessage);
    };
  }, [capabilities.hasServiceWorker, updatePendingRequests, trackAnalytics]);

  // Periodic updates
  useEffect(() => {
    const interval = setInterval(() => {
      updatePendingRequests();

      // Send queued analytics if online
      if (offlineState.isOnline) {
        sendAnalytics();
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [updatePendingRequests, offlineState.isOnline, sendAnalytics]);

  return {
    // Install state
    canInstall: installState.canInstall,
    isInstalled: installState.isInstalled,
    isInstalling: installState.isInstalling,
    installError: installState.installError,

    // Update state
    updateAvailable: updateState.updateAvailable,
    isUpdating: updateState.isUpdating,
    updateError: updateState.updateError,

    // Offline state
    isOnline: offlineState.isOnline,
    wasOffline: offlineState.wasOffline,
    offlineReady: offlineState.offlineReady,
    needRefresh: offlineState.needRefresh,

    // Notification state
    notificationPermission: notificationState.permission,
    isNotificationSubscribed: notificationState.isSubscribed,
    isSubscribingToNotifications: notificationState.isSubscribing,
    notificationError: notificationState.notificationError,

    // Background sync state
    isBackgroundSyncRegistered: backgroundSyncState.isRegistered,
    pendingRequests: backgroundSyncState.pendingRequests,
    lastSyncTime: backgroundSyncState.lastSyncTime,
    syncError: backgroundSyncState.syncError,

    // Capabilities
    capabilities,

    // Methods
    installPWA,
    updatePWA,
    subscribeToPushNotifications,
    unsubscribeFromPushNotifications,
    registerBackgroundSync,
    updatePendingRequests,
    sendAnalytics,
    trackAnalytics,

    // Service worker registration
    serviceWorkerRegistration: registrationRef.current,
  };
};

export default usePWA;
export type { PWACapabilities, PWAAnalyticsEvent, UsePWAOptions };
