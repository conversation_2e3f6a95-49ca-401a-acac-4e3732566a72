import mongoose from 'mongoose';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add MONGODB_URI to .env file');
}

let isConnected = false;

export const connectDB = async () => {
  if (isConnected && mongoose.connection.readyState === 1) {
    return;
  }

  try {
    const options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 3000,
      socketTimeoutMS: 30000,
      family: 4,
      bufferCommands: false,
      maxIdleTimeMS: 30000,
      retryWrites: true,
    };

    await mongoose.connect(process.env.MONGODB_URI as string, options);

    isConnected = true;

    mongoose.connection.on('connected', () => {
      isConnected = true;
    });

    mongoose.connection.on('disconnected', () => {
      isConnected = false;
    });

    mongoose.connection.on('error', () => {
      isConnected = false;
    });
  } catch (error: any) {
    isConnected = false;
    throw error;
  }
};
