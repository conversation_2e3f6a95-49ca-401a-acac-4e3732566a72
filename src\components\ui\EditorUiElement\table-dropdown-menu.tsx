'use client';

import * as React from 'react';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { TablePlugin, useTableMergeState } from '@udecode/plate-table/react';
import { useEditorPlugin, useEditorSelector } from '@udecode/plate/react';
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Combine,
  Grid3x3Icon,
  Table,
  Trash2Icon,
  Ungroup,
  XIcon,
  ChevronDownIcon,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

export function TableDropdownMenu(props: DropdownMenuProps) {
  const tableSelected = useEditorSelector(
    editor => editor.api.some({ match: { type: TablePlugin.key } }),
    []
  );

  const { editor, tf } = useEditorPlugin(TablePlugin);
  const [open, setOpen] = React.useState(false);
  const mergeState = useTableMergeState();

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Table"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className="theme-transition text-blue-600 dark:text-blue-400">
              <Table className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        <DropdownMenuGroup>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
              <div className="theme-transition text-blue-600 dark:text-blue-400">
                <Grid3x3Icon className="h-3 w-3 sm:h-4 sm:w-4" />
              </div>
              <span className="font-medium text-sm theme-text-primary">Table</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent className="m-0 p-0">
              <TablePicker />
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              disabled={!tableSelected}
            >
              <div className="theme-transition text-green-600 dark:text-green-400">
                <Combine className="h-3 w-3 sm:h-4 sm:w-4" />
              </div>
              <span className="font-medium text-sm theme-text-primary">Cell</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!mergeState.canMerge}
                onSelect={() => {
                  tf.table.merge();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-purple-600 dark:text-purple-400">
                  <Combine className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Merge cells</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!mergeState.canSplit}
                onSelect={() => {
                  tf.table.split();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-orange-600 dark:text-orange-400">
                  <Ungroup className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Split cell</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              disabled={!tableSelected}
            >
              <div className="theme-transition text-indigo-600 dark:text-indigo-400">
                <ArrowUp className="h-3 w-3 sm:h-4 sm:w-4" />
              </div>
              <span className="font-medium text-sm theme-text-primary">Row</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.insert.tableRow({ before: true });
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-blue-600 dark:text-blue-400">
                  <ArrowUp className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Insert row before</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.insert.tableRow();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-green-600 dark:text-green-400">
                  <ArrowDown className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Insert row after</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.remove.tableRow();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-red-600 dark:text-red-400">
                  <XIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Delete row</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              disabled={!tableSelected}
            >
              <div className="theme-transition text-cyan-600 dark:text-cyan-400">
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4" />
              </div>
              <span className="font-medium text-sm theme-text-primary">Column</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.insert.tableColumn({ before: true });
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-blue-600 dark:text-blue-400">
                  <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Insert column before</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.insert.tableColumn();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-green-600 dark:text-green-400">
                  <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Insert column after</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
                disabled={!tableSelected}
                onSelect={() => {
                  tf.remove.tableColumn();
                  editor.tf.focus();
                }}
              >
                <div className="theme-transition text-red-600 dark:text-red-400">
                  <XIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
                <span className="font-medium text-sm theme-text-primary">Delete column</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuItem
            className="theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg min-w-[180px]"
            disabled={!tableSelected}
            onSelect={() => {
              tf.remove.table();
              editor.tf.focus();
            }}
          >
            <div className="theme-transition text-red-600 dark:text-red-400">
              <Trash2Icon className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
            <span className="font-medium text-sm theme-text-primary">Delete table</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function TablePicker() {
  const { editor, tf } = useEditorPlugin(TablePlugin);

  const [tablePicker, setTablePicker] = React.useState({
    grid: Array.from({ length: 8 }, () => Array.from({ length: 8 }).fill(0)),
    size: { colCount: 0, rowCount: 0 },
  });

  const onCellMove = (rowIndex: number, colIndex: number) => {
    const newGrid = [...tablePicker.grid];

    for (let i = 0; i < newGrid.length; i++) {
      for (let j = 0; j < newGrid[i].length; j++) {
        newGrid[i][j] = i >= 0 && i <= rowIndex && j >= 0 && j <= colIndex ? 1 : 0;
      }
    }

    setTablePicker({
      grid: newGrid,
      size: { colCount: colIndex + 1, rowCount: rowIndex + 1 },
    });
  };

  return (
    <div
      className="m-0 flex! flex-col p-0"
      onClick={() => {
        tf.insert.table(tablePicker.size, { select: true });
        editor.tf.focus();
      }}
    >
      <div className="grid size-[130px] grid-cols-8 gap-0.5 p-1">
        {tablePicker.grid.map((rows, rowIndex) =>
          rows.map((value, columIndex) => {
            return (
              <div
                key={`(${rowIndex},${columIndex})`}
                className={cn(
                  'col-span-1 size-3 border border-solid bg-secondary',
                  !!value && 'border-current'
                )}
                onMouseMove={() => {
                  onCellMove(rowIndex, columIndex);
                }}
              />
            );
          })
        )}
      </div>

      <div className="text-center text-xs text-current">
        {tablePicker.size.rowCount} x {tablePicker.size.colCount}
      </div>
    </div>
  );
}
