'use client';

import * as React from 'react';

import {
  useIndentTodoToolBarButton,
  useIndentTodoToolBarButtonState,
} from '@udecode/plate-indent-list/react';
import { ListTodoIcon } from 'lucide-react';

import { Too<PERSON>barButton } from './toolbar';
import { cn } from '@/lib/utils';

export function IndentTodoToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const state = useIndentTodoToolBarButtonState({ nodeType: 'todo' });
  const { props: buttonProps } = useIndentTodoToolBarButton(state);

  return (
    <ToolbarButton {...props} {...buttonProps} tooltip="Todo">
      <div className={cn('theme-transition', 'text-blue-600 dark:text-blue-400')}>
        <ListTodoIcon className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
