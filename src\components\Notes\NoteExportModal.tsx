'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import Modal from '../Global/Modal';
import { Download, Loader2, FileText, File, FileImage, CheckCircle2 } from 'lucide-react';
import { NoteExportUtils, type ExportNote } from '@/utils/noteExport';

interface NoteExportModalProps {
  note: ExportNote;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  className?: string;
}

export const NoteExportModal: React.FC<NoteExportModalProps> = ({
  note,
  isOpen,
  setIsOpen,
  className,
}) => {
  const [exportingFormat, setExportingFormat] = useState<string | null>(null);
  const [completedExports, setCompletedExports] = useState<Set<string>>(new Set());

  const exportFormats = [
    {
      value: 'pdf',
      label: 'PDF Document',
      description: 'High-quality portable document with preserved formatting and styling',
      icon: FileImage,
      color: 'text-red-500',
      bgColor: 'bg-red-50 dark:bg-red-950/20',
      borderColor: 'border-red-200 dark:border-red-800/30',
      hoverBg: 'hover:bg-red-50/80 dark:hover:bg-red-950/30',
    },
    {
      value: 'markdown',
      label: 'Markdown File',
      description: 'Developer-friendly format with syntax highlighting and original structure',
      icon: FileText,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
      borderColor: 'border-blue-200 dark:border-blue-800/30',
      hoverBg: 'hover:bg-blue-50/80 dark:hover:bg-blue-950/30',
    },
    {
      value: 'text',
      label: 'Plain Text',
      description: 'Clean, simple text format compatible with any text editor or system',
      icon: File,
      color: 'text-slate-500',
      bgColor: 'bg-slate-50 dark:bg-slate-950/20',
      borderColor: 'border-slate-200 dark:border-slate-800/30',
      hoverBg: 'hover:bg-slate-50/80 dark:hover:bg-slate-950/30',
    },
  ];

  const handleExport = async (format: string) => {
    setExportingFormat(format);
    try {
      await NoteExportUtils.exportNote(note, format);
      setCompletedExports(prev => new Set([...prev, format]));
      setTimeout(() => {
        if (completedExports.size === 0) {
          setIsOpen(false);
        }
      }, 1500);
    } catch (error) {
      // Error is already handled in the export utility
    } finally {
      setExportingFormat(null);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    // Reset completed exports when closing
    setTimeout(() => setCompletedExports(new Set()), 300);
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <div className={`overflow-hidden rounded-xl ${className || ''}`}>
        <div className="border-b pb-4 mb-2">
          <div className="flex items-center gap-3">
            <div className="bg-slate-100 dark:bg-slate-800 p-2.5 rounded-lg">
              <Download className="h-5 w-5 text-slate-700 dark:text-slate-300" />
            </div>
            <div>
              <h2 className="text-xl font-semibold theme-text-primary tracking-tight">
                Export Note
              </h2>
              <p className="text-sm theme-text-secondary mt-0.5">"{note.title}"</p>
            </div>
          </div>
        </div>

        <div className="mt-2">
          <div className="space-y-3">
            {exportFormats.map(format => {
              const IconComponent = format.icon;
              const isExporting = exportingFormat === format.value;
              const isCompleted = completedExports.has(format.value);
              const isDisabled = exportingFormat !== null && !isExporting;

              return (
                <div
                  key={format.value}
                  className={`
                    group relative overflow-hidden rounded-xl border transition-all duration-200
                    ${
                      isCompleted
                        ? 'border-green-200 dark:border-green-800/50 bg-green-50/30 dark:bg-green-950/10'
                        : isExporting
                          ? `${format.borderColor} ${format.bgColor}`
                          : isDisabled
                            ? 'border-gray-100 dark:border-gray-800 bg-gray-50/30 dark:bg-gray-950/10 opacity-60'
                            : `border-gray-200 dark:border-gray-700 hover:${format.borderColor} ${format.hoverBg}`
                    }
                  `}
                >
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`
                        relative flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200
                        ${
                          isCompleted
                            ? 'bg-green-100 dark:bg-green-900/20'
                            : isExporting
                              ? format.bgColor
                              : 'bg-gray-100 dark:bg-gray-800 group-hover:' + format.bgColor
                        }
                      `}
                      >
                        {isCompleted ? (
                          <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                        ) : (
                          <IconComponent
                            className={`h-5 w-5 transition-colors ${
                              isExporting
                                ? format.color
                                : isDisabled
                                  ? 'text-gray-400'
                                  : `text-gray-500 group-hover:${format.color}`
                            }`}
                          />
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="text-sm font-semibold theme-text-primary">
                            {format.label}
                          </h4>
                          {isCompleted && (
                            <span className="px-1.5 py-0.5 text-[10px] font-medium bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-full">
                              Done
                            </span>
                          )}
                        </div>
                        <p className="text-xs theme-text-secondary">{format.description}</p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExport(format.value)}
                      disabled={isDisabled || isCompleted}
                      className={`
                        min-w-[100px] rounded-lg font-medium transition-all duration-200
                        ${
                          isCompleted
                            ? 'bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300'
                            : isExporting
                              ? `${format.bgColor} ${format.borderColor} ${format.color}`
                              : 'theme-button-secondary hover:scale-105'
                        }
                      `}
                    >
                      {isCompleted ? (
                        <CheckCircle2 className="h-4 w-4" />
                      ) : isExporting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Download className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {isExporting && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-200 dark:bg-gray-700 overflow-hidden">
                      <div className={`h-full ${format.bgColor} animate-pulse`}></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex items-center justify-between mt-6 pt-4 border-t theme-divider">
            <div className="flex items-center gap-1.5 text-xs theme-text-secondary">
              <div className="w-1.5 h-1.5 rounded-full bg-green-400 animate-pulse"></div>
              <span>Saving to downloads</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={exportingFormat !== null}
              className="theme-button-ghost rounded-lg px-4"
            >
              {completedExports.size > 0 ? 'Done' : 'Cancel'}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default NoteExportModal;
