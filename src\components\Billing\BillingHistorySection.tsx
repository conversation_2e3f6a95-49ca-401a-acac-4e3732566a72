'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, History, Plus, HardDrive, Crown, Receipt, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { EmptyState } from '../Global/EmptyState';

interface Transaction {
  id: string;
  type: 'storage_purchase' | 'subscription';
  description: string;
  amount: number;
  storageAmount?: number;
  pricePerMB?: number;
  date: string;
  paymentMethod: string;
  status: 'completed' | 'pending' | 'failed';
}

interface BillingHistorySectionProps {
  billingHistory?: Transaction[];
  isLoadingHistory?: boolean;
  formatPrice?: (amount: number) => string;
  formatMB?: (bytes: number) => number;
  onBuyStorage?: () => void;
  onDownloadReceipt?: (transactionId: string) => void;
  downloadingReceipt?: string | null;
}

export const BillingHistorySection: React.FC<BillingHistorySectionProps> = ({
  billingHistory,
  isLoadingHistory = false,
  formatPrice = amount => `₹${amount}`,
  formatMB = mb => mb.toString(),
  onBuyStorage,
  onDownloadReceipt,
  downloadingReceipt = null,
}) => {
  const getTransactionIcon = (type: string) => {
    return type === 'storage_purchase' ? (
      <HardDrive className="h-5 w-5 text-blue-600 dark:text-blue-400 theme-transition" />
    ) : (
      <Crown className="h-5 w-5 text-purple-600 dark:text-purple-400 theme-transition" />
    );
  };

  const getTransactionBgColor = (type: string) => {
    return type === 'storage_purchase'
      ? 'bg-blue-100 dark:bg-blue-900/30'
      : 'bg-purple-100 dark:bg-purple-900/30';
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400 border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20';
      case 'failed':
        return 'text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20';
      default:
        return 'theme-badge-secondary';
    }
  };

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="flex items-center justify-between p-4 theme-surface rounded-lg theme-border"
        >
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-10 rounded-md" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right space-y-1">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-12" />
            </div>
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </motion.div>
      ))}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="dashboard-card hover-reveal">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 dashboard-card-title">
                <Receipt className="h-5 w-5 text-indigo-600 dark:text-indigo-400 theme-transition" />
                Billing History
              </CardTitle>
              <CardDescription className="dashboard-card-description">
                View your subscription and purchase history
              </CardDescription>
            </div>
            {billingHistory && billingHistory.length > 0 && (
              <Badge variant="secondary" className="theme-badge-secondary">
                {billingHistory.length} transaction{billingHistory.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="theme-scrollbar">
          {isLoadingHistory ? (
            <LoadingSkeleton />
          ) : billingHistory && billingHistory.length > 0 ? (
            <div className="space-y-4">
              {billingHistory.map((transaction, index) => (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 theme-surface rounded-lg theme-border hover-reveal theme-transition"
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={`p-2 rounded-md ${getTransactionBgColor(transaction.type)} theme-transition`}
                    >
                      {getTransactionIcon(transaction.type)}
                    </div>
                    <div>
                      <p className="font-medium theme-text-primary">{transaction.description}</p>
                      <p className="text-sm theme-text-secondary">
                        {transaction.type === 'storage_purchase'
                          ? `${formatMB(transaction.storageAmount || 0)}MB storage purchased`
                          : 'Subscription payment'}
                      </p>
                      <p className="text-xs theme-text-secondary">
                        {new Date(transaction.date).toLocaleDateString()} •{' '}
                        {transaction.paymentMethod}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium theme-text-primary">
                        {formatPrice(transaction.amount)}
                      </p>
                      {transaction.type === 'storage_purchase' && transaction.pricePerMB && (
                        <p className="text-xs theme-text-secondary">₹{transaction.pricePerMB}/MB</p>
                      )}
                    </div>
                    <Badge
                      variant="outline"
                      className={`${getStatusBadgeClass(transaction.status)} theme-transition`}
                    >
                      {transaction.status}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onDownloadReceipt?.(transaction.id)}
                      disabled={downloadingReceipt === transaction.id}
                      className="h-8 w-8 theme-button-ghost"
                      title="Download Receipt"
                    >
                      {downloadingReceipt === transaction.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Download className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <EmptyState
              type="custom"
              icon={<History className="w-full h-full" />}
              title="No Billing History"
              description="You haven't made any purchases yet. Start by buying additional storage for your organization."
              actions={[
                {
                  label: 'Buy Storage',
                  onClick: onBuyStorage || (() => {}),
                  icon: <Plus className="h-4 w-4" />,
                },
              ]}
              size="md"
              animated={true}
            />
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};
