'use client';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Eye, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import { EmptyState } from '@/components/Global/EmptyState';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import MindMapCanvas from '@/components/MindMap/MindMapCanvas';
import { NodePalette } from '@/components/MindMap/node-palette';
import { SaveButton } from '@/components/MindMap/SaveButton';
import { UnsavedChangesGuard } from '@/components/MindMap/UnsavedChangesGuard';
import { SaveStatusIndicator } from '@/components/MindMap/SaveStatusIndicator';
import { useMindMapEditor } from '@/hooks/useMindMapEditor';
import { getDefaultNodeData } from '@/utils/mindMapTransforms';
import { toast } from 'sonner';
import { useCallback } from 'react';

export default function MindMapEditPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const {
    mindMap,
    isLoading,
    error,
    hasUnsavedChanges,
    isSaving,
    saveError,
    lastSaved,
    addNode,
    save,
  } = useMindMapEditor(id);
  const getStatusColor = (status: string) => {
    const colors = {
      active: 'theme-badge-success',
      draft: 'theme-badge-warning',
      archived: 'theme-badge-secondary',
      template: 'theme-badge-primary',
    };
    return colors[status as keyof typeof colors] || 'theme-badge-secondary';
  };

  const handleBackNavigation = () => {
    if (hasUnsavedChanges) {
      return;
    }
    router.push('/mind-maps');
  };

  const handleViewNavigation = () => {
    if (hasUnsavedChanges) {
      return;
    }
    router.push(`/mind-maps/${id}/view`);
  };
  const handleSave = async () => {
    try {
      await save();
    } catch (error) {
      toast.error('Save failed:');
    }
  };
  const getViewportCenter = useCallback(() => {
    const reactFlowElement = document.querySelector('.react-flow__viewport');
    if (!reactFlowElement) {
      return { x: 400, y: 300 }; // Default fallback
    }

    const containerElement = reactFlowElement.parentElement;
    if (!containerElement) {
      return { x: 400, y: 300 }; // Default fallback
    }

    // Get the transform values from the viewport
    const transform = window.getComputedStyle(reactFlowElement).transform;
    let translateX = 0;
    let translateY = 0;
    let scale = 1;

    if (transform && transform !== 'none') {
      const matrix = transform.match(/matrix\(([^)]+)\)/);
      if (matrix) {
        const values = matrix[1].split(',').map(v => parseFloat(v.trim()));
        scale = values[0] || 1;
        translateX = values[4] || 0;
        translateY = values[5] || 0;
      }
    }

    // Calculate the center of the visible area
    const containerWidth = containerElement.clientWidth;
    const containerHeight = containerElement.clientHeight;

    const centerX = (-translateX + containerWidth / 2) / scale;
    const centerY = (-translateY + containerHeight / 2) / scale;

    return { x: centerX, y: centerY };
  }, []);

  const handleAddNodeFromPalette = (type: string, position: { x: number; y: number }) => {
    try {
      const nodeId = `node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const defaultData = getDefaultNodeData(type);

      const newMindMapNode = {
        id: nodeId,
        type: type as any,
        position,
        size: { width: 200, height: 100 },
        content: {
          text: defaultData.text || defaultData.title || 'New Node',
          ...defaultData,
        },
        style: {
          borderColor: '#cccccc',
          borderWidth: 1,
          borderRadius: 8,
          opacity: 1,
          shadow: false,
          // Add default text styling for text nodes
          ...(type === 'text'
            ? {
                color: '#333333',
                backgroundColor: '#ffffff',
                fontSize: 14,
                fontWeight: 'normal',
              }
            : {}),
        },
        parentId: undefined,
        childIds: [],
        collapsed: false,
        zIndex: 1,
        selected: false,
        dragging: false,
      };

      addNode(newMindMapNode);
    } catch (error) {
      toast.error('Failed to add node:');
    }
  };

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="theme-surface rounded-lg h-full w-full flex items-center justify-center"
      >
        <LoadingSpinner variant="wave" />
      </motion.div>
    );
  }

  if (error || !mindMap) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="theme-surface rounded-lg h-full w-full flex items-center justify-center"
      >
        <EmptyState
          title="Error"
          description="An error occurred while loading the mind map."
          icon="error"
        />
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface rounded-lg h-full w-full flex flex-col"
    >
      <div className="flex-shrink-0 flex items-center justify-between p-4 theme-surface-elevated theme-border rounded-t-lg">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackNavigation}
            className="theme-button-ghost"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Separator orientation="vertical" className="h-6 theme-divider" />
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-semibold theme-text-primary">{mindMap.title}</h1>
            </div>
            <div className="flex items-center gap-2 text-sm theme-text-secondary">
              <Badge className={getStatusColor(mindMap.status)}>{mindMap.status}</Badge>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {mindMap.analytics?.viewCount || 0} views
              </div>
              <span>•</span>
              <span>
                Updated {formatDistanceToNow(new Date(mindMap.updatedAt), { addSuffix: true })}
              </span>
              <span>•</span>
              <SaveStatusIndicator
                hasUnsavedChanges={hasUnsavedChanges}
                isSaving={isSaving}
                saveError={saveError}
                lastSaved={lastSaved}
                compact={true}
              />
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Save Button */}
          <SaveButton
            hasUnsavedChanges={hasUnsavedChanges}
            isSaving={isSaving}
            saveError={saveError}
            onSave={handleSave}
          />

          <Separator orientation="vertical" className="h-6 theme-divider" />

          {/* Edit Mode Indicator */}
          <Badge className="theme-badge-primary">
            <Edit className="w-3 h-3 mr-1" />
            Edit Mode
          </Badge>

          <Separator orientation="vertical" className="h-6 theme-divider" />

          {/* Actions */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleViewNavigation}
            className="theme-button-ghost"
          >
            <Eye className="w-4 h-4 mr-2" />
            View
          </Button>
        </div>
      </div>

      <div className="flex-1 min-h-0 flex">
        <div className="flex-shrink-0 w-80 theme-border-r">
          <NodePalette onAddNode={handleAddNodeFromPalette} getViewportCenter={getViewportCenter} />
        </div>
        <div className="flex-1 min-h-0 relative">
          <MindMapCanvas isEditing={true} />
        </div>
      </div>
      {mindMap && <UnsavedChangesGuard hasUnsavedChanges={hasUnsavedChanges} />}
    </motion.div>
  );
}
