import mongoose, { Document, Schema, Model } from 'mongoose';

export interface IRole extends Document {
  name: string;
  displayName: string;
  description?: string;
  type: 'system' | 'organization' | 'project' | 'custom';
  permissions: Record<string, Record<string, boolean>>;
  restrictions?: {
    maxUsers?: number;
    maxProjects?: number;
    timeRestriction?: {
      startTime?: Date;
      endTime?: Date;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestriction?: string[];
    locationRestriction?: {
      countries?: string[];
      regions?: string[];
    };
    featureRestrictions?: string[];
  };
  metadata?: {
    color?: string;
    icon?: string;
    badge?: string;
    customFields?: Record<string, any>;
  };
  assignedUsers?: Array<{
    userId: mongoose.Types.ObjectId;
    assignedAt: Date;
    assignedBy: mongoose.Types.ObjectId;
  }>;
  isSystem: boolean;
  isDefault: boolean;
  organizationId?: mongoose.Types.ObjectId;
  projectId?: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  hasPermission(resourceType: string, action: string): boolean;
  assignUser(userId: string, assignedBy: string): void;
  unassignUser(userId: string): void;
}

const RoleSchema = new Schema<IRole>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    displayName: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ['system', 'organization', 'project', 'custom'],
      required: true,
      default: 'custom',
    },
    permissions: {
      type: Schema.Types.Mixed,
      required: true,
      default: {},
    },
    restrictions: {
      maxUsers: {
        type: Number,
        min: 0,
      },
      maxProjects: {
        type: Number,
        min: 0,
      },
      timeRestriction: {
        startTime: Date,
        endTime: Date,
        daysOfWeek: {
          type: [Number],
          validate: {
            validator: function (days: number[]) {
              return days.every(day => day >= 0 && day <= 6);
            },
            message: 'Days of week must be between 0 (Sunday) and 6 (Saturday)',
          },
        },
        timezone: String,
      },
      ipRestriction: [String],
      locationRestriction: {
        countries: [String],
        regions: [String],
      },
      featureRestrictions: [String],
    },
    metadata: {
      color: {
        type: String,
        default: '#64748b',
      },
      icon: {
        type: String,
        default: '👥',
      },
      badge: String,
      customFields: Schema.Types.Mixed,
    },
    assignedUsers: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        assignedAt: {
          type: Date,
          default: Date.now,
        },
        assignedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
      },
    ],
    isSystem: {
      type: Boolean,
      default: false,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      index: true,
    },
    projectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
    collection: 'roles',
  }
);

// Compound indexes
RoleSchema.index({ organizationId: 1, name: 1 }, { unique: true });
RoleSchema.index({ projectId: 1, name: 1 });
RoleSchema.index({ type: 1, isSystem: 1 });

// Instance methods
RoleSchema.methods.hasPermission = function (resourceType: string, action: string): boolean {
  return this.permissions[resourceType]?.[action] === true;
};

RoleSchema.methods.assignUser = function (userId: string, assignedBy: string) {
  const existingAssignment = this.assignedUsers?.find(
    (assignment: any) => assignment.userId.toString() === userId
  );

  if (!existingAssignment) {
    if (!this.assignedUsers) this.assignedUsers = [];
    this.assignedUsers.push({
      userId: new mongoose.Types.ObjectId(userId),
      assignedAt: new Date(),
      assignedBy: new mongoose.Types.ObjectId(assignedBy),
    });
  }
};

RoleSchema.methods.unassignUser = function (userId: string) {
  if (this.assignedUsers) {
    this.assignedUsers = this.assignedUsers.filter(
      (assignment: any) => assignment.userId.toString() !== userId
    );
  }
};

// Static methods
export interface RoleModel extends Model<IRole> {
  getOrganizationRoles(organizationId: string): Promise<IRole[]>;
  getProjectRoles(projectId: string): Promise<IRole[]>;
  getSystemRoles(): Promise<IRole[]>;
  createCustomRole(roleData: any, createdBy: string): Promise<IRole>;
}

RoleSchema.statics.getOrganizationRoles = async function (organizationId: string) {
  return this.find({
    $or: [{ organizationId: new mongoose.Types.ObjectId(organizationId) }, { type: 'system' }],
  })
    .populate('assignedUsers.userId', 'name email image')
    .populate('createdBy', 'name email')
    .sort({ isSystem: -1, createdAt: -1 });
};

RoleSchema.statics.getProjectRoles = async function (projectId: string) {
  return this.find({
    $or: [{ projectId: new mongoose.Types.ObjectId(projectId) }, { type: 'system' }],
  })
    .populate('assignedUsers.userId', 'name email image')
    .populate('createdBy', 'name email')
    .sort({ isSystem: -1, createdAt: -1 });
};

RoleSchema.statics.getSystemRoles = async function () {
  return this.find({ type: 'system' })
    .populate('assignedUsers.userId', 'name email image')
    .sort({ name: 1 });
};

RoleSchema.statics.createCustomRole = async function (roleData: any, createdBy: string) {
  const role = new this({
    ...roleData,
    createdBy: new mongoose.Types.ObjectId(createdBy),
    isSystem: false,
    isDefault: false,
  });

  return role.save();
};

export const Role =
  (mongoose.models.Role as RoleModel) || mongoose.model<IRole, RoleModel>('Role', RoleSchema);
