'use client';

import React from 'react';
import {
  Webhook,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  Trash2,
  Plus,
  ExternalLink,
} from 'lucide-react';
import Modal from '../Global/Modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useWebhook } from '@/hooks/useWebhook';
import { formatDistanceToNow } from 'date-fns';
import { PermissionsChecker } from './PermissionsChecker';

interface WebhookManagementModalProps {
  repositoryId: string;
  repositoryName: string;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isConnected?: boolean;
}

export const WebhookManagementModal: React.FC<WebhookManagementModalProps> = ({
  repositoryId,
  repositoryName,
  isOpen,
  setIsOpen,
  isConnected = true,
}) => {
  const {
    webhook,
    webhookStatus,
    webhookHealth,
    isLoadingWebhook,
    isCreatingWebhook,
    isDeletingWebhook,
    createWebhook,
    deleteWebhook,
    hasWebhook,
    isWebhookActive,
    createError,
  } = useWebhook(repositoryId, isConnected);

  const handleCreateWebhook = () => {
    createWebhook(['issues', 'pull_request']);
  };

  const handleDeleteWebhook = () => {
    deleteWebhook();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
      size="lg"
      className="theme-surface-elevated"
    >
      {/* Modal Header */}
      <div className="border-b theme-border pb-4 mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-lg theme-surface">
            <Webhook className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <h2 className="text-xl font-semibold theme-text-primary">Webhook Management</h2>
        </div>
        <p className="theme-text-secondary">
          Manage webhook settings for{' '}
          <span className="font-medium theme-text-primary">{repositoryName}</span>
        </p>
      </div>

      <div className="space-y-6">
        {/* Webhook Status Overview */}
        <Card className="theme-surface-elevated theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between theme-text-primary">
              <span>Webhook Status</span>
              {isLoadingWebhook ? (
                <RefreshCw className="h-4 w-4 animate-spin theme-text-secondary" />
              ) : (
                <Badge
                  variant="outline"
                  className={cn('text-sm theme-border', webhookStatus.statusColor)}
                >
                  {webhookStatus.statusText}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingWebhook ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin theme-text-secondary" />
                <span className="ml-2 theme-text-secondary">Loading webhook data...</span>
              </div>
            ) : hasWebhook && webhook ? (
              <div className="space-y-4">
                {/* Webhook Details */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Events</label>
                    <p className="text-sm theme-text-primary mt-1">{webhook.events.join(', ')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Status</label>
                    <div className="flex items-center gap-2 mt-1">
                      {isWebhookActive ? (
                        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                      )}
                      <span className="text-sm theme-text-primary">
                        {isWebhookActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Deliveries</label>
                    <p className="text-sm theme-text-primary mt-1">{webhook.deliveryCount}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Errors</label>
                    <p
                      className={cn(
                        'text-sm mt-1',
                        webhook.errorCount > 0
                          ? 'text-red-600 dark:text-red-400'
                          : 'theme-text-primary'
                      )}
                    >
                      {webhook.errorCount}
                    </p>
                  </div>
                </div>

                <Separator className="theme-border" />

                {/* Health Score */}
                <div>
                  <label className="text-sm font-medium theme-text-secondary">Health Score</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Activity className={cn('h-4 w-4', webhookHealth.color)} />
                    <span className={cn('text-sm font-medium', webhookHealth.color)}>
                      {webhookHealth.label} ({webhookHealth.score}%)
                    </span>
                  </div>
                </div>

                {/* Last Delivery */}
                {webhook.lastDeliveryAt && (
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">
                      Last Delivery
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="h-4 w-4 theme-text-secondary" />
                      <span className="text-sm theme-text-primary">
                        {(() => {
                          try {
                            const date = new Date(webhook.lastDeliveryAt);
                            return isNaN(date.getTime())
                              ? 'Invalid date'
                              : formatDistanceToNow(date, { addSuffix: true });
                          } catch {
                            return 'Invalid date';
                          }
                        })()}
                      </span>
                    </div>
                  </div>
                )}

                {/* Last Error */}
                {webhook.lastError && (
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Last Error</label>
                    <div className="mt-1 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                      <p className="text-sm text-red-700 dark:text-red-300">
                        {webhook.lastError.message}
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {(() => {
                          try {
                            const date = new Date(webhook.lastError.timestamp);
                            return isNaN(date.getTime())
                              ? 'Invalid date'
                              : formatDistanceToNow(date, { addSuffix: true });
                          } catch {
                            return 'Invalid date';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex flex-wrap gap-3 pt-4">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteWebhook}
                    disabled={isDeletingWebhook}
                    className="theme-button-destructive"
                  >
                    {isDeletingWebhook ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="mr-2 h-4 w-4" />
                    )}
                    {isDeletingWebhook ? 'Deleting...' : 'Delete Webhook'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      window.open(`https://github.com/${repositoryName}/settings/hooks`, '_blank')
                    }
                    className="theme-button-secondary theme-border"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    GitHub Settings
                  </Button>
                </div>
              </div>
            ) : (
              <div className="empty-state-container py-8">
                <div className="empty-state-icon">
                  <Webhook className="h-8 w-8 theme-text-secondary" />
                </div>
                <h3 className="empty-state-title">No Webhook Configured</h3>
                <p className="empty-state-description mb-6">
                  Create a webhook to automatically sync GitHub issues with tasks and enable
                  real-time updates.
                </p>
                <Button
                  onClick={handleCreateWebhook}
                  disabled={isCreatingWebhook}
                  className="theme-button-primary w-full sm:w-auto"
                >
                  {isCreatingWebhook ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Plus className="mr-2 h-4 w-4" />
                  )}
                  {isCreatingWebhook ? 'Creating...' : 'Create Webhook'}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Webhook Information */}
        <Card className="theme-surface theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg theme-text-primary flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              About Webhooks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm theme-text-secondary">
              <p>
                Webhooks automatically create tasks when new GitHub issues are opened in this
                repository.
              </p>
              <p>
                The webhook listens for{' '}
                <code className="px-2 py-1 theme-surface rounded text-xs font-mono theme-text-primary">
                  issues
                </code>{' '}
                and{' '}
                <code className="px-2 py-1 theme-surface rounded text-xs font-mono theme-text-primary">
                  pull_request
                </code>{' '}
                events.
              </p>
              <p>
                Tasks are only created when the repository's "Auto Create Tasks" setting is enabled.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Show permissions checker if webhook creation fails with 403 */}
        {createError && createError.message.includes('403') && (
          <PermissionsChecker repositoryId={repositoryId} repositoryName={repositoryName} />
        )}
      </div>
    </Modal>
  );
};
