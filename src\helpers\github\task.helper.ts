import { Task } from '@/models/Task';
import { GitHubService } from '@/services/GitHub.service';
import { NotificationService } from '@/services/Notification.service';

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed';
  html_url: string;
  user: {
    login: string;
    avatar_url: string;
  };
  assignees: Array<{
    login: string;
    avatar_url: string;
  }>;
  labels: Array<{
    name: string;
    color: string;
    description?: string;
  }>;
  created_at: string;
  updated_at: string;
}

export class GitHubTaskHelper {
  static async createTaskFromIssue(
    userId: string,
    organizationId: string,
    issueNumber: number,
    repository: any,
    integration: any
  ) {
    const [owner, repo] = repository.fullName.split('/');
    const { issues } = await GitHubService.getRepositoryIssues(
      integration.accessToken,
      owner,
      repo,
      1,
      100,
      'all'
    );

    const issue = issues.find((i: GitHubIssue) => i.number === issueNumber);
    if (!issue) {
      throw new Error('Issue not found');
    }

    const existingTask = await Task.findOne({
      'metadata.githubIssue.id': issue.id,
      userId,
    });

    if (existingTask) {
      throw new Error('Task already exists for this issue');
    }

    const taskLabels = issue.labels.map(label => ({
      name: label.name,
      color: `#${label.color}`,
      description: label.description,
    }));

    const priority = this.determinePriority(issue.labels);

    const status = this.determineStatus(issue);

    const task = await Task.create({
      title: issue.title,
      description: issue.body || '',
      status,
      priority,
      projectId: null,
      userId,
      organizationId,
      assignedTo: userId,
      labels: taskLabels,
      dueDate: null,
      estimatedTime: null,
      actualTime: 0,
      metadata: {
        source: 'github',
        githubIssue: {
          id: issue.id,
          number: issue.number,
          htmlUrl: issue.html_url,
          repositoryId: repository.repositoryId,
          repositoryName: repository.fullName,
          state: issue.state,
          createdAt: issue.created_at,
          updatedAt: issue.updated_at,
          author: {
            login: issue.user.login,
            avatarUrl: issue.user.avatar_url,
          },
          assignees: issue.assignees.map(assignee => ({
            login: assignee.login,
            avatarUrl: assignee.avatar_url,
          })),
        },
      },
    });

    // Create notification
    await NotificationService.createNotification({
      userId,
      title: 'Task Created from GitHub Issue',
      description: `Created task "${task.title}" from GitHub issue #${issue.number}`,
      type: 'task',
      link: `/tasks/${task._id}`,
      metadata: {
        taskId: task._id,
        issueNumber: issue.number,
        repositoryName: repository.fullName,
      },
    });

    return {
      success: true,
      task: {
        id: task._id,
        title: task.title,
        status: task.status,
        priority: task.priority,
        projectId: task.projectId,
        githubIssue: {
          number: issue.number,
          htmlUrl: issue.html_url,
        },
      },
    };
  }
  // Determine task priority based on GitHub labels
  private static determinePriority(labels: Array<{ name: string }>): string {
    const priorityLabels = labels.map(l => l.name.toLowerCase());

    if (priorityLabels.some(label => ['critical', 'urgent', 'high'].includes(label))) {
      return 'High';
    } else if (priorityLabels.some(label => ['low', 'minor'].includes(label))) {
      return 'Low';
    }

    return 'Medium';
  }

  // Determine task status based on GitHub issue state
  private static determineStatus(issue: GitHubIssue): string {
    if (issue.state === 'closed') {
      return 'Completed';
    } else if (issue.assignees.length > 0) {
      return 'In Progress';
    }

    return 'To Do';
  }
}
