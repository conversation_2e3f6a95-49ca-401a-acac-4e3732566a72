'use client';

import type React from 'react';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, CheckSquare } from 'lucide-react';

interface TaskNodeData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'todo' | 'in-progress' | 'completed' | 'blocked';
  assignee: string;
}

interface TaskNodeFormProps {
  data: TaskNodeData;
  onSave: (data: Partial<TaskNodeData>) => void;
  onCancel: () => void;
}

export function TaskNodeForm({ data, onSave, onCancel }: TaskNodeFormProps) {
  const [formData, setFormData] = useState<TaskNodeData>({
    title: data.title || 'New Task',
    description: data.description || '',
    priority: data.priority || 'medium',
    status: data.status || 'todo',
    assignee: data.assignee || '',
  });

  const handleChange = (field: keyof TaskNodeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const priorityOptions = [
    { value: 'low', label: '🟢 Low Priority', color: 'text-green-600' },
    { value: 'medium', label: '🟡 Medium Priority', color: 'text-yellow-600' },
    { value: 'high', label: '🔴 High Priority', color: 'text-red-600' },
  ];

  const statusOptions = [
    { value: 'todo', label: '📋 To Do', color: 'text-gray-600' },
    { value: 'in-progress', label: '⚡ In Progress', color: 'text-blue-600' },
    { value: 'completed', label: '✅ Completed', color: 'text-green-600' },
    { value: 'blocked', label: '🚫 Blocked', color: 'text-red-600' },
  ];

  return (
    <div className="min-w-[400px] p-6">
      <div className="flex items-center gap-3 mb-6 pb-4 border-b border-gray-200">
        <CheckSquare className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Edit Task</h3>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-semibold text-gray-700">
              Task Title
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleChange('title', e.target.value)}
              className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
              placeholder="Enter task title..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={e => handleChange('description', e.target.value)}
              className="min-h-[80px] border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 resize-none"
              placeholder="Describe the task details..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority" className="text-sm font-semibold text-gray-700">
                Priority
              </Label>
              <Select
                value={formData.priority}
                onValueChange={value => handleChange('priority', value)}
              >
                <SelectTrigger className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className={option.color}>{option.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-sm font-semibold text-gray-700">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={value => handleChange('status', value)}
              >
                <SelectTrigger className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className={option.color}>{option.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assignee" className="text-sm font-semibold text-gray-700">
              Assignee
            </Label>
            <Input
              id="assignee"
              value={formData.assignee}
              onChange={e => handleChange('assignee', e.target.value)}
              className="h-10 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
              placeholder="Who is responsible for this task?"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-4 py-2 border-gray-300 hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
          >
            <Save className="h-4 w-4 mr-2" /> Save Task
          </Button>
        </div>
      </form>
    </div>
  );
}
