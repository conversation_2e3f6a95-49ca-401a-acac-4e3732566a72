'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { EmptyState } from '@/components/Global/EmptyState';
import { ConfirmDialog } from '@/components/Global/ConfirmDialog';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Search,
  Crown,
  Star,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  UserPlus,
  Clock,
  FileText,
  Database,
  Bell,
  FileImage,
  DollarSign,
  Zap,
} from 'lucide-react';

type IRole = {
  id?: string;
  _id?: string;
  name: string;
  displayName: string;
  description?: string;
  type: 'system' | 'organization' | 'project' | 'custom';
  permissions: Record<string, Record<string, boolean>>;
  restrictions?: any;
  metadata?: any;
  assignedUsers?: any[];
  isSystem?: boolean;
  isDefault?: boolean;
};
import { format } from 'date-fns';

interface RoleManagerProps {
  organizationId?: string;
  projectId?: string;
  showSystemRoles?: boolean;
  allowRoleCreation?: boolean;
  allowRoleEditing?: boolean;
  allowRoleDeletion?: boolean;
  allowUserAssignment?: boolean;
  className?: string;
}

interface RoleFormData {
  name: string;
  displayName: string;
  description: string;
  type: 'system' | 'organization' | 'project' | 'custom';
  permissions: Record<string, Record<string, boolean>>;
  restrictions: {
    maxUsers?: number;
    maxProjects?: number;
    timeRestriction?: {
      startTime?: Date;
      endTime?: Date;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestriction?: string[];
    locationRestriction?: {
      countries?: string[];
      regions?: string[];
    };
    featureRestrictions?: string[];
  };
  metadata: {
    color?: string;
    icon?: string;
    badge?: string;
    customFields?: Record<string, any>;
  };
}

const RESOURCE_TYPES = [
  { key: 'tasks', label: 'Tasks', icon: CheckCircle, color: 'text-blue-600' },
  { key: 'projects', label: 'Projects', icon: Database, color: 'text-green-600' },
  { key: 'notes', label: 'Notes', icon: FileText, color: 'text-purple-600' },
  { key: 'users', label: 'Users', icon: Users, color: 'text-orange-600' },
  { key: 'organization', label: 'Organization', icon: Crown, color: 'text-red-600' },
  { key: 'integrations', label: 'Integrations', icon: Zap, color: 'text-cyan-600' },
  { key: 'timeline', label: 'Timeline', icon: Clock, color: 'text-pink-600' },
  { key: 'notifications', label: 'Notifications', icon: Bell, color: 'text-yellow-600' },
  { key: 'files', label: 'Files', icon: FileImage, color: 'text-gray-600' },
  { key: 'budgets', label: 'Budgets', icon: DollarSign, color: 'text-violet-600' },
];

const PERMISSION_ACTIONS = {
  tasks: ['read', 'write', 'delete', 'admin', 'assign', 'manage'],
  projects: ['read', 'write', 'delete', 'admin', 'create', 'manage', 'archive'],
  notes: ['read', 'write', 'delete', 'share', 'export'],
  users: ['read', 'write', 'delete', 'admin', 'invite', 'manage'],
  organization: ['read', 'write', 'delete', 'admin', 'manage', 'billing'],
  integrations: ['read', 'write', 'delete', 'manage', 'configure'],
  timeline: ['read', 'write', 'manage'],
  notifications: ['read', 'write', 'manage'],
  files: ['read', 'write', 'delete', 'upload', 'manage'],
  budgets: ['read', 'write', 'delete', 'manage'],
};

const ROLE_TYPES = [
  { value: 'system', label: 'System Role', description: 'Global system-wide role' },
  { value: 'organization', label: 'Organization Role', description: 'Organization-specific role' },
  { value: 'project', label: 'Project Role', description: 'Project-specific role' },
  { value: 'custom', label: 'Custom Role', description: 'Custom role with specific permissions' },
];

const ROLE_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
  '#64748b',
];

const ROLE_ICONS = [
  '👑',
  '⭐',
  '🛡️',
  '🔧',
  '📝',
  '👥',
  '🎯',
  '🚀',
  '💎',
  '🔥',
  '⚡',
  '🌟',
  '💼',
  '🎨',
  '🔍',
  '📊',
  '🏆',
  '🎭',
  '🔑',
  '📱',
];

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
];

export const RoleManager: React.FC<RoleManagerProps> = ({
  organizationId,
  projectId,
  showSystemRoles = false,
  allowRoleCreation = true,
  allowRoleEditing = true,
  allowRoleDeletion = true,
  allowUserAssignment = true,
  className = '',
}) => {
  const { toast } = useToast();

  // State
  const [roles, setRoles] = useState<IRole[]>([]);
  const [users, setUsers] = useState<
    Array<{ id: string; name: string; email: string; roles: string[] }>
  >([]);
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<IRole | null>(null);
  const [showCreateRole, setShowCreateRole] = useState(false);
  const [showEditRole, setShowEditRole] = useState(false);
  const [showUserAssignment, setShowUserAssignment] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [expandedRoles, setExpandedRoles] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  // const [viewMode, setViewMode] = useState<'list' | 'hierarchy' | 'permissions'>('list'); // unused

  // Form state
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    displayName: '',
    description: '',
    type: 'custom',
    permissions: {},
    restrictions: {},
    metadata: {},
  });

  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // Fetch roles and users
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (organizationId) params.append('organizationId', organizationId);
      if (projectId) params.append('projectId', projectId);
      if (showSystemRoles) params.append('includeSystem', 'true');

      const [rolesResponse, usersResponse] = await Promise.all([
        fetch(`/api/permissions/roles?${params}`),
        fetch(`/api/permissions/users?${params}`),
      ]);

      if (!rolesResponse.ok || !usersResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      const rolesData = await rolesResponse.json();
      const usersData = await usersResponse.json();

      setRoles(rolesData.roles || []);
      setUsers(usersData.users || []);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch roles and users',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [organizationId, projectId, showSystemRoles, toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Initialize form data
  const initializeFormData = useCallback((role?: IRole) => {
    if (role) {
      setFormData({
        name: role.name,
        displayName: role.displayName,
        description: role.description || '',
        type: role.type,
        permissions: role.permissions || {},
        restrictions: role.restrictions || {},
        metadata: role.metadata || {},
      });
    } else {
      setFormData({
        name: '',
        displayName: '',
        description: '',
        type: 'custom',
        permissions: {},
        restrictions: {},
        metadata: {},
      });
    }
  }, []);

  // Form handlers
  const handleFormSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      try {
        const method = selectedRole ? 'PUT' : 'POST';
        const url = selectedRole
          ? `/api/permissions/roles/${selectedRole._id}`
          : '/api/permissions/roles';

        const payload = {
          ...formData,
          organizationId,
          projectId,
        };

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to save role');
        }

        const result = await response.json();

        if (selectedRole) {
          setRoles(prev => prev.map(role => (role._id === selectedRole._id ? result.role : role)));
          setShowEditRole(false);
          toast({
            title: 'Role Updated',
            description: 'Role has been updated successfully.',
          });
        } else {
          setRoles(prev => [...prev, result.role]);
          setShowCreateRole(false);
          toast({
            title: 'Role Created',
            description: 'Role has been created successfully.',
          });
        }

        setSelectedRole(null);
        initializeFormData();
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to save role',
          variant: 'destructive',
        });
      }
    },
    [formData, selectedRole, organizationId, projectId, toast, initializeFormData]
  );

  const handleDeleteRole = useCallback(async () => {
    if (!selectedRole) return;

    try {
      const response = await fetch(`/api/permissions/roles/${selectedRole._id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete role');
      }

      setRoles(prev => prev.filter(role => role._id !== selectedRole._id));
      setShowDeleteConfirm(false);
      setSelectedRole(null);

      toast({
        title: 'Role Deleted',
        description: 'Role has been deleted successfully.',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete role',
        variant: 'destructive',
      });
    }
  }, [selectedRole, toast]);

  const handleUserAssignment = useCallback(async () => {
    if (!selectedRole) return;

    try {
      const response = await fetch(`/api/permissions/roles/${selectedRole._id}/users`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIds: selectedUsers }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to assign users');
      }

      await fetchData(); // Refresh data
      setShowUserAssignment(false);
      setSelectedUsers([]);

      toast({
        title: 'Users Assigned',
        description: `${selectedUsers.length} user(s) assigned to role successfully.`,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to assign users',
        variant: 'destructive',
      });
    }
  }, [selectedRole, selectedUsers, fetchData, toast]);

  // Filter and search
  const filteredRoles = useMemo(() => {
    return roles.filter(role => {
      const matchesSearch =
        role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        role.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (role.description || '').toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = filterType === 'all' || role.type === filterType;

      return matchesSearch && matchesType;
    });
  }, [roles, searchTerm, filterType]);

  // Permission helpers
  const updatePermission = useCallback((resourceType: string, action: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [resourceType]: {
          ...prev.permissions[resourceType],
          [action]: value,
        },
      },
    }));
  }, []);

  const hasPermission = useCallback(
    (resourceType: string, action: string) => {
      return formData.permissions[resourceType]?.[action] || false;
    },
    [formData.permissions]
  );

  const toggleAllPermissions = useCallback(
    (resourceType: string, value: boolean) => {
      const actions = PERMISSION_ACTIONS[resourceType as keyof typeof PERMISSION_ACTIONS] || [];
      const newPermissions = { ...formData.permissions };

      newPermissions[resourceType] = {};
      actions.forEach(action => {
        newPermissions[resourceType][action] = value;
      });

      setFormData(prev => ({ ...prev, permissions: newPermissions }));
    },
    [formData.permissions]
  );

  // Render role form
  const renderRoleForm = () => (
    <form onSubmit={handleFormSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="restrictions">Restrictions</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Role Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="admin, editor, viewer..."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                value={formData.displayName}
                onChange={e => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                placeholder="Administrator, Editor, Viewer..."
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Role Type</Label>
            <Select
              value={formData.type}
              onValueChange={value => setFormData(prev => ({ ...prev, type: value as any }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ROLE_TYPES.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Role description..."
              rows={3}
            />
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <div className="space-y-6">
            {RESOURCE_TYPES.map(resourceType => {
              const actions =
                PERMISSION_ACTIONS[resourceType.key as keyof typeof PERMISSION_ACTIONS] || [];
              const ResourceIcon = resourceType.icon;
              const allSelected = actions.every(action => hasPermission(resourceType.key, action));
              const someSelected = actions.some(action => hasPermission(resourceType.key, action));

              return (
                <Card key={resourceType.key}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <ResourceIcon className={`w-5 h-5 ${resourceType.color}`} />
                        <CardTitle className="text-lg">{resourceType.label}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={allSelected}
                          onCheckedChange={checked =>
                            toggleAllPermissions(resourceType.key, checked)
                          }
                        />
                        <span className="text-sm text-muted-foreground">
                          {allSelected ? 'All' : someSelected ? 'Some' : 'None'}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-3">
                      {actions.map(action => (
                        <div key={action} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${resourceType.key}-${action}`}
                            checked={hasPermission(resourceType.key, action)}
                            onCheckedChange={checked =>
                              updatePermission(resourceType.key, action, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`${resourceType.key}-${action}`}
                            className="text-sm font-normal capitalize"
                          >
                            {action}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="restrictions" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Max Users</Label>
              <Input
                type="number"
                value={formData.restrictions.maxUsers || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    restrictions: {
                      ...prev.restrictions,
                      maxUsers: e.target.value ? parseInt(e.target.value) : undefined,
                    },
                  }))
                }
                placeholder="Unlimited"
              />
            </div>

            <div className="space-y-2">
              <Label>Max Projects</Label>
              <Input
                type="number"
                value={formData.restrictions.maxProjects || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    restrictions: {
                      ...prev.restrictions,
                      maxProjects: e.target.value ? parseInt(e.target.value) : undefined,
                    },
                  }))
                }
                placeholder="Unlimited"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Time Restrictions</Label>
              <div className="mt-2 space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Time</Label>
                    <Input
                      type="time"
                      value={
                        formData.restrictions.timeRestriction?.startTime
                          ? format(formData.restrictions.timeRestriction.startTime, 'HH:mm')
                          : ''
                      }
                      onChange={e => {
                        if (e.target.value) {
                          const [hours, minutes] = e.target.value.split(':');
                          const date = new Date();
                          date.setHours(parseInt(hours), parseInt(minutes));
                          setFormData(prev => ({
                            ...prev,
                            restrictions: {
                              ...prev.restrictions,
                              timeRestriction: {
                                ...prev.restrictions.timeRestriction,
                                startTime: date,
                              },
                            },
                          }));
                        }
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>End Time</Label>
                    <Input
                      type="time"
                      value={
                        formData.restrictions.timeRestriction?.endTime
                          ? format(formData.restrictions.timeRestriction.endTime, 'HH:mm')
                          : ''
                      }
                      onChange={e => {
                        if (e.target.value) {
                          const [hours, minutes] = e.target.value.split(':');
                          const date = new Date();
                          date.setHours(parseInt(hours), parseInt(minutes));
                          setFormData(prev => ({
                            ...prev,
                            restrictions: {
                              ...prev.restrictions,
                              timeRestriction: {
                                ...prev.restrictions.timeRestriction,
                                endTime: date,
                              },
                            },
                          }));
                        }
                      }}
                    />
                  </div>
                </div>

                <div>
                  <Label>Allowed Days</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {DAYS_OF_WEEK.map(day => (
                      <div key={day.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`day-${day.value}`}
                          checked={formData.restrictions.timeRestriction?.daysOfWeek?.includes(
                            day.value
                          )}
                          onCheckedChange={checked => {
                            const currentDays =
                              formData.restrictions.timeRestriction?.daysOfWeek || [];
                            const newDays = checked
                              ? [...currentDays, day.value]
                              : currentDays.filter(d => d !== day.value);

                            setFormData(prev => ({
                              ...prev,
                              restrictions: {
                                ...prev.restrictions,
                                timeRestriction: {
                                  ...prev.restrictions.timeRestriction,
                                  daysOfWeek: newDays,
                                },
                              },
                            }));
                          }}
                        />
                        <Label htmlFor={`day-${day.value}`} className="text-sm">
                          {day.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>IP Address Restrictions</Label>
              <Textarea
                value={formData.restrictions.ipRestriction?.join('\n') || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    restrictions: {
                      ...prev.restrictions,
                      ipRestriction: e.target.value.split('\n').filter(ip => ip.trim()),
                    },
                  }))
                }
                placeholder="Enter IP addresses, one per line..."
                rows={3}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Role Color</Label>
              <div className="mt-2 grid grid-cols-6 gap-2">
                {ROLE_COLORS.map(color => (
                  <button
                    key={color}
                    type="button"
                    className={`w-8 h-8 rounded border-2 ${
                      formData.metadata.color === color ? 'border-gray-400' : 'border-gray-200'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() =>
                      setFormData(prev => ({
                        ...prev,
                        metadata: { ...prev.metadata, color },
                      }))
                    }
                  />
                ))}
              </div>
            </div>

            <div>
              <Label className="text-base font-medium">Role Icon</Label>
              <div className="mt-2 grid grid-cols-10 gap-2">
                {ROLE_ICONS.map(icon => (
                  <button
                    key={icon}
                    type="button"
                    className={`w-8 h-8 rounded border-2 flex items-center justify-center ${
                      formData.metadata.icon === icon ? 'border-gray-400' : 'border-gray-200'
                    }`}
                    onClick={() =>
                      setFormData(prev => ({
                        ...prev,
                        metadata: { ...prev.metadata, icon },
                      }))
                    }
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Badge Text</Label>
              <Input
                value={formData.metadata.badge || ''}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    metadata: { ...prev.metadata, badge: e.target.value },
                  }))
                }
                placeholder="VIP, Premium, Basic..."
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => {
            setShowCreateRole(false);
            setShowEditRole(false);
            setSelectedRole(null);
            initializeFormData();
          }}
        >
          Cancel
        </Button>
        <Button type="submit">{selectedRole ? 'Update Role' : 'Create Role'}</Button>
      </div>
    </form>
  );

  // Render role card
  const renderRoleCard = (role: IRole) => {
    const assignedUserCount = role.assignedUsers?.length || 0;
    const isExpanded = expandedRoles.has(role.id || role._id || '');

    return (
      <Card key={role.id || role._id || ''} className="transition-all duration-200 hover:shadow-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center text-lg font-bold text-white"
                style={{ backgroundColor: role.metadata?.color || '#64748b' }}
              >
                {role.metadata?.icon || role.displayName.charAt(0)}
              </div>

              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{role.displayName}</h3>
                  {role.metadata?.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {role.metadata.badge}
                    </Badge>
                  )}
                  {role.isSystem && (
                    <Badge variant="outline" className="text-xs">
                      <Crown className="w-3 h-3 mr-1" />
                      System
                    </Badge>
                  )}
                  {role.isDefault && (
                    <Badge variant="outline" className="text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      Default
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{role.description}</p>
                <div className="flex items-center gap-4 mt-2">
                  <span className="text-xs text-muted-foreground">
                    {assignedUserCount} user{assignedUserCount !== 1 ? 's' : ''}
                  </span>
                  <span className="text-xs text-muted-foreground capitalize">{role.type} role</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setExpandedRoles(prev => {
                    const newSet = new Set(prev);
                    const key = role.id || role._id || '';
                    if (newSet.has(key)) {
                      newSet.delete(key);
                    } else {
                      newSet.add(key);
                    }
                    return newSet;
                  })
                }
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </Button>

              <div className="flex items-center gap-1">
                {allowRoleEditing && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedRole(role);
                      setShowEditRole(true);
                      initializeFormData(role);
                    }}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                )}
                {allowUserAssignment && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedRole(role);
                      setShowUserAssignment(true);
                    }}
                  >
                    <UserPlus className="w-4 h-4" />
                  </Button>
                )}
                {allowRoleDeletion && !role.isSystem && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedRole(role);
                      setShowDeleteConfirm(true);
                    }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Permissions</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(role.permissions || {}).map(([resource, actions]) => {
                    const enabledActions = Object.entries(actions as Record<string, boolean>)
                      .filter(([, enabled]) => enabled)
                      .map(([action]) => action);

                    if (enabledActions.length === 0) return null;

                    return (
                      <div key={resource} className="text-sm">
                        <span className="font-medium capitalize">{resource}:</span>
                        <span className="ml-1 text-muted-foreground">
                          {enabledActions.join(', ')}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {assignedUserCount > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Assigned Users ({assignedUserCount})</h4>
                  <div className="text-sm text-muted-foreground">
                    {assignedUserCount} user{assignedUserCount !== 1 ? 's' : ''} assigned to this
                    role
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Role Management</h2>
          <p className="text-muted-foreground">
            Manage roles and permissions for your {projectId ? 'project' : 'organization'}
          </p>
        </div>

        {allowRoleCreation && (
          <Button onClick={() => setShowCreateRole(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Role
          </Button>
        )}
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search roles..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="system">System</SelectItem>
            <SelectItem value="organization">Organization</SelectItem>
            <SelectItem value="project">Project</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {filteredRoles.length === 0 ? (
        <EmptyState
          icon={<Users className="w-8 h-8" />}
          title="No roles found"
          description={searchTerm ? 'No roles match your search' : 'No roles available'}
          actions={
            allowRoleCreation
              ? [
                  {
                    label: 'Create Role',
                    onClick: () => setShowCreateRole(true),
                    icon: <Plus className="w-4 h-4 mr-2" />,
                    variant: 'outline',
                  },
                ]
              : undefined
          }
        />
      ) : (
        <div className="grid gap-4">{filteredRoles.map(renderRoleCard)}</div>
      )}

      <Dialog open={showCreateRole} onOpenChange={setShowCreateRole}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
          </DialogHeader>
          {renderRoleForm()}
        </DialogContent>
      </Dialog>

      <Dialog open={showEditRole} onOpenChange={setShowEditRole}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
          </DialogHeader>
          {renderRoleForm()}
        </DialogContent>
      </Dialog>

      <Dialog open={showUserAssignment} onOpenChange={setShowUserAssignment}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Users to Role</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="max-h-60 overflow-y-auto space-y-2">
              {users.map(user => (
                <div key={user.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`user-${user.id}`}
                    checked={selectedUsers.includes(user.id)}
                    onCheckedChange={checked => {
                      if (checked) {
                        setSelectedUsers(prev => [...prev, user.id]);
                      } else {
                        setSelectedUsers(prev => prev.filter(id => id !== user.id));
                      }
                    }}
                  />
                  <Label htmlFor={`user-${user.id}`} className="flex-1">
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </Label>
                </div>
              ))}
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowUserAssignment(false);
                  setSelectedUsers([]);
                }}
              >
                Cancel
              </Button>
              <Button onClick={handleUserAssignment} disabled={selectedUsers.length === 0}>
                Assign {selectedUsers.length} User{selectedUsers.length !== 1 ? 's' : ''}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="Delete Role"
        description={`Are you sure you want to delete the role "${selectedRole?.displayName}"? This action cannot be undone.`}
        onConfirm={handleDeleteRole}
        variant="destructive"
        confirmText="Delete Role"
        cancelText="Cancel"
      />
    </div>
  );
};
