'use client';

import { useEffect } from 'react';
import { useTheme } from 'next-themes';
import { useSettingsStore } from '@/stores/settingsStore';
import { useAuth } from '@/context/AuthProvider';

export function useThemeInitializer() {
  const { setTheme } = useTheme();
  const { isAuthenticated } = useAuth();
  const { initializeTheme, isInitialized } = useSettingsStore();

  useEffect(() => {
    if (isAuthenticated && !isInitialized) {
      initializeTheme().then(dbTheme => {
        // Only set theme if it's different from current
        setTheme(dbTheme);
      });
    }
  }, [isAuthenticated, isInitialized, initializeTheme, setTheme]);
}
