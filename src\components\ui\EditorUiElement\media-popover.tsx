'use client';

import * as React from 'react';

import type { WithRequiredKey } from '@udecode/plate';

import {
  FloatingMedia as FloatingMediaPrimitive,
  FloatingMediaStore,
  useFloatingMediaValue,
  useImagePreviewValue,
} from '@udecode/plate-media/react';
import {
  useEditorRef,
  useEditorSelector,
  useElement,
  useReadOnly,
  useRemoveNodeButton,
  useSelected,
} from '@udecode/plate/react';
import { cva } from 'class-variance-authority';
import {
  Link,
  Trash2Icon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Edit3,
  Image as ImageIcon,
} from 'lucide-react';

import { Button, buttonVariants } from '@/components/ui/button';
import { Popover, PopoverAnchor, PopoverContent } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { CaptionButton } from './caption';

const inputVariants = cva(
  'flex h-[28px] w-full rounded-md border-none bg-transparent px-1.5 py-1 text-base placeholder:text-muted-foreground focus-visible:ring-transparent focus-visible:outline-none md:text-sm'
);

export interface MediaPopoverProps {
  children: React.ReactNode;
  plugin: WithRequiredKey;
}

export function MediaPopover({ children, plugin }: MediaPopoverProps) {
  const editor = useEditorRef();
  const readOnly = useReadOnly();
  const selected = useSelected();

  const selectionCollapsed = useEditorSelector(editor => !editor.api.isExpanded(), []);
  const isImagePreviewOpen = useImagePreviewValue('isOpen', editor.id);
  const isOpen = !readOnly && selected && selectionCollapsed && !isImagePreviewOpen;
  const isEditing = useFloatingMediaValue('isEditing');

  React.useEffect(() => {
    if (!isOpen && isEditing) {
      FloatingMediaStore.set('isEditing', false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const element = useElement();
  const { props: buttonProps } = useRemoveNodeButton({ element });

  // Image-specific state
  const [editingAlt, setEditingAlt] = React.useState(false);
  const [altText, setAltText] = React.useState((element as any)?.alt || '');

  // Handle alt text update
  const updateAltText = () => {
    editor.tf.setNodes({ alt: altText }, { at: editor.api.findPath(element) });
    setEditingAlt(false);
  };

  // Handle alignment change
  const handleAlignmentChange = (alignment: 'left' | 'center' | 'right') => {
    editor.tf.setNodes({ align: alignment }, { at: editor.api.findPath(element) });
  };

  if (readOnly) return <>{children}</>;

  return (
    <Popover open={isOpen} modal={false}>
      <PopoverAnchor>{children}</PopoverAnchor>

      <PopoverContent className="w-auto p-1" onOpenAutoFocus={e => e.preventDefault()}>
        {isEditing ? (
          <div className="flex w-[330px] flex-col">
            <div className="flex items-center">
              <div className="flex items-center pr-1 pl-2 text-muted-foreground">
                <Link className="size-4" />
              </div>

              <FloatingMediaPrimitive.UrlInput
                className={inputVariants()}
                placeholder="Paste the embed link..."
                options={{ plugin }}
              />
            </div>
          </div>
        ) : editingAlt ? (
          <div className="flex w-[280px] flex-col gap-2 p-2">
            <div className="flex items-center gap-2">
              <ImageIcon className="size-4 text-muted-foreground" />
              <span className="text-sm font-medium">Alt Text</span>
            </div>
            <Input
              value={altText}
              onChange={e => setAltText(e.target.value)}
              placeholder="Describe this image..."
              className="text-sm"
              onKeyDown={e => {
                if (e.key === 'Enter') updateAltText();
                if (e.key === 'Escape') setEditingAlt(false);
              }}
              autoFocus
            />
            <div className="flex gap-1">
              <Button size="sm" onClick={updateAltText}>
                Save
              </Button>
              <Button size="sm" variant="ghost" onClick={() => setEditingAlt(false)}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="box-content flex items-center">
            <FloatingMediaPrimitive.EditButton
              className={buttonVariants({ size: 'sm', variant: 'ghost' })}
            >
              Edit link
            </FloatingMediaPrimitive.EditButton>

            <CaptionButton variant="ghost">Caption</CaptionButton>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setEditingAlt(true)}
              className="flex items-center gap-1"
            >
              <Edit3 className="size-3" />
              Alt text
            </Button>

            {/* Alignment Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="ghost" className="flex items-center gap-1">
                  <AlignCenter className="size-3" />
                  Align
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                <DropdownMenuItem onClick={() => handleAlignmentChange('left')}>
                  <AlignLeft className="size-4 mr-2" />
                  Left
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAlignmentChange('center')}>
                  <AlignCenter className="size-4 mr-2" />
                  Center
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAlignmentChange('right')}>
                  <AlignRight className="size-4 mr-2" />
                  Right
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Separator orientation="vertical" className="mx-1 h-6" />

            <Button size="icon" variant="ghost" {...buttonProps}>
              <Trash2Icon />
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
