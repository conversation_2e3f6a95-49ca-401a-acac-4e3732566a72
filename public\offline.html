<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFluxio - You're Offline</title>
    <link rel="icon" href="/logo.png" type="image/png">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary: #8b5cf6;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --bg-dark: #0f172a;
            --bg-dark-secondary: #1e293b;
            --bg-light: #f8fafc;
            --bg-light-secondary: #f1f5f9;
            --text-dark: #e2e8f0;
            --text-light: #334155;
            --border-dark: rgba(59, 130, 246, 0.2);
            --border-light: rgba(59, 130, 246, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
            color: var(--text-dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        body.light-theme {
            background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-light-secondary) 100%);
            color: var(--text-light);
        }

        .container {
            text-align: center;
            max-width: 600px;
            width: 100%;
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-dark);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        body.light-theme .container {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-light);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            transition: transform 0.2s;
            padding: 0.5rem;
            border-radius: 8px;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            background: rgba(59, 130, 246, 0.1);
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .section {
            background: rgba(15, 23, 42, 0.4);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border: 1px solid var(--border-dark);
            text-align: left;
        }

        body.light-theme .section {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid var(--border-light);
        }

        .section h3 {
            color: var(--primary);
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
            opacity: 0.9;
        }

        .features li::before {
            content: "✓";
            color: var(--success);
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: translateY(-1px);
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
        }

        .status.online {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border-radius: 8px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            background: rgba(15, 23, 42, 0.6);
            color: var(--text-dark);
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        body.light-theme .form-group input,
        body.light-theme .form-group textarea {
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-light);
            border: 1px solid var(--border-light);
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .task-list {
            list-style: none;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .task-list li {
            background: rgba(59, 130, 246, 0.1);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .task-list li .delete-btn {
            background: none;
            border: none;
            color: var(--error);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .task-list li .delete-btn:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        .progress-bar-container {
            width: 100%;
            background-color: rgba(59, 130, 246, 0.2);
            border-radius: 5px;
            margin-top: 0.5rem;
            height: 8px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            width: 0%;
            background-color: var(--primary);
            border-radius: 5px;
            transition: width 0.5s ease;
        }

        details {
            margin-top: 1.5rem;
        }

        summary {
            font-weight: 600;
            color: var(--primary);
            cursor: pointer;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        details[open] summary {
            margin-bottom: 1rem;
        }

        details p,
        details ul {
            margin-top: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        details ul {
            padding-left: 1.5rem;
        }

        #installPromptContainer {
            display: none;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1.5rem;
                margin: 0.5rem;
            }

            h1 {
                font-size: 2rem;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">☀️</button>
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p class="subtitle">
            Don't worry! TaskFluxio works offline too. You can still view cached content and create new tasks.
        </p>

        <div class="section features">
            <h3>Available Offline Features</h3>
            <ul>
                <li>View cached tasks and projects</li>
                <li>Create new tasks (syncs when online)</li>
                <li>Access your notes and calendar</li>
                <li>Dark/Light theme support</li>
                <li>Full keyboard navigation</li>
            </ul>
        </div>

        <div class="section offline-task-section">
            <h3>Create Offline Task</h3>
            <form id="offlineTaskForm">
                <div class="form-group">
                    <label for="taskTitle">Task Title</label>
                    <input type="text" id="taskTitle" name="title" required autocomplete="off" placeholder="Enter task title">
                </div>
                <div class="form-group">
                    <label for="taskDescription">Description (optional)</label>
                    <textarea id="taskDescription" name="description" rows="3" placeholder="Add task description"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Add Task</button>
            </form>

            <h4 style="margin-top: 1.5rem; text-align: center; color: var(--primary);">Pending Tasks</h4>
            <ul id="offlineTaskList" class="task-list">
                <!-- Tasks will be loaded here -->
            </ul>
        </div>

        <div class="section status-section">
            <h3>Connection Status</h3>
            <div class="status" id="connectionStatus" role="status" aria-live="polite">
                🔴 Currently offline
            </div>
            <div id="syncStatus" style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                Offline tasks: <span id="pendingTaskCount">0</span> pending sync
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar" id="syncProgressBar"></div>
            </div>
            <div id="swStatus" style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                Service Worker: <span id="swRegStatus">Checking...</span>
            </div>
        </div>

        <div class="button-group">
            <button class="btn btn-primary" onclick="tryReconnect()">
                🔄 Try Again
            </button>
            <a href="/" class="btn btn-secondary">
                🏠 Dashboard
            </a>
            <div id="installPromptContainer">
                <button id="installButton" class="btn btn-secondary">
                    📱 Install App
                </button>
            </div>
        </div>

        <details>
            <summary>Help & Troubleshooting</summary>
            <p>Having trouble? Here are some quick fixes:</p>
            <ul>
                <li><strong>Check Connection:</strong> Verify your Wi-Fi or mobile data is working</li>
                <li><strong>Refresh Page:</strong> Try reloading to fix temporary issues</li>
                <li><strong>Offline Mode:</strong> Tasks created offline will sync automatically when connected</li>
                <li><strong>Cached Content:</strong> Previously visited pages may still be accessible</li>
                <li><strong>Install App:</strong> Install as PWA for better offline experience</li>
            </ul>
        </details>
    </div>

    <script>
            // Constants
            const STORAGE_KEYS = {
                TASKS: 'offline_tasks',
                ANALYTICS: 'offline_analytics',
                THEME: 'app_theme'
            };

            // Global variables
            let deferredPrompt;

            // DOM elements
            const elements = {
                themeToggle: document.getElementById('themeToggle'),
                connectionStatus: document.getElementById('connectionStatus'),
                offlineTaskForm: document.getElementById('offlineTaskForm'),
                offlineTaskList: document.getElementById('offlineTaskList'),
                pendingTaskCount: document.getElementById('pendingTaskCount'),
                syncProgressBar: document.getElementById('syncProgressBar'),
                syncStatus: document.getElementById('syncStatus'),
                swRegStatus: document.getElementById('swRegStatus'),
                installPromptContainer: document.getElementById('installPromptContainer'),
                installButton: document.getElementById('installButton'),
                taskTitle: document.getElementById('taskTitle'),
                taskDescription: document.getElementById('taskDescription')
            };

            // Theme Management
            class ThemeManager {
                static apply(theme) {
                    const isLight = theme === 'light';
                    document.body.classList.toggle('light-theme', isLight);
                    elements.themeToggle.textContent = isLight ? '🌙' : '☀️';
                    localStorage.setItem(STORAGE_KEYS.THEME, theme);
                }

            static toggle() {
                const current = localStorage.getItem(STORAGE_KEYS.THEME) || 'dark';
                this.apply(current === 'dark' ? 'light' : 'dark');
            }

            static init() {
                const stored = localStorage.getItem(STORAGE_KEYS.THEME);
                const preferred = window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
                this.apply(stored || preferred);
                elements.themeToggle.addEventListener('click', () => this.toggle());
            }
        }

            // Connection Management
            class ConnectionManager {
                static updateStatus() {
                    const isOnline = navigator.onLine;
                    elements.connectionStatus.textContent = isOnline
                        ? '🟢 Connection restored! Refreshing...'
                        : '🔴 Currently offline';
                    elements.connectionStatus.className = isOnline ? 'status online' : 'status';

                    if (isOnline) {
                        AnalyticsManager.track('connection_restored');
                        setTimeout(() => window.location.reload(), 1500);
                    } else {
                    AnalyticsManager.track('connection_lost');
                }
            }

            static tryReconnect() {
                navigator.onLine ? window.location.reload() : this.updateStatus();
            }

            static init() {
                window.addEventListener('online', () => this.updateStatus());
                window.addEventListener('offline', () => this.updateStatus());
                this.updateStatus();
            }
        }

            // Task Management
            class TaskManager {
                static getTasks() {
                    try {
                    return JSON.parse(localStorage.getItem(STORAGE_KEYS.TASKS) || '[]');
                } catch (e) {
                    console.error('Error parsing offline tasks:', e);
                    return [];
                }
            }

            static saveTasks(tasks) {
                localStorage.setItem(STORAGE_KEYS.TASKS, JSON.stringify(tasks));
                this.updateCount();
            }

            static updateCount() {
                const count = this.getTasks().length;
                elements.pendingTaskCount.textContent = count;
                elements.syncProgressBar.style.width = '0%';
            }

            static display() {
                const tasks = this.getTasks();
                elements.offlineTaskList.innerHTML = tasks.length === 0
                    ? '<li style="opacity: 0.6;">No offline tasks yet</li>'
                    : tasks.map((task, index) => `
                        <li>
                            <div>
                                <strong>${this.escapeHtml(task.title)}</strong>
                                ${task.description ? `<br><small>${this.escapeHtml(task.description)}</small>` : ''}
                                <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">
                                    ${new Date(task.timestamp).toLocaleString()}
                                </div>
                            </div>
                            <button class="delete-btn" data-index="${index}" aria-label="Delete task">✖</button>
                        </li>
                    `).join('');
            }

            static escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            static async add(title, description) {
                const tasks = this.getTasks();
                const newTask = {
                    id: Date.now(),
                    title: title.trim(),
                    description: description.trim(),
                    status: 'pending',
                    timestamp: new Date().toISOString()
                };

                tasks.push(newTask);
                this.saveTasks(tasks);
                this.display();
                AnalyticsManager.track('task_created_offline', { title });

                // Register background sync
                if ('serviceWorker' in navigator && 'SyncManager' in window) {
                    try {
                        const registration = await navigator.serviceWorker.ready;
                        await registration.sync.register('offline-queue-sync');
                        this.showSyncFeedback('Sync initiated!', '#10b981');
                    } catch (err) {
                        console.error('Background sync failed:', err);
                        AnalyticsManager.track('background_sync_failed', { error: err.message });
                    }
                }
            }

            static delete(index) {
                const tasks = this.getTasks();
                tasks.splice(index, 1);
                this.saveTasks(tasks);
                this.display();
                AnalyticsManager.track('task_deleted_offline', { index });
            }

            static showSyncFeedback(message, color) {
                elements.syncStatus.textContent = `Offline tasks: ${this.getTasks().length} pending sync. ${message}`;
                elements.syncStatus.style.color = color;
                setTimeout(() => {
                    elements.syncStatus.style.color = '';
                    this.updateCount();
                }, 3000);
            }

            static init() {
                elements.offlineTaskForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const title = elements.taskTitle.value.trim();
                    const description = elements.taskDescription.value.trim();

                    if (title) {
                        await this.add(title, description);
                        elements.taskTitle.value = '';
                        elements.taskDescription.value = '';
                    }
                });

                elements.offlineTaskList.addEventListener('click', (e) => {
                    if (e.target.classList.contains('delete-btn')) {
                        const index = parseInt(e.target.dataset.index);
                        this.delete(index);
                    }
                });

                this.display();
            }
        }

            // Analytics Management
            class AnalyticsManager {
                static track(eventName, data = {}) {
                    try {
                        const events = JSON.parse(localStorage.getItem(STORAGE_KEYS.ANALYTICS) || '[]');
                        events.push({ eventName, data, timestamp: new Date().toISOString() });
                    localStorage.setItem(STORAGE_KEYS.ANALYTICS, JSON.stringify(events));

                    if (navigator.onLine) {
                        this.sync();
                    } else if ('serviceWorker' in navigator && 'SyncManager' in window) {
                        navigator.serviceWorker.ready.then(registration => {
                            registration.sync.register('offline-analytics-sync');
                        });
                    }
                } catch (e) {
                    console.error('Analytics tracking failed:', e);
                }
            }

            static async sync() {
                const events = JSON.parse(localStorage.getItem(STORAGE_KEYS.ANALYTICS) || '[]');
                if (events.length === 0) return;

                try {
                    const response = await fetch('/api/analytics', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ events })
                    });

                    if (response.ok) {
                        localStorage.removeItem(STORAGE_KEYS.ANALYTICS);
                    }
                } catch (error) {
                    console.error('Analytics sync failed:', error);
                }
            }
        }

            // PWA Management
            class PWAManager {
                static init() {
                    window.addEventListener('beforeinstallprompt', (e) => {
                    e.preventDefault();
                    deferredPrompt = e;
                    elements.installPromptContainer.style.display = 'block';
                    AnalyticsManager.track('pwa_install_prompt_shown');
                });

                elements.installButton.addEventListener('click', async () => {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        const { outcome } = await deferredPrompt.userChoice;
                        AnalyticsManager.track('pwa_install_choice', { outcome });
                        deferredPrompt = null;
                        elements.installPromptContainer.style.display = 'none';
                    }
                });

                window.addEventListener('appinstalled', () => {
                    elements.installPromptContainer.style.display = 'none';
                    AnalyticsManager.track('pwa_installed');
                });
            }
        }

            // Service Worker Management
            class ServiceWorkerManager {
                static init() {
                    if (!('serviceWorker' in navigator)) {
                        elements.swRegStatus.textContent = 'Not Supported';
                        elements.swRegStatus.style.color = 'var(--error)';
                        return;
                    }

                navigator.serviceWorker.getRegistration()
                    .then(registration => {
                        if (registration) {
                            elements.swRegStatus.textContent = 'Active';
                            elements.swRegStatus.style.color = 'var(--success)';
                            this.setupMessageListener();

                            if (registration.waiting) {
                                elements.swRegStatus.textContent = 'Update available';
                                elements.swRegStatus.style.color = 'var(--warning)';
                            }
                        } else {
                            elements.swRegStatus.textContent = 'Not Registered';
                            elements.swRegStatus.style.color = 'var(--error)';
                        }
                    })
                    .catch(() => {
                        elements.swRegStatus.textContent = 'Error';
                        elements.swRegStatus.style.color = 'var(--error)';
                    });
            }

            static setupMessageListener() {
                navigator.serviceWorker.addEventListener('message', event => {
                    const { data } = event;
                    if (data?.type === 'SYNC_COMPLETE') {
                        const tasks = TaskManager.getTasks();
                        const message = tasks.length === 0
                            ? 'All tasks synced!'
                            : `${data.failed} failed, ${tasks.length} pending`;
                        TaskManager.showSyncFeedback(message, tasks.length === 0 ? 'var(--success)' : 'var(--warning)');
                        TaskManager.display();
                    } else if (data?.type === 'SW_UPDATED') {
                        if (confirm('New version available. Refresh now?')) {
                            window.location.reload();
                        }
                    }
                });
            }
        }

            // Global reconnect function
            window.tryReconnect = () => ConnectionManager.tryReconnect();

            // Initialize everything
            document.addEventListener('DOMContentLoaded', () => {
                ThemeManager.init();
                ConnectionManager.init();
                TaskManager.init();
                PWAManager.init();
                ServiceWorkerManager.init();
            });
    </script>
</body>

</html>