import axios from 'axios';
import { IRole, IRoleFormData } from '@/types/Role';

export class RoleService {
  // Get roles
  static async getRoles(params?: {
    organizationId?: string;
    projectId?: string;
    includeSystem?: boolean;
    type?: string;
  }): Promise<{ success: boolean; roles: IRole[] }> {
    try {
      const searchParams = new URLSearchParams();

      if (params?.organizationId) searchParams.append('organizationId', params.organizationId);
      if (params?.projectId) searchParams.append('projectId', params.projectId);
      if (params?.includeSystem) searchParams.append('includeSystem', 'true');
      if (params?.type) searchParams.append('type', params.type);

      const response = await axios.get(`/api/roles?${searchParams}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch roles');
    }
  }

  // Get organization roles
  static async getOrganizationRoles(
    organizationId: string
  ): Promise<{ success: boolean; roles: IRole[] }> {
    try {
      const response = await axios.get(`/api/roles?organizationId=${organizationId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch organization roles');
    }
  }

  // Get project roles
  static async getProjectRoles(projectId: string): Promise<{ success: boolean; roles: IRole[] }> {
    try {
      const response = await axios.get(`/api/roles/project?projectId=${projectId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch project roles');
    }
  }

  // Get system roles
  static async getSystemRoles(): Promise<{ success: boolean; roles: IRole[] }> {
    try {
      const response = await axios.get('/api/roles/system');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch system roles');
    }
  }

  // Create role
  static async createRole(
    roleData: IRoleFormData & {
      organizationId?: string;
      projectId?: string;
    }
  ): Promise<{ success: boolean; role: IRole; message: string }> {
    try {
      const response = await axios.post('/api/roles', roleData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create role');
    }
  }

  // Update role
  static async updateRole(
    roleId: string,
    updates: Partial<IRoleFormData>
  ): Promise<{ success: boolean; role: IRole; message: string }> {
    try {
      const response = await axios.put(`/api/roles/${roleId}`, updates);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to update role');
    }
  }

  // Delete role
  static async deleteRole(roleId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.delete(`/api/roles/${roleId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to delete role');
    }
  }

  // Assign users to role
  static async assignUsersToRole(
    roleId: string,
    userIds: string[]
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post(`/api/roles/${roleId}/assign-users`, {
        userIds,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to assign users to role');
    }
  }
}
