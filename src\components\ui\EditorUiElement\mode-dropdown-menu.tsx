'use client';

import * as React from 'react';

import { type DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import { useEditorRef, usePlateState } from '@udecode/plate/react';
import { EyeIcon, PenIcon, ChevronDownIcon } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import { ToolbarButton } from './toolbar';

export function ModeDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorRef();
  const [readOnly, setReadOnly] = usePlateState('readOnly');
  const [open, setOpen] = React.useState(false);

  let value = 'editing';
  if (readOnly) value = 'viewing';

  const item: Record<
    string,
    {
      icon: React.ReactNode;
      label: string;
      description: string;
      color: string;
    }
  > = {
    editing: {
      icon: <PenIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Editing',
      description: 'Make changes to your content',
      color: 'text-blue-600 dark:text-blue-400',
    },
    viewing: {
      icon: <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4" />,
      label: 'Viewing',
      description: 'Read-only preview mode',
      color: 'text-green-600 dark:text-green-400',
    },
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={open}
          tooltip="Switch editing mode"
          isDropdown
          className={cn('theme-button-ghost theme-transition', open && 'theme-surface-elevated')}
        >
          <div className="flex items-center gap-1 sm:gap-2">
            <div className={cn('theme-transition', item[value].color)}>{item[value].icon}</div>
            <span className="hidden md:inline font-medium text-xs sm:text-sm theme-text-primary">
              {item[value].label}
            </span>
            <ChevronDownIcon
              className={cn(
                'h-2 w-2 sm:h-3 sm:w-3 theme-transition theme-text-secondary',
                open && 'rotate-180'
              )}
            />
          </div>
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="theme-surface-elevated min-w-[180px] sm:min-w-[220px] p-1 sm:p-2"
        align="start"
        sideOffset={8}
      >
        <DropdownMenuItem
          className={cn(
            'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
            value === 'editing' && 'theme-surface'
          )}
          onSelect={() => {
            setReadOnly(false);
            editor.tf.focus();
            setOpen(false);
          }}
        >
          <div className={cn('theme-transition', item.editing.color)}>{item.editing.icon}</div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm theme-text-primary">{item.editing.label}</span>
            </div>
            <p className="text-xs theme-text-secondary">{item.editing.description}</p>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          className={cn(
            'theme-button-ghost theme-transition flex items-center gap-2 p-2 rounded-lg',
            value === 'viewing' && 'theme-surface'
          )}
          onSelect={() => {
            setReadOnly(true);
            setOpen(false);
          }}
        >
          <div className={cn('theme-transition', item.viewing.color)}>{item.viewing.icon}</div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm theme-text-primary">{item.viewing.label}</span>
            </div>
            <p className="text-xs theme-text-secondary">{item.viewing.description}</p>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
