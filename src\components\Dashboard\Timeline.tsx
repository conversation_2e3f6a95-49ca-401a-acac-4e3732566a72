'use client';

import React, { useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  CheckCircle,
  Clock,
  Circle,
  Filter,
  PlusCircle,
  AlertTriangle,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { useProjectTimelineStore, FrontendTimelineItem } from '@/stores/projectTimelineStore';
import { format } from 'date-fns';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Image from 'next/image';

interface TimelineProps {
  projectId?: string;
  onCreateItem?: () => void;
}

const Timeline: React.FC<TimelineProps> = () => {
  const { items, isLoading, filter, fetchTimelineItems, updateItemStatus, setFilter } =
    useProjectTimelineStore();

  useEffect(() => {
    const filters: any = {};
    if (filter !== 'all') {
      filters.status = filter;
    }
    fetchTimelineItems(filters);
  }, [fetchTimelineItems, filter]);

  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter);
  };

  const handleStatusChange = async (itemId: string, newStatus: FrontendTimelineItem['status']) => {
    await updateItemStatus(itemId, newStatus);
  };

  const getStatusConfig = (status: FrontendTimelineItem['status']) => {
    const configs = {
      in_progress: {
        badge:
          'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800',
        dot: 'bg-blue-500 ring-blue-500/30',
        icon: <Clock className="w-3 h-3" />,
        label: 'In Progress',
      },
      completed: {
        badge:
          'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800',
        dot: 'bg-emerald-500 ring-emerald-500/30',
        icon: <CheckCircle className="w-3 h-3" />,
        label: 'Completed',
      },
      planned: {
        badge:
          'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800',
        dot: 'bg-slate-400 ring-slate-400/30',
        icon: <Circle className="w-3 h-3" />,
        label: 'Planned',
      },
      delayed: {
        badge:
          'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800',
        dot: 'bg-red-500 ring-red-500/30',
        icon: <AlertTriangle className="w-3 h-3" />,
        label: 'Delayed',
      },
    };
    return configs[status];
  };

  const TimelineItemComponent = ({ item }: { item: FrontendTimelineItem }) => {
    const statusConfig = getStatusConfig(item.status);
    const startDate = format(new Date(item.startDate), 'MMM d');
    const endDate = format(new Date(item.endDate), 'MMM d');

    return (
      <div className="relative group animate-in slide-in-from-left-4 duration-300">
        <div className="absolute left-[-21px] top-3 z-10">
          <div
            className={cn(
              'w-3 h-3 rounded-full border-2 border-background transition-all duration-200',
              'group-hover:scale-125 group-hover:ring-2 ring-offset-1 ring-offset-background',
              statusConfig?.dot
            )}
          />
        </div>

        <div
          className={cn(
            'p-3 rounded-lg transition-all duration-200 border',
            'bg-card/80 backdrop-blur-sm hover:bg-card',
            'border-border/50 hover:border-border',
            'hover:shadow-md hover:-translate-y-0.5'
          )}
        >
          <div className="flex items-center justify-between gap-2 mb-2">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className={cn('p-1 rounded', statusConfig?.badge)}>{statusConfig?.icon}</div>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <h3 className="font-medium text-base  truncate cursor-default">{item.title}</h3>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-sm">{item.title}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="flex items-center gap-2 shrink-0">
              <div
                className={cn(
                  'px-2 py-1 rounded-md text-sm font-medium border hidden sm:flex items-center gap-1',
                  statusConfig?.badge
                )}
              >
                <span>{statusConfig?.label}</span>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-accent">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(item.id, 'planned')}
                    className="text-sm"
                  >
                    <Circle className="w-3 h-3 mr-2" />
                    Planned
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(item.id, 'in_progress')}
                    className="text-sm"
                  >
                    <Clock className="w-3 h-3 mr-2" />
                    In Progress
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(item.id, 'completed')}
                    className="text-sm"
                  >
                    <CheckCircle className="w-3 h-3 mr-2" />
                    Completed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(item.id, 'delayed')}
                    className="text-sm"
                  >
                    <AlertTriangle className="w-3 h-3 mr-2" />
                    Delayed
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {item.description && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2 cursor-default">
                    {item.description}
                  </p>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p className="text-sm">{item.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span className="shrink-0">
              {startDate} - {endDate}
            </span>

            {/* Compact avatar stack */}
            <div className="flex items-center gap-1">
              <div className="flex -space-x-1">
                {item.users.slice(0, 3).map((user, index) => (
                  <TooltipProvider key={user.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Image
                          src={
                            user.avatar ||
                            `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random&size=24`
                          }
                          alt={user.name}
                          className="w-6 h-6 rounded-full border border-background hover:z-10 hover:scale-110 transition-transform cursor-pointer"
                          style={{ zIndex: 10 - index }}
                          height={24}
                          width={24}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-sm">{user.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}

                {item.users.length > 3 && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="w-5 h-5 rounded-full bg-muted text-muted-foreground text-[10px] font-medium flex items-center justify-center border border-background cursor-pointer hover:scale-110 transition-transform">
                          +{item.users.length - 3}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-sm max-w-sm">
                          {item.users
                            .slice(3)
                            .map(u => u.name)
                            .join(', ')}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>

              <span className="text-[11px] ml-1">{item.users.length}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[1, 2, 3, 4].map(i => (
        <div key={i} className="relative pl-4">
          <div className="absolute -left-4 top-3 w-3 h-3 rounded-full bg-muted animate-pulse" />
          <div className="p-3 rounded-lg bg-card/50 border border-border/30 space-y-2">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
            <Skeleton className="h-3 w-full" />
            <div className="flex justify-between items-center">
              <Skeleton className="h-3 w-20" />
              <div className="flex -space-x-1">
                <Skeleton className="h-5 w-5 rounded-full" />
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <Card className={cn('theme-surface-elevated h-full')}>
      <div className="max-h-[86vh] overflow-hidden">
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
          <div className="space-y-1">
            <CardTitle className="theme-text-primary text-lg sm:text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent leading-tight">
              Project Timeline
            </CardTitle>
            <CardDescription className="text-sm theme-text-secondary">
              Track your project milestones and progress
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-7 text-sm theme-button-secondary">
                <Filter className="mr-1 h-3 w-3" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleFilterChange('all')} className="text-sm">
                All Items
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleFilterChange('planned')} className="text-sm">
                Planned
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleFilterChange('in_progress')}
                className="text-sm"
              >
                In Progress
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleFilterChange('completed')} className="text-sm">
                Completed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleFilterChange('delayed')} className="text-sm">
                Delayed
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>

        <CardContent className="p-0 h-full overflow-auto">
          <ScrollArea className="h-[calc(100vh-27vh)]">
            <div className="relative pl-6 space-y-3 pb-4">
              {/* Thin Timeline Line */}
              <div className="absolute left-2.5 top-0 bottom-0 w-px bg-border/50" />

              {isLoading && <LoadingSkeleton />}

              {!isLoading &&
                items.length > 0 &&
                items.map((item, index) => (
                  <div
                    key={item.id}
                    style={{
                      animationDelay: `${index * 50}ms`,
                    }}
                  >
                    <TimelineItemComponent item={item} />
                  </div>
                ))}

              {!isLoading && items.length === 0 && (
                <div className="text-center py-8 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full theme-surface flex items-center justify-center mb-3 border border-dashed border-border/50">
                    <PlusCircle className="w-5 h-5 theme-text-secondary" />
                  </div>
                  <h3 className="text-sm font-medium theme-text-primary mb-1">No items yet</h3>
                  <p className="text-sm theme-text-secondary max-w-sm">
                    Create timeline items to track progress
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </div>
    </Card>
  );
};

export default Timeline;
