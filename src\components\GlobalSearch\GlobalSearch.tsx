import { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, Filter, Loader2 } from 'lucide-react';
import { GlobalSearchService, SearchQuery, SearchResult } from '@/services/GlobalSearch.service';
import { SearchResultItem } from './SearchResultItem';
import { SearchFilters } from './SearchFilters';
import { useDebounce } from '@/hooks/useDebounce';
import { toast } from 'sonner';
import { Input } from '../ui/input';

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GlobalSearch = ({ isOpen, onClose }: GlobalSearchProps) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<Partial<SearchQuery>>({});
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [searchTime, setSearchTime] = useState(0);
  const [isCached, setIsCached] = useState(false);

  const debouncedQuery = useDebounce(query, 300);
  const inputRef = useRef<HTMLInputElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }

    // Load popular searches
    const loadPopularSearches = async () => {
      try {
        const response = await fetch('/api/search/popular');
        if (response.ok) {
          const data = await response.json();
          setPopularSearches(data.searches || []);
        }
      } catch (error) {
        console.error('Failed to load popular searches:', error);
      }
    };

    if (isOpen) {
      loadPopularSearches();
    }
  }, [isOpen]);

  useEffect(() => {
    if (debouncedQuery.trim().length >= 2) {
      // Only search if query is at least 2 characters
      performSearch();
    } else {
      setResults([]);
      setTotalResults(0);
      setSearchTime(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedQuery, filters]);

  const performSearch = async () => {
    if (!debouncedQuery.trim()) return;

    setIsLoading(true);
    try {
      const searchParams: SearchQuery = {
        query: debouncedQuery,
        ...filters,
        limit: 20,
      };

      const response = await GlobalSearchService.search(searchParams);

      // Handle successful response
      setResults(response.results || []);
      setTotalResults(response.total || 0);
      setSearchTime(response.searchTime || 0);
      setIsCached(response.cached || false);

      // Save to recent searches only if we got results or no error occurred
      const updatedRecent = [
        debouncedQuery,
        ...recentSearches.filter(s => s !== debouncedQuery),
      ].slice(0, 5);
      setRecentSearches(updatedRecent);
      localStorage.setItem('recentSearches', JSON.stringify(updatedRecent));
    } catch (error: any) {
      console.error('Search error:', error);

      // Set empty results instead of showing error
      setResults([]);
      setTotalResults(0);
      setSearchTime(0);

      // Only show toast for actual network/server errors, not "no results"
      if (error?.response?.status !== 500 && error?.message !== 'Network Error') {
        // Don't show error toast for empty results
      } else {
        toast.error('Search service temporarily unavailable. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setTotalResults(0);
    inputRef.current?.focus();
  };

  const handleRecentSearch = (searchTerm: string) => {
    setQuery(searchTerm);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className="flex items-start justify-center min-h-screen pt-16 px-4">
        <div
          ref={modalRef}
          className="w-full max-w-2xl theme-surface-elevated rounded-2xl theme-shadow-lg overflow-hidden glow-on-hover"
        >
          {/* Search Header */}
          <div className="p-4 theme-border border-b">
            <div className="relative flex items-center gap-3">
              <Search className="w-5 h-5 theme-text-secondary flex-shrink-0" />
              <Input
                ref={inputRef}
                type="text"
                value={query}
                onChange={e => setQuery(e.target.value)}
                placeholder="Search tasks, projects, notes, and users..."
                className="flex-1  theme-text-primary placeholder:theme-text-secondary theme-focus"
              />
              <div className="flex items-center gap-2">
                {query && (
                  <button
                    onClick={clearSearch}
                    className="p-1 theme-hover-surface rounded-full theme-transition"
                  >
                    <X className="w-4 h-4 theme-text-secondary" />
                  </button>
                )}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`p-2 rounded-lg theme-transition ${
                    showFilters ? 'theme-active-primary' : 'theme-hover-surface'
                  }`}
                >
                  <Filter className="w-4 h-4" />
                </button>
                <button
                  onClick={onClose}
                  className="p-2 theme-hover-surface rounded-lg theme-transition"
                >
                  <X className="w-4 h-4 theme-text-secondary" />
                </button>
              </div>
            </div>

            {/* Search Stats */}
            {(totalResults > 0 || searchTime > 0) && (
              <div className="mt-2 text-sm theme-text-secondary flex items-center gap-4">
                {totalResults > 0 && (
                  <span className="theme-badge-secondary">{totalResults} results</span>
                )}
                {searchTime > 0 && (
                  <span className="theme-badge-primary">
                    {searchTime}ms {isCached && '(cached)'}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Filters */}
          {showFilters && <SearchFilters filters={filters} onFiltersChange={setFilters} />}

          {/* Search Content */}
          <div className="max-h-96 overflow-y-auto theme-scrollbar">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
                <span className="ml-2 theme-text-secondary">Searching...</span>
              </div>
            ) : query.trim() ? (
              results.length > 0 ? (
                <div className="divide-y theme-divider">
                  {results.map(result => (
                    <SearchResultItem key={result.id} result={result} onSelect={onClose} />
                  ))}
                </div>
              ) : (
                <div className="empty-state-container">
                  <div className="empty-state-icon">
                    <Search className="w-8 h-8 theme-text-secondary" />
                  </div>
                  <h3 className="empty-state-title">No results found</h3>
                  <p className="empty-state-description">
                    No results found for "{query}". Try adjusting your search terms or filters.
                  </p>
                </div>
              )
            ) : (
              <div className="p-4">
                {recentSearches.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium theme-text-primary mb-3 flex items-center gap-2">
                      <Clock className="w-4 h-4 theme-text-secondary" />
                      Recent Searches
                    </h3>
                    <div className="space-y-1">
                      {recentSearches.map((search, index) => (
                        <button
                          key={index}
                          onClick={() => handleRecentSearch(search)}
                          className="w-full text-left px-3 py-2 text-sm interactive-hover rounded-lg theme-transition"
                        >
                          {search}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {popularSearches.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium theme-text-primary mb-3 flex items-center gap-2">
                      <Search className="w-4 h-4 theme-text-secondary" />
                      Popular Searches
                    </h3>
                    <div className="space-y-1">
                      {popularSearches.slice(0, 5).map((search, index) => (
                        <button
                          key={index}
                          onClick={() => handleRecentSearch(search)}
                          className="w-full text-left px-3 py-2 text-sm interactive-hover rounded-lg theme-transition"
                        >
                          {search}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="text-center theme-text-secondary">
                  <div className="w-16 h-16 mx-auto mb-4 theme-surface rounded-full flex items-center justify-center">
                    <Search className="w-8 h-8 opacity-50" />
                  </div>
                  <p className="text-lg font-medium mb-2 theme-text-primary">Search Everything</p>
                  <p className="text-sm">Find tasks, projects, notes, and team members instantly</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
