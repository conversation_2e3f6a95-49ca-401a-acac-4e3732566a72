'use client';

import React, { useState } from 'react';
import { Plus, RefreshCw, ArrowRight, SearchIcon, Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { NotionConnectModal } from '@/components/Integrations/NotionConnectModal';
import { GitHubConnectModal } from '@/components/Integrations/GitHubConnectModal';
import {
  ConnectedIntegrationCard,
  AvailableIntegrationCard,
  FeaturedIntegrationCard,
} from '@/components/Integrations/IntegrationCard';
import { toast } from 'sonner';
import { useIntegrations } from '@/hooks/useIntegrations';
import { useIntegrationFilters, useIntegrationActions } from '@/hooks/useIntegrationFilters';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { AVAILABLE_INTEGRATIONS } from '@/constant/Integration';
import { useRouter } from 'next/navigation';
import { EmptyState } from '@/components/Global/EmptyState';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';
import { useQueryClient } from '@tanstack/react-query';

export default function IntegrationsPage() {
  const [activeTab, setActiveTab] = useState('available');
  const [isNotionModalOpen, setIsNotionModalOpen] = useState(false);
  const [isGitHubModalOpen, setIsGitHubModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  const queryClient = useQueryClient();

  const { integrations, isLoading, error, syncNotion, disconnectNotion } = useIntegrations();
  const { handleGitHubDisconnect } = useIntegrationActions();
  const { connectedProviders, availableIntegrations, connectedIntegrations } =
    useIntegrationFilters({
      integrations: integrations || [],
      searchQuery,
    });

  const handleDisconnect = async (provider: string) => {
    try {
      if (provider === 'github') {
        await handleGitHubDisconnect();
        toast.success('GitHub disconnected successfully');
        queryClient.invalidateQueries({ queryKey: ['integrations'] });
      } else if (provider === 'notion') {
        disconnectNotion();
      } else {
        const integrationMeta =
          AVAILABLE_INTEGRATIONS[provider as keyof typeof AVAILABLE_INTEGRATIONS];
        toast.info(`Disconnecting ${integrationMeta?.name} will be available soon.`);
      }
    } catch (error: any) {
      toast.error(`Failed to disconnect ${provider}`, {
        description: error.message || 'Please try again later.',
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="theme-surface px-3 rounded-lg py-2 h-full w-full overflow-y-auto custom-scrollbar"
    >
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-3">
        <div>
          <h2 className="text-xl font-bold theme-text-primary">Integrations</h2>
          <p className="theme-text-secondary mt-1 text-sm">
            Connect your favorite tools and services
          </p>
        </div>

        <div className="relative w-full sm:w-56">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 theme-text-secondary h-4 w-4" />
          <Input
            placeholder="Search integrations..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-9 pr-4 py-2 theme-input text-sm"
          />
        </div>
      </div>

      <Tabs defaultValue={activeTab} className="space-y-6" onValueChange={setActiveTab}>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 border-b border-border/20 pb-3">
          <TabsList className="w-full sm:w-auto">
            <TabsTrigger value="connected" className="text-sm">
              Connected ({connectedIntegrations.length})
            </TabsTrigger>
            <TabsTrigger value="available" className="text-sm">
              Available ({availableIntegrations.length})
            </TabsTrigger>
          </TabsList>

          <div className="hidden sm:flex items-center gap-2 text-xs theme-text-secondary">
            <Filter className="h-3 w-3" />
            <span>
              Showing{' '}
              {activeTab === 'connected'
                ? connectedIntegrations.length
                : availableIntegrations.length}{' '}
              integrations
            </span>
          </div>
        </div>

        <TabsContent
          value="connected"
          className="space-y-6 mt-4 focus-visible:outline-none focus-visible:ring-0"
        >
          <AnimatePresence>
            {isLoading ? (
              <LoadingSpinner
                variant="dots"
                size="md"
                color="primary"
                label="Loading your integrations..."
                showLabel={true}
              />
            ) : error ? (
              <EmptyState
                type="error"
                title="Failed to load integrations"
                description="We couldn't fetch your connected integrations. Please try again."
                actions={[
                  {
                    label: 'Retry',
                    onClick: () => window.location.reload(),
                    icon: <RefreshCw className="h-3 w-3" />,
                  },
                ]}
                size="md"
                animated={true}
              />
            ) : connectedIntegrations.length === 0 ? (
              searchQuery ? (
                <EmptyState
                  type="search"
                  title="No matching integrations"
                  description={`No connected integrations match your search for "${searchQuery}"`}
                  actions={[
                    {
                      label: 'Clear search',
                      onClick: () => setSearchQuery(''),
                      variant: 'outline',
                    },
                  ]}
                  size="md"
                  animated={true}
                />
              ) : (
                <EmptyState
                  type="custom"
                  icon={<Plus className="w-full h-full" />}
                  title="No connected integrations yet"
                  description="Get started by connecting your first integration from our available options"
                  actions={[
                    {
                      label: 'Browse Available Integrations',
                      onClick: () => setActiveTab('available'),
                      icon: <ArrowRight className="h-3 w-3" />,
                    },
                  ]}
                  size="md"
                  animated={true}
                />
              )
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {connectedIntegrations.map((integration: any, i: number) => (
                  <ConnectedIntegrationCard
                    key={integration.id}
                    integration={integration}
                    index={i}
                    onDisconnect={() => handleDisconnect(integration.provider)}
                    onSync={() => {
                      if (integration.provider === 'notion') {
                        syncNotion();
                      } else {
                        const integrationMeta =
                          AVAILABLE_INTEGRATIONS[
                            integration.provider as keyof typeof AVAILABLE_INTEGRATIONS
                          ];
                        toast.info(`Syncing ${integrationMeta?.name} will be available soon.`);
                      }
                    }}
                    onOpenHub={
                      integration.provider === 'notion'
                        ? () => router.push('/notion-hub')
                        : integration.provider === 'github'
                          ? () => router.push('/github-hub')
                          : undefined
                    }
                  />
                ))}
              </div>
            )}
          </AnimatePresence>
        </TabsContent>

        <TabsContent
          value="available"
          className="space-y-6 mt-4 focus-visible:outline-none focus-visible:ring-0"
        >
          <AnimatePresence>
            {!connectedProviders.has('notion') && searchQuery === '' && (
              <FeaturedIntegrationCard
                integration={AVAILABLE_INTEGRATIONS.notion}
                onConnect={() => setIsNotionModalOpen(true)}
                features={['Import pages', 'Database sync', 'Two-way sync', 'Auto-conversion']}
                learnMoreUrl="https://www.notion.so/my-integrations"
              />
            )}
            {availableIntegrations.length > 0 ? (
              <div>
                {searchQuery === '' && (
                  <h3 className="text-base font-medium mb-4 flex items-center theme-text-primary">
                    <Badge
                      variant="outline"
                      className="mr-2 bg-blue-50 text-blue-700 border-blue-200 text-xs"
                    >
                      Explore
                    </Badge>
                    <span>Available Integrations</span>
                  </h3>
                )}
                {searchQuery !== '' && (
                  <div className="mb-4">
                    <h3 className="text-base font-medium flex items-center theme-text-primary">
                      <SearchIcon className="h-4 w-4 mr-2 theme-text-secondary" />
                      <span>Search results for "{searchQuery}"</span>
                    </h3>
                    <p className="text-xs theme-text-secondary mt-1">
                      Found {availableIntegrations.length} integration
                      {availableIntegrations.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                )}

                {isLoading ? (
                  <LoadingSpinner
                    variant="dots"
                    size="md"
                    color="primary"
                    label="Loading integrations..."
                    showLabel={true}
                  />
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {availableIntegrations
                      .filter(
                        integration =>
                          integration.id !== 'notion' ||
                          connectedProviders.has('notion') ||
                          searchQuery !== ''
                      )
                      .map((integration, i) => (
                        <AvailableIntegrationCard
                          key={integration.id}
                          integration={integration}
                          index={i}
                          onConnect={() => {
                            if (integration.id === 'notion') {
                              setIsNotionModalOpen(true);
                            } else if (integration.id === 'github') {
                              setIsGitHubModalOpen(true);
                            } else {
                              toast.info(
                                `${integration.name} integration will be available soon.`,
                                {
                                  description: 'Check back later for updates.',
                                }
                              );
                            }
                          }}
                        />
                      ))}
                  </div>
                )}
              </div>
            ) : searchQuery !== '' ? (
              <EmptyState
                type="search"
                title="No matching integrations"
                description={`No available integrations match your search for "${searchQuery}"`}
                actions={[
                  {
                    label: 'Clear search',
                    onClick: () => setSearchQuery(''),
                    variant: 'outline',
                  },
                ]}
                size="md"
                animated={true}
              />
            ) : null}
          </AnimatePresence>
        </TabsContent>
      </Tabs>

      {/* Notion Connect Modal */}
      <NotionConnectModal isOpen={isNotionModalOpen} onOpenChange={setIsNotionModalOpen} />

      {/* GitHub Connect Modal */}
      <GitHubConnectModal isOpen={isGitHubModalOpen} onOpenChange={setIsGitHubModalOpen} />
    </motion.div>
  );
}
