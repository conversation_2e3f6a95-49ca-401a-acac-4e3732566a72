import { useState, useEffect, useCallback } from 'react';

type NotificationPermission = 'default' | 'granted' | 'denied';

export interface UsePushNotifications {
    isSupported: boolean;
    permission: NotificationPermission;
    isSubscribed: boolean;
    isLoading: boolean;
    error: string | null;
    subscribe: () => Promise<boolean>;
    unsubscribe: () => Promise<boolean>;
    requestPermission: () => Promise<NotificationPermission>;
}

const VAPID_PUBLIC_KEY_ENDPOINT = '/api/push/vapid-key';
const SUBSCRIBE_ENDPOINT = '/api/push/subscribe';

export function usePushNotifications(): UsePushNotifications {
    const [isSupported, setIsSupported] = useState(false);
    const [permission, setPermission] = useState<NotificationPermission>('default');
    const [isSubscribed, setIsSubscribed] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Check if push notifications are supported
    useEffect(() => {
        const checkSupport = () => {
            const supported =
                'serviceWorker' in navigator &&
                'PushManager' in window &&
                'Notification' in window;

            setIsSupported(supported);

            if (supported) {
                setPermission(Notification.permission);
                checkSubscriptionStatus();
            }
        };

        checkSupport();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Check current subscription status
    const checkSubscriptionStatus = useCallback(async () => {
        if (!isSupported) return;

        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.getSubscription();
            setIsSubscribed(!!subscription);
        } catch (err) {
            console.error('Error checking subscription status:', err);
            setError('Failed to check subscription status');
        }
    }, [isSupported]);

    // Get VAPID public key from server
    const getVapidPublicKey = useCallback(async (): Promise<string> => {
        try {
            const response = await fetch(VAPID_PUBLIC_KEY_ENDPOINT);
            if (!response.ok) {
                throw new Error('Failed to get VAPID public key');
            }
            const data = await response.json();
            return data.publicKey;
        } catch (err) {
            console.error('Error getting VAPID public key:', err);
            throw new Error('Failed to get VAPID public key');
        }
    }, []);

    // Convert VAPID key to Uint8Array
    const urlBase64ToUint8Array = useCallback((base64String: string): Uint8Array => {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }, []);

    // Request notification permission
    const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
        if (!isSupported) {
            throw new Error('Push notifications are not supported');
        }

        try {
            const permission = await Notification.requestPermission();
            setPermission(permission);
            return permission;
        } catch (err) {
            console.error('Error requesting permission:', err);
            setError('Failed to request notification permission');
            throw err;
        }
    }, [isSupported]);

    // Subscribe to push notifications
    const subscribe = useCallback(async (): Promise<boolean> => {
        if (!isSupported) {
            setError('Push notifications are not supported');
            return false;
        }

        if (permission !== 'granted') {
            const newPermission = await requestPermission();
            if (newPermission !== 'granted') {
                setError('Notification permission denied');
                return false;
            }
        }

        setIsLoading(true);
        setError(null);

        try {
            // Register service worker if not already registered
            let registration = await navigator.serviceWorker.getRegistration();
            if (!registration) {
                registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                await navigator.serviceWorker.ready;
            }

            // Check if already subscribed
            let subscription = await registration.pushManager.getSubscription();

            if (!subscription) {
                // Get VAPID public key
                const vapidPublicKey = await getVapidPublicKey();
                const applicationServerKey = urlBase64ToUint8Array(vapidPublicKey);

                // Create new subscription
                subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: applicationServerKey
                });
            }

            // Send subscription to server
            const response = await fetch(SUBSCRIBE_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscription.toJSON())
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to save subscription');
            }

            setIsSubscribed(true);
            console.log('Push notification subscription successful');
            return true;

        } catch (err: any) {
            console.error('Error subscribing to push notifications:', err);
            setError(err.message || 'Failed to subscribe to push notifications');
            return false;
        } finally {
            setIsLoading(false);
        }
    }, [isSupported, permission, requestPermission, getVapidPublicKey, urlBase64ToUint8Array]);

    // Unsubscribe from push notifications
    const unsubscribe = useCallback(async (): Promise<boolean> => {
        if (!isSupported) {
            setError('Push notifications are not supported');
            return false;
        }

        setIsLoading(true);
        setError(null);

        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.getSubscription();

            if (subscription) {
                // Unsubscribe from browser
                await subscription.unsubscribe();

                // Remove subscription from server
                const response = await fetch(SUBSCRIBE_ENDPOINT, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    console.warn('Failed to remove subscription from server, but local unsubscribe succeeded');
                }
            }

            setIsSubscribed(false);
            console.log('Push notification unsubscribe successful');
            return true;

        } catch (err: any) {
            console.error('Error unsubscribing from push notifications:', err);
            setError(err.message || 'Failed to unsubscribe from push notifications');
            return false;
        } finally {
            setIsLoading(false);
        }
    }, [isSupported]);

    // Listen for permission changes
    useEffect(() => {
        if (!isSupported) return;

        const handlePermissionChange = () => {
            setPermission(Notification.permission);
            if (Notification.permission !== 'granted') {
                setIsSubscribed(false);
            }
        };

        // Check for permission changes periodically
        const interval = setInterval(() => {
            if (Notification.permission !== permission) {
                handlePermissionChange();
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [isSupported, permission]);

    return {
        isSupported,
        permission,
        isSubscribed,
        isLoading,
        error,
        subscribe,
        unsubscribe,
        requestPermission
    };
}