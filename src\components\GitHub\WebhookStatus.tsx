'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Webhook, Activity, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useWebhook } from '@/hooks/useWebhook';
import { formatDistanceToNow } from 'date-fns';

interface WebhookStatusProps {
  repositoryId: string;
  className?: string;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isConnected?: boolean;
}

export const WebhookStatus: React.FC<WebhookStatusProps> = ({
  repositoryId,
  className,
  showDetails = false,
  size = 'md',
  isConnected = false,
}) => {
  const {
    webhook,
    webhookStatus,
    webhookHealth,
    isLoadingWebhook,
    hasWebhook,
    isWebhookActive,
    hasWebhookErrors,
  } = useWebhook(repositoryId, isConnected);

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }[size];

  const textSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }[size];

  if (isLoadingWebhook) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <RefreshCw className={cn(iconSize, 'animate-spin text-gray-400')} />
        {showDetails && <span className={cn(textSize, 'text-gray-500')}>Loading...</span>}
      </div>
    );
  }

  if (!hasWebhook) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn('flex items-center gap-2', className)}>
              <Webhook className={cn(iconSize, 'text-gray-300')} />
              {showDetails && <span className={cn(textSize, 'text-gray-500')}>No webhook</span>}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>No webhook configured</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  const getStatusIcon = () => {
    if (!isWebhookActive) {
      return <AlertCircle className={cn(iconSize, 'text-orange-500')} />;
    }
    if (hasWebhookErrors) {
      return <AlertCircle className={cn(iconSize, 'text-red-500')} />;
    }
    return <CheckCircle className={cn(iconSize, 'text-green-500')} />;
  };

  const getStatusColor = () => {
    if (!isWebhookActive) return 'text-orange-500';
    if (hasWebhookErrors) return 'text-red-500';
    return 'text-green-500';
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn('flex items-center gap-2', className)}
          >
            {getStatusIcon()}
            {showDetails && (
              <div className="flex flex-col">
                <span className={cn(textSize, 'font-medium', getStatusColor())}>
                  {webhookStatus.statusText}
                </span>
                {webhook && (
                  <span className={cn('text-xs text-gray-500')}>
                    {webhook.deliveryCount} deliveries
                  </span>
                )}
              </div>
            )}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-medium">{webhookStatus.statusText}</p>
            <p className="text-xs text-gray-500">{webhookStatus.deliveryInfo}</p>
            {webhook && (
              <>
                <div className="flex items-center gap-1 text-xs">
                  <Activity className={cn('h-3 w-3', webhookHealth.color)} />
                  <span className={webhookHealth.color}>
                    {webhookHealth.label} ({webhookHealth.score}%)
                  </span>
                </div>
                {webhook.lastDeliveryAt && (
                  <p className="text-xs text-gray-500">
                    Last:{' '}
                    {formatDistanceToNow(new Date(webhook.lastDeliveryAt), { addSuffix: true })}
                  </p>
                )}
              </>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

interface WebhookBadgeProps {
  repositoryId: string;
  className?: string;
  isConnected?: boolean;
}

export const WebhookBadge: React.FC<WebhookBadgeProps> = ({
  repositoryId,
  className,
  isConnected = false,
}) => {
  const { webhookStatus, isLoadingWebhook, hasWebhook } = useWebhook(repositoryId, isConnected);

  if (isLoadingWebhook) {
    return (
      <Badge variant="outline" className={cn('text-xs', className)}>
        <RefreshCw className="mr-1 h-2 w-2 animate-spin" />
        Loading
      </Badge>
    );
  }

  if (!hasWebhook) {
    return (
      <Badge variant="outline" className={cn('text-xs text-gray-500', className)}>
        <Webhook className="mr-1 h-2 w-2" />
        No Webhook
      </Badge>
    );
  }

  return (
    <Badge variant="outline" className={cn('text-xs', webhookStatus.statusColor, className)}>
      <Webhook className="mr-1 h-2 w-2" />
      {webhookStatus.statusText}
    </Badge>
  );
};

interface WebhookHealthIndicatorProps {
  repositoryId: string;
  className?: string;
  isConnected?: boolean;
}

export const WebhookHealthIndicator: React.FC<WebhookHealthIndicatorProps> = ({
  repositoryId,
  className,
  isConnected = false,
}) => {
  const { webhookHealth, isLoadingWebhook, hasWebhook } = useWebhook(repositoryId, isConnected);

  if (isLoadingWebhook || !hasWebhook) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn('flex items-center gap-1', className)}>
            <Activity className={cn('h-3 w-3', webhookHealth.color)} />
            <span className={cn('text-xs font-medium', webhookHealth.color)}>
              {webhookHealth.score}%
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Webhook Health: {webhookHealth.label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
