import React from 'react';
import { TeamMember } from './ResourceAllocationGrid';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CustomSkillsSelect } from './CustomSkillsSelect';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from '@/hooks/use-toast';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import ReactSelect, { Option } from '@/components/Global/ReactSelect';

interface MemberEditFormProps {
  member: TeamMember;
  onSave: () => void;
}

const memberSchema = yup.object({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  role: yup.string().required('Role is required'),
  skills: yup.array().of(yup.string()),
  availableHours: yup
    .number()
    .min(0, 'Hours cannot be negative')
    .required('Available hours is required'),
  notes: yup.string(),
});

type MemberFormValues = yup.InferType<typeof memberSchema>;

export function MemberEditForm({ member, onSave }: MemberEditFormProps) {
  const form = useForm<MemberFormValues>({
    resolver: yupResolver(memberSchema),
    defaultValues: {
      name: member.name,
      email: member.email,
      role: member.role,
      skills: member.skills,
      availableHours: member.availableHours,
      notes: '',
    },
  });

  const onSubmit = async (data: MemberFormValues) => {
    try {
      // In a real application, you would make an API call here
      // to update the member information
      await fetch(`/api/members/${member.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      toast({
        title: 'Member updated',
        description: 'Member information has been updated successfully',
      });

      onSave();
    } catch (error) {
      toast({
        title: 'Error updating member',
        description: 'There was an error updating the member information',
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input {...field} type="email" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Primary Role</FormLabel>
                <FormControl>
                  <ReactSelect
                    options={[
                      { value: 'Developer', label: 'Developer' },
                      { value: 'Designer', label: 'Designer' },
                      { value: 'Project Manager', label: 'Project Manager' },
                      { value: 'QA Engineer', label: 'QA Engineer' },
                      { value: 'DevOps Engineer', label: 'DevOps Engineer' },
                      { value: 'Product Manager', label: 'Product Manager' },
                    ]}
                    value={{ value: field.value, label: field.value }}
                    onChange={option => {
                      if (option) {
                        field.onChange((option as Option).value);
                      }
                    }}
                    placeholder="Select role"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="availableHours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Available Hours (per week)</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="skills"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Skills</FormLabel>
              <FormControl>
                <CustomSkillsSelect
                  selectedSkills={
                    field.value?.filter((skill): skill is string => skill !== undefined) || []
                  }
                  onChange={field.onChange}
                  placeholder="Select or add skills"
                />
              </FormControl>
              <FormDescription>Add or select skills relevant to this team member</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Add any additional notes about this team member"
                  rows={4}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={() => onSave()}>
            Cancel
          </Button>
          <Button type="submit">Save Changes</Button>
        </div>
      </form>
    </Form>
  );
}
