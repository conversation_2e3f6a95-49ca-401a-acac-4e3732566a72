import React from 'react';
import Modal from '../Global/Modal';
import { Clock, Edit, MapPin, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '../ui/button';

const EventDetailsModal = ({
  showEventDialog,
  setShowEventDialog,
  selectedEvent,
  allowEventEditing,
  handleUpdateEvent,
  allowEventDeletion,
  setShowDeleteConfirm,
}) => {
  return (
    <Modal isOpen={showEventDialog} onClose={() => setShowEventDialog(false)}>
      <div className="max-w-lg">
        <div>
          <pattern>{selectedEvent?.title}</pattern>
        </div>

        {selectedEvent && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span>
                {format(new Date(selectedEvent.startTime), 'MMM d, yyyy HH:mm')} -
                {format(new Date(selectedEvent.endTime), 'HH:mm')}
              </span>
            </div>

            {selectedEvent.location && (
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span>{selectedEvent.location}</span>
              </div>
            )}

            {selectedEvent.description && (
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-gray-600">{selectedEvent.description}</p>
              </div>
            )}

            {selectedEvent.attendees.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Attendees ({selectedEvent.attendees.length})</h4>
                <div className="space-y-1">
                  {selectedEvent.attendees.map((attendee, index) => (
                    <div key={index} className="text-sm">
                      {attendee.name} ({attendee.email})
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              {allowEventEditing && (
                <Button
                  variant="outline"
                  onClick={() => {
                    handleUpdateEvent(selectedEvent._id as string, {
                      title: selectedEvent.title,
                      description: selectedEvent.description,
                      location: selectedEvent.location,
                      startTime: selectedEvent.startTime,
                      endTime: selectedEvent.endTime,
                    });
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              )}

              {allowEventDeletion && (
                <Button variant="destructive" onClick={() => setShowDeleteConfirm(true)}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EventDetailsModal;
