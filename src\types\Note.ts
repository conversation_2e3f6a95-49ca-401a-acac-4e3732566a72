export type Note = {
  _id: string;
  title: string;
  content: string;
  tags: string[];
  category: string;
  color: string;
  isPinned: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  userId: string;
  organizationId: string;
  collaborators: Array<{
    userId: { _id: string; name: string; email: string; avatar?: string };
    permission: 'view' | 'edit' | 'admin';
    addedAt: Date;
  }>;
  version: number;
  metadata: {
    wordCount: number;
    readingTime: number;
    lastViewedAt: Date;
    viewCount: number;
    exportedFormats: string[];
  };
  settings: {
    allowComments: boolean;
    allowDownload: boolean;
    allowPrint: boolean;
    autoSave: boolean;
    fontSize: 'small' | 'medium' | 'large';
    theme: 'light' | 'dark' | 'auto';
  };
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
};
export type CreateNoteData = {
  title: string;
  content: string;
  tags: string[];
  category: string;
  color: string;
  isPinned: boolean;
  isFavorite: boolean;
  settings: {
    allowComments: boolean;
    allowDownload: boolean;
    allowPrint: boolean;
    autoSave: boolean;
    fontSize: 'small' | 'medium' | 'large';
    theme: 'light' | 'dark' | 'auto';
  };
};
