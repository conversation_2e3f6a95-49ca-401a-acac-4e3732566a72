'use client';

import { DndPlugin } from '@udecode/plate-dnd';
import { PlaceholderPlugin, ImagePlugin } from '@udecode/plate-media/react';
import { NodeIdPlugin } from '@udecode/plate-node-id';
import axios from 'axios';
import { toast } from 'sonner';

import { DraggableAboveNodes } from '@/components/ui/EditorUiElement/draggable';

// Enhanced file upload handler for drag and drop
const handleDropFiles = async ({ dragItem, editor, target }: any) => {
  const files = dragItem.files as File[];

  for (const file of files) {
    // Check if it's an image file
    if (file.type.startsWith('image/')) {
      try {
        // Validate image
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          toast.error(`Unsupported image type: ${file.type}`);
          continue;
        }

        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
          toast.error('Image too large. Maximum size is 10MB.');
          continue;
        }

        // Create FormData for upload
        const formData = new FormData();
        formData.append('image', file);

        // Upload to backend
        const response = await axios.post('/api/notes/upload-image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data.success) {
          // Insert the uploaded image into the editor
          editor.tf.insertNodes(
            {
              children: [{ text: '' }],
              type: ImagePlugin.key,
              url: response.data.file.url,
              width: response.data.file.width,
              height: response.data.file.height,
              alt: file.name,
              caption: '',
            },
            { at: target, nextBlock: false }
          );

          toast.success(`${file.name} uploaded successfully`);
        } else {
          throw new Error(response.data.error || 'Upload failed');
        }
      } catch (error: any) {
        console.error('Upload error:', error);
        toast.error(`Failed to upload ${file.name}: ${error.message}`);
      }
    } else {
      // Use default behavior for non-image files
      editor
        .getTransforms(PlaceholderPlugin)
        .insert.media([file], { at: target, nextBlock: false });
    }
  }
};

export const dndPlugins = [
  NodeIdPlugin,
  DndPlugin.configure({
    options: {
      enableScroller: true,
      onDropFiles: handleDropFiles,
    },
    render: {
      aboveNodes: DraggableAboveNodes,
    },
  }),
] as const;
