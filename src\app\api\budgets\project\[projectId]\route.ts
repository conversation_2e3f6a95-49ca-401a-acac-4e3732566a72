import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { zValidator } from '@hono/zod-validator';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Budget } from '@/models/Budget';
import { Project } from '@/models/Project';
import { NotificationService } from '@/services/Notification.service';
import { z } from 'zod';

type Variables = {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/budgets/project');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const userData = {
      id: session.user.id || '',
      name: session.user.name || '',
      email: session.user.email || '',
      image: session.user.image || '',
      organizationId: session.user.organizationId || '',
    };
    c.set('user', userData);
    await next();
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

const expenseSchema = z.object({
  amount: z.number().min(0.01),
  category: z.string().min(1),
  description: z.string().optional(),
  date: z.string().datetime(),
  receiptNumber: z.string().optional(),
  vendor: z.string().optional(),
  attachments: z
    .array(
      z.object({
        name: z.string(),
        url: z.string(),
        public_id: z.string(),
      })
    )
    .optional(),
});

// Get budget for a specific project
app.get('/:projectId', async c => {
  const user = c.get('user');
  const projectId = c.req.param('projectId');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      projectId,
      organizationId: user.organizationId,
    }).populate('createdBy', 'name email');

    if (!budget) {
      return c.json({ error: 'Budget not found for this project' }, 404);
    }

    return c.json({ budget });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Add expense to budget
app.post('/:projectId/expenses', zValidator('json', expenseSchema), async c => {
  const user = c.get('user');
  const projectId = c.req.param('projectId');
  const expenseData = c.req.valid('json');

  try {
    await connectDB();

    const budget = await Budget.findOne({
      projectId,
      organizationId: user.organizationId,
    });

    if (!budget) {
      return c.json({ error: 'Budget not found' }, 404);
    }

    const category = budget.categories.find(cat => cat.name === expenseData.category);
    if (!category) {
      return c.json({ error: 'Category not found in budget' }, 400);
    }

    const expense = {
      ...expenseData,
      date: new Date(expenseData.date),
      createdBy: user.id,
      attachments: expenseData.attachments || [],
    };

    budget.expenses.push(expense);
    await budget.save();

    await checkBudgetThresholds(budget);

    return c.json({ success: true, expense, budget });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get budget expenses
app.get('/:projectId/expenses', async c => {
  const user = c.get('user');
  const projectId = c.req.param('projectId');
  const { category, startDate, endDate, limit = '50', skip = '0' } = c.req.query();

  try {
    await connectDB();

    const budget = await Budget.findOne({
      projectId,
      organizationId: user.organizationId,
    }).populate('expenses.createdBy', 'name email');

    if (!budget) {
      return c.json({ error: 'Budget not found' }, 404);
    }

    let expenses = budget.expenses;

    if (category) {
      expenses = expenses.filter(expense => expense.category === category);
    }

    if (startDate || endDate) {
      expenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        if (startDate && expenseDate < new Date(startDate)) return false;
        if (endDate && expenseDate > new Date(endDate)) return false;
        return true;
      });
    }

    expenses.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    const startIndex = parseInt(skip);
    const endIndex = startIndex + parseInt(limit);
    const paginatedExpenses = expenses.slice(startIndex, endIndex);

    return c.json({
      expenses: paginatedExpenses,
      total: expenses.length,
      page: Math.floor(startIndex / parseInt(limit)) + 1,
      totalPages: Math.ceil(expenses.length / parseInt(limit)),
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

async function checkBudgetThresholds(budget: any): Promise<void> {
  if (!budget.alerts?.enabled) return;

  const utilizationPercentage =
    budget.totalBudget > 0 ? (budget.spentAmount / budget.totalBudget) * 100 : 0;

  if (utilizationPercentage >= budget.alerts.thresholdPercentage) {
    try {
      const project = await Project.findById(budget.projectId);
      const recipients =
        budget.alerts.notifyUsers?.length > 0 ? budget.alerts.notifyUsers : project?.members || [];

      const notifications = recipients.map(userId => ({
        userId,
        type: 'error',
        title: 'Budget Threshold Alert',
        description: `Budget utilization has reached ${utilizationPercentage.toFixed(1)}% for project ${project?.name}`,
        metadata: {
          projectId: budget.projectId,
          budgetId: budget.budgetId,
          utilizationPercentage,
          threshold: budget.alerts.thresholdPercentage,
          remainingAmount: budget.totalBudget - budget.spentAmount,
        },
      }));

      await Promise.all(
        notifications.map(notification => NotificationService.createNotification(notification))
      );
    } catch (error) {
      console.error('Failed to send budget threshold notification:', error);
    }
  }

  for (const category of budget.categories) {
    const categoryUtilization =
      category.allocatedAmount > 0 ? (category.spentAmount / category.allocatedAmount) * 100 : 0;

    if (categoryUtilization >= budget.alerts.thresholdPercentage) {
      try {
        const project = await Project.findById(budget.projectId);
        const recipients =
          budget.alerts.notifyUsers?.length > 0
            ? budget.alerts.notifyUsers
            : project?.members || [];

        const notifications = recipients.map(userId => ({
          userId,
          type: 'error',
          title: 'Category Budget Alert',
          description: `${category.name} category has reached ${categoryUtilization.toFixed(1)}% utilization in project ${project?.name}`,
          metadata: {
            projectId: budget.projectId,
            budgetId: budget.budgetId,
            category: category.name,
            utilizationPercentage: categoryUtilization,
            threshold: budget.alerts.thresholdPercentage,
          },
        }));

        await Promise.all(
          notifications.map(notification => NotificationService.createNotification(notification))
        );
      } catch (error) {
        console.error('Failed to send category threshold notification:', error);
      }
    }
  }
}

export const GET = handle(app);
export const POST = handle(app);
