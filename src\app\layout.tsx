import React from 'react';
import type { Metadata } from 'next';
import './globals.css';
import { Inter, Outfit } from 'next/font/google';
import Providers from '@/components/Providers/query-provider';
import { NextUiProviders } from '@/context/HeroUiProvider';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import ClientProviders from '@/context/ClientProviders';
import { ThemeProvider } from '@/components/Providers/ThemeProvider';
import { ThemeSyncProvider } from '@/components/Providers/ThemeSyncProvider';
import { Analytics } from '@vercel/analytics/react';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorFallback from '@/components/ErrorFallback';

const inter = Inter({ subsets: ['latin'] });

const outfit = Outfit({
  subsets: ['latin'],
  variable: '--font-outfit',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'TaskFluxio',
  description: 'TaskFluxio is a project management tool that helps you manage your projects.',
  metadataBase: new URL('https://task-fluxio.vercel.app'),
  applicationName: 'TaskFluxio',
  keywords: [
    'project management',
    'task management',
    'productivity',
    'collaboration',
    'team management',
  ],
  authors: [{ name: 'TaskFluxio Team' }],
  creator: 'TaskFluxio Team',
  publisher: 'TaskFluxio',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://task-fluxio.vercel.app',
    title: 'TaskFluxio - Project Management Tool',
    description: 'A powerful project and task management tool for teams and individuals.',
    siteName: 'TaskFluxio',
    images: [
      {
        url: '/logo.png',
        width: 800,
        height: 600,
        alt: 'TaskFluxio Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TaskFluxio - Project Management Tool',
    description: 'A powerful project and task management tool for teams and individuals.',
    images: ['/logo.png'],
  },
  formatDetection: {
    telephone: false,
  },
  category: 'productivity',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5, viewport-fit=cover, user-scalable=yes"
        />
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://va.vercel-scripts.com; connect-src 'self' https://*.vercel.app; img-src 'self' data: blob: https://res.cloudinary.com https://*.googleusercontent.com; style-src 'self' 'unsafe-inline';"
        />
        <link rel="canonical" href="https://task-fluxio.vercel.app" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="TaskFluxio" />
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-navbutton-color" content="#3b82f6" />
        <link
          rel="preload"
          href="/fonts/GeistVF.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/GeistMonoVF.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link rel="preload" href="/logo.png" as="image" />
        <script
          id="connection-status"
          dangerouslySetInnerHTML={{
            __html: `
              window.addEventListener('online', function() {
                document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: true } }));
              });
              
              window.addEventListener('offline', function() {
                document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: false } }));
              });

              const isOnline = navigator.onLine;
              document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: isOnline } }));
            `,
          }}
        />
      </head>
      <body
        className={`${inter.className} ${outfit.variable} antialiased`}
        suppressHydrationWarning
      >
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <ClientProviders>
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem={false}
              disableTransitionOnChange
            >
              <ThemeSyncProvider>
                <Providers>
                  <NextUiProviders>
                    <main>{children}</main>
                    <Analytics />
                    <Toaster />
                    <Sonner richColors />
                  </NextUiProviders>
                </Providers>
              </ThemeSyncProvider>
            </ThemeProvider>
          </ClientProviders>
        </ErrorBoundary>
      </body>
    </html>
  );
}
