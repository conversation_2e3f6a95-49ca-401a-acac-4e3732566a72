import axios from 'axios';
// SubscriptionService not needed in this utility service

export interface UsageMetrics {
  projects: number;
  users: number;
  integrations: number;
  automations: number;
  apiCalls: number;
}

export interface UsageLimit {
  current: number;
  limit: number | 'unlimited';
  percentage: number;
  isNearLimit: boolean;
  isAtLimit: boolean;
  isOverLimit: boolean;
}

export interface UsageSummary {
  organizationId: string;
  planId: string;
  metrics: UsageMetrics;
  limits: {
    projects: UsageLimit;
    users: UsageLimit;
    storage: UsageLimit;
    integrations: UsageLimit;
    automations: UsageLimit;
    apiCalls: UsageLimit;
  };
  recommendations: {
    shouldUpgrade: boolean;
    recommendedPlan: string | null;
    reasons: string[];
  };
  lastUpdated: Date;
}

export class UsageTrackingService {
  private static readonly baseURL = '/api/subscriptions';

  /**
   * Get current usage metrics for an organization
   */
  static async getCurrentUsage(_organizationId: string): Promise<UsageMetrics> {
    try {
      const response = await axios.get(`${this.baseURL}/usage`);
      return response.data.usage.metrics;
    } catch (error: any) {
      console.error('Error getting current usage:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to retrieve usage metrics');
    }
  }

  /**
   * Get comprehensive usage summary
   */
  static async getUsageSummary(_organizationId: string): Promise<UsageSummary> {
    try {
      const response = await axios.get(`${this.baseURL}/usage`);
      return response.data.usage;
    } catch (error: any) {
      console.error('Error getting usage summary:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to generate usage summary');
    }
  }

  /**
   * Calculate usage limit status
   */
  static calculateUsageLimit(current: number, limit: number | 'unlimited'): UsageLimit {
    if (limit === 'unlimited') {
      return {
        current,
        limit,
        percentage: 0,
        isNearLimit: false,
        isAtLimit: false,
        isOverLimit: false,
      };
    }

    const percentage = (current / limit) * 100;

    return {
      current,
      limit,
      percentage: Math.min(percentage, 100),
      isNearLimit: percentage >= 80 && percentage < 100,
      isAtLimit: percentage >= 100,
      isOverLimit: current > limit,
    };
  }

  /**
   * Format usage for display
   */
  static formatUsageDisplay(usage: UsageLimit, type: string): string {
    if (usage.limit === 'unlimited') {
      return `${usage.current} / Unlimited`;
    }

    const unit = type === 'storage' ? 'MB' : '';
    return `${usage.current}${unit} / ${usage.limit}${unit}`;
  }

  /**
   * Get usage color based on percentage
   */
  static getUsageColor(usage: UsageLimit): 'green' | 'yellow' | 'red' {
    if (usage.isOverLimit || usage.isAtLimit) return 'red';
    if (usage.isNearLimit) return 'yellow';
    return 'green';
  }
}
