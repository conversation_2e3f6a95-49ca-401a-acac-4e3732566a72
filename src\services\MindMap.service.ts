import axios from 'axios';
import { MindMapFilters, CreateMindMapRequest, UpdateMindMapRequest } from '@/types/MindMapsTypes';

const API_BASE = '/api/mind-maps';

export const MindMapService = {
  getMindMaps: async (filters?: MindMapFilters) => {
    const params = new URLSearchParams();

    if (filters?.projectId && filters.projectId !== 'all') {
      params.append('projectId', filters.projectId);
    }
    if (filters?.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }

    const response = await axios.get(`${API_BASE}?${params}`);
    return response.data;
  },

  getMindMap: async (id: string) => {
    const response = await axios.get(`${API_BASE}/${id}`);
    return response.data;
  },

  createMindMap: async (data: CreateMindMapRequest) => {
    const response = await axios.post(API_BASE, data);
    return response.data;
  },

  updateMindMap: async (id: string, data: UpdateMindMapRequest) => {
    const response = await axios.put(`${API_BASE}/${id}`, data);
    return response.data;
  },

  deleteMindMap: async (id: string) => {
    const response = await axios.delete(`${API_BASE}/${id}`);
    return response.data;
  },

  duplicateMindMap: async (id: string, title: string) => {
    const response = await axios.post(`${API_BASE}/${id}/duplicate`, { title });
    return response.data;
  },
};
