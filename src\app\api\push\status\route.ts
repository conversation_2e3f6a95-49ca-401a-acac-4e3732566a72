import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { PushNotificationService } from '@/services/PushNotification.service';
import { User } from '@/models/User';
import { connectDB } from '@/Utility/db';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    systemRole?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/push/status');

app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      await connectDB();
      const user = await User.findById(session.user.id).select('systemRole');
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        systemRole: user?.systemRole || 'User',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Auth middleware error:', error);
  }
  await next();
});

// GET - Get comprehensive push notification system status
app.get('/', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  // Only allow admins to check system status
  if (user.systemRole !== 'Admin') {
    return c.json({ error: 'Forbidden - Admin access required' }, 403);
  }

  try {
    const [
      configStatus,
      subscriptionStats,
      connectivity,
      recommendations,
      lastCleanupTime
    ] = await Promise.all([
      PushNotificationService.getConfigStatus(),
      PushNotificationService.getSubscriptionStats(),
      PushNotificationService.testPushServiceConnectivity(),
      PushNotificationService.getCleanupRecommendations(),
      PushNotificationService.getLastCleanupTime()
    ]);

    const systemHealth = {
      overall: 'healthy' as 'healthy' | 'warning' | 'critical',
      issues: [] as string[],
    };

    // Determine overall system health
    if (!configStatus.configured) {
      systemHealth.overall = 'critical';
      systemHealth.issues.push('Push notifications not configured');
    } else if (!connectivity.reachable) {
      systemHealth.overall = 'critical';
      systemHealth.issues.push('Push service not reachable');
    } else if (subscriptionStats.invalid > subscriptionStats.total * 0.1) {
      systemHealth.overall = 'warning';
      systemHealth.issues.push('High number of invalid subscriptions');
    } else if (subscriptionStats.expired > subscriptionStats.total * 0.05) {
      systemHealth.overall = 'warning';
      systemHealth.issues.push('High number of expired subscriptions');
    }

    if (systemHealth.issues.length === 0) {
      systemHealth.issues.push('All systems operational');
    }

    return c.json({
      timestamp: new Date().toISOString(),
      systemHealth,
      configuration: {
        ...configStatus,
        vapidConfigured: !!process.env.VAPID_PUBLIC_KEY && !!process.env.VAPID_PRIVATE_KEY,
        emailConfigured: !!process.env.VAPID_EMAIL,
      },
      subscriptions: {
        ...subscriptionStats,
        healthPercentage: subscriptionStats.total > 0 
          ? Math.round((subscriptionStats.active / subscriptionStats.total) * 100)
          : 0,
      },
      connectivity,
      maintenance: {
        recommendations,
        lastCleanup: lastCleanupTime,
        cleanupNeeded: recommendations.some(r => r.includes('should be cleaned')),
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        platform: process.platform,
        nodeVersion: process.version,
      },
    });
  } catch (error: any) {
    console.error('Push status check error:', error);
    return c.json(
      { 
        error: 'Failed to get push notification system status',
        timestamp: new Date().toISOString(),
        systemHealth: {
          overall: 'critical',
          issues: ['System status check failed'],
        },
      },
      500
    );
  }
});

// POST - Perform system maintenance tasks
app.post('/cleanup', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  // Only allow admins to perform maintenance
  if (user.systemRole !== 'Admin') {
    return c.json({ error: 'Forbidden - Admin access required' }, 403);
  }

  try {
    const [invalidCleanup, expiredCleanup] = await Promise.all([
      PushNotificationService.cleanupInvalidSubscriptions(),
      PushNotificationService.cleanupExpiredSubscriptions(),
    ]);

    const totalCleaned = invalidCleanup.cleaned + expiredCleanup.cleaned;
    const totalErrors = invalidCleanup.errors + expiredCleanup.errors;

    return c.json({
      success: true,
      message: `Cleanup completed: ${totalCleaned} subscriptions cleaned, ${totalErrors} errors`,
      results: {
        invalidSubscriptions: invalidCleanup,
        expiredSubscriptions: expiredCleanup,
        totalCleaned,
        totalErrors,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('Push cleanup error:', error);
    return c.json(
      { error: 'Failed to perform push notification cleanup' },
      500
    );
  }
});

// GET - Get current user's push subscription status
app.get('/user', async (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  try {
    await connectDB();
    const userDoc = await User.findById(user.id).select('pushSubscription');

    const hasSubscription = !!(
      userDoc?.pushSubscription?.endpoint &&
      userDoc?.pushSubscription?.keys?.p256dh &&
      userDoc?.pushSubscription?.keys?.auth
    );

    let subscriptionValid = false;
    if (hasSubscription) {
      try {
        subscriptionValid = await PushNotificationService.testSubscription(user.id);
      } catch (error) {
        console.error('Subscription test error:', error);
      }
    }

    return c.json({
      userId: user.id,
      hasSubscription,
      subscriptionValid,
      subscribedAt: userDoc?.pushSubscription?.subscribedAt,
      expirationTime: userDoc?.pushSubscription?.expirationTime,
      pushConfigured: PushNotificationService.isConfigured(),
    });
  } catch (error: any) {
    console.error('User push status error:', error);
    return c.json(
      { error: 'Failed to get user push subscription status' },
      500
    );
  }
});

export const GET = handle(app);
export const POST = handle(app);
