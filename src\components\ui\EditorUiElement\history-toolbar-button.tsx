'use client';

import * as React from 'react';

import { useEditorRef, useEditorSelector } from '@udecode/plate/react';
import { Redo2Icon, Undo2Icon } from 'lucide-react';

import { ToolbarButton } from './toolbar';
import { cn } from '@/lib/utils';

export function RedoToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const editor = useEditorRef();
  const disabled = useEditorSelector(editor => editor.history.redos.length === 0, []);

  return (
    <ToolbarButton
      {...props}
      disabled={disabled}
      onClick={() => editor.redo()}
      onMouseDown={e => e.preventDefault()}
      tooltip="Redo"
    >
      <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
        <Redo2Icon className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}

export function UndoToolbarButton(props: React.ComponentProps<typeof ToolbarButton>) {
  const editor = useEditorRef();
  const disabled = useEditorSelector(editor => editor.history.undos.length === 0, []);

  return (
    <ToolbarButton
      {...props}
      disabled={disabled}
      onClick={() => editor.undo()}
      onMouseDown={e => e.preventDefault()}
      tooltip="Undo"
    >
      <div className={cn('theme-transition', 'text-purple-600 dark:text-purple-400')}>
        <Undo2Icon className="h-3 w-3 sm:h-4 sm:w-4" />
      </div>
    </ToolbarButton>
  );
}
