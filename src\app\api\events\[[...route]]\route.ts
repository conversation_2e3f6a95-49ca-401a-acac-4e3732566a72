import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { Event } from '@/models/Event';
import { Task } from '@/models/Task';
import { ActivityLog } from '@/models/ActivityLog';
import mongoose from 'mongoose';
import { connectDB } from '@/Utility/db';
import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { handle } from 'hono/vercel';

type Variables = {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/events');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      c.set('user', {
        id: session.user.id ?? '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      });
    }
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
  await next();
});

interface EventRequest {
  title: string;
  description?: string;
  location?: string;
  startTime: Date;
  endTime: Date;
  isAllDay?: boolean;
  timezone?: string;
  attendees?: Array<{
    email: string;
    name?: string;
    isOptional?: boolean;
  }>;
  reminders?: Array<{
    method: 'email' | 'popup' | 'sms';
    minutes: number;
  }>;
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    count?: number;
    until?: Date;
    byWeekDay?: number[];
    byMonthDay?: number[];
    byMonth?: number[];
  };
  taskIds?: string[];
  colorId?: string;
  category?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
  visibility?: 'default' | 'public' | 'private' | 'confidential';
  organizationId?: string;
  projectId?: string;
}

interface RecurringEventRequest {
  eventId: string;
  action: 'update_single' | 'update_all' | 'delete_single' | 'delete_all';
  updates?: Partial<EventRequest>;
  newStartTime?: Date;
  newEndTime?: Date;
}

interface EventFilters {
  startDate?: Date;
  endDate?: Date;
  projectId?: string;
  organizationId?: string;
  category?: string;
  priority?: string;
  tags?: string[];
  hasAttendees?: boolean;
  hasReminders?: boolean;
  isRecurring?: boolean;
  status?: string;
}
interface RecurringEvent extends Event {
  parentEventId: mongoose.Types.ObjectId;
  startTime: Date;
  endTime: Date;
}
// Auth middleware
const requireAuth = async (c: any, next: any) => {
  const user = c.get('user');
  if (!user?.id) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  await connectDB();
  await next();
};

// GET routes
app.get('/', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleGetEvents(user?.id, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/:eventId', requireAuth, async c => {
  try {
    const user = c.get('user');
    const eventId = c.req.param('eventId');
    const result = await handleGetEvent(eventId, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/recurring/:eventId', requireAuth, async c => {
  try {
    const eventId = c.req.param('eventId');
    const url = new URL(c.req.url);
    const result = await handleGetRecurringInstances(eventId, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/conflicts', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleGetConflicts(user?.id, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/upcoming', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleGetUpcomingEvents(user?.id, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/calendar', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleGetCalendarEvents(user?.id, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.get('/analytics', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleGetEventAnalytics(user?.id, url.searchParams);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

// POST routes
app.post('/', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleCreateEvent(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.post('/recurring', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleCreateRecurringEvents(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.post('/duplicate', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleDuplicateEvent(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.post('/from-task', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleCreateEventFromTask(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.post('/bulk', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleBulkCreateEvents(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.post('/invite-attendees', requireAuth, async c => {
  try {
    const body = await c.req.json();
    const result = await handleInviteAttendees(body);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

// PUT routes
app.put('/:eventId', requireAuth, async c => {
  try {
    const user = c.get('user');
    const eventId = c.req.param('eventId');
    const body = await c.req.json();
    const result = await handleUpdateEvent(eventId, body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.put('/recurring', requireAuth, async c => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const result = await handleUpdateRecurringEvent(body, user?.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.put('/attendee-response/:eventId', requireAuth, async c => {
  try {
    const eventId = c.req.param('eventId');
    const body = await c.req.json();
    const result = await handleAttendeeResponse(eventId, body);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.put('/drag-drop/:eventId', requireAuth, async c => {
  try {
    const user = c.get('user');
    const eventId = c.req.param('eventId');
    const body = await c.req.json();
    const result = await handleDragDropUpdate(eventId, body, user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.put('/task-link/:eventId', requireAuth, async c => {
  try {
    const user = c.get('user');
    const eventId = c.req.param('eventId');
    const body = await c.req.json();
    const result = await handleTaskLink(eventId, body, user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

// DELETE routes
app.delete('/:eventId', requireAuth, async c => {
  try {
    const user = c.get('user');
    const eventId = c.req.param('eventId');
    const result = await handleDeleteEvent(eventId, user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.delete('/recurring', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleDeleteRecurringEvent(url.searchParams, user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

app.delete('/bulk', requireAuth, async c => {
  try {
    const user = c.get('user');
    const url = new URL(c.req.url);
    const result = await handleBulkDeleteEvents(url.searchParams, user.id);
    return c.json(result);
  } catch (error: any) {
    return c.json({ error: error.message || 'Internal server error' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);

// Handler functions
async function handleGetEvents(userId, searchParams) {
  try {
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const filters = buildEventFilters(searchParams);

    const query = buildEventQuery(userId, filters);

    const events = await Event.find(query)
      .populate('userId', 'name email')
      .populate('projectId', 'name')
      .populate('taskIds', 'title status priority')
      .populate('createdBy', 'name email')
      .sort({ startTime: 1 })
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Event.countDocuments(query);

    return {
      success: true,
      events,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch events: ${error.message}`);
  }
}

async function handleGetEvent(eventId, userId) {
  try {
    const event = await Event.findById(eventId)
      .populate('userId', 'name email')
      .populate('projectId', 'name')
      .populate('taskIds', 'title status priority dueDate')
      .populate('createdBy', 'name email');

    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    // Check if user can view this event
    if (!event.canUserEdit(userId) && !event.userId.equals(new mongoose.Types.ObjectId(userId))) {
      // Check if user is an attendee
      const isAttendee = event.attendees.some(
        attendee => attendee.userId && attendee.userId.equals(new mongoose.Types.ObjectId(userId))
      );

      if (!isAttendee) {
        return { error: 'Access denied', status: 403 };
      }
    }

    return {
      success: true,
      event,
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch event: ${error.message}`);
  }
}

async function handleCreateEvent(eventData: EventRequest, userId) {
  try {
    // Validate event data
    if (!eventData.title || !eventData.startTime || !eventData.endTime) {
      return { error: 'Title, start time, and end time are required', status: 400 };
    }

    if (new Date(eventData.endTime) <= new Date(eventData.startTime)) {
      return { error: 'End time must be after start time', status: 400 };
    }

    // Check for conflicts if requested
    const conflicts = await Event.find({
      userId: new mongoose.Types.ObjectId(userId),
      $or: [
        {
          startTime: { $lt: new Date(eventData.endTime) },
          endTime: { $gt: new Date(eventData.startTime) },
        },
      ],
    });

    const event = new Event({
      title: eventData.title,
      description: eventData.description,
      location: eventData.location,
      startTime: new Date(eventData.startTime),
      endTime: new Date(eventData.endTime),
      isAllDay: eventData.isAllDay || false,
      timezone: eventData.timezone || 'UTC',
      userId: new mongoose.Types.ObjectId(userId),
      organizationId: eventData.organizationId
        ? new mongoose.Types.ObjectId(eventData.organizationId)
        : undefined,
      projectId: eventData.projectId ? new mongoose.Types.ObjectId(eventData.projectId) : undefined,
      taskIds: (eventData.taskIds || []).map(id => new mongoose.Types.ObjectId(id)),
      attendees: (eventData.attendees || []).map(attendee => ({
        email: attendee.email,
        name: attendee.name,
        responseStatus: 'needsAction',
        isOptional: attendee.isOptional || false,
        addedAt: new Date(),
      })),
      reminders: (eventData.reminders || []).map(reminder => ({
        method: reminder.method,
        minutes: reminder.minutes,
        isActive: true,
      })),
      recurrence: eventData.recurrence,
      isRecurring: !!eventData.recurrence,
      colorId: eventData.colorId,
      category: eventData.category,
      tags: eventData.tags || [],
      priority: eventData.priority || 'medium',
      visibility: eventData.visibility || 'default',
      createdBy: new mongoose.Types.ObjectId(userId),
      metadata: {
        source: 'manual',
        conflictsWith: conflicts.map(c => c._id),
      },
    });

    await event.save();

    // Create recurring instances if needed
    if (eventData.recurrence) {
      await createRecurringInstances(event);
    }

    // Link tasks if specified
    if (eventData.taskIds && eventData.taskIds.length > 0) {
      await linkTasksToEvent(event._id.toString(), eventData.taskIds);
    }

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'create',
      resourceType: 'timeline',
      resourceId: event._id,
      description: `Created event: ${event.title}`,
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        startTime: event.startTime,
        endTime: event.endTime,
        isRecurring: event.isRecurring,
        attendeeCount: event.attendees.length,
        hasConflicts: conflicts.length > 0,
      },
    });

    return {
      success: true,
      event,
      conflicts: conflicts.length > 0 ? conflicts : undefined,
      message: 'Event created successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to create event: ${error.message}`);
  }
}

async function handleUpdateEvent(eventId: string, updates: Partial<EventRequest>, userId: string) {
  try {
    const event = await Event.findById(eventId);

    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    if (!event.canUserEdit(userId)) {
      return { error: 'Access denied', status: 403 };
    }

    // Update event fields
    Object.assign(event, updates);
    event.updatedBy = new mongoose.Types.ObjectId(userId);

    // Validate times if changed
    if (updates.startTime || updates.endTime) {
      if (event.endTime <= event.startTime) {
        return { error: 'End time must be after start time', status: 400 };
      }

      // Check for new conflicts
      const conflicts = await Event.find({
        _id: { $ne: eventId },
        userId: new mongoose.Types.ObjectId(userId),
        $or: [
          {
            startTime: { $lt: event.endTime },
            endTime: { $gt: event.startTime },
          },
        ],
      });

      if (conflicts.length > 0) {
        event.metadata = {
          ...event.metadata,
          conflictsWith: conflicts.map(c => c._id),
        };
      }
    }

    // Handle task linking updates
    if (updates.taskIds) {
      event.taskIds = updates.taskIds.map(id => new mongoose.Types.ObjectId(id));
    }

    // Handle attendee updates
    if (updates.attendees) {
      event.attendees = updates.attendees.map(attendee => ({
        email: attendee.email,
        name: attendee.name,
        responseStatus: 'needsAction',
        isOptional: attendee.isOptional || false,
        addedAt: new Date(),
      }));
    }

    await event.save();

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'update',
      resourceType: 'timeline',
      resourceId: event._id,
      description: `Updated event: ${event.title}`,
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        changes: Object.keys(updates),
        timeChanged: !!(updates.startTime || updates.endTime),
      },
    });

    return {
      success: true,
      event,
      message: 'Event updated successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to update event: ${error.message}`);
  }
}

async function handleDeleteEvent(eventId: string, userId: string) {
  try {
    const event = await Event.findById(eventId);

    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    if (!event.canUserEdit(userId)) {
      return { error: 'Access denied', status: 403 };
    }

    // Handle recurring events
    if (event.isRecurring && !event.parentEventId) {
      // This is the parent recurring event
      await Event.deleteMany({ parentEventId: event._id });
    }

    // Soft delete
    event.isDeleted = true;
    event.deletedAt = new Date();
    event.deletedBy = new mongoose.Types.ObjectId(userId);
    await event.save();

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'delete',
      resourceType: 'timeline',
      resourceId: event._id,
      description: `Deleted event: ${event.title}`,
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        wasRecurring: event.isRecurring,
      },
    });

    return {
      success: true,
      message: 'Event deleted successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to delete event: ${error.message}`);
  }
}

async function handleCreateRecurringEvents(eventData: EventRequest, userId: string) {
  try {
    if (!eventData.recurrence) {
      return { error: 'Recurrence rules are required', status: 400 };
    }

    // Create the parent event
    const parentEvent = await handleCreateEvent(eventData, userId);

    if (!parentEvent) {
      throw new Error('Failed to create parent recurring event');
    }

    return {
      success: true,
      parentEvent,
      message: 'Recurring events created successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to create recurring events: ${error.message}`);
  }
}

async function handleUpdateRecurringEvent(data: RecurringEventRequest, userId: string) {
  try {
    const { eventId, action, updates } = data;

    const event = await Event.findById(eventId);
    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    if (!event.canUserEdit(userId)) {
      return { error: 'Access denied', status: 403 };
    }

    let result;

    switch (action) {
      case 'update_single':
        // Update only this instance
        if (event.parentEventId) {
          // This is already an instance
          Object.assign(event, updates);
          await event.save();
          result = { updatedEvents: 1, event };
        } else {
          // This is the parent - create a new exception instance
          const exceptionEvent = new Event({
            ...event.toObject(),
            _id: new mongoose.Types.ObjectId(),
            parentEventId: event._id,
            ...updates,
            updatedBy: new mongoose.Types.ObjectId(userId),
          });
          await exceptionEvent.save();
          result = { updatedEvents: 1, event: exceptionEvent };
        }
        break;

      case 'update_all': {
        // Update parent and all instances
        if (event.parentEventId) {
          // Get the parent event
          const parentEvent = await Event.findById(event.parentEventId);
          if (parentEvent) {
            Object.assign(parentEvent, updates);
            await parentEvent.save();
          }
        } else {
          Object.assign(event, updates);
          await event.save();
        }

        // Update all instances
        const updateResult = await Event.updateMany(
          {
            $or: [
              { _id: event.parentEventId || event._id },
              { parentEventId: event.parentEventId || event._id },
            ],
          },
          {
            ...updates,
            updatedBy: new mongoose.Types.ObjectId(userId),
          }
        );

        result = { updatedEvents: updateResult.modifiedCount };
        break;
      }
      default:
        return { error: 'Invalid action', status: 400 };
    }

    // Log activity
    await ActivityLog.create({
      userId,
      action: 'update',
      resourceType: 'timeline',
      resourceId: event._id,
      description: `Updated recurring event: ${event.title} (${action})`,
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        action,
        updatedCount: result.updatedEvents,
      },
    });

    return {
      success: true,
      ...result,
      message: 'Recurring event updated successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to update recurring event: ${error.message}`);
  }
}

async function handleGetRecurringInstances(parentEventId: string, searchParams: URLSearchParams) {
  try {
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const query: any = {
      $or: [{ _id: parentEventId }, { parentEventId: parentEventId }],
      isDeleted: false,
    };

    if (startDate && endDate) {
      query.$and = [
        { endTime: { $gte: new Date(startDate) } },
        { startTime: { $lte: new Date(endDate) } },
      ];
    }

    const instances = await Event.find(query).sort({ startTime: 1 });

    return {
      success: true,
      instances,
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch recurring instances: ${error.message}`);
  }
}

async function handleGetConflicts(userId: string, searchParams: URLSearchParams) {
  try {
    const startTime = searchParams.get('startTime');
    const endTime = searchParams.get('endTime');
    const excludeEventId = searchParams.get('excludeEventId');

    if (!startTime || !endTime) {
      return { error: 'startTime and endTime are required', status: 400 };
    }

    const query: any = {
      userId: new mongoose.Types.ObjectId(userId),
      isDeleted: false,
      $or: [
        {
          startTime: { $lt: new Date(endTime) },
          endTime: { $gt: new Date(startTime) },
        },
      ],
    };

    if (excludeEventId) {
      query._id = { $ne: excludeEventId };
    }

    const conflicts = await Event.find(query);

    return {
      success: true,
      conflicts,
    };
  } catch (error: any) {
    throw new Error(`Failed to check conflicts: ${error.message}`);
  }
}

async function handleCreateEventFromTask(
  data: { taskId: string; eventData?: Partial<EventRequest> },
  userId: string
) {
  try {
    const task = await Task.findById(data.taskId);
    if (!task) {
      return { error: 'Task not found', status: 404 };
    }

    const eventData: EventRequest = {
      title: `[Task] ${task.title}`,
      description: task.description,
      startTime: task.dueDate || new Date(),
      endTime: new Date((task.dueDate || new Date()).getTime() + 60 * 60 * 1000), // 1 hour duration
      taskIds: [task._id.toString()],
      projectId: task.projectId?.toString(),
      priority: task.priority as any,
      colorId: '#ef4444', // Red for tasks
      category: 'task',
      ...data.eventData,
    };

    const result = await handleCreateEvent(eventData, userId);
    return result;
  } catch (error: any) {
    throw new Error(`Failed to create event from task: ${error.message}`);
  }
}

async function handleDragDropUpdate(
  eventId: string,
  data: { newStartTime: Date; newEndTime: Date },
  userId: string
) {
  try {
    const event = await Event.findById(eventId);
    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    if (!event.canUserEdit(userId)) {
      return { error: 'Access denied', status: 403 };
    }

    // Calculate duration to maintain it
    const duration = event.endTime.getTime() - event.startTime.getTime();

    event.startTime = new Date(data.newStartTime);
    event.endTime = new Date(data.newEndTime || event.startTime.getTime() + duration);
    event.updatedBy = new mongoose.Types.ObjectId(userId);

    // Update drag drop position
    event.updateDragDropPosition(
      {
        x: 0, // These would come from the frontend
        y: 0,
        viewType: 'week', // This would be determined by the frontend
      },
      userId
    );

    await event.save();

    return {
      success: true,
      event,
      message: 'Event moved successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to update event position: ${error.message}`);
  }
}

async function handleTaskLink(
  eventId: string,
  data: { taskIds: string[]; action: 'add' | 'remove' },
  userId: string
) {
  try {
    const event = await Event.findById(eventId);
    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    if (!event.canUserEdit(userId)) {
      return { error: 'Access denied', status: 403 };
    }

    for (const taskId of data.taskIds) {
      if (data.action === 'add') {
        event.linkTask(taskId);
      } else {
        event.unlinkTask(taskId);
      }
    }

    await event.save();

    return {
      success: true,
      event,
      message: `Tasks ${data.action === 'add' ? 'linked' : 'unlinked'} successfully`,
    };
  } catch (error: any) {
    throw new Error(`Failed to ${data.action} task links: ${error.message}`);
  }
}

async function handleAttendeeResponse(
  eventId: string,
  data: { email: string; responseStatus: string; comment?: string }
) {
  try {
    const event = await Event.findById(eventId);
    if (!event) {
      return { error: 'Event not found', status: 404 };
    }

    const success = event.updateAttendeeResponse(
      data.email,
      data.responseStatus as any,
      data.comment
    );

    if (!success) {
      return { error: 'Attendee not found', status: 404 };
    }

    await event.save();

    return {
      success: true,
      message: 'Attendee response updated successfully',
    };
  } catch (error: any) {
    throw new Error(`Failed to update attendee response: ${error.message}`);
  }
}

// Helper functions
function buildEventFilters(searchParams: URLSearchParams): EventFilters {
  const filters: EventFilters = {};

  const startDate = searchParams.get('startDate');
  if (startDate) filters.startDate = new Date(startDate);

  const endDate = searchParams.get('endDate');
  if (endDate) filters.endDate = new Date(endDate);

  const projectId = searchParams.get('projectId');
  if (projectId) filters.projectId = projectId;

  const organizationId = searchParams.get('organizationId');
  if (organizationId) filters.organizationId = organizationId;

  const category = searchParams.get('category');
  if (category) filters.category = category;

  const priority = searchParams.get('priority');
  if (priority) filters.priority = priority;

  const tags = searchParams.get('tags');
  if (tags) filters.tags = tags.split(',');

  const hasAttendees = searchParams.get('hasAttendees');
  if (hasAttendees) filters.hasAttendees = hasAttendees === 'true';

  const hasReminders = searchParams.get('hasReminders');
  if (hasReminders) filters.hasReminders = hasReminders === 'true';

  const isRecurring = searchParams.get('isRecurring');
  if (isRecurring) filters.isRecurring = isRecurring === 'true';

  const status = searchParams.get('status');
  if (status) filters.status = status;

  return filters;
}

function buildEventQuery(userId: string, filters: EventFilters): any {
  const query: any = {
    userId: new mongoose.Types.ObjectId(userId),
    isDeleted: false,
  };

  if (filters.startDate || filters.endDate) {
    query.$and = [];
    if (filters.startDate) {
      query.$and.push({ endTime: { $gte: filters.startDate } });
    }
    if (filters.endDate) {
      query.$and.push({ startTime: { $lte: filters.endDate } });
    }
  }

  if (filters.projectId) {
    query.projectId = new mongoose.Types.ObjectId(filters.projectId);
  }

  if (filters.organizationId) {
    query.organizationId = new mongoose.Types.ObjectId(filters.organizationId);
  }

  if (filters.category) {
    query.category = filters.category;
  }

  if (filters.priority) {
    query.priority = filters.priority;
  }

  if (filters.tags && filters.tags.length > 0) {
    query.tags = { $in: filters.tags };
  }

  if (filters.hasAttendees !== undefined) {
    if (filters.hasAttendees) {
      query['attendees.0'] = { $exists: true };
    } else {
      query.attendees = { $size: 0 };
    }
  }

  if (filters.hasReminders !== undefined) {
    if (filters.hasReminders) {
      query['reminders.0'] = { $exists: true };
    } else {
      query.reminders = { $size: 0 };
    }
  }

  if (filters.isRecurring !== undefined) {
    query.isRecurring = filters.isRecurring;
  }

  return query;
}

// Additional helper functions
async function createRecurringInstances(parentEvent: any) {
  if (!parentEvent.recurrence) return;

  const { frequency, interval = 1, count = 10 } = parentEvent.recurrence;
  const instances: RecurringEvent[] = [];
  let currentDate = new Date(parentEvent.startTime);
  const duration =
    new Date(parentEvent.endTime).getTime() - new Date(parentEvent.startTime).getTime();

  for (let i = 1; i < count; i++) {
    switch (frequency) {
      case 'daily':
        currentDate = new Date(currentDate.getTime() + interval * 24 * 60 * 60 * 1000);
        break;
      case 'weekly':
        currentDate = new Date(currentDate.getTime() + interval * 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        currentDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + interval,
          currentDate.getDate()
        );
        break;
      case 'yearly':
        currentDate = new Date(
          currentDate.getFullYear() + interval,
          currentDate.getMonth(),
          currentDate.getDate()
        );
        break;
    }

    const instance: RecurringEvent = new Event({
      ...parentEvent.toObject(),
      _id: new mongoose.Types.ObjectId(),
      parentEventId: parentEvent._id,
      startTime: new Date(currentDate),
      endTime: new Date(currentDate.getTime() + duration),
    });

    instances.push(instance as any);
  }

  if (instances.length > 0) {
    await Event.insertMany(instances);
  }
}

async function linkTasksToEvent(eventId: string, taskIds: string[]) {
  if (!taskIds || taskIds.length === 0) return;

  await Task.updateMany(
    { _id: { $in: taskIds.map(id => new mongoose.Types.ObjectId(id)) } },
    { $addToSet: { eventIds: new mongoose.Types.ObjectId(eventId) } }
  );
}

async function handleGetUpcomingEvents(userId: string, searchParams: URLSearchParams) {
  const limit = parseInt(searchParams.get('limit') || '10');
  const events = await Event.find({
    userId: new mongoose.Types.ObjectId(userId),
    startTime: { $gte: new Date() },
    isDeleted: false,
  })
    .sort({ startTime: 1 })
    .limit(limit);
  return { success: true, events };
}

async function handleGetCalendarEvents(userId: string, searchParams: URLSearchParams) {
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const query: any = {
    userId: new mongoose.Types.ObjectId(userId),
    isDeleted: false,
  };
  if (startDate && endDate) {
    query.$and = [
      { endTime: { $gte: new Date(startDate) } },
      { startTime: { $lte: new Date(endDate) } },
    ];
  }
  const events = await Event.find(query).sort({ startTime: 1 });
  return { success: true, events };
}

async function handleGetEventAnalytics(userId: string, _searchParams: URLSearchParams) {
  const analytics = {
    totalEvents: await Event.countDocuments({
      userId: new mongoose.Types.ObjectId(userId),
      isDeleted: false,
    }),
    upcomingEvents: await Event.countDocuments({
      userId: new mongoose.Types.ObjectId(userId),
      startTime: { $gte: new Date() },
      isDeleted: false,
    }),
    recurringEvents: await Event.countDocuments({
      userId: new mongoose.Types.ObjectId(userId),
      isRecurring: true,
      isDeleted: false,
    }),
  };
  return { success: true, analytics };
}

async function handleDeleteRecurringEvent(searchParams: URLSearchParams, userId: string) {
  const eventId = searchParams.get('eventId');
  const action = searchParams.get('action') || 'delete_single';
  if (!eventId) {
    return { error: 'Event ID is required', status: 400 };
  }
  const event = await Event.findById(eventId);
  if (!event) {
    return { error: 'Event not found', status: 404 };
  }
  if (action === 'delete_all') {
    await Event.updateMany(
      { $or: [{ _id: event._id }, { parentEventId: event._id }] },
      { isDeleted: true, deletedAt: new Date(), deletedBy: new mongoose.Types.ObjectId(userId) }
    );
  } else {
    event.isDeleted = true;
    event.deletedAt = new Date();
    event.deletedBy = new mongoose.Types.ObjectId(userId);
    await event.save();
  }
  return { success: true, message: 'Event(s) deleted successfully' };
}

async function handleBulkDeleteEvents(searchParams: URLSearchParams, userId: string) {
  const eventIds = searchParams.get('eventIds')?.split(',') || [];
  if (eventIds.length === 0) {
    return { error: 'No event IDs provided', status: 400 };
  }
  await Event.updateMany(
    { _id: { $in: eventIds.map(id => new mongoose.Types.ObjectId(id)) } },
    { isDeleted: true, deletedAt: new Date(), deletedBy: new mongoose.Types.ObjectId(userId) }
  );
  return { success: true, message: `${eventIds.length} events deleted successfully` };
}

async function handleDuplicateEvent(
  data: { eventId: string; newStartTime?: Date },
  userId: string
) {
  const originalEvent = await Event.findById(data.eventId);
  if (!originalEvent) {
    return { error: 'Event not found', status: 404 };
  }
  const newEvent = new Event({
    ...originalEvent.toObject(),
    _id: new mongoose.Types.ObjectId(),
    title: `${originalEvent.title} (Copy)`,
    startTime: data.newStartTime || originalEvent.startTime,
    endTime: data.newStartTime
      ? new Date(
          data.newStartTime.getTime() +
            (originalEvent.endTime.getTime() - originalEvent.startTime.getTime())
        )
      : originalEvent.endTime,
    createdBy: new mongoose.Types.ObjectId(userId),
    parentEventId: undefined,
    isRecurring: false,
    recurrence: undefined,
  });
  await newEvent.save();
  return { success: true, event: newEvent };
}

async function handleBulkCreateEvents(data: { events: EventRequest[] }, userId: string) {
  const createdEvents: Event[] = [];
  for (const eventData of data.events) {
    try {
      const result = await handleCreateEvent(eventData, userId);
      if (result.success) {
        createdEvents.push(result.event);
      }
    } catch (error) {
      console.error('Failed to create bulk event:', error);
    }
  }
  return {
    success: true,
    events: createdEvents,
    message: `${createdEvents.length} events created successfully`,
  };
}

async function handleInviteAttendees(data) {
  const event = await Event.findById(data.eventId);
  if (!event) {
    return { error: 'Event not found', status: 404 };
  }
  for (const attendee of data.attendees) {
    if (!event.attendees.some(a => a.email === attendee.email)) {
      event.attendees.push({
        email: attendee.email,
        name: attendee.name,
        responseStatus: 'needsAction',
        isOptional: false,
        addedAt: new Date(),
      });
    }
  }
  await event.save();
  return { success: true, event, message: 'Attendees invited successfully' };
}
