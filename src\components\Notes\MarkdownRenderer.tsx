'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { cn } from '@/lib/utils';
import 'highlight.js/styles/github-dark.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  settings?: {
    fontSize: 'small' | 'medium' | 'large';
    theme: 'light' | 'dark' | 'auto';
  };
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className,
  settings = { fontSize: 'medium', theme: 'auto' },
}) => {
  const fontSizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
  };

  if (!content || content.trim() === '') {
    return (
      <div
        className={cn(
          'flex items-center justify-center h-32 text-muted-foreground',
          fontSizeClasses[settings.fontSize],
          className
        )}
      >
        <p>No content to display</p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'prose prose-slate dark:prose-invert max-w-none',
        'prose-headings:theme-text-primary prose-p:theme-text-primary prose-strong:theme-text-primary',
        'prose-code:bg-muted/50 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-code:font-mono prose-code:before:content-none prose-code:after:content-none',
        'prose-pre:bg-muted/50 prose-pre:border prose-pre:border-border',
        'prose-blockquote:border-l-4 prose-blockquote:border-muted-foreground/20 prose-blockquote:theme-text-secondary',
        'prose-a:text-primary prose-a:no-underline hover:prose-a:underline',
        'prose-ul:my-4 prose-ol:my-4 prose-li:my-1',
        'prose-hr:border-muted-foreground/20',
        'prose-table:border prose-table:border-border prose-th:border prose-th:border-border prose-td:border prose-td:border-border',
        fontSizeClasses[settings.fontSize],
        className
      )}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Custom components for better styling
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold mt-8 mb-4 theme-text-primary">{children}</h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold mt-8 mb-4 theme-text-primary">{children}</h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-semibold mt-6 mb-3 theme-text-primary">{children}</h3>
          ),
          p: ({ children }) => (
            <p className="mb-4 theme-text-primary leading-relaxed">{children}</p>
          ),
          code: ({ children, className: codeClassName }) => {
            const isInline = !codeClassName;
            if (isInline) {
              return (
                <code className="bg-muted/50 px-1.5 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              );
            }
            return <code className={codeClassName}>{children}</code>;
          },
          pre: ({ children }) => (
            <pre className="bg-muted/50 rounded-md p-4 my-4 overflow-x-auto border border-border">
              {children}
            </pre>
          ),
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-muted-foreground/20 pl-4 my-4 italic theme-text-secondary">
              {children}
            </blockquote>
          ),
          a: ({ href, children }) => (
            <a
              href={href}
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          ul: ({ children }) => (
            <ul className="my-4 space-y-1 list-disc list-inside">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="my-4 space-y-1 list-decimal list-inside">{children}</ol>
          ),
          li: ({ children }) => <li className="theme-text-primary">{children}</li>,
          hr: () => <hr className="my-6 border-muted-foreground/20" />,
          table: ({ children }) => (
            <div className="overflow-x-auto my-4">
              <table className="min-w-full border border-border rounded-md">{children}</table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-border px-4 py-2 bg-muted/50 font-semibold text-left">
              {children}
            </th>
          ),
          td: ({ children }) => <td className="border border-border px-4 py-2">{children}</td>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
