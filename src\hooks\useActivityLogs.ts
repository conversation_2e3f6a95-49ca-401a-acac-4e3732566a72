import { useQuery, useInfiniteQuery, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import {
    activityLogApiService,
    GetLogsParams,
    ActivityLogsResponse,
    ActivityLog,
    User
} from '@/services/ActivityLogApi.service';

// Query keys for consistent caching
export const ACTIVITY_LOG_QUERY_KEYS = {
    all: ['activity-logs'] as const,
    lists: () => [...ACTIVITY_LOG_QUERY_KEYS.all, 'list'] as const,
    list: (filters: GetLogsParams) => [...ACTIVITY_LOG_QUERY_KEYS.lists(), filters] as const,
    analytics: (params: Record<string, any>) => [...ACTIVITY_LOG_QUERY_KEYS.all, 'analytics', params] as const,
    availableActions: () => [...ACTIVITY_LOG_QUERY_KEYS.all, 'available-actions'] as const,
    availableResourceTypes: () => [...ACTIVITY_LOG_QUERY_KEYS.all, 'available-resource-types'] as const,
    availableUsers: (organizationId?: string) => [...ACTIVITY_LOG_QUERY_KEYS.all, 'available-users', organizationId] as const,
};

export interface UseActivityLogsOptions {
    filters?: GetLogsParams;
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
}

export interface UseActivityLogsReturn {
    data: ActivityLogsResponse | undefined;
    logs: ActivityLog[];
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
    refetch: () => Promise<any>;
    isFetching: boolean;
    isRefetching: boolean;
}

export const useActivityLogs = (options: UseActivityLogsOptions = {}): UseActivityLogsReturn => {
    const {
        filters = {},
        enabled = true,
        staleTime = 5 * 60 * 1000, // 5 minutes
        cacheTime = 10 * 60 * 1000, // 10 minutes
    } = options;

    const queryKey = useMemo(() => ACTIVITY_LOG_QUERY_KEYS.list(filters), [filters]);

    const {
        data,
        isLoading,
        isError,
        error,
        refetch,
        isFetching,
        isRefetching,
    } = useQuery<ActivityLogsResponse, Error>({
        queryKey,
        queryFn: () => activityLogApiService.getLogs(filters),
        enabled,
        staleTime,
        gcTime: cacheTime, // Updated from cacheTime to gcTime for newer TanStack Query versions
        retry: (failureCount, error: Error) => {
            // Don't retry on client errors (4xx)
            if ('response' in error && typeof error.response === 'object' && error.response && 'status' in error.response) {
                const status = error.response.status as number;
                if (status && status < 500) {
                    return false;
                }
            }
            return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });

    const logs = useMemo(() => data?.logs || [], [data?.logs]);

    return {
        data,
        logs,
        isLoading,
        isError,
        error: error || null,
        refetch,
        isFetching,
        isRefetching,
    };
};

// Hook for infinite scrolling/pagination
export interface UseInfiniteActivityLogsOptions {
    filters?: Omit<GetLogsParams, 'page'>;
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    pageSize?: number;
}

export interface UseInfiniteActivityLogsReturn {
    data: ActivityLog[];
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
    fetchNextPage: () => Promise<any>;
    hasNextPage: boolean;
    isFetchingNextPage: boolean;
    refetch: () => Promise<any>;
    totalItems: number;
}

export const useInfiniteActivityLogs = (
    options: UseInfiniteActivityLogsOptions = {}
): UseInfiniteActivityLogsReturn => {
    const {
        filters = {},
        enabled = true,
        staleTime = 5 * 60 * 1000,
        cacheTime = 10 * 60 * 1000,
        pageSize = 25,
    } = options;

    const queryKey = useMemo(() =>
        [...ACTIVITY_LOG_QUERY_KEYS.lists(), 'infinite', filters],
        [filters]
    );

    const {
        data,
        isLoading,
        isError,
        error,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        refetch,
    } = useInfiniteQuery<ActivityLogsResponse, Error>({
        queryKey,
        queryFn: ({ pageParam = 1 }) =>
            activityLogApiService.getLogs({
                ...filters,
                page: pageParam as number,
                limit: pageSize,
            }),
        getNextPageParam: (lastPage: ActivityLogsResponse) => {
            const { pagination } = lastPage;
            return pagination.page < pagination.totalPages ? pagination.page + 1 : undefined;
        },
        initialPageParam: 1,
        enabled,
        staleTime,
        gcTime: cacheTime, // Updated from cacheTime to gcTime
        retry: (failureCount, error: Error) => {
            if ('response' in error && typeof error.response === 'object' && error.response && 'status' in error.response) {
                const status = error.response.status as number;
                if (status && status < 500) {
                    return false;
                }
            }
            return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });

    const flattenedLogs = useMemo(() => {
        return data?.pages.flatMap(page => page.logs) || [];
    }, [data?.pages]);

    const totalItems = useMemo(() => {
        return data?.pages[0]?.pagination?.total || 0;
    }, [data?.pages]);

    return {
        data: flattenedLogs,
        isLoading,
        isError,
        error: error || null,
        fetchNextPage,
        hasNextPage: !!hasNextPage,
        isFetchingNextPage,
        refetch,
        totalItems,
    };
};

// Hook for available actions
export const useAvailableActions = (): UseQueryResult<string[], Error> => {
    return useQuery<string[], Error>({
        queryKey: ACTIVITY_LOG_QUERY_KEYS.availableActions(),
        queryFn: () => activityLogApiService.getAvailableActions(),
        staleTime: 30 * 60 * 1000, // 30 minutes - actions don't change often
        gcTime: 60 * 60 * 1000, // 1 hour - updated from cacheTime
        retry: 2,
    });
};

// Hook for available resource types
export const useAvailableResourceTypes = (): UseQueryResult<string[], Error> => {
    return useQuery<string[], Error>({
        queryKey: ACTIVITY_LOG_QUERY_KEYS.availableResourceTypes(),
        queryFn: () => activityLogApiService.getAvailableResourceTypes(),
        staleTime: 30 * 60 * 1000, // 30 minutes
        gcTime: 60 * 60 * 1000, // 1 hour - updated from cacheTime
        retry: 2,
    });
};

// Hook for available users
export const useAvailableUsers = (organizationId?: string): UseQueryResult<User[], Error> => {
    return useQuery<User[], Error>({
        queryKey: ACTIVITY_LOG_QUERY_KEYS.availableUsers(organizationId),
        queryFn: () => activityLogApiService.getAvailableUsers(organizationId),
        enabled: !!organizationId,
        staleTime: 10 * 60 * 1000, // 10 minutes
        gcTime: 20 * 60 * 1000, // 20 minutes - updated from cacheTime
        retry: 2,
    });
};

// Hook for invalidating activity log queries
export const useActivityLogQueryInvalidation = () => {
    const queryClient = useQueryClient();

    const invalidateAll = useCallback(() => {
        queryClient.invalidateQueries({ queryKey: ACTIVITY_LOG_QUERY_KEYS.all });
    }, [queryClient]);

    const invalidateLists = useCallback(() => {
        queryClient.invalidateQueries({ queryKey: ACTIVITY_LOG_QUERY_KEYS.lists() });
    }, [queryClient]);

    const invalidateAnalytics = useCallback(() => {
        queryClient.invalidateQueries({
            queryKey: [...ACTIVITY_LOG_QUERY_KEYS.all, 'analytics']
        });
    }, [queryClient]);

    const invalidateSpecificList = useCallback((filters: GetLogsParams) => {
        queryClient.invalidateQueries({
            queryKey: ACTIVITY_LOG_QUERY_KEYS.list(filters)
        });
    }, [queryClient]);

    return {
        invalidateAll,
        invalidateLists,
        invalidateAnalytics,
        invalidateSpecificList,
    };
};