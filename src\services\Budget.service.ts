import axios from 'axios';

export interface IBudgetCategory {
  name: string;
  allocatedAmount: number;
  spentAmount: number;
  description?: string;
}

export interface IExpense {
  _id?: string;
  amount: number;
  category: string;
  description?: string;
  date: Date;
  receiptNumber?: string;
  vendor?: string;
  attachments?: Array<{
    name: string;
    url: string;
    public_id: string;
  }>;
  createdBy?: string;
}

export interface IBudget {
  _id?: string;
  budgetId: string;
  projectId: string;
  totalBudget: number;
  spentAmount: number;
  categories: IBudgetCategory[];
  currency: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD' | 'CHF' | 'CNY' | 'INR';
  expenses: IExpense[];
  alerts?: {
    thresholdPercentage: number;
    notifyUsers?: string[];
    enabled: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
  status: 'Active' | 'Completed' | 'Exceeded' | 'Frozen';
  fiscalYear?: number;
  createdBy?: string;
  organizationId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IBudgetCreateData {
  projectId: string;
  totalBudget: number;
  categories: IBudgetCategory[];
  currency?: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD' | 'CHF' | 'CNY' | 'INR';
  alerts?: {
    thresholdPercentage: number;
    notifyUsers?: string[];
    enabled: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
  status?: 'Active' | 'Completed' | 'Exceeded' | 'Frozen';
  fiscalYear?: number;
}

export interface IBudgetUpdateData {
  totalBudget?: number;
  categories?: IBudgetCategory[];
  currency?: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD' | 'CHF' | 'CNY' | 'INR';
  alerts?: {
    thresholdPercentage: number;
    notifyUsers?: string[];
    enabled: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
  status?: 'Active' | 'Completed' | 'Exceeded' | 'Frozen';
  fiscalYear?: number;
}

export interface IExpenseCreateData {
  amount: number;
  category: string;
  description?: string;
  date: string;
  receiptNumber?: string;
  vendor?: string;
  attachments?: Array<{
    name: string;
    url: string;
    public_id: string;
  }>;
}

export interface IBudgetListResponse {
  budgets: IBudget[];
  total: number;
  page: number;
  totalPages: number;
}

export interface IExpenseListResponse {
  expenses: IExpense[];
  total: number;
  page: number;
  totalPages: number;
}

export interface IBudgetResponse {
  budget: IBudget;
}

export interface IExpenseResponse {
  expense: IExpense;
  budget: IBudget;
}

export interface IBudgetAnalytics {
  summary: {
    totalBudget: number;
    spentAmount: number;
    remainingAmount: number;
    utilizationPercentage: number;
    currency: string;
    status: string;
  };
  categoryAnalysis: Array<{
    name: string;
    allocated: number;
    spent: number;
    remaining: number;
    utilization: number;
    expenseCount: number;
  }>;
  spendingTrends: Array<{
    period: string;
    amount: number;
    count: number;
  }>;
  breakdown: Array<{
    category: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
  varianceAnalysis: Array<{
    category: string;
    budgetVariance: number;
    variancePercentage: number;
    status: string;
  }>;
  forecast: {
    projectedSpend: number;
    projectedCompletion: Date | null;
    burnRate: number;
    daysRemaining: number | null;
  };
  alerts: {
    overBudgetCategories: any[];
    nearLimitCategories: any[];
    underutilizedCategories: any[];
  };
}

export interface IAnalyticsResponse {
  analytics: IBudgetAnalytics;
}

class BudgetServiceClass {
  private static instance: BudgetServiceClass;
  private baseURL: string;

  private constructor() {
    this.baseURL = '/api/budgets';
  }

  public static getInstance(): BudgetServiceClass {
    if (!BudgetServiceClass.instance) {
      BudgetServiceClass.instance = new BudgetServiceClass();
    }
    return BudgetServiceClass.instance;
  }

  async createBudget(data: IBudgetCreateData): Promise<IBudgetResponse> {
    try {
      const response = await axios.post(this.baseURL, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create budget');
    }
  }

  async getBudgets(params?: {
    limit?: number;
    skip?: number;
    status?: string;
    fiscalYear?: number;
  }): Promise<IBudgetListResponse> {
    try {
      const response = await axios.get(this.baseURL, { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch budgets');
    }
  }

  async getBudgetByProject(projectId: string): Promise<IBudgetResponse> {
    try {
      const response = await axios.get(`${this.baseURL}/project/${projectId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch budget');
    }
  }

  async updateBudget(budgetId: string, data: IBudgetUpdateData): Promise<IBudgetResponse> {
    try {
      const response = await axios.put(`${this.baseURL}/${budgetId}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to update budget');
    }
  }

  async deleteBudget(budgetId: string): Promise<{ success: boolean }> {
    try {
      const response = await axios.delete(`${this.baseURL}/${budgetId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to delete budget');
    }
  }

  async addExpense(projectId: string, data: IExpenseCreateData): Promise<IExpenseResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/project/${projectId}/expenses`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to add expense');
    }
  }

  async getExpenses(
    projectId: string,
    params?: {
      category?: string;
      startDate?: string;
      endDate?: string;
      limit?: number;
      skip?: number;
    }
  ): Promise<IExpenseListResponse> {
    try {
      const response = await axios.get(`${this.baseURL}/project/${projectId}/expenses`, { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch expenses');
    }
  }

  async updateExpense(expenseId: string, data: IExpenseCreateData): Promise<IExpenseResponse> {
    try {
      const response = await axios.put(`${this.baseURL}/expenses/${expenseId}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to update expense');
    }
  }

  async deleteExpense(expenseId: string): Promise<{ success: boolean }> {
    try {
      const response = await axios.delete(`${this.baseURL}/expenses/${expenseId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to delete expense');
    }
  }

  async getBudgetAnalytics(budgetId: string, period?: string): Promise<IAnalyticsResponse> {
    try {
      const response = await axios.get(`${this.baseURL}/reports/${budgetId}`, {
        params: { period },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch budget analytics');
    }
  }

  async uploadExpenseAttachment(
    file: File
  ): Promise<{ name: string; url: string; public_id: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post('/api/handle-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        name: file.name,
        url: response.data.url,
        public_id: response.data.public_id,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to upload attachment');
    }
  }
}

export const BudgetService = BudgetServiceClass.getInstance();
