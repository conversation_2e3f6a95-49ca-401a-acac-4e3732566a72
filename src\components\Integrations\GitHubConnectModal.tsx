'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Github, AlertCircle, Check } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Modal from '../Global/Modal';
import { toast } from 'sonner';
import { IntegrationOnboarding } from './IntegrationOnboarding';
import { useQueryClient } from '@tanstack/react-query';
import { GitHubService } from '@/services/GitHub.service';

interface GitHubConnectModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function GitHubConnectModal({ isOpen, onOpenChange }: GitHubConnectModalProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [connectionSuccess, setConnectionSuccess] = useState(false);
  const queryClient = useQueryClient();

  const handleConnect = async () => {
    setError(null);
    setIsConnecting(true);

    try {
      const { url } = await GitHubService.getOAuthUrlClient();

      const width = 600;
      const height = 700;
      const left = (window.screen.width - width) / 2;
      const top = (window.screen.height - height) / 2;

      const popup = window.open(
        url,
        'github-app-install',
        `width=${width},height=${height},scrollbars=yes,resizable=yes,top=${top},left=${left}`
      );

      if (!popup) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          setIsConnecting(false);
          window.removeEventListener('message', messageListener);
        }
      }, 1000);

      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'GITHUB_OAUTH_SUCCESS') {
          clearInterval(checkClosed);
          popup.close();
          setIsConnecting(false);
          setConnectionSuccess(true);
          setShowOnboarding(true);
          toast.success('GitHub connected successfully!');
          queryClient.invalidateQueries({ queryKey: ['integrations'] });
          window.removeEventListener('message', messageListener);
        } else if (event.data.type === 'GITHUB_OAUTH_ERROR') {
          clearInterval(checkClosed);
          popup.close();
          setIsConnecting(false);
          setError(event.data.error || 'Failed to connect GitHub');
          toast.error('Failed to connect GitHub');
          window.removeEventListener('message', messageListener);
        }
      };

      window.addEventListener('message', messageListener);

      return () => {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageListener);
        if (popup && !popup.closed) {
          popup.close();
        }
      };
    } catch (err: any) {
      setIsConnecting(false);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to connect GitHub';
      setError(errorMessage);
      toast.error('Failed to connect GitHub', {
        description: errorMessage,
      });
    }
  };

  return (
    <>
      <Modal
        isOpen={isOpen && !showOnboarding}
        onClose={() => {
          setError(null);
          onOpenChange(false);
        }}
        size="md"
        className="theme-surface-elevated"
      >
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-primary/10 p-2 rounded-lg">
              <Github className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h2 className="text-lg font-semibold theme-text-primary">Connect GitHub</h2>
              <p className="text-sm theme-text-secondary">
                Link your GitHub account to import repositories and issues
              </p>
            </div>
          </div>

          {error && (
            <Alert className="mb-4 border-destructive/20 bg-destructive/5">
              <AlertCircle className="h-4 w-4 text-destructive" />
              <AlertDescription className="text-destructive">{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4 mb-6">
            <div className="theme-surface p-4 rounded-lg border border-border/20">
              <h3 className="font-medium theme-text-primary mb-3 flex items-center">
                <Check className="h-4 w-4 text-success mr-2" />
                What you'll get:
              </h3>
              <ul className="space-y-2 text-sm theme-text-secondary">
                <li className="flex items-center">
                  <Check className="h-3 w-3 text-success mr-2 flex-shrink-0" />
                  Access to your repositories
                </li>
                <li className="flex items-center">
                  <Check className="h-3 w-3 text-success mr-2 flex-shrink-0" />
                  Import GitHub issues as tasks
                </li>
                <li className="flex items-center">
                  <Check className="h-3 w-3 text-success mr-2 flex-shrink-0" />
                  Sync pull requests and commits
                </li>
                <li className="flex items-center">
                  <Check className="h-3 w-3 text-success mr-2 flex-shrink-0" />
                  Webhook notifications for updates
                </li>
              </ul>
            </div>

            <div className="text-xs theme-text-secondary bg-muted/50 p-3 rounded-lg">
              <p className="flex items-start">
                <AlertCircle className="h-3 w-3 mr-2 mt-0.5 flex-shrink-0" />
                We'll redirect you to GitHub to authorize access. This is secure and you can revoke
                access anytime from your GitHub settings.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isConnecting}
              className="px-4"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConnect}
              disabled={isConnecting}
              className="px-4 font-medium theme-button-primary"
            >
              {isConnecting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                  Connecting...
                </>
              ) : (
                <>
                  <Github className="mr-2 h-3 w-3" />
                  Connect to GitHub
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {connectionSuccess && (
        <IntegrationOnboarding
          isOpen={showOnboarding}
          onOpenChange={open => {
            setShowOnboarding(open);
            if (!open) {
              onOpenChange(false);
            }
          }}
          provider="github"
          providerName="GitHub"
          workspaceName="GitHub Integration"
        />
      )}
    </>
  );
}
