import { ActivityLog, IActivityLog } from '@/models/ActivityLog';
import { connectDB } from '@/Utility/db';
import { Types } from 'mongoose';

export interface ActivityLogData {
  userId: string;
  userEmail?: string;
  userName?: string;
  action:
    | 'create'
    | 'update'
    | 'delete'
    | 'login'
    | 'logout'
    | 'view'
    | 'export'
    | 'import'
    | 'share'
    | 'archive';
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'report'
    | 'timeline'
    | 'notification'
    | 'file'
    | 'comment'
    | 'budget';
  resourceId?: string;
  resourceName?: string;
  description: string;
  metadata?: {
    previousData?: Record<string, any>;
    newData?: Record<string, any>;
    changes?: Record<string, { from: any; to: any }>;
    projectId?: string;
    organizationId?: string;
    sessionId?: string;
    userAgent?: string;
    source?: 'web' | 'mobile' | 'api' | 'webhook' | 'system';
    duration?: number;
    fileSize?: number;
    errorMessage?: string;
    additionalContext?: Record<string, any>;
  };
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  deviceInfo?: {
    browser?: string;
    browserVersion?: string;
    os?: string;
    osVersion?: string;
    device?: string;
    isMobile?: boolean;
    isTablet?: boolean;
    isDesktop?: boolean;
  };
  timestamp?: Date;
}

export interface ActivityLogFilters {
  userId?: string;
  actions?: string[];
  resourceTypes?: string[];
  resourceId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
  search?: string;
  source?: string;
  ipAddress?: string;
}

export interface ActivityAnalytics {
  totalActions: number;
  actionBreakdown: Record<string, number>;
  resourceBreakdown: Record<string, number>;
  dailyActivity: Array<{ date: string; count: number }>;
  topUsers: Array<{ userId: string; userName: string; count: number }>;
  recentActivity: IActivityLog[];
  uniqueUsers: number;
  avgActionsPerUser: number;
  peakActivityHour: number;
  mostActiveDay: string;
}

export interface BatchLogOptions {
  batchSize?: number;
  flushInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
}

class ActivityLogService {
  private static instance: ActivityLogService;
  private logQueue: ActivityLogData[] = [];
  private batchTimer: ReturnType<typeof setTimeout> | null = null;
  private isProcessing = false;
  private readonly defaultBatchOptions: BatchLogOptions = {
    batchSize: 50,
    flushInterval: 5000, // 5 seconds
    maxRetries: 3,
    retryDelay: 1000, // 1 second
  };

  private constructor() {
    this.startBatchProcessor();
  }

  static getInstance(): ActivityLogService {
    if (!ActivityLogService.instance) {
      ActivityLogService.instance = new ActivityLogService();
    }
    return ActivityLogService.instance;
  }

  // Automatic activity tracking
  async log(data: ActivityLogData): Promise<boolean> {
    try {
      await connectDB();
      // @ts-expect-error: createLog is a custom static method
      const logEntry = await ActivityLog.createLog({
        ...data,
        userId: new Types.ObjectId(data.userId),
        resourceId: data.resourceId ? new Types.ObjectId(data.resourceId) : undefined,
        'metadata.projectId': data.metadata?.projectId
          ? new Types.ObjectId(data.metadata.projectId)
          : undefined,
        'metadata.organizationId': data.metadata?.organizationId
          ? new Types.ObjectId(data.metadata.organizationId)
          : undefined,
        timestamp: data.timestamp || new Date(),
      });
      return !!logEntry;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to log activity:', error);
      return false;
    }
  }

  // Batch logging for high-volume scenarios
  addToBatch(data: ActivityLogData): void {
    this.logQueue.push(data);

    if (this.logQueue.length >= this.defaultBatchOptions.batchSize!) {
      this.processBatch();
    }
  }

  private startBatchProcessor(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }

    this.batchTimer = setInterval(() => {
      if (this.logQueue.length > 0) {
        this.processBatch();
      }
    }, this.defaultBatchOptions.flushInterval);
  }

  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }
    this.isProcessing = true;
    const batch = this.logQueue.splice(0, this.defaultBatchOptions.batchSize);

    try {
      await connectDB();
      const logEntries = batch.map(data => ({
        ...data,
        userId: new Types.ObjectId(data.userId),
        resourceId: data.resourceId ? new Types.ObjectId(data.resourceId) : undefined,
        'metadata.projectId': data.metadata?.projectId
          ? new Types.ObjectId(data.metadata.projectId)
          : undefined,
        'metadata.organizationId': data.metadata?.organizationId
          ? new Types.ObjectId(data.metadata.organizationId)
          : undefined,
        timestamp: data.timestamp || new Date(),
      }));
      await ActivityLog.insertMany(logEntries);
      // eslint-disable-next-line no-console
      console.log(`Successfully processed batch of ${batch.length} activity logs`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to process activity log batch:', error);
      // Re-add failed items back to queue for retry
      this.logQueue.unshift(...batch);
    } finally {
      this.isProcessing = false;
    }
  }

  // Force flush all pending logs
  async flush(): Promise<void> {
    if (this.logQueue.length > 0) {
      await this.processBatch();
    }
  }

  // Query activity logs with filtering
  async getLogs(filters: ActivityLogFilters = {}): Promise<IActivityLog[]> {
    try {
      await connectDB();
      // @ts-expect-error: getLogsByUser is a custom static method
      return await ActivityLog.getLogsByUser(filters.userId || '', {
        limit: filters.limit,
        offset: filters.offset,
        startDate: filters.startDate,
        endDate: filters.endDate,
        actions: filters.actions,
        resourceTypes: filters.resourceTypes,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get activity logs:', error);
      return [];
    }
  }

  // Get logs for specific resource
  async getResourceLogs(
    resourceType: string,
    resourceId: string,
    filters: Partial<ActivityLogFilters> = {}
  ): Promise<IActivityLog[]> {
    try {
      await connectDB();
      // @ts-expect-error: getLogsByResource is a custom static method
      return await ActivityLog.getLogsByResource(resourceType, resourceId, {
        limit: filters.limit,
        offset: filters.offset,
        startDate: filters.startDate,
        endDate: filters.endDate,
        actions: filters.actions,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get resource logs:', error);
      return [];
    }
  }

  // Analytics and reporting
  async getAnalytics(
    userId?: string,
    organizationId?: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<ActivityAnalytics> {
    try {
      await connectDB();
      // @ts-expect-error: getAnalytics is a custom static method
      const analytics = await ActivityLog.getAnalytics(userId, organizationId, dateRange);
      // Calculate additional metrics
      const uniqueUsers = analytics.topUsers.length;
      const avgActionsPerUser = uniqueUsers > 0 ? analytics.totalActions / uniqueUsers : 0;
      // Find peak activity hour (simplified - would need more complex aggregation for real peak hour)
      const peakActivityHour = 14; // Default to 2 PM
      // Find most active day
      const mostActiveDay = analytics.dailyActivity.reduce(
        (prev, current) => (current.count > prev.count ? current : prev),
        { date: '', count: 0 }
      ).date;
      return {
        ...analytics,
        uniqueUsers,
        avgActionsPerUser: Math.round(avgActionsPerUser * 100) / 100,
        peakActivityHour,
        mostActiveDay,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get activity analytics:', error);
      return {
        totalActions: 0,
        actionBreakdown: {},
        resourceBreakdown: {},
        dailyActivity: [],
        topUsers: [],
        recentActivity: [],
        uniqueUsers: 0,
        avgActionsPerUser: 0,
        peakActivityHour: 0,
        mostActiveDay: '',
      };
    }
  }

  // Track specific user actions
  async trackUserLogin(
    userId: string,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      location?: any;
      deviceInfo?: any;
      sessionId?: string;
    }
  ): Promise<void> {
    await this.log({
      userId,
      action: 'login',
      resourceType: 'user',
      resourceId: userId,
      description: 'User logged in',
      metadata: {
        source: 'web',
        sessionId: metadata?.sessionId,
        userAgent: metadata?.userAgent,
      },
      ipAddress: metadata?.ipAddress,
      userAgent: metadata?.userAgent,
      location: metadata?.location,
      deviceInfo: metadata?.deviceInfo,
    });
  }

  async trackUserLogout(
    userId: string,
    metadata?: {
      sessionId?: string;
      duration?: number;
    }
  ): Promise<void> {
    await this.log({
      userId,
      action: 'logout',
      resourceType: 'user',
      resourceId: userId,
      description: 'User logged out',
      metadata: {
        source: 'web',
        sessionId: metadata?.sessionId,
        duration: metadata?.duration,
      },
    });
  }

  // Track resource operations
  async trackResourceCreate(
    userId: string,
    resourceType: string,
    resourceId: string,
    resourceName: string,
    data?: any,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'create',
      resourceType: resourceType as any,
      resourceId,
      resourceName,
      description: `Created ${resourceType}: ${resourceName}`,
      metadata: {
        source: 'web',
        newData: data,
        ...metadata,
      },
    });
  }

  async trackResourceUpdate(
    userId: string,
    resourceType: string,
    resourceId: string,
    resourceName: string,
    changes: Record<string, { from: any; to: any }>,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'update',
      resourceType: resourceType as any,
      resourceId,
      resourceName,
      description: `Updated ${resourceType}: ${resourceName}`,
      metadata: {
        source: 'web',
        changes,
        ...metadata,
      },
    });
  }

  async trackResourceDelete(
    userId: string,
    resourceType: string,
    resourceId: string,
    resourceName: string,
    previousData?: any,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'delete',
      resourceType: resourceType as any,
      resourceId,
      resourceName,
      description: `Deleted ${resourceType}: ${resourceName}`,
      metadata: {
        source: 'web',
        previousData,
        ...metadata,
      },
    });
  }

  async trackResourceView(
    userId: string,
    resourceType: string,
    resourceId: string,
    resourceName: string,
    metadata?: any
  ): Promise<void> {
    // Use batch logging for view events to avoid overwhelming the database
    this.addToBatch({
      userId,
      action: 'view',
      resourceType: resourceType as any,
      resourceId,
      resourceName,
      description: `Viewed ${resourceType}: ${resourceName}`,
      metadata: {
        source: 'web',
        ...metadata,
      },
    });
  }

  // Track export/import operations
  async trackExport(
    userId: string,
    resourceType: string,
    format: string,
    count: number,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'export',
      resourceType: resourceType as any,
      description: `Exported ${count} ${resourceType}(s) as ${format}`,
      metadata: {
        source: 'web',
        format,
        count,
        ...metadata,
      },
    });
  }

  async trackImport(
    userId: string,
    resourceType: string,
    source: string,
    count: number,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'import',
      resourceType: resourceType as any,
      description: `Imported ${count} ${resourceType}(s) from ${source}`,
      metadata: {
        source: 'web',
        importSource: source,
        count,
        ...metadata,
      },
    });
  }

  // Track sharing and collaboration
  async trackShare(
    userId: string,
    resourceType: string,
    resourceId: string,
    resourceName: string,
    sharedWith: string[],
    permissions: string[],
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'share',
      resourceType: resourceType as any,
      resourceId,
      resourceName,
      description: `Shared ${resourceType}: ${resourceName} with ${sharedWith.length} user(s)`,
      metadata: {
        source: 'web',
        sharedWith,
        permissions,
        ...metadata,
      },
    });
  }

  // Track API usage
  async trackAPICall(
    userId: string,
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number,
    metadata?: any
  ): Promise<void> {
    this.addToBatch({
      userId,
      action: 'view', // Using view for API calls
      resourceType: 'user', // API calls are user-related
      description: `API call: ${method} ${endpoint}`,
      metadata: {
        source: 'api',
        endpoint,
        method,
        statusCode,
        duration,
        ...metadata,
      },
    });
  }

  // Track errors and system events
  async trackError(
    userId: string,
    errorType: string,
    errorMessage: string,
    context?: any
  ): Promise<void> {
    await this.log({
      userId,
      action: 'view', // Using view for error tracking
      resourceType: 'user',
      description: `Error occurred: ${errorType}`,
      metadata: {
        source: 'system',
        errorMessage,
        additionalContext: context,
      },
    });
  }

  // Get activity summary for user
  async getUserActivitySummary(
    userId: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<{
    totalActions: number;
    actionBreakdown: Record<string, number>;
    resourceBreakdown: Record<string, number>;
    recentActivity: IActivityLog[];
    topResources: Array<{ type: string; name: string; count: number }>;
  }> {
    try {
      const logs = await this.getLogs({
        userId,
        startDate: dateRange?.start,
        endDate: dateRange?.end,
        limit: 1000,
      });
      const actionBreakdown: Record<string, number> = {};
      const resourceBreakdown: Record<string, number> = {};
      const resourceCounts: Record<string, number> = {};
      logs.forEach(log => {
        actionBreakdown[log.action] = (actionBreakdown[log.action] || 0) + 1;
        resourceBreakdown[log.resourceType] = (resourceBreakdown[log.resourceType] || 0) + 1;
        if (log.resourceName) {
          const key = `${log.resourceType}:${log.resourceName}`;
          resourceCounts[key] = (resourceCounts[key] || 0) + 1;
        }
      });
      const topResources = Object.entries(resourceCounts)
        .map(([key, count]) => {
          const [type, name] = key.split(':');
          return { type, name, count };
        })
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
      return {
        totalActions: logs.length,
        actionBreakdown,
        resourceBreakdown,
        recentActivity: logs.slice(0, 20),
        topResources,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get user activity summary:', error);
      return {
        totalActions: 0,
        actionBreakdown: {},
        resourceBreakdown: {},
        recentActivity: [],
        topResources: [],
      };
    }
  }

  // Cleanup old logs (called by scheduled job)
  async cleanupOldLogs(olderThanDays: number = 365): Promise<number> {
    try {
      await connectDB();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const result = await ActivityLog.deleteMany({
        createdAt: { $lt: cutoffDate },
      });
      // eslint-disable-next-line no-console
      console.log(
        `Cleaned up ${result.deletedCount} activity logs older than ${olderThanDays} days`
      );
      return result.deletedCount || 0;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to cleanup old logs:', error);
      return 0;
    }
  }

  // Graceful shutdown
  async shutdown(): Promise<void> {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }

    // Flush any remaining logs
    await this.flush();
  }
}

export default ActivityLogService;
