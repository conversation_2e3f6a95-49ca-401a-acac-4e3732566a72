import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { Permission } from '@/models/Permission';
import { User } from '@/models/User';
import { Organization } from '@/models/organization';
import { ActivityLog } from '@/models/ActivityLog';
import mongoose from 'mongoose';
import { connectDB } from '@/Utility/db';

interface PermissionRequest {
  userId?: string;
  resourceType:
    | 'task'
    | 'project'
    | 'note'
    | 'user'
    | 'organization'
    | 'integration'
    | 'report'
    | 'timeline'
    | 'notification'
    | 'file'
    | 'comment'
    | 'budget'
    | 'global';
  resourceId?: string;
  actions: {
    read?: boolean;
    write?: boolean;
    delete?: boolean;
    admin?: boolean;
    share?: boolean;
    export?: boolean;
    import?: boolean;
    manage?: boolean;
  };
  scope?: 'global' | 'organization' | 'project' | 'resource';
  conditions?: any;
  expiresAt?: Date;
  reason?: string;
}

interface UserRoleUpdateRequest {
  userId: string;
  organizationId: string;
  role: 'Owner' | 'Member' | 'Guest';
}

// GET - Check permissions, get user permissions, get organization members
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ route?: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const resolvedParams = await params;
    const route = resolvedParams.route?.[0];
    const searchParams = request.nextUrl.searchParams;

    switch (route) {
      case 'check':
        return await handlePermissionCheck(searchParams);

      case 'user-permissions':
        return await handleGetUserPermissions(session.user.id, searchParams);

      case 'organization-members':
        return await handleGetOrganizationMembers(session.user.id, searchParams);

      case 'resource-permissions':
        return await handleGetResourcePermissions(searchParams);

      case 'roles':
        return await handleGetRoles(searchParams);

      case 'users':
        return await handleGetUsers(searchParams);

      default:
        return await handleGetPermissions(session.user.id, searchParams);
    }
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

// POST - Grant permissions, update user roles
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ route?: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const resolvedParams = await params;
    const route = resolvedParams.route?.[0];
    const body = await request.json();

    switch (route) {
      case 'grant':
        return await handleGrantPermission(body, session.user.id);

      case 'update-user-role':
        return await handleUpdateUserRole(body, session.user.id);

      case 'bulk-grant':
        return await handleBulkGrantPermissions(body, session.user.id);

      case 'roles':
        return await handleCreateRole(body, session.user.id);

      default:
        return NextResponse.json({ error: 'Invalid route' }, { status: 400 });
    }
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

// PUT - Update permissions
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ route?: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const resolvedParams = await params;
    const route = resolvedParams.route?.[0];
    const id = resolvedParams.route?.[1];
    const body = await request.json();

    switch (route) {
      case 'permissions':
        if (id) {
          return await handleUpdatePermission(id, body, session.user.id);
        }
        break;
    }

    return NextResponse.json({ error: 'Invalid route' }, { status: 400 });
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

// DELETE - Revoke permissions, remove user from organization
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ route?: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const resolvedParams = await params;
    const route = resolvedParams.route?.[0];
    const id = resolvedParams.route?.[1];

    switch (route) {
      case 'permissions':
        if (id) {
          return await handleRevokePermission(id, session.user.id);
        }
        break;

      case 'remove-member':
        return await handleRemoveMember(request.nextUrl.searchParams, session.user.id);
    }

    return NextResponse.json({ error: 'Invalid route' }, { status: 400 });
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

// Handler functions
async function handlePermissionCheck(searchParams: URLSearchParams) {
  try {
    const userId = searchParams.get('userId');
    const resourceType = searchParams.get('resourceType');
    const action = searchParams.get('action');
    const organizationId = searchParams.get('organizationId');

    if (!userId || !resourceType || !action) {
      return NextResponse.json(
        { error: 'userId, resourceType, and action are required' },
        { status: 400 }
      );
    }

    // Check if user has permission based on organization role
    const hasPermission = await checkUserPermission(
      userId ?? undefined,
      resourceType ?? undefined,
      action ?? undefined,
      undefined,
      organizationId ?? undefined
    );

    return NextResponse.json({
      success: true,
      hasPermission,
      userId,
      resourceType,
      action,
      organizationId,
    });
  } catch (error: any) {
    throw new Error(`Permission check failed: ${error.message}`);
  }
}

async function handleGetUserPermissions(targetUserId: string, searchParams: URLSearchParams) {
  try {
    const resourceType = searchParams.get('resourceType');
    const resourceId = searchParams.get('resourceId');
    const organizationId = searchParams.get('organizationId');

    const options: any = {};
    if (resourceType) options.resourceType = resourceType;
    if (resourceId) options.resourceId = resourceId;
    if (organizationId) options.organizationId = organizationId;

    // Get explicit permissions
    const permissions = await Permission.getUserPermissions(targetUserId ?? undefined, options);

    // Get user's organization role
    const user = await User.findById(targetUserId);
    if (user) {
      console.log(`[Permissions] Fetched permissions for user: ${user.name} (${user.email})`);
    }
    const organizationRole = await getUserOrganizationRole(
      (targetUserId ?? undefined) as string,
      (organizationId ?? undefined) as string
    );

    return NextResponse.json({
      success: true,
      permissions,
      organizationRole,
      userId: targetUserId,
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch user permissions: ${error.message}`);
  }
}

async function handleGetOrganizationMembers(userId: string, searchParams: URLSearchParams) {
  try {
    console.log(`[Permissions] Organization members requested by userId: ${userId}`);
    const organizationId = searchParams.get('organizationId');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!organizationId) {
      return NextResponse.json({ error: 'organizationId is required' }, { status: 400 });
    }

    const organization = await Organization.findById(organizationId)
      .populate('members.userId', 'name email image')
      .populate('ownerId', 'name email image');

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    let members = organization.members || [];

    // Add owner to members list
    if (organization.ownerId) {
      const ownerMember = {
        userId: organization.ownerId,
        role: 'Owner' as const,
        joinedAt: organization.createdAt,
      };
      members = [ownerMember, ...members];
    }

    // Filter by search term
    if (search) {
      members = members.filter(
        member =>
          member.userId.name?.toLowerCase().includes(search.toLowerCase()) ||
          member.userId.email?.toLowerCase().includes(search.toLowerCase())
      );
    }

    const total = members.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMembers = members.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      members: paginatedMembers,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch organization members: ${error.message}`);
  }
}

async function handleGetResourcePermissions(searchParams: URLSearchParams) {
  try {
    const resourceType = searchParams.get('resourceType');
    const resourceId = searchParams.get('resourceId');
    const userId = searchParams.get('userId');
    const organizationId = searchParams.get('organizationId');

    if (!resourceType || !resourceId) {
      return NextResponse.json(
        { error: 'resourceType and resourceId are required' },
        { status: 400 }
      );
    }

    const options: any = {};
    if (userId) options.userId = userId;
    if (organizationId) options.organizationId = organizationId;

    const permissions = await Permission.getResourcePermissions(
      resourceType ?? undefined,
      resourceId ?? undefined,
      options
    );

    return NextResponse.json({
      success: true,
      permissions,
      resourceType,
      resourceId,
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch resource permissions: ${error.message}`);
  }
}

async function handleGetPermissions(userId: string, searchParams: URLSearchParams) {
  try {
    const targetUserId = searchParams.get('userId') || userId;
    const resourceType = searchParams.get('resourceType');
    const resourceId = searchParams.get('resourceId');
    const organizationId = searchParams.get('organizationId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const query: any = {};

    if (targetUserId) {
      query.userId = new mongoose.Types.ObjectId(targetUserId);
    }

    if (resourceType) {
      query.resourceType = resourceType;
    }

    if (resourceId) {
      query.resourceId = new mongoose.Types.ObjectId(resourceId);
    }

    if (organizationId) {
      query.organizationId = new mongoose.Types.ObjectId(organizationId);
    }

    query.isActive = true;

    const permissions = await Permission.find(query)
      .populate('userId', 'name email')
      .populate('grantedBy', 'name email')
      .populate('revokedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Permission.countDocuments(query);

    return NextResponse.json({
      success: true,
      permissions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch permissions: ${error.message}`);
  }
}

async function handleGrantPermission(permissionData: PermissionRequest, grantedBy: string) {
  try {
    const permission: any = await Permission.grantPermission({
      ...permissionData,
      grantedBy: new mongoose.Types.ObjectId(grantedBy),
    });

    // Log activity
    await ActivityLog.create({
      userId: grantedBy,
      action: 'create',
      resourceType: 'user',
      resourceId: permissionData.userId
        ? new mongoose.Types.ObjectId(permissionData.userId)
        : undefined,
      description: `Granted ${permissionData.resourceType} permissions`,
      metadata: {
        permissionId: (permission as any)._id,
        resourceType: permissionData.resourceType,
        actions: permissionData.actions,
        scope: permissionData.scope,
      },
    });

    return NextResponse.json({
      success: true,
      permission,
      message: 'Permission granted successfully',
    });
  } catch (error: any) {
    throw new Error(`Failed to grant permission: ${error.message}`);
  }
}

async function handleUpdateUserRole(data: UserRoleUpdateRequest, updatedBy: string) {
  try {
    const { userId, organizationId, role } = data;

    // Check if user has permission to update roles
    const canUpdate = await checkUserPermission(
      updatedBy,
      'user',
      'manage',
      undefined,
      organizationId
    );
    if (!canUpdate) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update user roles' },
        { status: 403 }
      );
    }

    const organization = await Organization.findById(organizationId);
    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if user is trying to change owner role
    if (organization.ownerId?.toString() === userId && role !== 'Owner') {
      return NextResponse.json({ error: 'Cannot change organization owner role' }, { status: 403 });
    }

    // Update member role
    const memberIndex = organization.members.findIndex(
      member => member.userId.toString() === userId
    );

    if (memberIndex === -1) {
      return NextResponse.json(
        { error: 'User is not a member of this organization' },
        { status: 404 }
      );
    }

    const oldRole = organization.members[memberIndex].role;
    organization.members[memberIndex].role = role;

    await organization.save();

    // Log activity
    await ActivityLog.create({
      userId: updatedBy,
      action: 'update',
      resourceType: 'user',
      resourceId: new mongoose.Types.ObjectId(userId),
      description: `Updated user role from ${oldRole} to ${role}`,
      metadata: {
        organizationId,
        oldRole,
        newRole: role,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'User role updated successfully',
    });
  } catch (error: any) {
    throw new Error(`Failed to update user role: ${error.message}`);
  }
}

async function handleBulkGrantPermissions(
  data: { permissions: PermissionRequest[] },
  grantedBy: string
) {
  try {
    const result: any = await Permission.bulkGrantPermissions(data.permissions, grantedBy);

    // Log activity
    await ActivityLog.create({
      userId: grantedBy,
      action: 'create',
      resourceType: 'user',
      description: `Bulk granted permissions: ${result.granted} granted, ${result.updated} updated`,
      metadata: {
        granted: result.granted,
        updated: result.updated,
        errors: result.errors.length,
        totalPermissions: data.permissions.length,
      },
    });

    return NextResponse.json({
      success: true,
      result,
      message: `Bulk permission grant completed: ${result.granted} granted, ${result.updated} updated`,
    });
  } catch (error: any) {
    throw new Error(`Failed to bulk grant permissions: ${error.message}`);
  }
}

async function handleUpdatePermission(
  permissionId: string,
  updates: Partial<PermissionRequest>,
  updatedBy: string
) {
  try {
    const permission: any = await Permission.findById(permissionId);

    if (!permission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 });
    }

    const oldData = { ...permission.toObject() };

    Object.assign(permission, updates);
    (permission as any).addAuditEntry('modified', updatedBy, 'Permission updated', {
      from: oldData,
      to: updates,
    });

    await permission.save();

    // Log activity
    await ActivityLog.create({
      userId: updatedBy,
      action: 'update',
      resourceType: 'user',
      resourceId: permission.userId,
      description: `Updated ${permission.resourceType} permissions`,
      metadata: {
        permissionId: permission._id,
        resourceType: permission.resourceType,
        changes: updates,
      },
    });

    return NextResponse.json({
      success: true,
      permission,
      message: 'Permission updated successfully',
    });
  } catch (error: any) {
    throw new Error(`Failed to update permission: ${error.message}`);
  }
}

async function handleRevokePermission(permissionId: string, revokedBy: string) {
  try {
    const success = await Permission.revokePermission(permissionId, revokedBy);

    if (success) {
      const permission: any = await Permission.findById(permissionId);

      // Log activity
      await ActivityLog.create({
        userId: revokedBy,
        action: 'delete',
        resourceType: 'user',
        resourceId: permission?.userId,
        description: `Revoked ${permission?.resourceType || 'unknown'} permissions`,
        metadata: {
          permissionId,
          resourceType: permission?.resourceType,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Permission revoked successfully',
      });
    } else {
      return NextResponse.json({ error: 'Failed to revoke permission' }, { status: 500 });
    }
  } catch (error: any) {
    throw new Error(`Failed to revoke permission: ${error.message}`);
  }
}

async function handleRemoveMember(searchParams: URLSearchParams, removedBy: string) {
  try {
    const organizationId = searchParams.get('organizationId');
    const userId = searchParams.get('userId');

    if (!organizationId || !userId) {
      return NextResponse.json(
        { error: 'organizationId and userId are required' },
        { status: 400 }
      );
    }

    // Check if user has permission to remove members
    const canRemove = await checkUserPermission(
      removedBy,
      'user',
      'manage',
      undefined,
      organizationId
    );
    if (!canRemove) {
      return NextResponse.json(
        { error: 'Insufficient permissions to remove members' },
        { status: 403 }
      );
    }

    const organization = await Organization.findById(organizationId);
    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if user is trying to remove owner
    if (organization.ownerId?.toString() === userId) {
      return NextResponse.json({ error: 'Cannot remove organization owner' }, { status: 403 });
    }

    // Remove member
    organization.members = organization.members.filter(
      member => member.userId.toString() !== userId
    );

    await organization.save();

    // Log activity
    await ActivityLog.create({
      userId: removedBy,
      action: 'delete',
      resourceType: 'user',
      resourceId: new mongoose.Types.ObjectId(userId),
      description: `Removed user from organization`,
      metadata: {
        organizationId,
        removedUserId: userId,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Member removed successfully',
    });
  } catch (error: any) {
    throw new Error(`Failed to remove member: ${error.message}`);
  }
}

// Helper functions
async function checkUserPermission(
  userId: string,
  resourceType: string,
  action: string,
  resourceId?: string,
  organizationId?: string
): Promise<boolean> {
  if (resourceId) {
    console.log(`[Permissions] Checking permission for resourceId: ${resourceId}`);
  }
  try {
    // Get user's organization role
    const userRole = await getUserOrganizationRole(userId, organizationId);

    // Define role-based permissions
    const rolePermissions = {
      Owner: {
        task: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        project: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        note: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        user: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        organization: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        integration: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        report: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        timeline: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        notification: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        file: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        comment: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        budget: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
        global: ['read', 'write', 'delete', 'admin', 'share', 'export', 'import', 'manage'],
      },
      Member: {
        task: ['read', 'write', 'share', 'export'],
        project: ['read', 'write', 'share', 'export'],
        note: ['read', 'write', 'share', 'export'],
        user: ['read'],
        organization: ['read'],
        integration: ['read', 'write'],
        report: ['read', 'export'],
        timeline: ['read', 'write'],
        notification: ['read', 'write'],
        file: ['read', 'write', 'share'],
        comment: ['read', 'write'],
        budget: ['read'],
        global: ['read'],
      },
      Guest: {
        task: ['read'],
        project: ['read'],
        note: ['read'],
        user: ['read'],
        organization: ['read'],
        integration: ['read'],
        report: ['read'],
        timeline: ['read'],
        notification: ['read'],
        file: ['read'],
        comment: ['read'],
        budget: ['read'],
        global: ['read'],
      },
    };

    // Check if user has the required permission
    const permissions = rolePermissions[userRole as keyof typeof rolePermissions];
    if (!permissions) return false;

    const resourcePermissions = permissions[resourceType as keyof typeof permissions];
    if (!resourcePermissions) return false;

    return resourcePermissions.includes(action);
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}

async function getUserOrganizationRole(userId: string, organizationId?: string): Promise<string> {
  try {
    if (!organizationId) return 'Guest';

    const organization = await Organization.findById(organizationId);
    if (!organization) return 'Guest';

    // Check if user is owner
    if (organization.ownerId?.toString() === userId) {
      return 'Owner';
    }

    // Check if user is a member
    const member = organization.members.find(m => m.userId.toString() === userId);
    return member?.role || 'Guest';
  } catch (error) {
    console.error('Error getting user organization role:', error);
    return 'Guest';
  }
}

// Default roles for organizations
const DEFAULT_ROLES = [
  {
    name: 'owner',
    displayName: 'Owner',
    description: 'Full access to all features and settings',
    type: 'system',
    permissions: {
      tasks: { read: true, write: true, delete: true, admin: true, assign: true, manage: true },
      projects: {
        read: true,
        write: true,
        delete: true,
        admin: true,
        create: true,
        manage: true,
        archive: true,
      },
      users: { read: true, write: true, delete: true, admin: true, invite: true, manage: true },
      organization: {
        read: true,
        write: true,
        delete: true,
        admin: true,
        manage: true,
        billing: true,
      },
      integrations: { read: true, write: true, delete: true, manage: true, configure: true },
    },
    isSystem: true,
    isDefault: false,
  },
  {
    name: 'member',
    displayName: 'Member',
    description: 'Can create and manage tasks and projects',
    type: 'system',
    permissions: {
      tasks: { read: true, write: true, delete: false, admin: false, assign: true, manage: false },
      projects: {
        read: true,
        write: true,
        delete: false,
        admin: false,
        create: true,
        manage: false,
        archive: false,
      },
      users: {
        read: true,
        write: false,
        delete: false,
        admin: false,
        invite: false,
        manage: false,
      },
      organization: {
        read: true,
        write: false,
        delete: false,
        admin: false,
        manage: false,
        billing: false,
      },
      integrations: { read: true, write: false, delete: false, manage: false, configure: false },
    },
    isSystem: true,
    isDefault: true,
  },
  {
    name: 'guest',
    displayName: 'Guest',
    description: 'View-only access to projects and tasks',
    type: 'system',
    permissions: {
      tasks: {
        read: true,
        write: false,
        delete: false,
        admin: false,
        assign: false,
        manage: false,
      },
      projects: {
        read: true,
        write: false,
        delete: false,
        admin: false,
        create: false,
        manage: false,
        archive: false,
      },
      users: {
        read: true,
        write: false,
        delete: false,
        admin: false,
        invite: false,
        manage: false,
      },
      organization: {
        read: false,
        write: false,
        delete: false,
        admin: false,
        manage: false,
        billing: false,
      },
      integrations: { read: false, write: false, delete: false, manage: false, configure: false },
    },
    isSystem: true,
    isDefault: false,
  },
];

async function handleGetRoles(_searchParams: URLSearchParams) {
  try {
    // Return default roles with some mock assigned users
    const roles = DEFAULT_ROLES.map((role, index) => ({
      ...role,
      _id: `role_${index + 1}`,
      id: `role_${index + 1}`,
      assignedUsers: [
        { id: 'user_1', name: 'John Doe', email: '<EMAIL>' },
        { id: 'user_2', name: 'Jane Smith', email: '<EMAIL>' },
      ].slice(0, Math.floor(Math.random() * 3) + 1),
      metadata: {
        color: ['#ef4444', '#22c55e', '#3b82f6'][index],
        icon: ['👑', '👥', '👁️'][index],
      },
    }));

    return NextResponse.json({
      success: true,
      roles,
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch roles: ${error.message}`);
  }
}

async function handleGetUsers(searchParams: URLSearchParams) {
  try {
    const organizationId = searchParams.get('organizationId');
    searchParams.get('search'); // Unused but keeping for API compatibility

    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    // This should redirect to the main API route
    // For now, return empty array since this route should not be used
    return NextResponse.json({
      success: true,
      users: [],
      message: 'Please use /api/users endpoint instead',
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch users: ${error.message}`);
  }
}

async function handleCreateRole(roleData: any, _createdBy: string) {
  try {
    const { name, displayName, description, type, permissions, restrictions, metadata } = roleData;

    // Validate required fields
    if (!name || !displayName) {
      return NextResponse.json({ error: 'Name and display name are required' }, { status: 400 });
    }

    // Create new role (mock implementation)
    const newRole = {
      _id: `role_${Date.now()}`,
      id: `role_${Date.now()}`,
      name,
      displayName,
      description: description || '',
      type: type || 'custom',
      permissions: permissions || {},
      restrictions: restrictions || {},
      metadata: metadata || {},
      isSystem: false,
      isDefault: false,
      assignedUsers: [],
    };

    return NextResponse.json({
      success: true,
      role: newRole,
      message: 'Role created successfully',
    });
  } catch (error: any) {
    throw new Error(`Failed to create role: ${error.message}`);
  }
}
