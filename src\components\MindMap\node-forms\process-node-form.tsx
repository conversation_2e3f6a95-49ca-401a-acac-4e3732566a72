'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, X, Save } from 'lucide-react';

interface Step {
  number: number;
  description: string;
}

interface ProcessNodeData {
  title: string;
  steps: Step[];
  currentStep: number;
}

interface ProcessNodeFormProps {
  data: ProcessNodeData;
  onSave: (d: Partial<ProcessNodeData>) => void;
  onCancel: () => void;
}

export function ProcessNodeForm({ data, onSave, onCancel }: ProcessNodeFormProps) {
  const [formData, setFormData] = useState<ProcessNodeData>({
    ...data,
    steps: [...data.steps],
  });

  const handleTitle = (v: string) => setFormData(p => ({ ...p, title: v }));

  const handleStepChange = (idx: number, v: string) =>
    setFormData(p => {
      const steps = [...p.steps];
      steps[idx].description = v;
      return { ...p, steps };
    });

  const addStep = () =>
    setFormData(p => ({
      ...p,
      steps: [
        ...p.steps,
        { number: p.steps.length + 1, description: `Step ${p.steps.length + 1}` },
      ],
    }));

  const removeStep = (idx: number) =>
    setFormData(p => ({
      ...p,
      steps: p.steps.filter((_, i) => i !== idx).map((s, i) => ({ ...s, number: i + 1 })),
      currentStep: Math.min(p.currentStep, p.steps.length - 1),
    }));

  const setCurrent = (num: number) => setFormData(p => ({ ...p, currentStep: num }));

  return (
    <div className="min-w-[320px]">
      <form
        onSubmit={e => {
          e.preventDefault();
          onSave(formData);
        }}
        className="space-y-4"
      >
        <div>
          <Label htmlFor="title">Title</Label>
          <Input id="title" value={formData.title} onChange={e => handleTitle(e.target.value)} />
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Steps</Label>
            <Button
              size="sm"
              variant="outline"
              type="button"
              onClick={addStep}
              className="px-2 bg-transparent"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {formData.steps.map((s, idx) => (
            <div key={s.number} className="flex items-center gap-2 mb-2">
              <Button
                type="button"
                size="icon"
                variant={formData.currentStep === s.number ? 'default' : 'outline'}
                className="h-8 w-8"
                onClick={() => setCurrent(s.number)}
              >
                {s.number}
              </Button>
              <Input
                value={s.description}
                onChange={e => handleStepChange(idx, e.target.value)}
                className="flex-1"
              />
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => removeStep(idx)}
                disabled={formData.steps.length <= 1}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
