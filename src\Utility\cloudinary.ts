import { v2 as cloudinary } from 'cloudinary';
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export interface UploadResult {
  secure_url: string;
  public_id: string;
  bytes?: number;
  error?: string;
}

export const uploadToCloudinary = async (
  file: string, // file should contain proper base64 data
  folder: string = 'TaskFluxio'
): Promise<UploadResult> => {
  try {
    // Upload to Cloudinary with timeout
    const result = await Promise.race([
      new Promise<any>((resolve, reject) => {
        cloudinary.uploader.upload(
          file,
          {
            folder,
            resource_type: 'auto',
            timeout: 30000, // 30 second timeout
          },
          (error, result) => {
            if (error) reject(error);
            resolve(result);
          }
        );
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Upload timeout after 30 seconds')), 30000)
      ),
    ]);

    return {
      secure_url: result.secure_url,
      public_id: result.public_id,
      bytes: result.bytes,
    };
  } catch (error) {
    return {
      secure_url: '',
      public_id: '',
      error: 'Failed to upload file',
    };
  }
};

export const deleteFromCloudinary = async (publicId: string): Promise<boolean> => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    const success = result.result === 'ok';
    return success;
  } catch (error) {
    return false;
  }
};
