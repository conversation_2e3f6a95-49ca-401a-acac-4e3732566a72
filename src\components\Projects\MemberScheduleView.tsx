import React from 'react';
import { Team<PERSON>ember } from './ResourceAllocationGrid';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { format, addDays, startOfWeek } from 'date-fns';
import { Calendar, Clock } from 'lucide-react';
import Image from 'next/image';

interface MemberScheduleViewProps {
  member: TeamMember;
}

export function MemberScheduleView({ member }: MemberScheduleViewProps) {
  // Helper function to safely get project name
  const getProjectName = (projectId: any): string => {
    if (typeof projectId === 'object' && projectId !== null && 'name' in projectId) {
      return String(projectId.name);
    }
    return String(projectId || 'Unknown Project');
  };
  // Generate dates for the next 4 weeks
  const today = startOfWeek(new Date());
  const weeks = Array.from({ length: 4 }, (_, weekIndex) => {
    return Array.from({ length: 7 }, (_, dayIndex) => {
      return addDays(today, weekIndex * 7 + dayIndex);
    });
  });

  // Get allocation status for a specific date
  const getAllocationStatus = (date: Date) => {
    const activeAllocations = member.allocations.filter(allocation => {
      const startDate = new Date(allocation.startDate);
      const endDate = new Date(allocation.endDate);
      return date >= startDate && date <= endDate;
    });

    if (activeAllocations.length === 0) return null;

    const totalCapacity = activeAllocations.reduce(
      (sum, allocation) => sum + allocation.capacity,
      0
    );

    if (totalCapacity > 100) {
      return { status: 'overallocated', capacity: totalCapacity, allocations: activeAllocations };
    } else if (totalCapacity >= 80) {
      return { status: 'full', capacity: totalCapacity, allocations: activeAllocations };
    } else if (totalCapacity > 0) {
      return { status: 'partial', capacity: totalCapacity, allocations: activeAllocations };
    }

    return null;
  };

  // Get color class based on allocation status
  const getStatusColorClass = (status: string | null) => {
    if (!status) return 'bg-gray-100';

    switch (status) {
      case 'overallocated':
        return 'bg-red-500';
      case 'full':
        return 'bg-amber-500';
      case 'partial':
        return 'bg-green-500';
      default:
        return 'bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
            {member.avatar ? (
              <Image
                height={48}
                width={48}
                src={member.avatar}
                alt={member.name}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              member.name.charAt(0).toUpperCase()
            )}
          </div>
          <div>
            <h3 className="font-semibold text-lg">{member.name}</h3>
            <p className="text-sm text-muted-foreground">{member.email}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={member.isOverAllocated ? 'destructive' : 'outline'}>
            {member.currentUtilization}% Utilized
          </Badge>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Schedule
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px]">
            <div className="space-y-6">
              {weeks.map((week, weekIndex) => (
                <div key={weekIndex} className="space-y-2">
                  <h4 className="font-medium">Week {weekIndex + 1}</h4>
                  <div className="grid grid-cols-7 gap-2">
                    {week.map((date, dayIndex) => {
                      const allocationStatus = getAllocationStatus(date);
                      return (
                        <div
                          key={dayIndex}
                          className={`border rounded-md p-2 ${
                            date.getDay() === 0 || date.getDay() === 6 ? 'bg-gray-50' : ''
                          }`}
                        >
                          <div className="text-center mb-2">
                            <div className="text-xs text-gray-500">{format(date, 'EEE')}</div>
                            <div className="font-medium">{format(date, 'd')}</div>
                          </div>

                          {allocationStatus ? (
                            <div className="space-y-2">
                              <div
                                className={`h-2 rounded-full ${getStatusColorClass(allocationStatus.status)}`}
                                title={`${allocationStatus.capacity}% allocated`}
                              ></div>
                              <div className="text-xs font-medium text-center">
                                {allocationStatus.capacity}%
                              </div>
                              <div className="space-y-1">
                                {allocationStatus.allocations.map((allocation, i) => (
                                  <div
                                    key={i}
                                    className="text-xs p-1 bg-gray-100 rounded truncate"
                                    title={allocation.role}
                                  >
                                    {allocation.role} ({allocation.capacity}%)
                                  </div>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center h-12 text-gray-400">
                              <Clock className="h-4 w-4" />
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Allocation Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {member.allocations.map((allocation, index) => (
              <div key={index} className="border rounded-md p-3">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">{allocation.role}</h4>
                  <Badge variant="outline">{allocation.capacity}%</Badge>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Project: </span>
                    {getProjectName(allocation.projectId)}
                  </div>
                  <div>
                    <span className="text-gray-500">Period: </span>
                    {format(new Date(allocation.startDate), 'MMM d')} -{' '}
                    {format(new Date(allocation.endDate), 'MMM d')}
                  </div>
                  {allocation.skills && allocation.skills.length > 0 && (
                    <div className="col-span-2">
                      <span className="text-gray-500">Skills: </span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {allocation.skills.map((skill, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
