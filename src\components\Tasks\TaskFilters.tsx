import React from 'react';
import { Filter, Search, SortAsc } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  TaskFilterState,
  TaskSortOption,
  sortOptions,
  statusOptions,
  priorityOptions,
} from './types';

interface TaskFiltersProps {
  filters: TaskFilterState;
  onFilterChange: (filters: Partial<TaskFilterState>) => void;
  onSortChange: (sortOption: TaskSortOption) => void;
  currentSort: TaskSortOption;
}

const TaskFilters: React.FC<TaskFiltersProps> = ({
  filters,
  onFilterChange,
  onSortChange,
  currentSort,
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="relative flex-grow">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 theme-text-secondary" />
        <Input
          placeholder="Search tasks..."
          className="pl-10 theme-input theme-border theme-transition"
          value={filters.searchQuery}
          onChange={e => onFilterChange({ searchQuery: e.target.value })}
        />
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full sm:w-auto gap-2 theme-button-secondary theme-border theme-transition"
          >
            <Filter className="h-4 w-4" /> Filter
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-56 theme-surface-elevated theme-border theme-shadow-lg"
        >
          <DropdownMenuLabel className="theme-text-primary">Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator className="theme-border" />
          <DropdownMenuItem
            onClick={() => onFilterChange({ status: 'all' })}
            className={`theme-transition ${filters.status === 'all' ? 'theme-surface' : 'theme-button-ghost'}`}
          >
            All
          </DropdownMenuItem>
          {statusOptions.map(status => (
            <DropdownMenuItem
              key={status.value}
              onClick={() => onFilterChange({ status: status.value })}
              className={`flex items-center gap-2 theme-transition ${filters.status === status.value ? 'theme-surface' : 'theme-button-ghost'}`}
            >
              <div className={`w-2 h-2 rounded-full ${status.color}`} />
              {status.label}
            </DropdownMenuItem>
          ))}

          <DropdownMenuSeparator className="theme-border" />
          <DropdownMenuLabel className="theme-text-primary">Filter by Priority</DropdownMenuLabel>
          <DropdownMenuSeparator className="theme-border" />
          <DropdownMenuItem
            onClick={() => onFilterChange({ priority: 'all' })}
            className={`theme-transition ${filters.priority === 'all' ? 'theme-surface' : 'theme-button-ghost'}`}
          >
            All
          </DropdownMenuItem>
          {priorityOptions.map(priority => (
            <DropdownMenuItem
              key={priority.value}
              onClick={() => onFilterChange({ priority: priority.value })}
              className={`flex items-center gap-2 theme-transition ${filters.priority === priority.value ? 'theme-surface' : 'theme-button-ghost'}`}
            >
              <div className={`w-2 h-2 rounded-full ${priority.color.replace('text-', 'bg-')}`} />
              {priority.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full sm:w-auto gap-2 theme-button-secondary theme-border theme-transition"
          >
            <SortAsc className="h-4 w-4" /> Sort
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-56 theme-surface-elevated theme-border theme-shadow-lg"
        >
          {sortOptions.map(option => (
            <DropdownMenuItem
              key={option.label}
              onClick={() => onSortChange(option)}
              className={`flex items-center gap-2 theme-transition ${currentSort.label === option.label ? 'theme-surface' : 'theme-button-ghost'}`}
            >
              {currentSort.label === option.label && (
                <div className="w-2 h-2 rounded-full bg-primary" />
              )}
              {option.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default TaskFilters;
