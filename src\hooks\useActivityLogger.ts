import { useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { activityLogApiService, CreateLogData } from '@/services/ActivityLogApi.service';
import { ACTIVITY_LOG_QUERY_KEYS } from './useActivityLogs';

// Types for the hook
export type ActivityAction =
  | 'create'
  | 'update'
  | 'delete'
  | 'login'
  | 'logout'
  | 'view'
  | 'export'
  | 'import'
  | 'share'
  | 'archive';

export type ResourceType =
  | 'task'
  | 'project'
  | 'note'
  | 'user'
  | 'organization'
  | 'integration'
  | 'report'
  | 'timeline'
  | 'notification'
  | 'file'
  | 'comment'
  | 'budget';

export interface LogActivityParams {
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  resourceName?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface UseActivityLoggerReturn {
  logActivity: (params: LogActivityParams) => Promise<boolean>;
  logPageView: (pageName: string, metadata?: Record<string, any>) => Promise<boolean>;
  logResourceCreate: (
    resourceType: ResourceType,
    resourceId: string,
    resourceName: string,
    metadata?: Record<string, any>
  ) => Promise<boolean>;
  logResourceUpdate: (
    resourceType: ResourceType,
    resourceId: string,
    resourceName: string,
    changes?: Record<string, { from: any; to: any }>,
    metadata?: Record<string, any>
  ) => Promise<boolean>;
  logResourceDelete: (
    resourceType: ResourceType,
    resourceId: string,
    resourceName: string,
    previousData?: any,
    metadata?: Record<string, any>
  ) => Promise<boolean>;
  logExport: (
    resourceType: ResourceType,
    format: string,
    count: number,
    metadata?: Record<string, any>
  ) => Promise<boolean>;
  isLogging: boolean;
  error: Error | null;
}

// Deduplication cache
const logCache = new Map<string, { timestamp: number; promise: Promise<boolean> }>();
const CACHE_DURATION = 5000; // 5 seconds
const PAGE_VIEW_DEBOUNCE_TIME = 2000; // 2 seconds for page views

// Generate cache key for deduplication
const generateCacheKey = (params: LogActivityParams, userId: string): string => {
  const { action, resourceType, resourceId, resourceName, description } = params;
  return `${userId}-${action}-${resourceType}-${resourceId || 'none'}-${resourceName || 'none'}-${description || 'none'}`;
};

// Clean expired cache entries
const cleanCache = () => {
  const now = Date.now();
  for (const [key, value] of logCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      logCache.delete(key);
    }
  }
};

export const useActivityLogger = (): UseActivityLoggerReturn => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const pageViewTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastPageViewRef = useRef<string>('');

  // Mutation for creating activity logs
  const mutation = useMutation({
    mutationFn: (data: CreateLogData) => activityLogApiService.createLog(data),
    onSuccess: () => {
      // Invalidate activity log queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: ACTIVITY_LOG_QUERY_KEYS.lists()
      });
      queryClient.invalidateQueries({
        queryKey: [...ACTIVITY_LOG_QUERY_KEYS.all, 'analytics']
      });
    },
    onError: (error) => {
      console.error('Failed to log activity:', error);
    },
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx)
      if (error?.response?.status && error.response.status < 500) {
        return false;
      }
      return failureCount < 2; // Reduced retries for logging
    },
    retryDelay: 1000, // 1 second delay
  });

  const logActivity = useCallback(
    async (params: LogActivityParams): Promise<boolean> => {
      if (!session?.user?.id) {
        console.warn('Cannot log activity: user not authenticated');
        return false;
      }

      // Clean expired cache entries periodically
      cleanCache();

      const cacheKey = generateCacheKey(params, session.user.id);
      const now = Date.now();

      // Check if we have a recent identical request
      const cachedEntry = logCache.get(cacheKey);
      if (cachedEntry && now - cachedEntry.timestamp < CACHE_DURATION) {
        console.debug('Deduplicating activity log request:', cacheKey);
        return cachedEntry.promise;
      }

      // Create the log data
      const logData: CreateLogData = {
        action: params.action,
        resourceType: params.resourceType,
        resourceId: params.resourceId,
        resourceName: params.resourceName,
        description: params.description ||
          `${params.action} ${params.resourceType}${params.resourceName ? `: ${params.resourceName}` : ''}`,
        metadata: {
          ...params.metadata,
          organizationId: session.user.organizationId,
          url: typeof window !== 'undefined' ? window.location.href : '',
          timestamp: new Date().toISOString(),
        },
      };

      // Create the promise and cache it
      const promise = mutation.mutateAsync(logData)
        .then(() => true)
        .catch((error) => {
          console.error('Activity logging failed:', error);
          return false;
        });

      // Cache the promise to prevent duplicates
      logCache.set(cacheKey, { timestamp: now, promise });

      return promise;
    },
    [session?.user?.id, session?.user?.organizationId, mutation]
  );

  /**
   * Log a page view with debouncing to prevent rapid duplicate entries
   */
  const logPageView = useCallback(
    (pageName: string, metadata?: Record<string, any>) => {
      return new Promise<boolean>((resolve) => {
        // Clear existing timeout
        if (pageViewTimeoutRef.current) {
          clearTimeout(pageViewTimeoutRef.current);
        }

        // Check if this is the same page view as the last one
        if (lastPageViewRef.current === pageName) {
          resolve(true);
          return;
        }

        // Set up debounced logging
        pageViewTimeoutRef.current = setTimeout(async () => {
          lastPageViewRef.current = pageName;
          const result = await logActivity({
            action: 'view',
            resourceType: 'user',
            description: `Viewed ${pageName}`,
            metadata: {
              ...metadata,
              pageName,
            },
          });
          resolve(result);
        }, PAGE_VIEW_DEBOUNCE_TIME);
      });
    },
    [logActivity]
  );

  /**
   * Log a resource creation
   */
  const logResourceCreate = useCallback(
    (
      resourceType: ResourceType,
      resourceId: string,
      resourceName: string,
      metadata?: Record<string, any>
    ) => {
      return logActivity({
        action: 'create',
        resourceType,
        resourceId,
        resourceName,
        metadata,
      });
    },
    [logActivity]
  );

  /**
   * Log a resource update
   */
  const logResourceUpdate = useCallback(
    (
      resourceType: ResourceType,
      resourceId: string,
      resourceName: string,
      changes?: Record<string, { from: any; to: any }>,
      metadata?: Record<string, any>
    ) => {
      return logActivity({
        action: 'update',
        resourceType,
        resourceId,
        resourceName,
        metadata: {
          ...metadata,
          changes,
        },
      });
    },
    [logActivity]
  );

  /**
   * Log a resource deletion
   */
  const logResourceDelete = useCallback(
    (
      resourceType: ResourceType,
      resourceId: string,
      resourceName: string,
      previousData?: any,
      metadata?: Record<string, any>
    ) => {
      return logActivity({
        action: 'delete',
        resourceType,
        resourceId,
        resourceName,
        metadata: {
          ...metadata,
          previousData,
        },
      });
    },
    [logActivity]
  );

  /**
   * Log a data export
   */
  const logExport = useCallback(
    (
      resourceType: ResourceType,
      format: string,
      count: number,
      metadata?: Record<string, any>
    ) => {
      return logActivity({
        action: 'export',
        resourceType,
        description: `Exported ${count} ${resourceType}(s) as ${format}`,
        metadata: {
          ...metadata,
          format,
          count,
        },
      });
    },
    [logActivity]
  );

  return {
    logActivity,
    logPageView,
    logResourceCreate,
    logResourceUpdate,
    logResourceDelete,
    logExport,
    isLogging: mutation.isPending,
    error: mutation.error as Error | null,
  };
};

export default useActivityLogger;
