// Subscription Types
export type PlanId = 'free' | 'basic' | 'professional' | 'enterprise';
export type BillingCycle = 'monthly' | 'yearly';
export type SubscriptionStatus =
  | 'active'
  | 'suspended'
  | 'cancelled'
  | 'past_due'
  | 'trialing'
  | 'paused';
export type PaymentMethods = 'upi' | 'netbanking' | 'card' | 'wallet' | 'emi';
export type SupportLevel = 'community' | 'email' | 'priority' | 'phone' | 'dedicated';

export type UsageAction =
  | 'create_project'
  | 'invite_user'
  | 'upload_file'
  | 'create_integration'
  | 'create_automation'
  | 'api_call';
export type UsageUpdateAction =
  | 'project_created'
  | 'user_invited'
  | 'file_uploaded'
  | 'integration_created'
  | 'automation_created'
  | 'api_call_made';

// Subscription Interface
export interface Subscription {
  id: string;
  organizationId: string;
  plan: PlanId;
  status: SubscriptionStatus;
  billingCycle: BillingCycle;
  amount: number;
  originalAmount: number;
  discountPercentage: number;
  currency: string;

  // Periods
  currentPeriodStart: string;
  currentPeriodEnd: string | null;
  trialStart?: string;
  trialEnd?: string;
  pausedAt?: string;
  pauseReason?: string;

  // Payment
  subscriptionId?: string;
  customerId?: string;
  priceId?: string;
  lastPaymentDate?: string;
  nextPaymentDate?: string;
  paymentMethod?: PaymentMethods;
  paymentMethodDetails?: {
    last4?: string;
    brand?: string;
    bankName?: string;
    upiId?: string;
  };

  // Limits
  userLimit: number | 'unlimited';
  projectLimit: number | 'unlimited';
  integrationLimit: number | 'unlimited';
  automationLimit: number | 'unlimited';
  apiCallLimit: number | 'unlimited';
  storageLimit: number; // in MB
  storageUsed: number; // in MB

  // Features
  features: SubscriptionFeatures;

  // Usage tracking
  usage: UsageMetrics;

  // History
  planHistory: PlanHistoryEntry[];

  // Metadata
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Subscription Features
export interface SubscriptionFeatures {
  // Core features
  unlimitedProjects: boolean;
  unlimitedUsers: boolean;
  advancedCalendarViews: boolean;
  customTaskFields: boolean;

  // Analytics & Reporting
  basicReporting: boolean;
  advancedAnalytics: boolean;
  exportCapabilities: boolean;

  // Collaboration
  teamCollaboration: boolean;
  projectSharing: boolean;
  permissionControls: boolean;
  timeTracking: boolean;

  // Integrations & Automation
  basicIntegrations: boolean;
  advancedIntegrations: boolean;
  customIntegrations: boolean;
  basicAutomation: boolean;
  advancedAutomation: boolean;
  apiAccess: boolean;

  // Support & Service
  emailSupport: boolean;
  prioritySupport: boolean;
  phoneSupport: boolean;
  dedicatedAccountManager: boolean;

  // Security & Compliance
  basicSecurity: boolean;
  advancedSecurity: boolean;
  ssoEnabled: boolean;
  auditLogs: boolean;

  // Customization
  customBranding: boolean;
  whiteLabel: boolean;
}

// Usage Metrics
export interface UsageMetrics {
  projectsCreated: number;
  usersInvited: number;
  storageUsed: number;
  integrationsConnected: number;
  automationsCreated: number;
  apiCallsThisMonth: number;
  lastUsageReset: string;
}

// Usage Limit
export interface UsageLimit {
  current: number;
  limit: number | 'unlimited';
  percentage: number;
  isNearLimit: boolean;
  isAtLimit: boolean;
  isOverLimit: boolean;
}

// Usage Summary
export interface UsageSummary {
  organizationId: string;
  planId: PlanId;
  metrics: {
    projects: number;
    users: number;
    storage: number;
    integrations: number;
    automations: number;
    apiCalls: number;
  };
  limits: {
    projects: UsageLimit;
    users: UsageLimit;
    storage: UsageLimit;
    integrations: UsageLimit;
    automations: UsageLimit;
    apiCalls: UsageLimit;
  };
  recommendations: {
    shouldUpgrade: boolean;
    recommendedPlan: PlanId | null;
    reasons: string[];
  };
  lastUpdated: string;
}

// Plan History
export interface PlanHistoryEntry {
  previousPlan: PlanId;
  changedAt: string;
  reason: 'upgrade' | 'downgrade' | 'trial_end' | 'payment_failed' | 'user_request';
}

// Pricing Plan
export interface PricingPlan {
  id: PlanId;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    yearlyDiscount: number;
  };
  limits: {
    projects: number | 'unlimited';
    users: number | 'unlimited';
    storage: number;
    integrations: number | 'unlimited';
    automations: number | 'unlimited';
    apiCalls: number | 'unlimited';
    supportLevel: SupportLevel;
  };
  features: PlanFeature[];
  popular?: boolean;
  badge?: string;
  ctaText: string;
  targetAudience: string;
}

// Plan Feature
export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number | string;
  tooltip?: string;
}

// API Request/Response Types
export interface CreateSubscriptionRequest {
  planId: PlanId;
  billingCycle: BillingCycle;
}

export interface UpdateSubscriptionRequest {
  planId?: PlanId;
  billingCycle?: BillingCycle;
  status?: SubscriptionStatus;
}

export interface PaymentVerificationRequest {
  razorpay_payment_id: string;
  razorpay_subscription_id: string;
  razorpay_signature: string;
}

export interface SubscriptionResponse {
  subscription: Subscription;
}

export interface UsageSummaryResponse {
  usage: UsageSummary;
}

// Validation Result
export interface ValidationResult {
  allowed: boolean;
  reason?: string;
  currentUsage?: number;
  limit?: number | 'unlimited';
  upgradeRequired?: boolean;
  recommendedPlan?: PlanId;
}

// Payment Types
export interface PaymentMethod {
  name: string;
  description: string;
  icon: string;
  popular?: boolean;
  note?: string;
}

export interface BillingBenefit {
  name: string;
  description: string;
  flexibility: string;
  commitment: string;
  benefits?: string[];
}

// Error Types
export interface SubscriptionError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Hook Return Types
export interface UseSubscriptionReturn {
  // Data
  subscription: Subscription | null;
  usage: UsageSummary | null;
  currentPlan: PlanId;
  planStatus: SubscriptionStatus;
  billingCycle: BillingCycle;

  // Loading states
  isLoading: boolean;
  isLoadingSubscription: boolean;
  isLoadingUsage: boolean;
  hasError: boolean;

  // Mutations
  createSubscription: (data: CreateSubscriptionRequest) => Promise<any>;
  verifyPayment: (data: PaymentVerificationRequest) => Promise<any>;
  updateSubscription: (data: UpdateSubscriptionRequest) => Promise<SubscriptionResponse>;
  cancelSubscription: () => Promise<{ success: boolean; message: string }>;
  pauseSubscription: (reason?: string) => Promise<{ success: boolean; message: string }>;

  // Mutation states
  isCreatingSubscription: boolean;
  isVerifyingPayment: boolean;
  isUpdatingSubscription: boolean;
  isCancellingSubscription: boolean;
  isPausingSubscription: boolean;

  // Helper functions
  getUsagePercentage: (limitType: string) => number;
  isLimitReached: (limitType: string) => boolean;
  isNearLimit: (limitType: string) => boolean;
  formatUsageDisplay: (limitType: string) => string;
  canUpgrade: () => boolean;
  canDowngrade: () => boolean;
  getRecommendedPlan: () => PlanId | null;
  shouldShowUpgradePrompt: () => boolean;
  formatPrice: (amount: number, currency?: string) => string;
  refreshData: () => Promise<void>;

  // Refetch functions
  refetchSubscription: () => Promise<any>;
  refetchUsage: () => Promise<any>;
}
