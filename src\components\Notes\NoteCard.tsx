import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { cn } from '@/lib/utils';
import { Checkbox } from '../ui/checkbox';
import {
  Archive,
  ArchiveRestore,
  Copy,
  Edit,
  Eye,
  Hash,
  MoreHorizontal,
  Pin,
  PinOff,
  Share2,
  Star,
  StarOff,
  Trash2,
  Users,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';

const NoteCard = ({
  note,
  viewMode,
  isSelectionMode,
  isSelected,
  onToggleSelection,
  onView,
  onEdit,
  onDelete,
  onQuickAction,
}) => {
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const now = new Date();
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) {
      // 7 days
      return d.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return d.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });
    }
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const getContentPreview = () => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = note.content;
    const plainText =
      note.contentType === 'plate'
        ? tempDiv.textContent || ''
        : note.content.replace(/<[^>]*>/g, '');

    const cleanText = plainText
      .replace(/\s+/g, ' ')
      .replace(/&nbsp;/g, ' ')
      .trim();
    const hasCodeBlock = note.content.includes('<pre') || note.content.includes('<code');
    if (hasCodeBlock) {
      return truncateContent(cleanText, 100);
    }

    return truncateContent(cleanText);
  };

  return (
    <Card
      className={cn(
        'group relative transition-all duration-200 hover:shadow-md cursor-pointer',
        isSelected && 'ring-2 ring-primary',
        viewMode === 'list' && 'flex-row'
      )}
      style={{ backgroundColor: note.color !== '#ffffff' ? note.color : undefined }}
      onClick={isSelectionMode ? onToggleSelection : onView}
    >
      {/* Selection Checkbox */}
      {isSelectionMode && (
        <div className="absolute top-3 left-3 z-10">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onToggleSelection}
            onClick={e => e.stopPropagation()}
          />
        </div>
      )}

      {/* Pin Indicator */}
      {note.isPinned && (
        <div className="absolute top-3 right-3">
          <Pin className="h-4 w-4 text-primary fill-current" />
        </div>
      )}

      <CardHeader className={cn('pb-3', isSelectionMode && 'pl-10')}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold line-clamp-2 mb-1">
              {note.title}
            </CardTitle>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{formatDate(note.updatedAt)}</span>
              {note.metadata.wordCount > 0 && (
                <>
                  <span>•</span>
                  <span>{note.metadata.wordCount} words</span>
                </>
              )}
              {note.metadata.readingTime > 0 && (
                <>
                  <span>•</span>
                  <span>{note.metadata.readingTime} min read</span>
                </>
              )}
            </div>
          </div>

          {!isSelectionMode && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={e => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onView();
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onEdit();
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onQuickAction(note._id, 'isPinned');
                  }}
                >
                  {note.isPinned ? (
                    <PinOff className="h-4 w-4 mr-2" />
                  ) : (
                    <Pin className="h-4 w-4 mr-2" />
                  )}
                  {note.isPinned ? 'Unpin' : 'Pin'}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onQuickAction(note._id, 'isFavorite');
                  }}
                >
                  {note.isFavorite ? (
                    <StarOff className="h-4 w-4 mr-2" />
                  ) : (
                    <Star className="h-4 w-4 mr-2" />
                  )}
                  {note.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onQuickAction(note._id, 'isArchived');
                  }}
                >
                  {note.isArchived ? (
                    <ArchiveRestore className="h-4 w-4 mr-2" />
                  ) : (
                    <Archive className="h-4 w-4 mr-2" />
                  )}
                  {note.isArchived ? 'Unarchive' : 'Archive'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation(); /* TODO: Duplicate */
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation(); /* TODO: Share */
                  }}
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Content Preview */}
        {note.content && (
          <div className="mb-3">
            <p className="text-sm text-muted-foreground line-clamp-3">{getContentPreview()}</p>
          </div>
        )}

        {/* Tags and Category */}
        <div className="flex items-center gap-2 flex-wrap">
          {note.category && note.category !== 'general' && (
            <Badge variant="outline" className="text-xs">
              <Hash className="h-3 w-3 mr-1" />
              {note.category}
            </Badge>
          )}
          {note.tags.slice(0, 3).map((tag: string) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {note.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{note.tags.length - 3}
            </Badge>
          )}
        </div>

        {/* Status Indicators */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-2">
            {note.isFavorite && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Favorite</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {note.isArchived && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Archive className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Archived</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {note.collaborators.length > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{note.collaborators.length} collaborator(s)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NoteCard;
